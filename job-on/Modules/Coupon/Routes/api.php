<?php

use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

Route::group(['namespace' => 'API'], function () {

    // Coupon
    Route::get('coupon', 'CouponController@index');

    Route::middleware('auth:api')->group(function () {
        Route::apiResource('coupon', 'CouponController', [
            'only' => ['store', 'update', 'destroy'],
        ]);
    });
});
