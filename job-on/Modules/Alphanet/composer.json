{"name": "nwidart/alphanet", "description": "", "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "extra": {"laravel": {"providers": [], "aliases": {}}}, "autoload": {"psr-4": {"Modules\\Alphanet\\": "", "Modules\\Alphanet\\App\\": "app/", "Modules\\Alphanet\\Database\\Factories\\": "database/factories/", "Modules\\Alphanet\\Database\\Seeders\\": "database/seeders/"}}, "autoload-dev": {"psr-4": {"Modules\\Alphanet\\Tests\\": "tests/"}}}