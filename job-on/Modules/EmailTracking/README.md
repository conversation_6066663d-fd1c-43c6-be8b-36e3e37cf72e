# Email Tracking Module for JobON

This module provides email tracking functionality for the JobON application, allowing you to track when emails are opened and links are clicked.

## Features

- Track email opens with invisible tracking pixels
- Track link clicks in emails
- Collect tracking data including IP address and user agent
- View email tracking statistics
- Easy integration with existing notification system
- Automatic exclusion of emails sent to <PERSON> Admins (role: 'Supreme Admin')

## Installation

The module is automatically loaded as part of the JobON application. Make sure to run the migrations to create the necessary database tables:

```bash
php artisan module:migrate EmailTracking
```

## Usage
### Adding Tracking to Notifications


To add tracking to your notification emails, use the `TrackableEmail` trait in your notification class:

```php
use Modules\EmailTracking\Services\TrackableEmail;

class YourNotification extends Notification
{
    use Queueable, SerializesModels, TrackableEmail;
    
    public function toMail($notifiable)
    {
        $mail = (new MailMessage)
            ->subject('Your Subject')
            ->line('Your email content');
            
        // Add tracking to the email
        return $this->makeTrackable($mail, $notifiable);
    }
}
```

### Viewing Tracking Statistics

You can view tracking statistics through the API endpoint:

```
GET /api/email-tracking/statistics
```

This endpoint supports the following query parameters:

- `type`: Filter by tracking type (`open` or `click`)
- `start_date`: Filter by start date (format: Y-m-d)
- `end_date`: Filter by end date (format: Y-m-d)
- `notification_type`: Filter by notification class name
- `email`: Filter by recipient email
- `per_page`: Number of results per page (default: 15)
- `page`: Page number for pagination

## Database Schema

The module creates two tables:

1. `email_tracking_opens`: Records when emails are opened
2. `email_tracking_clicks`: Records when links in emails are clicked

## How It Works

1. When an email is sent, a unique tracking ID is generated
2. A tracking pixel is added to the email to track opens
3. Links in the email are replaced with trackable URLs
4. When the email is opened or a link is clicked, the event is recorded in the database
5. The tracking data can be viewed through the API

## Configuration

You can configure the module by publishing the configuration file:

```bash
php artisan vendor:publish --tag=emailtracking-config
```

The configuration file allows you to:

- Enable/disable email open tracking
- Enable/disable link click tracking
- Set the expiration time for tracking URLs

## Security

Tracking URLs are signed to prevent tampering and have a configurable expiration time.

## Note on Super Admin Emails

Emails sent to users with the 'Supreme Admin' role are automatically excluded from tracking to respect privacy and security concerns for administrative users.