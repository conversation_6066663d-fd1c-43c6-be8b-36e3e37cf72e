<?php

namespace Modules\EmailTracking\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Modules\EmailTracking\Services\EmailTrackingService;

class TrackEmailMiddleware
{
    /**
     * @var EmailTrackingService
     */
    protected $trackingService;

    /**
     * Create a new middleware instance.
     *
     * @param EmailTrackingService $trackingService
     */
    public function __construct(EmailTrackingService $trackingService)
    {
        $this->trackingService = $trackingService;
    }

    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        $response = $next($request);

        // Only process responses that are HTML
        if ($this->isHtmlResponse($response) && config('emailtracking.track_clicks', true)) {
            $content = $response->getContent();
            
            // Process the response content to add tracking to links
            $content = $this->trackingService->processHtmlContent($content);
            
            $response->setContent($content);
        }

        return $response;
    }

    /**
     * Determine if the response is an HTML response.
     *
     * @param  \Illuminate\Http\Response  $response
     * @return bool
     */
    protected function isHtmlResponse($response)
    {
        return $response->headers->get('Content-Type') && 
               strpos($response->headers->get('Content-Type'), 'text/html') !== false;
    }
}