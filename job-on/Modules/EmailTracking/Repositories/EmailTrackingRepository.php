<?php

namespace Modules\EmailTracking\Repositories;

use Exception;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Exceptions\ExceptionHandler;
use Modules\EmailTracking\Models\EmailTrackingOpen;
use Modules\EmailTracking\Models\EmailTrackingClick;
use Prettus\Repository\Eloquent\BaseRepository;

class EmailTrackingRepository extends BaseRepository
{
    /**
     * Specify Model class name
     *
     * @return string
     */
    function model()
    {
        return EmailTrackingOpen::class;
    }
    
    /**
     * Record an email open event.
     *
     * @param array $data
     * @return \Modules\EmailTracking\Models\EmailTrackingOpen
     */
    public function recordOpen(array $data)
    {
        DB::beginTransaction();
        try {
            $open = EmailTrackingOpen::create([
                'tracking_id' => $data['tracking_id'],
                'notification_id' => $data['notification_id'],
                'notifiable_id' => $data['notifiable_id'] ?? null,
                'notifiable_type' => $data['notifiable_type'] ?? null,
                'email' => $data['email'] ?? null,
                'ip_address' => $data['ip_address'] ?? null,
                'user_agent' => $data['user_agent'] ?? null,
                'opened_at' => Carbon::now(),
            ]);
            
            DB::commit();
            return $open;
        } catch (Exception $e) {
            DB::rollback();
            Log::error('Failed to record email open: ' . $e->getMessage());
            throw new ExceptionHandler($e->getMessage(), $e->getCode());
        }
    }
    
    /**
     * Record an email link click event.
     *
     * @param array $data
     * @return \Modules\EmailTracking\Models\EmailTrackingClick
     */
    public function recordClick(array $data)
    {
        DB::beginTransaction();
        try {
            $click = EmailTrackingClick::create([
                'tracking_id' => $data['tracking_id'],
                'notification_id' => $data['notification_id'],
                'notifiable_id' => $data['notifiable_id'] ?? null,
                'notifiable_type' => $data['notifiable_type'] ?? null,
                'email' => $data['email'] ?? null,
                'original_url' => $data['original_url'],
                'ip_address' => $data['ip_address'] ?? null,
                'user_agent' => $data['user_agent'] ?? null,
                'clicked_at' => Carbon::now(),
            ]);
            
            DB::commit();
            return $click;
        } catch (Exception $e) {
            DB::rollback();
            Log::error('Failed to record email click: ' . $e->getMessage());
            throw new ExceptionHandler($e->getMessage(), $e->getCode());
        }
    }
    
    /**
     * Get email tracking statistics.
     *
     * @param array $filters
     * @return array
     */
    public function getStatistics(array $filters = [])
    {
        $query = DB::table('notifications')
            ->leftJoin('email_tracking_opens', 'notifications.id', '=', 'email_tracking_opens.notification_id')
            ->leftJoin('email_tracking_clicks', 'notifications.id', '=', 'email_tracking_clicks.notification_id')
            ->leftJoin('users', function($join) {
                $join->on('notifications.notifiable_id', '=', 'users.id')
                     ->where('notifications.notifiable_type', '=', 'App\\Models\\User');
            })
            ->leftJoin('model_has_roles', function($join) {
                $join->on('users.id', '=', 'model_has_roles.model_id')
                     ->where('model_has_roles.model_type', '=', 'App\\Models\\User');
            })
            ->leftJoin('roles', 'model_has_roles.role_id', '=', 'roles.id')
            ->select(
                'notifications.id as notification_id',
                'notifications.created_at as sent_at',
                'notifications.type',
                'notifications.notifiable_type',
                'notifications.notifiable_id',
                'users.email',
                DB::raw('COALESCE(email_tracking_opens.email, email_tracking_clicks.email, users.email) as recipient_email'),
                DB::raw('CASE WHEN roles.name IS NOT NULL THEN roles.name ELSE "Unknown" END as role_name'),
                DB::raw('MAX(email_tracking_opens.opened_at) as opened_at'),
                DB::raw('COUNT(DISTINCT email_tracking_opens.id) as open_count'),
                DB::raw('COUNT(DISTINCT email_tracking_clicks.id) as click_count')
            )
            ->groupBy(
                'notifications.id',
                'notifications.created_at',
                'notifications.type',
                'notifications.notifiable_type',
                'notifications.notifiable_id',
                'users.email',
                'recipient_email',
                'role_name'
            );
        
        // Apply filters
        if (!empty($filters['notification_type'])) {
            $query->where('notifications.type', $filters['notification_type']);
        }
        
        if (!empty($filters['notifiable_type'])) {
            $query->where('notifications.notifiable_type', $filters['notifiable_type']);
        }
        
        if (!empty($filters['notifiable_id'])) {
            $query->where('notifications.notifiable_id', $filters['notifiable_id']);
        }
        
        if (!empty($filters['start_date'])) {
            $query->where('notifications.created_at', '>=', $filters['start_date']);
        }
        
        if (!empty($filters['end_date'])) {
            $query->where('notifications.created_at', '<=', $filters['end_date']);
        }
        
        // Paginate results
        $perPage = $filters['per_page'] ?? 15;
        return $query->paginate($perPage);
    }
    
    /**
     * Get daily email metrics for performance visualization.
     *
     * @param array $filters
     * @return array
     */
    public function getDailyMetrics(array $filters = [])
    {
        // Default to last 30 days if no date range provided
        $startDate = !empty($filters['start_date']) 
            ? Carbon::parse($filters['start_date']) 
            : Carbon::now()->subDays(30);
            
        $endDate = !empty($filters['end_date']) 
            ? Carbon::parse($filters['end_date']) 
            : Carbon::now();
        
        // Get sent emails count by day
        $sentQuery = DB::table('notifications')
            ->select(
                DB::raw('DATE(created_at) as date'),
                DB::raw('COUNT(*) as count')
            )
            ->whereBetween('created_at', [$startDate, $endDate]);
            
        // Apply filters
        if (!empty($filters['notification_type'])) {
            $sentQuery->where('type', $filters['notification_type']);
        }
        
        if (!empty($filters['notifiable_type'])) {
            $sentQuery->where('notifiable_type', $filters['notifiable_type']);
        }
        
        $sentByDay = $sentQuery->groupBy(DB::raw('DATE(created_at)'))
            ->orderBy('date')
            ->get()
            ->keyBy('date')
            ->toArray();
        
        // Get opened emails count by day
        $openedQuery = DB::table('email_tracking_opens')
            ->select(
                DB::raw('DATE(opened_at) as date'),
                DB::raw('COUNT(DISTINCT notification_id) as count')
            )
            ->whereBetween('opened_at', [$startDate, $endDate]);
            
        // Apply filters for notification_id if needed
        if (!empty($filters['notification_type']) || !empty($filters['notifiable_type'])) {
            $openedQuery->whereIn('notification_id', function($query) use ($filters) {
                $query->select('id')
                    ->from('notifications');
                    
                if (!empty($filters['notification_type'])) {
                    $query->where('type', $filters['notification_type']);
                }
                
                if (!empty($filters['notifiable_type'])) {
                    $query->where('notifiable_type', $filters['notifiable_type']);
                }
            });
        }
        
        $openedByDay = $openedQuery->groupBy(DB::raw('DATE(opened_at)'))
            ->orderBy('date')
            ->get()
            ->keyBy('date')
            ->toArray();
        
        // Get clicked emails count by day
        $clickedQuery = DB::table('email_tracking_clicks')
            ->select(
                DB::raw('DATE(clicked_at) as date'),
                DB::raw('COUNT(DISTINCT notification_id) as count')
            )
            ->whereBetween('clicked_at', [$startDate, $endDate]);
            
        // Apply filters for notification_id if needed
        if (!empty($filters['notification_type']) || !empty($filters['notifiable_type'])) {
            $clickedQuery->whereIn('notification_id', function($query) use ($filters) {
                $query->select('id')
                    ->from('notifications');
                    
                if (!empty($filters['notification_type'])) {
                    $query->where('type', $filters['notification_type']);
                }
                
                if (!empty($filters['notifiable_type'])) {
                    $query->where('notifiable_type', $filters['notifiable_type']);
                }
            });
        }
        
        $clickedByDay = $clickedQuery->groupBy(DB::raw('DATE(clicked_at)'))
            ->orderBy('date')
            ->get()
            ->keyBy('date')
            ->toArray();
        
        // Generate a complete date range
        $dateRange = [];
        $currentDate = clone $startDate;
        
        while ($currentDate <= $endDate) {
            $dateStr = $currentDate->format('Y-m-d');
            $dateRange[] = [
                'date' => $dateStr,
                'sent' => $sentByDay[$dateStr]->count ?? 0,
                'opened' => $openedByDay[$dateStr]->count ?? 0,
                'clicked' => $clickedByDay[$dateStr]->count ?? 0,
            ];
            $currentDate->addDay();
        }
        
        return $dateRange;
    }
    
    /**
     * Get email distribution data for pie/donut charts.
     *
     * @param array $filters
     * @return array
     */
    public function getEmailDistribution(array $filters = [])
    {
        // Default to last 30 days if no date range provided
        $startDate = !empty($filters['start_date']) 
            ? Carbon::parse($filters['start_date']) 
            : Carbon::now()->subDays(30);
            
        $endDate = !empty($filters['end_date']) 
            ? Carbon::parse($filters['end_date']) 
            : Carbon::now();
        
        // Base query to get all emails in the period
        $baseQuery = DB::table('notifications')
            ->whereBetween('created_at', [$startDate, $endDate]);
            
        // Apply filters
        if (!empty($filters['notification_type'])) {
            $baseQuery->where('type', $filters['notification_type']);
        }
        
        if (!empty($filters['notifiable_type'])) {
            $baseQuery->where('notifiable_type', $filters['notifiable_type']);
        }
        
        // Get total emails sent
        $totalSent = (clone $baseQuery)->count();
        
        if ($totalSent === 0) {
            return [
                'total' => 0,
                'distribution' => [
                    ['status' => 'Unopened', 'count' => 0, 'percentage' => 0],
                    ['status' => 'Opened', 'count' => 0, 'percentage' => 0],
                    ['status' => 'Clicked', 'count' => 0, 'percentage' => 0],
                ]
            ];
        }
        
        // Get opened emails (distinct notification_ids)
        $openedIds = DB::table('email_tracking_opens')
            ->select('notification_id')
            ->distinct()
            ->pluck('notification_id')
            ->toArray();
        
        // Get clicked emails (distinct notification_ids)
        $clickedIds = DB::table('email_tracking_clicks')
            ->select('notification_id')
            ->distinct()
            ->pluck('notification_id')
            ->toArray();
        
        // Calculate counts
        $clickedCount = count($clickedIds);
        $openedOnlyCount = count(array_diff($openedIds, $clickedIds));
        $unopenedCount = $totalSent - $clickedCount - $openedOnlyCount;
        
        // Calculate percentages
        $clickedPercentage = round(($clickedCount / $totalSent) * 100, 1);
        $openedPercentage = round(($openedOnlyCount / $totalSent) * 100, 1);
        $unopenedPercentage = round(($unopenedCount / $totalSent) * 100, 1);
        
        return [
            'total' => $totalSent,
            'distribution' => [
                [
                    'status' => 'Unopened', 
                    'count' => $unopenedCount, 
                    'percentage' => $unopenedPercentage
                ],
                [
                    'status' => 'Opened', 
                    'count' => $openedOnlyCount, 
                    'percentage' => $openedPercentage
                ],
                [
                    'status' => 'Clicked', 
                    'count' => $clickedCount, 
                    'percentage' => $clickedPercentage
                ],
            ]
        ];
    }
}