<?php

use Illuminate\Support\Facades\Route;
use Modules\EmailTracking\Http\Controllers\API\EmailTrackingController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

Route::prefix('email-tracking')->group(function () {
    // Public routes for tracking
    Route::get('track-open/{tracking_id}', [EmailTrackingController::class, 'trackOpen'])
        ->name('email-tracking.track-open-event')
        ->middleware('signed');
        
    Route::get('track-click/{tracking_id}', [EmailTrackingController::class, 'trackClick'])
        ->name('email-tracking.track-click')
        ->middleware('signed');
    
    // Protected routes for statistics and analytics
    Route::middleware('auth:api')->group(function () {
        Route::get('statistics', [EmailTrackingController::class, 'getStatistics'])
            ->name('email-tracking.statistics');
            
        // Analytics endpoints
        Route::get('analytics/daily-metrics', [EmailTrackingController::class, 'getDailyMetrics'])
            ->name('email-tracking.analytics.daily-metrics');
            
        Route::get('analytics/distribution', [EmailTrackingController::class, 'getEmailDistribution'])
            ->name('email-tracking.analytics.distribution');
    });
});