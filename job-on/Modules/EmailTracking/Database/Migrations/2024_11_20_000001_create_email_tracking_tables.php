<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Table for tracking email opens
        Schema::create('email_tracking_opens', function (Blueprint $table) {
            $table->id();
            $table->uuid('tracking_id')->unique()->index();
            $table->uuid('notification_id')->index();
            $table->morphs('notifiable'); // User or entity that received the email
            $table->string('email')->nullable();
            $table->string('ip_address')->nullable();
            $table->string('user_agent')->nullable();
            $table->timestamp('opened_at');
            $table->timestamps();
            
            $table->foreign('notification_id')
                  ->references('id')
                  ->on('notifications')
                  ->onDelete('cascade');
        });

        // Table for tracking email link clicks
        Schema::create('email_tracking_clicks', function (Blueprint $table) {
            $table->id();
            $table->uuid('tracking_id')->unique()->index();
            $table->uuid('notification_id')->index();
            $table->morphs('notifiable'); // User or entity that received the email
            $table->string('email')->nullable();
            $table->string('original_url');
            $table->string('ip_address')->nullable();
            $table->string('user_agent')->nullable();
            $table->timestamp('clicked_at');
            $table->timestamps();
            
            $table->foreign('notification_id')
                  ->references('id')
                  ->on('notifications')
                  ->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('email_tracking_clicks');
        Schema::dropIfExists('email_tracking_opens');
    }
};