<?php

namespace Modules\EmailTracking\Database\Seeders;

use Carbon\Carbon;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Log;
use App\Models\User;
use App\Enums\RoleEnum;

class EmailTrackingDemoSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {   
        try {
            // Get users for different roles to use as notifiables
            $consumers = User::role(RoleEnum::CONSUMER)->take(5)->get();
            $providers = User::role(RoleEnum::PROVIDER)->take(5)->get();
            
            // Check if we have users to work with
            if ($consumers->isEmpty() && $providers->isEmpty()) {
                // Create some dummy users if none exist
                $this->command->info('No users found with CONSUMER or PROVIDER roles. Creating dummy users for seeding.');
                
                // Create a dummy consumer
                $consumer = User::create([
                    'name' => 'Demo Consumer',
                    'email' => '<EMAIL>',
                    'password' => bcrypt('password'),
                    'status' => true,
                ]);
                $consumer->assignRole(RoleEnum::CONSUMER);
                $consumers = collect([$consumer]);
                
                // Create a dummy provider
                $provider = User::create([
                    'name' => 'Demo Provider',
                    'email' => '<EMAIL>',
                    'password' => bcrypt('password'),
                    'status' => true,
                ]);
                $provider->assignRole(RoleEnum::PROVIDER);
                $providers = collect([$provider]);
            }
            
            // Common notification types from the application
            $notificationTypes = [
                'App\\Notifications\\CreateBookingNotification',
                'App\\Notifications\\BookingReminderNotification',
                'App\\Notifications\\CreateBidNotification',
                'App\\Notifications\\UpdateBidNotification',
                'App\\Notifications\\CreateServiceNotification',
                'App\\Notifications\\CreateProviderNotification',
                'App\\Notifications\\StoreMessageNotification',
                'App\\Notifications\\AddExtraChargeNotification',
                'App\\Notifications\\CertificateStatusNotification',
                'App\\Notifications\\VerifyProofNotification',
            ];
            
            // User agents for more realistic data
            $userAgents = [
                'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.114 Safari/537.36',
                'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                'Mozilla/5.0 (iPhone; CPU iPhone OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1',
                'Mozilla/5.0 (iPad; CPU OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1',
                'Mozilla/5.0 (Linux; Android 11; SM-G991B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.120 Mobile Safari/537.36',
            ];
            
            // IP addresses for more realistic data
            $ipAddresses = [
                '***********',
                '********',
                '**********',
                '*******',
                '*******',
            ];
            
            // URLs for click tracking
            $urls = [
                'https://jobon.com/bookings/details',
                'https://jobon.com/services/view',
                'https://jobon.com/profile/edit',
                'https://jobon.com/messages',
                'https://jobon.com/payments',
            ];
            
            // Create 100 sample notifications with tracking data
            $startDate = Carbon::now()->subMonths(3);
            $endDate = Carbon::now();
            
            $notificationCount = 100;
            $notifications = [];
            $opens = [];
            $clicks = [];
            
            for ($i = 0; $i < $notificationCount; $i++) {
                // Randomly select a user and notification type
                $notifiableCollection = rand(0, 1) ? $consumers : $providers;
                $notifiable = $notifiableCollection->random();
                $notificationType = $notificationTypes[array_rand($notificationTypes)];
                
                // Generate a random date between start and end date
                $createdAt = Carbon::createFromTimestamp(
                    rand($startDate->timestamp, $endDate->timestamp)
                )->format('Y-m-d H:i:s');
                
                // Create notification
                $notificationId = Str::uuid()->toString();
                $notifications[] = [
                    'id' => $notificationId,
                    'type' => $notificationType,
                    'notifiable_type' => get_class($notifiable),
                    'notifiable_id' => $notifiable->id,
                    'data' => json_encode([
                        'message' => 'Sample notification message for ' . $notificationType,
                        'action_url' => $urls[array_rand($urls)],
                    ]),
                    'read_at' => rand(0, 1) ? Carbon::createFromTimestamp(
                        rand($startDate->timestamp, $endDate->timestamp)
                    )->format('Y-m-d H:i:s') : null,
                    'created_at' => $createdAt,
                    'updated_at' => $createdAt,
                ];
                
                // Determine if this email was opened (70% chance)
                if (rand(1, 10) <= 7) {
                    $openedAt = Carbon::parse($createdAt)->addMinutes(rand(5, 1440))->format('Y-m-d H:i:s');
                    $trackingId = Str::uuid()->toString();
                    
                    // Add open record
                    $opens[] = [
                        'tracking_id' => $trackingId,
                        'notification_id' => $notificationId,
                        'notifiable_type' => get_class($notifiable),
                        'notifiable_id' => $notifiable->id,
                        'email' => $notifiable->email,
                        'ip_address' => $ipAddresses[array_rand($ipAddresses)],
                        'user_agent' => $userAgents[array_rand($userAgents)],
                        'opened_at' => $openedAt,
                        'created_at' => $openedAt,
                        'updated_at' => $openedAt,
                    ];
                    
                    // Determine if links were clicked (50% chance if opened)
                    if (rand(1, 10) <= 5) {
                        $clickedAt = Carbon::parse($openedAt)->addMinutes(rand(1, 60))->format('Y-m-d H:i:s');
                        $clickTrackingId = Str::uuid()->toString();
                        
                        // Add click record
                        $clicks[] = [
                            'tracking_id' => $clickTrackingId,
                            'notification_id' => $notificationId,
                            'notifiable_type' => get_class($notifiable),
                            'notifiable_id' => $notifiable->id,
                            'email' => $notifiable->email,
                            'original_url' => $urls[array_rand($urls)],
                            'ip_address' => $ipAddresses[array_rand($ipAddresses)],
                            'user_agent' => $userAgents[array_rand($userAgents)],
                            'clicked_at' => $clickedAt,
                            'created_at' => $clickedAt,
                            'updated_at' => $clickedAt,
                        ];
                    }
                }
            }
            
            // Insert data in chunks to avoid memory issues
            DB::table('notifications')->insert($notifications);
            DB::table('email_tracking_opens')->insert($opens);
            DB::table('email_tracking_clicks')->insert($clicks);
            
            $this->command->info('Created ' . count($notifications) . ' sample notifications');
            $this->command->info('Created ' . count($opens) . ' sample email opens');
            $this->command->info('Created ' . count($clicks) . ' sample email clicks');
            
        } catch (\Exception $e) {
            Log::error('Error seeding email tracking data: ' . $e->getMessage());
            $this->command->error('Error seeding email tracking data: ' . $e->getMessage());
        }
    }
}