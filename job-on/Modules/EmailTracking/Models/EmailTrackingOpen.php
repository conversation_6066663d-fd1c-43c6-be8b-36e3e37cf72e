<?php

namespace Modules\EmailTracking\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Notifications\DatabaseNotification;

class EmailTrackingOpen extends Model
{
    protected $fillable = [
        'tracking_id',
        'notification_id',
        'notifiable_id',
        'notifiable_type',
        'email',
        'ip_address',
        'user_agent',
        'opened_at',
    ];

    protected $casts = [
        'opened_at' => 'datetime',
    ];

    /**
     * Get the notification that was opened.
     */
    public function notification(): BelongsTo
    {
        return $this->belongsTo(DatabaseNotification::class, 'notification_id', 'id');
    }

    /**
     * Get the notifiable entity that received the notification.
     */
    public function notifiable()
    {
        return $this->morphTo();
    }
}