# Email Tracking Module Installation Guide

This guide provides step-by-step instructions for installing and configuring the Email Tracking module for JobON.

## Prerequisites

- Laravel 11.x
- PHP 8.1+
- JobON application installed and configured

## Installation Steps

### 1. Register the Module

The module should be automatically registered if you're using the Laravel Modules package. If not, add the following to your `config/app.php` file:

```php
'providers' => [
    // ...
    Modules\EmailTracking\Providers\EmailTrackingServiceProvider::class,
],
```

### 2. Run Migrations

Run the migrations to create the necessary database tables:

```bash
php artisan module:migrate EmailTracking
```

### 3. Publish Configuration (Optional)

Publish the configuration file to customize the module settings:

```bash
php artisan vendor:publish --tag=emailtracking-config
```

### 4. Register Service Provider

Ensure the module's service provider is registered in `config/app.php`:

```php
'providers' => [
    // ...
    Modules\EmailTracking\Providers\EmailTrackingServiceProvider::class,
],
```

## Integration with Notifications

To add tracking to your notification emails, use the `TrackableEmail` trait in your notification class:

```php
use Modules\EmailTracking\Services\TrackableEmail;

class YourNotification extends Notification
{
    use Queueable, SerializesModels, TrackableEmail;
    
    public function toMail($notifiable)
    {
        $mail = (new MailMessage)
            ->subject('Your Subject')
            ->line('Your email content');
            
        // Add tracking to the email
        return $this->makeTrackable($mail, $notifiable);
    }
}
```

## Super Admin Exclusion

The module automatically excludes emails sent to users with the 'Supreme Admin' role from tracking. This is built into the `TrackableEmail` trait and requires no additional configuration.

## Troubleshooting

### Common Issues

1. **Migrations not running**
   - Make sure the module is properly registered
   - Check database connection settings
   - Run `php artisan module:list` to verify the module is detected

2. **Tracking not working**
   - Ensure the notification class is using the `TrackableEmail` trait
   - Check that the `makeTrackable` method is called in the `toMail` method
   - Verify that the email template includes the tracking pixel
   - Check that the recipient is not a Super Admin (tracking is disabled for them)

3. **API endpoints not accessible**
   - Check route configuration
   - Verify authentication middleware is properly configured
   - Ensure the user has the necessary permissions

## Support

For additional support, please contact the development team or refer to the module documentation.