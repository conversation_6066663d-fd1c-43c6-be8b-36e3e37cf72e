<?php

namespace Modules\EmailTracking\Services;

use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Support\Str;

trait TrackableEmail
{
    /**
     * Make a mail message trackable.
     *
     * @param \Illuminate\Notifications\Messages\MailMessage $mailMessage
     * @param object $notifiable
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    protected function makeTrackable(MailMessage $mailMessage, object $notifiable): MailMessage
    {
        // Skip tracking for super admin emails
        if (method_exists($notifiable, 'hasRole') && $notifiable->hasRole('Supreme Admin')) {
            return $mailMessage;
        }
        
        // Generate a notification ID if not already set
        $notificationId = $this->id ?? (string) Str::uuid();
        $this->id = $notificationId;
        
        // Get the email tracking service
        $trackingService = app(EmailTrackingService::class);
        
        // Generate a unique tracking ID for this email
        $trackingId = $trackingService->generateTrackingId();
        
        // Store tracking information in the mail message viewData
        $mailMessage->viewData['tracking_id'] = $trackingId;
        $mailMessage->viewData['notification_id'] = $notificationId;
        $mailMessage->viewData['notifiable_id'] = $notifiable->getKey();
        $mailMessage->viewData['notifiable_type'] = get_class($notifiable);
        $mailMessage->viewData['email'] = $notifiable->email ?? $notifiable->routeNotificationFor('mail');
        
        // Generate tracking pixel URL
        $trackingPixelUrl = $trackingService->generateTrackingPixelUrl($trackingId, $notificationId);
        $mailMessage->viewData['tracking_pixel_url'] = $trackingPixelUrl;
        
        return $mailMessage;
    }
}