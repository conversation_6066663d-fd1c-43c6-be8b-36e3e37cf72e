# Integrating Email Tracking with Existing Notifications

This guide explains how to add email tracking functionality to your existing notification classes in the JobON application.

## Overview

The Email Tracking module provides a trait called `TrackableEmail` that you can use to add tracking functionality to your notification classes. When this trait is used, emails sent through the notification will include:

1. A tracking pixel to detect when the email is opened
2. Tracked links to detect when links in the email are clicked

**Note:** Emails sent to users with the 'Supreme Admin' role are automatically excluded from tracking for privacy and security reasons.

## Integration Steps

### 1. Import the TrackableEmail Trait

Add the following import statement to your notification class:

```php
use Modules\EmailTracking\Services\TrackableEmail;
```

### 2. Use the Trait in Your Notification Class

Add the trait to your notification class:

```php
class YourNotification extends Notification
{
    use Queueable, SerializesModels, TrackableEmail;
    
    // ... existing code
}
```

### 3. Modify the toMail Method

Update your `toMail` method to use the `makeTrackable` method provided by the trait:

```php
public function toMail($notifiable)
{
    $mail = (new MailMessage)
        ->subject('Your Subject')
        ->line('Your email content')
        ->action('Click Here', url('/some-url'));
        
    // Add tracking to the email
    return $this->makeTrackable($mail, $notifiable);
}
```

## Example: Before and After

### Before Integration

```php
<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use Illuminate\Queue\SerializesModels;

class BookingConfirmation extends Notification
{
    use Queueable, SerializesModels;

    protected $booking;

    public function __construct($booking)
    {
        $this->booking = $booking;
    }

    public function via($notifiable)
    {
        return ['mail'];
    }

    public function toMail($notifiable)
    {
        return (new MailMessage)
            ->subject('Booking Confirmation')
            ->line('Your booking has been confirmed.')
            ->line('Booking ID: ' . $this->booking->id)
            ->action('View Booking', url('/bookings/' . $this->booking->id));
    }
}
```

### After Integration

```php
<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use Illuminate\Queue\SerializesModels;
use Modules\EmailTracking\Services\TrackableEmail;

class BookingConfirmation extends Notification
{
    use Queueable, SerializesModels, TrackableEmail;

    protected $booking;

    public function __construct($booking)
    {
        $this->booking = $booking;
    }

    public function via($notifiable)
    {
        return ['mail'];
    }

    public function toMail($notifiable)
    {
        $mail = (new MailMessage)
            ->subject('Booking Confirmation')
            ->line('Your booking has been confirmed.')
            ->line('Booking ID: ' . $this->booking->id)
            ->action('View Booking', url('/bookings/' . $this->booking->id));
            
        // Add tracking to the email
        return $this->makeTrackable($mail, $notifiable);
    }
}
```

## Important Notes

1. The `makeTrackable` method must be called at the end of the `toMail` method, after all content has been added to the mail message.

2. The tracking functionality works with both HTML and plain text emails, but open tracking (via the tracking pixel) only works with HTML emails.

3. Emails sent to users with the 'Supreme Admin' role will not be tracked. The `TrackableEmail` trait automatically checks for this role and skips tracking for these users.

4. If you need to customize the tracking behavior, you can override the `makeTrackable` method in your notification class.

## Troubleshooting

If tracking is not working as expected, check the following:

1. Ensure the notification class is using the `TrackableEmail` trait
2. Verify that the `makeTrackable` method is called at the end of the `toMail` method
3. Check that the email template includes the tracking pixel
4. Confirm that the recipient is not a user with the 'Supreme Admin' role (tracking is disabled for them)
5. Make sure the email client allows loading of remote images (for tracking pixels)