# Integrating Email Tracking with Existing Notifications

This guide demonstrates how to integrate the Email Tracking module with existing notification classes in your JobON application.

## Step 1: Import the TrackableEmail Trait

Add the `TrackableEmail` trait to your notification class:

```php
use Modules\EmailTracking\Services\TrackableEmail;

class CreateBookingNotification extends Notification
{
    use Queueable, SerializesModels, TrackableEmail;
    
    // Existing notification code...
}
```

## Step 2: Modify the toMail Method

Update the `toMail` method to make the email trackable:

### Before:

```php
public function toMail($notifiable)
{
    $locale = $this->booking->customer?->locale ?? config('app.locale');
    $emailTemplate = $this->getEmailTemplate($notifiable);
    
    return (new MailMessage)
        ->subject($emailTemplate->title[$locale] ?? $emailTemplate->title['en'])
        ->markdown('emails.email-template', [
            'content' => $emailTemplate,
            'locale' => $locale,
            'emailContent' => $this->getEmailContent($locale),
        ]);
}
```

### After:

```php
public function toMail($notifiable)
{
    $locale = $this->booking->customer?->locale ?? config('app.locale');
    $emailTemplate = $this->getEmailTemplate($notifiable);
    
    $mail = (new MailMessage)
        ->subject($emailTemplate->title[$locale] ?? $emailTemplate->title['en'])
        ->markdown('emails.email-template', [
            'content' => $emailTemplate,
            'locale' => $locale,
            'emailContent' => $this->getEmailContent($locale),
        ]);
    
    // Make the email trackable
    return $this->makeTrackable($mail, $notifiable);
}
```

## Step 3: Update the Email Template (Optional)

For better tracking pixel placement, you can use the tracked email template:

```php
public function toMail($notifiable)
{
    $locale = $this->booking->customer?->locale ?? config('app.locale');
    $emailTemplate = $this->getEmailTemplate($notifiable);
    
    $mail = (new MailMessage)
        ->subject($emailTemplate->title[$locale] ?? $emailTemplate->title['en'])
        ->markdown('emailtracking::emails.tracked-email-template', [ // Note the changed template path
            'content' => $emailTemplate,
            'locale' => $locale,
            'emailContent' => $this->getEmailContent($locale),
        ]);
    
    // Make the email trackable
    return $this->makeTrackable($mail, $notifiable);
}
```

## Example: Modifying CreateBookingNotification

Here's a complete example of how to modify the `CreateBookingNotification` class to include email tracking:

```php
<?php

namespace App\Notifications;

use App\Models\Booking;
use App\Models\EmailTemplate;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use Illuminate\Support\Facades\Lang;
use Modules\EmailTracking\Services\TrackableEmail;

class CreateBookingNotification extends Notification implements ShouldQueue
{
    use Queueable, TrackableEmail;

    protected $booking;

    public function __construct(Booking $booking)
    {
        $this->booking = $booking;
    }

    public function via($notifiable)
    {
        return ['mail', 'database'];
    }

    public function toMail($notifiable)
    {
        $locale = $this->booking->customer?->locale ?? config('app.locale');
        $emailTemplate = $this->getEmailTemplate($notifiable);
        
        $mail = (new MailMessage)
            ->subject($emailTemplate->title[$locale] ?? $emailTemplate->title['en'])
            ->markdown('emails.email-template', [
                'content' => $emailTemplate,
                'locale' => $locale,
                'emailContent' => $this->getEmailContent($locale),
            ]);
        
        // Make the email trackable
        return $this->makeTrackable($mail, $notifiable);
    }

    public function toArray($notifiable)
    {
        // Existing toArray implementation...
    }

    protected function getEmailTemplate($notifiable)
    {
        // Existing getEmailTemplate implementation...
    }

    protected function getEmailContent($locale)
    {
        // Existing getEmailContent implementation...
    }
}
```

## Viewing Tracking Data

Once you've integrated email tracking with your notifications, you can view tracking data through the API endpoint:

```
GET /api/email-tracking/statistics
```

This endpoint requires authentication and supports filtering by date range, notification type, and recipient email.