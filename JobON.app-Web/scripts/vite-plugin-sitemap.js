import { execSync } from 'child_process';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

export function sitemapPlugin() {
  return {
    name: 'sitemap-generator',
    apply: 'build', // Only run during build
    buildStart() {
      console.log('🔧 Vite Plugin: Generating dynamic sitemap...');
      try {
        execSync('node scripts/generate-dynamic-sitemap-v2.cjs', { 
          stdio: 'inherit',
          cwd: join(__dirname, '..')
        });
        console.log('✅ Vite Plugin: Sitemap generated successfully!');
      } catch (error) {
        console.error('❌ Vite Plugin: Error generating sitemap:', error.message);
        // Don't fail the build, just log the error
      }
    }
  };
}
