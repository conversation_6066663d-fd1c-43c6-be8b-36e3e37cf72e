
import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { useToast } from "@/hooks/use-toast";
import { useAddressAutocomplete } from "@/hooks/use-address-autocomplete";
import { ImageUploader } from "@/components/ImageUploader";
import { MapPin, Phone, Globe, Mail, Clock, Plus, Trash2, Loader2, AlertCircle, MessageSquare } from "lucide-react";
import { apiService } from "@/services/api";
import { assetsService } from "@/services/assetsService";
import useAuthHeader from "react-auth-kit/hooks/useAuthHeader";
import { BusinessType } from '@/pages/Business';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";

// Category options
const CATEGORY_OPTIONS = [
  { value: "Plumbing", label: "Plumbing" },
  { value: "Electrical", label: "Electrical" },
  { value: "Cleaning", label: "Cleaning" },
  { value: "Pest Control", label: "Pest Control" },
  { value: "Landscaping", label: "Landscaping" },
  { value: "Handyman", label: "Handyman" },
  { value: "Appliance Repair", label: "Appliance Repair" },
  { value: "HVAC", label: "HVAC" },
];

// Days of the week
const DAYS_OF_WEEK = [
  "Sunday", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday"
];

interface CreateBusinessFormProps {
  onClose: () => void;
  onSuccess: () => void;
  business?: BusinessType;
  isEditing?: boolean;
}

interface BusinessFormData {
  name: string;
  category: string;
  location: string;
  address: string;
  phone: string;
  website: string;
  email: string;
  hours: { [key: string]: string }; // Changed to object
  photos: string[];
  services: string[];
  lat: string;
  lng: string;
  reviews: {
    text: string;
    rating: string;
  }[];
}

interface FormErrors {
  name?: string;
  category?: string;
  location?: string;
  address?: string;
  phone?: string;
  website?: string;
  email?: string;
  hours?: string;
  photos?: string;
  services?: string;
  reviews?: string;
  lat?: string;
  lng?: string;
}

interface AddressSuggestionType {
  address: string;
  city: string;
  state: string;
  zipCode: string; // Added zipCode
  // Add other properties if available from the autocomplete service
}

export const CreateBusinessForm: React.FC<CreateBusinessFormProps> = ({
  onClose,
  onSuccess,
  business, 
  isEditing = false 
}) => {
  const { toast } = useToast();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const [formData, setFormData] = useState<BusinessFormData>({
    name: '',
    category: '',
    location: '',
    address: '',
    phone: '',
    website: '',
    email: '',
    hours: DAYS_OF_WEEK.reduce((acc, day) => { // Initialize as object
      acc[day.toLowerCase()] = 'Open 24 hours';
      return acc;
    }, {} as { [key: string]: string }),
    photos: [],
    services: [''],
    lat: '',
    lng: '',
    reviews: [],
  });
  const [errors, setErrors] = useState<FormErrors>({});
  const [photoFiles, setPhotoFiles] = useState<File[]>([]);
  const [submitAttempted, setSubmitAttempted] = useState(false);
  const [isDeleteReviewDialogOpen, setIsDeleteReviewDialogOpen] = useState(false);
  const [reviewToDelete, setReviewToDelete] = useState<number | null>(null);
  const [isReviewsModalOpen, setIsReviewsModalOpen] = useState(false);
  const authHeader = useAuthHeader()

  // Address autocomplete
  const {
    query,
    setQuery,
    suggestions,
    isLoading: isAddressLoading,
    error: addressError,
    handleSelectAddress,
  } = useAddressAutocomplete();

  // Initialize form data with business data if provided
  useEffect(() => {
    if (business && isEditing) {
      setFormData({
        name: business.name || '',
        category: business.category || '',
        location: business.location || '',
        address: business.address || '',
        phone: business.phone || '',
        website: business.website || '',
        email: business.email || '',
        hours: business.hours || DAYS_OF_WEEK.reduce((acc, day) => {
          acc[day.toLowerCase()] = 'Open 24 hours';
          return acc;
        }, {} as { [key: string]: string }),
        photos: business.photos || [],
        services: business.services?.length ? business.services : [''],
        lat: business?.lat || '',
        lng: business?.lng || '',
        reviews: business.reviews || [],
      });

      // Update query for address autocomplete
      setQuery(business.address || '');
    }
  }, [business, isEditing, setQuery]);

  // Handle input change
  const handleInputChange = (field: keyof BusinessFormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));

    // Clear error for the field
    if (errors[field as keyof FormErrors]) {
      setErrors(prev => ({ ...prev, [field]: undefined }));
    }
  };

  // Handle address input change
  const handleAddressInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setQuery(e.target.value);
    handleInputChange('address', e.target.value);
  };

  // Handle address selection
  const handleAddressSelection = (address: AddressSuggestionType) => {
    console.log(address);
    handleInputChange('address', address.address);
    handleInputChange('location', `${address.city}, ${address.state}`);
    handleInputChange('lat', '34.122144');
    handleInputChange('lng', '-118.263123');

    handleSelectAddress(address);
  };

  // Handle service change
  const handleServiceChange = (index: number, value: string) => {
    const updatedServices = [...formData.services];
    updatedServices[index] = value;
    setFormData(prev => ({ ...prev, services: updatedServices }));

    // Clear error for services
    if (errors.services) {
      setErrors(prev => ({ ...prev, services: undefined }));
    }
  };

  // Add new service
  const addService = () => {
    setFormData(prev => ({ ...prev, services: [...prev.services, ''] }));
  };

  // Remove service
  const removeService = (index: number) => {
    const updatedServices = formData.services.filter((_, i) => i !== index);
    setFormData(prev => ({ ...prev, services: updatedServices }));
  };

// Handle business hours change
const handleHoursChange = (dayKey: string, value: string) => {
  setFormData(prev => ({
    ...prev,
    hours: {
      ...prev.hours,
      [dayKey.toLowerCase()]: value,
    }
  }));
};

  // Handle photo change
  const handlePhotoChange = async (images: string[], files?: File[]) => {
    // Filter out data URLs from the images array
    const nonDataUrls = images.filter(url => !url.startsWith('data:'));

    // If there are files to upload
    if (files && files.length > 0) {
      setPhotoFiles(files);

      // Set loading state
      setIsUploading(true);

      // Show loading toast
      const loadingToast = toast({
        title: "Uploading Images",
        description: "Please wait while your images are being uploaded...",
        className: "bg-blue-400 text-white",
      });

      try {
        // Upload all files at once using assetsService
        const response = await assetsService.uploadFiles(files, authHeader || '');

        if (!response.isSuccess || !response.data) {
          throw new Error(response.error || 'Failed to upload images');
        }

        // Get the URLs from the response
        const uploadedUrls = response.data.urls;

        // Update the form data with only non-data URLs and newly uploaded URLs
        setFormData(prev => ({ ...prev, photos: [...nonDataUrls, ...uploadedUrls] }));

        // Dismiss loading toast and show success toast
        toast({
          title: "Upload Success",
          description: `${uploadedUrls.length} image(s) uploaded successfully`,
          className: "bg-green-400 text-white",
        });
      } catch (error) {
        console.error('Error uploading images:', error);

        // Show error toast
        toast({
          title: "Upload Error",
          description: error instanceof Error ? error.message : 'Failed to upload images',
          variant: "destructive",
        });
      } finally {
        // Reset loading state
        setIsUploading(false);
      }
    } else {
      // If no files to upload, just update with non-data URLs
      // This handles image removal case
      setFormData(prev => ({ ...prev, photos: nonDataUrls }));
    }

    // Clear error for photos
    if (errors.photos) {
      setErrors(prev => ({ ...prev, photos: undefined }));
    }
  };

  // Handle review change
  const handleReviewChange = (index: number, field: 'text' | 'rating', value: string) => {
    const updatedReviews = [...formData.reviews];
    updatedReviews[index] = {
      ...updatedReviews[index],
      [field]: value
    };
    setFormData(prev => ({ ...prev, reviews: updatedReviews }));

    // Clear error for reviews
    if (errors.reviews) {
      setErrors(prev => ({ ...prev, reviews: undefined }));
    }
  };

  // Add new review
  const addReview = () => {
    const newReview = {
      text: '',
      rating: '5'
    };
    setFormData(prev => ({ ...prev, reviews: [...prev.reviews, newReview] }));
  };

  // Show confirmation dialog for review deletion
  const confirmRemoveReview = (index: number) => {
    setReviewToDelete(index);
    setIsDeleteReviewDialogOpen(true);
  };

  // Remove review after confirmation
  const removeReview = () => {
    if (reviewToDelete !== null) {
      const updatedReviews = formData.reviews.filter((_, i) => i !== reviewToDelete);
      setFormData(prev => ({ ...prev, reviews: updatedReviews }));
      setReviewToDelete(null);
      setIsDeleteReviewDialogOpen(false);
    }
  };

  // Validate form
  const validateForm = (): boolean => {
    const newErrors: FormErrors = {};

    // Required fields
    if (!formData.name.trim()) newErrors.name = 'Business name is required';
    if (!formData.category) newErrors.category = 'Category is required';
    if (!formData.location.trim()) newErrors.location = 'Location is required';
    if (!formData.address.trim()) newErrors.address = 'Address is required';
    if (!formData.phone.trim()) newErrors.phone = 'Phone number is required';

    // Email format
    if (formData.email.trim() && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = 'Invalid email format';
    }

    // Phone format
    if (!/^\(\d{3}\) \d{3}-\d{4}$/.test(formData.phone)) {
      newErrors.phone = 'Phone number should be in format (XXX) XXX-XXXX';
    }

    // Website format
    if (formData.website.trim() && !/^(https?:\/\/)?(www\.)?[-a-zA-Z0-9@:%._+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_+.~#?&//=]*)$/.test(formData.website)) {
      newErrors.website = 'Invalid website URL';
    }

    // At least one service
    if (formData.services.length === 0 || !formData.services.some(service => service.trim())) {
      newErrors.services = 'At least one service is required';
    }

    // At least one photo
    if (formData.photos.length === 0) {
      newErrors.photos = 'At least one photo is required';
    }

    // Validate reviews if any exist
    if (formData.reviews.length > 0) {
      const invalidReviews = formData.reviews.some(review => 
        !review.text.trim() || 
        !review.rating || 
        parseInt(review.rating) < 1 || 
        parseInt(review.rating) > 5
      );

      if (invalidReviews) {
        newErrors.reviews = 'All reviews must have text and a rating between 1 and 5';
      }
    }

    console.log(formData.photos);
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setSubmitAttempted(true);

    if (!validateForm()) {
      toast({
        title: "Validation Error",
        description: "Please fix the errors in the form",
        variant: "destructive",
      });
      return;
    }

    setIsSubmitting(true);

    try {
      // Determine the endpoint and method based on whether we're editing or creating
      const endpoint = isEditing && business 
        ? `/api/businesses/${business.businessId}` 
        : '/api/businesses';

      const method = isEditing ? 'PUT' : 'POST';

      // At this point, formData.photos should contain the URLs of the uploaded images
      // since they were uploaded in the handlePhotoChange function

      const response = await apiService(endpoint, {
        method,
        headers: {
          'Authorization': authHeader || '',
        },
        body: formData,
      });

      if (response.isSuccess) {
        toast({
          title: "Success",
          description: isEditing ? "Business updated successfully" : "Business created successfully",
          className: "bg-green-400 text-white",
        });
        onSuccess();
      } else {
        toast({
          title: "Error",
          description: response.error || (isEditing ? "Failed to update business" : "Failed to create business"),
          variant: "destructive",
        });
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "An unexpected error occurred",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Format phone number as user types
  const formatPhoneNumber = (value: string) => {
    // Remove all non-numeric characters
    const phoneNumber = value.replace(/\D/g, '');

    // Format the phone number
    if (phoneNumber.length <= 3) {
      return phoneNumber;
    } else if (phoneNumber.length <= 6) {
      return `(${phoneNumber.slice(0, 3)}) ${phoneNumber.slice(3)}`;
    } else {
      return `(${phoneNumber.slice(0, 3)}) ${phoneNumber.slice(3, 6)}-${phoneNumber.slice(6, 10)}`;
    }
  };

  // Handle phone input change with formatting
  const handlePhoneChange = (value: string) => {
    const formattedPhone = formatPhoneNumber(value);
    handleInputChange('phone', formattedPhone);
  };

  // Floating action button for reviews
  const ReviewsFloatingButton = () => (
    <Button
      type="button"
      className="shadow-lg relative flex items-center justify-center z-50 bg-blue-600 hover:bg-blue-700"
      onClick={() => setIsReviewsModalOpen(true)}
      title="Click to manage business reviews"
    >
      <MessageSquare className="h-5 w-5 mr-2" />
      <span className="font-medium">Manage Reviews</span>
      {formData.reviews.length > 0 && (
        <span className="absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full h-6 w-6 flex items-center justify-center">
          {formData.reviews.length}
        </span>
      )}
    </Button>
  );

  return (
    <form onSubmit={handleSubmit} className="space-y-6 max-h-[80vh] overflow-y-auto p-1">
      {/* Floating button for reviews */}

      <div className="space-y-4">
        {/* Business Name */}
        <div className="space-y-2">
          <Label htmlFor="name">Business Name *</Label>
          <Input
            id="name"
            value={formData.name}
            onChange={(e) => handleInputChange('name', e.target.value)}
            placeholder="Enter business name"
            className={errors.name && submitAttempted ? 'border-red-500' : ''}
          />
          {errors.name && submitAttempted && (
            <p className="text-sm text-red-500">{errors.name}</p>
          )}
        </div>

        {/* Category */}
        <div className="space-y-2">
          <Label htmlFor="category">Category *</Label>
          <Select
            value={formData.category}
            onValueChange={(value) => handleInputChange('category', value)}
          >
            <SelectTrigger 
              id="category" 
              className={errors.category && submitAttempted ? 'border-red-500 focus:border-red-500 focus-visible:ring-red-300' : ''}
              aria-invalid={!!errors.category}>
              <SelectValue placeholder="Select a category" />
            </SelectTrigger>
            <SelectContent>
              {CATEGORY_OPTIONS.map((option) => (
                <SelectItem key={option.value} value={option.value}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          {errors.category && submitAttempted && (
            <p className="text-sm text-red-500">{errors.category}</p>
          )}
        </div>

        {/* Address with Autocomplete */}
        <div className="space-y-2 relative">
          <Label htmlFor="address">Address *</Label>
          <div className="relative">
            <MapPin className="absolute left-3 top-2.5 h-5 w-5 text-gray-400" />
            <Input
              id="address"
              value={query}
              onChange={handleAddressInputChange}
              placeholder="Enter address"
              className={`pl-10 ${errors.address && submitAttempted ? 'border-red-500' : ''}`}
            />
            {isAddressLoading && (
              <div className="absolute right-3 top-2.5">
                <Loader2 className="h-5 w-5 animate-spin text-gray-400" />
              </div>
            )}
          </div>

          {/* Address Suggestions */}
          {suggestions.length > 0 && (
            <div className="absolute z-10 w-full mt-1 max-h-60 overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none sm:text-sm">
              {suggestions.map((suggestion, index) => (
                <div
                  key={index}
                  className="relative cursor-pointer select-none py-2 px-4 hover:bg-blue-100 hover:text-blue-900"
                  onClick={() => handleAddressSelection(suggestion)}
                >
                  <div className="font-medium">{suggestion.address}</div>
                </div>
              ))}
            </div>
          )}

          {addressError && (
            <p className="text-sm text-red-500">Error fetching addresses: {addressError}</p>
          )}

          {errors.address && submitAttempted && (
            <p className="text-sm text-red-500">{errors.address}</p>
          )}
        </div>

        {/* Location */}
        <div className="space-y-2 hidden">
          <Label htmlFor="location">Location *</Label>
          <Input
            id="location"
            value={formData.location}
            onChange={(e) => handleInputChange('location', e.target.value)}
            placeholder="City, State"
            className={errors.location && submitAttempted ? 'border-red-500' : ''}
          />
          {errors.location && submitAttempted && (
            <p className="text-sm text-red-500">{errors.location}</p>
          )}
        </div>

        {/* Phone */}
        <div className="space-y-2">
          <Label htmlFor="phone">Phone Number *</Label>
          <div className="relative">
            <Phone className="absolute left-3 top-2.5 h-5 w-5 text-gray-400" />
            <Input
              id="phone"
              value={formData.phone}
              onChange={(e) => handlePhoneChange(e.target.value)}
              placeholder="(XXX) XXX-XXXX"
              className={`pl-10 ${errors.phone && submitAttempted ? 'border-red-500' : ''}`}
            />
          </div>
          {errors.phone && submitAttempted && (
            <p className="text-sm text-red-500">{errors.phone}</p>
          )}
        </div>

        {/* Website */}
        <div className="space-y-2">
          <Label htmlFor="website">Website</Label>
          <div className="relative">
            <Globe className="absolute left-3 top-2.5 h-5 w-5 text-gray-400" />
            <Input
              id="website"
              value={formData.website}
              onChange={(e) => handleInputChange('website', e.target.value)}
              placeholder="https://example.com"
              className={`pl-10 ${errors.website && submitAttempted ? 'border-red-500' : ''}`}
            />
          </div>
          {errors.website && submitAttempted && (
            <p className="text-sm text-red-500">{errors.website}</p>
          )}
        </div>

        {/* Email */}
        <div className="space-y-2">
          <Label htmlFor="email">Email</Label>
          <div className="relative">
            <Mail className="absolute left-3 top-2.5 h-5 w-5 text-gray-400" />
            <Input
              id="email"
              type="email"
              value={formData.email}
              onChange={(e) => handleInputChange('email', e.target.value)}
              placeholder="<EMAIL>"
              className={`pl-10 ${errors.email && submitAttempted ? 'border-red-500' : ''}`}
            />
          </div>
          {errors.email && submitAttempted && (
            <p className="text-sm text-red-500">{errors.email}</p>
          )}
        </div>

        {/* Business Hours */}
        <div className="space-y-2">
          <Label>Business Hours</Label>
          <div className="space-y-2 border rounded-md p-3">
            {DAYS_OF_WEEK.map((day) => (
              <div key={day} className="grid grid-cols-3 gap-2 items-center">
                <span className="text-sm font-medium">{day}</span>
                <Select
                  value={formData.hours[day.toLowerCase()] || 'Open 24 hours'}
                  onValueChange={(value) => handleHoursChange(day, value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select hours" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Open 24 hours">Open 24 hours</SelectItem>
                    <SelectItem value="Closed">Closed</SelectItem>
                    <SelectItem value="9:00 AM - 5:00 PM">9:00 AM - 5:00 PM</SelectItem>
                    <SelectItem value="8:00 AM - 6:00 PM">8:00 AM - 6:00 PM</SelectItem>
                    <SelectItem value="7:00 AM - 7:00 PM">7:00 AM - 7:00 PM</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            ))}
          </div>
        </div>

        {/* Services */}
        <div className="space-y-2">
          <Label>Services *</Label>
          <div className="space-y-2 border rounded-md p-3">
            {formData.services.map((service, index) => (
              <div key={index} className="flex items-center gap-2">
                <Input
                  value={service}
                  onChange={(e) => handleServiceChange(index, e.target.value)}
                  placeholder={`Service ${index + 1}`}
                  className={errors.services && submitAttempted ? 'border-red-500' : ''}
                />
                {formData.services.length > 1 && (
                  <Button
                    type="button"
                    variant="outline"
                    size="icon"
                    onClick={() => removeService(index)}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                )}
              </div>
            ))}
            <Button
              type="button"
              variant="outline"
              onClick={addService}
              className="w-full mt-2"
            >
              <Plus className="h-4 w-4 mr-2" />
              Add Service
            </Button>
          </div>
          {errors.services && submitAttempted && (
            <p className="text-sm text-red-500">{errors.services}</p>
          )}
        </div>

        {/* Photos */}
        <div className="space-y-2">
          <div className="flex justify-between items-center">
            <Label>Photos *</Label>
            {isUploading && (
              <div className="flex items-center text-blue-500 text-sm">
                <Loader2 className="h-4 w-4 mr-1 animate-spin" />
                <span>Uploading...</span>
              </div>
            )}
          </div>
          <ImageUploader
            onImagesSelected={handlePhotoChange}
            currentImages={formData.photos}
            maxImages={10}
            multiple={true}
          />
          {errors.photos && submitAttempted && (
            <p className="text-sm text-red-500">{errors.photos}</p>
          )}
        </div>

      </div>

      {/* Form Actions */}
      <div className="flex justify-end space-x-2 py-4 border-t">

        <ReviewsFloatingButton />
        <Button type="button" variant="outline" onClick={onClose} disabled={isSubmitting}>
          Cancel
        </Button>
        <Button type="submit" disabled={isSubmitting}>
          {isSubmitting ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              {isEditing ? 'Updating...' : 'Creating...'}
            </>
          ) : (
            isEditing ? 'Update Business' : 'Create Business'
          )}
        </Button>
      </div>

      {/* Reviews Management Modal */}
      <Dialog open={isReviewsModalOpen} onOpenChange={setIsReviewsModalOpen}>
        <DialogContent className="sm:max-w-[600px] max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Manage Reviews</DialogTitle>
            <DialogDescription>
              Add, edit, or remove reviews for this business.
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4 py-4">
            {formData.reviews.length > 0 ? (
              <div className="space-y-4">
                {formData.reviews.map((review, index) => (
                  <div key={index} className="p-3 border rounded-md bg-gray-50 dark:bg-gray-800">
                    <div className="flex justify-between items-start mb-2">
                      <div className="font-medium">Review #{index + 1}</div>
                      <Button
                        type="button"
                        variant="outline"
                        size="icon"
                        onClick={() => confirmRemoveReview(index)}
                        className="h-8 w-8"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>

                    <div className="space-y-3">
                      <div>
                        <Label htmlFor={`review-text-${index}`} className="text-sm">Review Text *</Label>
                        <Textarea
                          id={`review-text-${index}`}
                          value={review.text}
                          onChange={(e) => handleReviewChange(index, 'text', e.target.value)}
                          placeholder="Enter review text"
                          className={errors.reviews && submitAttempted ? 'border-red-500' : ''}
                        />
                      </div>

                      <div>
                        <Label htmlFor={`review-rating-${index}`} className="text-sm">Rating (1-5) *</Label>
                        <Select
                          value={review.rating}
                          onValueChange={(value) => handleReviewChange(index, 'rating', value)}
                        >
                          <SelectTrigger 
                            id={`review-rating-${index}`}
                            className={errors.reviews && submitAttempted ? 'border-red-500' : ''}
                          >
                            <SelectValue placeholder="Select rating" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="1">1 - Poor</SelectItem>
                            <SelectItem value="2">2 - Fair</SelectItem>
                            <SelectItem value="3">3 - Good</SelectItem>
                            <SelectItem value="4">4 - Very Good</SelectItem>
                            <SelectItem value="5">5 - Excellent</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-6 text-gray-500 dark:text-gray-400">
                No reviews yet. Add a review using the button below.
              </div>
            )}

            <Button
              type="button"
              variant="outline"
              onClick={addReview}
              className="w-full mt-2"
            >
              <Plus className="h-4 w-4 mr-2" />
              Add Review
            </Button>

            {errors.reviews && submitAttempted && (
              <p className="text-sm text-red-500">{errors.reviews}</p>
            )}
          </div>

          <DialogFooter>
            <Button type="button" onClick={() => setIsReviewsModalOpen(false)}>
              Done
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete Review Confirmation Dialog */}
      <AlertDialog open={isDeleteReviewDialogOpen} onOpenChange={setIsDeleteReviewDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone. This will permanently delete the review.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={removeReview}>Delete</AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </form>
  );
};

export default CreateBusinessForm;
