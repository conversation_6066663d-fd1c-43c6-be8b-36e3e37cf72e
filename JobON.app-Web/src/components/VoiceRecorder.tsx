
import React, { useState, useRef } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Loader2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { toast } from '@/hooks/use-toast';

interface VoiceRecorderProps {
  onTranscriptionComplete: (text: string) => void;
  onTranscription?: (text: string) => void; // Added this prop
  className?: string;
}

export const VoiceRecorder: React.FC<VoiceRecorderProps> = ({ 
  onTranscriptionComplete,
  onTranscription,
  className = ''
}) => {
  const [isRecording, setIsRecording] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const audioChunksRef = useRef<Blob[]>([]);
  
  const startRecording = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      const mediaRecorder = new MediaRecorder(stream);
      
      mediaRecorderRef.current = mediaRecorder;
      audioChunksRef.current = [];
      
      mediaRecorder.ondataavailable = (e) => {
        if (e.data.size > 0) {
          audioChunksRef.current.push(e.data);
        }
      };
      
      mediaRecorder.onstop = async () => {
        setIsProcessing(true);
        try {
          const audioBlob = new Blob(audioChunksRef.current, { type: 'audio/webm' });
          
          // Convert to base64 for easier handling
          const reader = new FileReader();
          reader.readAsDataURL(audioBlob);
          reader.onloadend = async () => {
            const base64data = reader.result as string;
            
            // Simple mock transcription for now (will be replaced with real AI transcription)
            await transcribeAudio(base64data);
          };
        } catch (error) {
          console.error('Error processing audio:', error);
          toast({
            title: 'Error',
            description: 'Failed to process audio recording',
            variant: 'destructive',
          });
          setIsProcessing(false);
        }
      };
      
      mediaRecorder.start();
      setIsRecording(true);
    } catch (error) {
      console.error('Error starting recording:', error);
      toast({
        title: 'Cannot access microphone',
        description: 'Please allow microphone access to record audio messages',
        variant: 'destructive',
      });
    }
  };
  
  const stopRecording = () => {
    if (mediaRecorderRef.current && isRecording) {
      mediaRecorderRef.current.stop();
      
      // Stop all audio tracks
      if (mediaRecorderRef.current.stream) {
        mediaRecorderRef.current.stream.getTracks().forEach(track => track.stop());
      }
      
      setIsRecording(false);
    }
  };

  const transcribeAudio = async (audioData: string) => {
    try {
      // Remove the prefix from the base64 data
      const base64Audio = audioData.split(',')[1];
      
      // Here you would normally send the audio to an AI service for transcription
      // For demonstration, we'll use a mock transcription
      
      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // For now, let's use a mock response
      const mockTranscription = "This is a simulated transcription. In a real implementation, this would be the result from an AI transcription service.";
      
      // Call both callbacks with the transcribed text
      onTranscriptionComplete(mockTranscription);
      if (onTranscription) {
        onTranscription(mockTranscription);
      }
      
      setIsProcessing(false);
      
      toast({
        title: 'Transcription complete',
        description: 'Your voice message has been converted to text',
      });
      
    } catch (error) {
      console.error('Error transcribing audio:', error);
      toast({
        title: 'Transcription failed',
        description: 'Unable to convert your voice message to text',
        variant: 'destructive',
      });
      setIsProcessing(false);
    }
  };
  
  return (
    <div className={className}>
      <Button
        type="button"
        variant={isRecording ? "destructive" : "outline"}
        size="icon"
        className="rounded-full"
        onClick={isRecording ? stopRecording : startRecording}
        disabled={isProcessing}
      >
        {isProcessing ? (
          <Loader2 className="h-4 w-4 animate-spin" />
        ) : isRecording ? (
          <MicOff className="h-4 w-4" />
        ) : (
          <Mic className="h-4 w-4" />
        )}
      </Button>
    </div>
  );
};
