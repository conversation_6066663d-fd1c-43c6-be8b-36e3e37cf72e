import React from 'react';
import { Chat } from '@/types/chat';
import { ChatFeatures, ChatTheme } from '../config/roleConfigurations';
import { ChatParticipants } from './ChatParticipants';
import { Sheet, SheetContent, SheetHeader, SheetTitle } from '@/components/ui/sheet';
import { Drawer, DrawerContent, DrawerHeader, DrawerTitle } from '@/components/ui/drawer';
import { useIsMobile } from '@/hooks/use-mobile';
import { Users } from 'lucide-react';

interface MobileChatParticipantsProps {
  chat: Chat | null;
  userRole: 'admin' | 'provider' | 'customer';
  features: ChatFeatures;
  theme: ChatTheme;
  isOpen: boolean;
  onClose: () => void;
  onAddParticipant?: (userId: string) => void;
  onRemoveParticipant?: (userId: string) => void;
  onPromoteParticipant?: (userId: string) => void;
}

/**
 * MobileChatParticipants Component
 * 
 * A mobile-optimized wrapper for ChatParticipants that uses Sheet on desktop
 * and Drawer on mobile devices for better UX.
 */
export const MobileChatParticipants: React.FC<MobileChatParticipantsProps> = ({
  chat,
  userRole,
  features,
  theme,
  isOpen,
  onClose,
  onAddParticipant,
  onRemoveParticipant,
  onPromoteParticipant,
}) => {
  const isMobile = useIsMobile();

  const participantCount = chat?.participants?.length || 0;

  // Mobile: Use Drawer for better mobile UX
  if (isMobile) {
    return (
      <Drawer open={isOpen} onOpenChange={onClose}>
        <DrawerContent className="max-h-[85vh]">
          <DrawerHeader className="pb-2">
            <DrawerTitle className="flex items-center gap-2 text-left">
              <Users className="h-5 w-5" />
              Participants ({participantCount})
            </DrawerTitle>
          </DrawerHeader>
          
          <div className="flex-1 overflow-hidden">
            <ChatParticipants
              chat={chat}
              userRole={userRole}
              features={features}
              theme={theme}
              onClose={onClose}
              onAddParticipant={onAddParticipant}
              onRemoveParticipant={onRemoveParticipant}
              onPromoteParticipant={onPromoteParticipant}
              isMobileSheet={true}
            />
          </div>
        </DrawerContent>
      </Drawer>
    );
  }

  // Desktop: Use Sheet for sidebar-like experience
  return (
    <Sheet open={isOpen} onOpenChange={onClose}>
      <SheetContent side="right" className="w-96 p-0">
        <div className="h-full">
          <ChatParticipants
            chat={chat}
            userRole={userRole}
            features={features}
            theme={theme}
            onClose={onClose}
            onAddParticipant={onAddParticipant}
            onRemoveParticipant={onRemoveParticipant}
            onPromoteParticipant={onPromoteParticipant}
            isMobileSheet={false}
          />
        </div>
      </SheetContent>
    </Sheet>
  );
};

export default MobileChatParticipants;
