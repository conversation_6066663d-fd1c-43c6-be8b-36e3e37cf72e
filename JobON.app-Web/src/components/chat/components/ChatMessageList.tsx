import React, { useEffect, useRef, useMemo, useState, useCallback } from 'react';
import { Message } from '@/types/chat';
import { ChatFeatures, ChatTheme } from '../config/roleConfigurations';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Loader2 } from 'lucide-react';
import {
  MoreVertical,
  Reply,
  Edit,
  Trash2,
  Pin,
  Copy
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { ScrollArea } from '@/components/ui/scroll-area';
import { cn } from '@/lib/utils';
import { format, isToday, isYesterday } from 'date-fns';
import { useChat } from '@/hooks/useChat';

interface ChatMessageListProps {
  messages: Message[];
  currentUserId: string;
  userRole: 'admin' | 'provider' | 'customer';
  features: ChatFeatures;
  theme: ChatTheme;
  isLoading: boolean;
  onMessageAction?: (action: string, messageId: string) => void;
  onReplyToMessage?: (message: Message) => void;
  onLoadMoreMessages?: () => void;
  hasMoreMessages?: boolean;
  isLoadingMore?: boolean;
}

/**
 * ChatMessageList Component with Infinite Scroll
 */
const ChatMessageListComponent: React.FC<ChatMessageListProps> = ({
  messages,
  currentUserId,
  userRole,
  features,
  isLoading,
  onMessageAction,
  onReplyToMessage,
  onLoadMoreMessages,
  hasMoreMessages = false,
  isLoadingMore = false,
}) => {
  const scrollAreaRef = useRef<HTMLDivElement>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const scrollViewportRef = useRef<HTMLDivElement>(null);
  const { currentChat } = useChat();
  
  // Track if user is at bottom for auto-scroll behavior
  const [isAtBottom, setIsAtBottom] = useState(true);
  const [userScrolled, setUserScrolled] = useState(false);

  // Function to resolve user name from message
  const resolveUserName = (message: Message) => {
    if (message.sender_id === currentUserId) {
      return 'You';
    }

    if (message.user?.name) {
      return message.user.name;
    }

    const participant = currentChat?.participants?.find(p =>
      String(p.id) === String(message.sender_id)
    );
    if (participant?.name) {
      return participant.name;
    }

    return 'Unknown User';
  };



  // Function to resolve user avatar from message
  const resolveUserAvatar = (message: Message) => {
    if (message.user?.avatar) {
      return message.user.avatar;
    }

    const participant = currentChat?.participants?.find(p =>
      String(p.id) === String(message.sender_id)
    );
    if (participant?.avatar) {
      return participant.avatar;
    }

    return '';
  };

  // Handle scroll events for infinite loading and auto-scroll tracking
  const handleScroll = useCallback((event: React.UIEvent<HTMLDivElement>) => {
    const target = event.target as HTMLDivElement;
    const { scrollTop, scrollHeight, clientHeight } = target;
    
    // Check if user is at the bottom (within 50px threshold)
    const atBottom = scrollHeight - scrollTop - clientHeight < 50;
    setIsAtBottom(atBottom);
    
    // Track if user manually scrolled
    if (!atBottom) {
      setUserScrolled(true);
    }

    // Load more messages when scrolled to top
    if (scrollTop < 100 && hasMoreMessages && !isLoadingMore && onLoadMoreMessages) {
      // Save current scroll position
      const prevScrollHeight = scrollHeight;
      
      onLoadMoreMessages();
      
      // Restore scroll position after new messages load
      setTimeout(() => {
        if (target) {
          const newScrollHeight = target.scrollHeight;
          const heightDiff = newScrollHeight - prevScrollHeight;
          target.scrollTop = scrollTop + heightDiff;
        }
      }, 100);
    }
  }, [hasMoreMessages, isLoadingMore, onLoadMoreMessages]);

  // Auto-scroll to bottom when new messages arrive (only if user is at bottom)
  useEffect(() => {
    if (messagesEndRef.current && isAtBottom && !userScrolled) {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [messages, isAtBottom, userScrolled]);

  // Reset scroll tracking when chat changes
  useEffect(() => {
    setUserScrolled(false);
    setIsAtBottom(true);
    // Scroll to bottom when opening a new chat
    setTimeout(() => {
      if (messagesEndRef.current) {
        messagesEndRef.current.scrollIntoView({ behavior: 'auto' });
      }
    }, 100);
  }, [currentChat?.id]);

  // Group messages by date
  const groupedMessages = useMemo(() => {
    const groups: { date: string; messages: Message[] }[] = [];
    let currentDate = '';
    let currentGroup: Message[] = [];

    // Sort messages by created_at timestamp first to ensure chronological order
    const sortedMessages = [...messages].sort((a, b) => 
      new Date(a.created_at).getTime() - new Date(b.created_at).getTime()
    );

    sortedMessages.forEach((message) => {
      const messageDate = format(new Date(message.created_at), 'yyyy-MM-dd');
      
      if (messageDate !== currentDate) {
        if (currentGroup.length > 0) {
          groups.push({ date: currentDate, messages: currentGroup });
        }
        currentDate = messageDate;
        currentGroup = [message];
      } else {
        currentGroup.push(message);
      }
    });

    if (currentGroup.length > 0) {
      groups.push({ date: currentDate, messages: currentGroup });
    }

    return groups;
  }, [messages]);

  // Format date for display
  const formatDateHeader = (dateString: string) => {
    const date = new Date(dateString);
    if (isToday(date)) return 'Today';
    if (isYesterday(date)) return 'Yesterday';
    return format(date, 'MMMM d, yyyy');
  };

  // Check if user can perform action on message
  const canPerformAction = (action: string, message: Message) => {
    const isOwnMessage = message.sender_id === currentUserId;
    
    switch (action) {
      case 'edit':
        return features.canEditMessages && isOwnMessage;
      case 'delete':
        return features.canDeleteMessages && (isOwnMessage || userRole === 'admin');
      case 'pin':
        return features.canPinMessages && (userRole === 'admin' || userRole === 'provider');
      case 'report':
        return features.canReportMessages && !isOwnMessage;
      default:
        return true;
    }
  };

  // Handle message action
  const handleMessageAction = (action: string, messageId: string) => {
    if (onMessageAction) {
      onMessageAction(action, messageId);
    }
  };

  // Render message actions menu
  const renderMessageActions = (message: Message) => {
    return (
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button
            variant="ghost"
            size="sm"
            className="h-6 w-6 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
          >
            <MoreVertical className="h-3 w-3" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" className="w-40">
          <DropdownMenuItem onClick={() => onReplyToMessage?.(message)}>
            <Reply className="h-3 w-3 mr-2" />
            Reply
          </DropdownMenuItem>
          
          <DropdownMenuItem onClick={() => navigator.clipboard.writeText(message.message)}>
            <Copy className="h-3 w-3 mr-2" />
            Copy
          </DropdownMenuItem>
          
          {canPerformAction('edit', message) && (
            <DropdownMenuItem onClick={() => handleMessageAction('edit', message.id)}>
              <Edit className="h-3 w-3 mr-2" />
              Edit
            </DropdownMenuItem>
          )}
          
          {canPerformAction('pin', message) && (
            <DropdownMenuItem onClick={() => handleMessageAction('pin', message.id)}>
              <Pin className="h-3 w-3 mr-2" />
              Pin
            </DropdownMenuItem>
          )}
          
          <DropdownMenuSeparator />
          
          {canPerformAction('report', message) && (
            <DropdownMenuItem 
              className="text-orange-600"
              onClick={() => handleMessageAction('report', message.id)}
            >
              Report
            </DropdownMenuItem>
          )}
          
          {canPerformAction('delete', message) && (
            <DropdownMenuItem 
              className="text-red-600"
              onClick={() => handleMessageAction('delete', message.id)}
            >
              <Trash2 className="h-3 w-3 mr-2" />
              Delete
            </DropdownMenuItem>
          )}
        </DropdownMenuContent>
      </DropdownMenu>
    );
  };

  // Render individual message
  const renderMessage = (message: Message) => {
    const isOwnMessage = message.sender_id === currentUserId;
    const showAvatar = !isOwnMessage;
    const messageTime = format(new Date(message.created_at), 'HH:mm');

    return (
      <div
        key={message.id}
        className={cn(
          'group flex items-start space-x-2 px-4 py-2 hover:bg-[var(--chat-surface)] transition-colors',
          isOwnMessage && 'flex-row-reverse space-x-reverse'
        )}
      >
        {/* Avatar */}
        {showAvatar && (
          <Avatar className="w-8 h-8 flex-shrink-0">
            <AvatarImage src={resolveUserAvatar(message)} />
            <AvatarFallback className="text-xs">
              {resolveUserName(message)?.charAt(0)?.toUpperCase() || '?'}
            </AvatarFallback>
          </Avatar>
        )}
        
        {/* Message content */}
        <div className={cn(
          'flex-1 min-w-0',
          isOwnMessage && 'flex flex-col items-end'
        )}>
          {/* Message header */}
          <div className={cn(
            'flex items-center space-x-2 mb-1',
            isOwnMessage && 'flex-row-reverse space-x-reverse'
          )}>
            <span className="text-sm font-medium">
              {resolveUserName(message)}
            </span>
            <span className="text-xs text-[var(--chat-text-secondary)]">
              {messageTime}
            </span>
            {(features.canEditMessages || features.canDeleteMessages || features.canPinMessages || features.canReportMessages) && renderMessageActions(message)}
          </div>
          
          {/* Message body */}
          <div className={cn(
              'relative max-w-[70%] w-fit rounded-lg px-3 py-2 break-words',
              isOwnMessage
                  ? 'bg-[var(--chat-primary)] text-white ml-auto'
                  : 'bg-[var(--chat-surface)] text-[var(--chat-text)] border border-[var(--chat-border)]'
          )}>
            <p className="text-sm whitespace-pre-wrap">{message.message}</p>
          </div>
          
          {/* Message status indicators */}
          {isOwnMessage && (
            <div className="flex items-center space-x-1 mt-1">
              {message.read_at && (
                <Badge variant="secondary" className="text-xs">
                  Read
                </Badge>
              )}
            </div>
          )}
        </div>
      </div>
    );
  };

  // Loading state
  if (isLoading && messages.length === 0) {
    return (
      <div className="flex-1 flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-[var(--chat-primary)]" />
          <p className="text-sm text-[var(--chat-text-secondary)]">Loading messages...</p>
        </div>
      </div>
    );
  }

  // Empty state
  if (!isLoading && messages.length === 0) {
    return (
      <div className="flex-1 flex items-center justify-center">
        <div className="text-center">
          <div className="w-16 h-16 bg-[var(--chat-surface)] rounded-full flex items-center justify-center mx-auto mb-4">
            <Reply className="h-8 w-8 text-[var(--chat-text-secondary)]" />
          </div>
          <p className="text-sm text-[var(--chat-text-secondary)]">No messages yet</p>
          <p className="text-xs text-[var(--chat-text-secondary)] mt-1">Start the conversation!</p>
        </div>
      </div>
    );
  }

  return (
    <div className="flex-1 flex flex-col min-h-0">
      <ScrollArea 
        ref={scrollAreaRef} 
        className="flex-1"
        onScrollCapture={handleScroll}
      >
        <div ref={scrollViewportRef} className="p-0">
          {/* Load more indicator */}
          {hasMoreMessages && (
            <div className="flex justify-center py-4">
              {isLoadingMore ? (
                <div className="flex items-center space-x-2 text-sm text-[var(--chat-text-secondary)]">
                  <Loader2 className="h-4 w-4 animate-spin" />
                  <span>Loading older messages...</span>
                </div>
              ) : (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={onLoadMoreMessages}
                  className="text-[var(--chat-primary)] hover:bg-[var(--chat-surface)]"
                >
                  Load older messages
                </Button>
              )}
            </div>
          )}

          {/* Messages grouped by date */}
          {groupedMessages.map((group) => (
            <div key={group.date}>
              {/* Date separator */}
              <div className="flex items-center justify-center py-4">
                <div className="bg-[var(--chat-surface)] px-3 py-1 rounded-full">
                  <span className="text-xs text-[var(--chat-text-secondary)] font-medium">
                    {formatDateHeader(group.date)}
                  </span>
                </div>
              </div>
              
              {/* Messages for this date */}
              {group.messages.map(renderMessage)}
            </div>
          ))}
          
          {/* Scroll anchor */}
          <div ref={messagesEndRef} />
        </div>
      </ScrollArea>

      {/* Back to bottom button - WhatsApp style centered positioning */}
      {!isAtBottom && userScrolled && (
        <div className="absolute bottom-24 left-1/2 transform -translate-x-1/2 z-10">
          <Button
            size="sm"
            variant="secondary"
            className="rounded-full shadow-lg bg-muted text-black hover:text-white backdrop-blur-sm border border-border hover:bg-muted transition-all duration-200"
            onClick={() => {
              setUserScrolled(false);
              messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
            }}
          >
            New messages
          </Button>
        </div>
      )}
    </div>
  );
};

export default React.memo(ChatMessageListComponent);