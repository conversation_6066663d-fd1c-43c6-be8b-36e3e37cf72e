import React, { useState } from 'react';
import { Chat } from '@/types/chat';
import { ChatFeatures, ChatTheme } from '../config/roleConfigurations';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Sheet, SheetContent, SheetHeader, SheetTitle } from '@/components/ui/sheet';
import {
  X,
  Plus,
  Search,
  MoreVertical,
  UserMinus,
  Crown,
  Shield,
  User,
  Users
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { cn } from '@/lib/utils';
import { useIsMobile } from '@/hooks/use-mobile';

interface ChatParticipantsProps {
  chat: Chat | null;
  userRole: 'admin' | 'provider' | 'customer';
  features: ChatFeatures;
  theme: ChatTheme;
  onClose: () => void;
  onAddParticipant?: (userId: string) => void;
  onRemoveParticipant?: (userId: string) => void;
  onPromoteParticipant?: (userId: string) => void;
  // Mobile-specific props
  isMobileSheet?: boolean;
}

/**
 * ChatParticipants Component
 * 
 * Displays and manages chat participants with role-based permissions.
 * Allows adding/removing participants, promoting users, and viewing participant details.
 */
export const ChatParticipants: React.FC<ChatParticipantsProps> = ({
  chat,
  userRole,
  features,
  theme,
  onClose,
  onAddParticipant,
  onRemoveParticipant,
  onPromoteParticipant,
  isMobileSheet = false,
}) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [showAddParticipant, setShowAddParticipant] = useState(false);
  const isMobile = useIsMobile();

  if (!chat) {
    return (
      <div className={cn(
        "border-l border-[var(--chat-border)] bg-[var(--chat-background)] flex flex-col h-full",
        // Mobile-first responsive width
        "w-full sm:w-80 md:w-96"
      )}>
        <div className="p-4 sm:p-6 border-b border-[var(--chat-border)]">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center gap-2">
              <Users className="h-5 w-5 text-[var(--chat-text-secondary)]" />
              <h3 className="text-lg font-semibold text-[var(--chat-text)]">Participants</h3>
            </div>
            <Button
              variant="ghost"
              size="sm"
              className="h-10 w-10 p-0 min-h-[44px] min-w-[44px]"
              onClick={onClose}
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
        </div>

        <div className="flex-1 flex items-center justify-center p-6">
          <div className="text-center space-y-3">
            <div className="w-16 h-16 mx-auto bg-[var(--chat-surface)] rounded-full flex items-center justify-center">
              <Users className="h-8 w-8 text-[var(--chat-text-secondary)]" />
            </div>
            <div>
              <p className="text-sm font-medium text-[var(--chat-text)] mb-1">No chat selected</p>
              <p className="text-xs text-[var(--chat-text-secondary)]">
                Select a conversation to view participants
              </p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Filter participants based on search
  const filteredParticipants = chat.participants.filter(participant =>
    participant.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    participant.email?.toLowerCase().includes(searchQuery.toLowerCase())
  );

  // Get participant role icon
  const getParticipantRoleIcon = (participantType: string) => {
    switch (participantType) {
      case 'admin':
        return <Crown className="h-3 w-3 text-yellow-500" />;
      case 'provider':
        return <Shield className="h-3 w-3 text-blue-500" />;
      case 'customer':
        return <User className="h-3 w-3 text-gray-500" />;
      default:
        return <User className="h-3 w-3 text-gray-500" />;
    }
  };

  // Check if user can manage participant
  const canManageParticipant = (_participantId: string, participantType: string) => {
    if (!features.canManageParticipants) return false;
    if (userRole !== 'admin') return false;
    if (participantType === 'admin' && userRole !== 'admin') return false;
    return true;
  };

  // Render participant actions
  const renderParticipantActions = (participant: any) => {
    if (!canManageParticipant(participant.id, participant.type)) {
      return null;
    }

    return (
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button
            variant="ghost"
            size="sm"
            className={cn(
              "p-0 transition-colors",
              // Mobile-friendly touch target
              "h-10 w-10 min-h-[44px] min-w-[44px]",
              // Desktop smaller size
              "sm:h-8 sm:w-8 sm:min-h-[32px] sm:min-w-[32px]"
            )}
          >
            <MoreVertical className="h-4 w-4 sm:h-3 sm:w-3" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" className="w-48 sm:w-40">
          <DropdownMenuItem className="py-3 sm:py-2">
            View Profile
          </DropdownMenuItem>

          <DropdownMenuItem className="py-3 sm:py-2">
            Send Message
          </DropdownMenuItem>

          {participant.type !== 'admin' && userRole === 'admin' && (
            <>
              <DropdownMenuSeparator />
              <DropdownMenuItem
                className="py-3 sm:py-2"
                onClick={() => onPromoteParticipant?.(participant.id)}
              >
                Promote to Admin
              </DropdownMenuItem>
            </>
          )}

          <DropdownMenuSeparator />

          <DropdownMenuItem
            className="text-red-600 py-3 sm:py-2"
            onClick={() => onRemoveParticipant?.(participant.id)}
          >
            <UserMinus className="h-4 w-4 mr-2 sm:h-3 sm:w-3" />
            Remove from Chat
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    );
  };

  // Render individual participant
  const renderParticipant = (participant: any) => {
    return (
      <div
        key={participant.id}
        className={cn(
          "flex items-center gap-3 p-4 sm:p-3 hover:bg-[var(--chat-surface)] rounded-lg transition-colors",
          // Mobile-friendly touch target
          "min-h-[60px] sm:min-h-[auto]"
        )}
      >
        {/* Avatar */}
        <div className="relative flex-shrink-0">
          <Avatar className="w-12 h-12 sm:w-10 sm:h-10">
            <AvatarImage src={participant.avatar} />
            <AvatarFallback className="text-sm font-medium">
              {participant.name.charAt(0).toUpperCase()}
            </AvatarFallback>
          </Avatar>

          {/* Online indicator */}
          {features.canSeeOnlineStatus && participant.is_online && (
            <div className="absolute -bottom-0.5 -right-0.5 w-3.5 h-3.5 sm:w-3 sm:h-3 bg-green-500 rounded-full border-2 border-white" />
          )}
        </div>

        {/* Participant info */}
        <div className="flex-1 min-w-0">
          <div className="flex items-center gap-2 mb-1">
            <h4 className="text-base sm:text-sm font-medium text-[var(--chat-text)] truncate">
              {participant.name}
            </h4>
            {getParticipantRoleIcon(participant.type)}
          </div>

          <div className="flex items-center gap-2 flex-wrap">
            <Badge variant="outline" className="text-xs px-2 py-1">
              {participant.type}
            </Badge>

            {features.canSeeOnlineStatus && (
              <span className="text-xs text-[var(--chat-text-secondary)] hidden sm:inline">
                {participant.is_online ? 'Online' :
                 participant.last_seen ? `Last seen ${participant.last_seen}` : 'Offline'}
              </span>
            )}
          </div>

          {/* Mobile: Show online status on separate line */}
          {features.canSeeOnlineStatus && (
            <div className="sm:hidden mt-1">
              <span className="text-xs text-[var(--chat-text-secondary)]">
                {participant.is_online ? 'Online' :
                 participant.last_seen ? `Last seen ${participant.last_seen}` : 'Offline'}
              </span>
            </div>
          )}

          {participant.email && (
            <p className="text-xs text-[var(--chat-text-secondary)] truncate mt-1 hidden sm:block">
              {participant.email}
            </p>
          )}
        </div>

        {/* Actions */}
        <div className="flex-shrink-0">
          {renderParticipantActions(participant)}
        </div>
      </div>
    );
  };

  // Render add participant section
  const renderAddParticipant = () => {
    if (!features.canManageParticipants || !showAddParticipant) {
      return null;
    }

    return (
      <div className="p-4 sm:p-3 border-t border-[var(--chat-border)]">
        <div className="space-y-4 sm:space-y-3">
          <div className="flex items-center justify-between">
            <h4 className="text-base sm:text-sm font-medium text-[var(--chat-text)]">Add Participant</h4>
            <Button
              variant="ghost"
              size="sm"
              className="h-10 w-10 p-0 min-h-[44px] min-w-[44px] sm:h-8 sm:w-8 sm:min-h-[32px] sm:min-w-[32px]"
              onClick={() => setShowAddParticipant(false)}
            >
              <X className="h-4 w-4 sm:h-3 sm:w-3" />
            </Button>
          </div>

          <Input
            placeholder="Search users to add..."
            className="text-base sm:text-sm h-12 sm:h-10"
          />

          {/* TODO: Add user search results */}
          <div className="text-sm sm:text-xs text-[var(--chat-text-secondary)] p-3 sm:p-2 bg-[var(--chat-surface)] rounded-lg">
            Search for users to add to this conversation
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className={cn(
      "border-l border-[var(--chat-border)] bg-[var(--chat-background)] flex flex-col h-full",
      // Mobile-first responsive width
      "w-full sm:w-80 md:w-96"
    )}>
      {/* Header */}
      <div className="p-4 sm:p-4 md:p-6 border-b border-[var(--chat-border)]">
        <div className="flex items-center justify-between mb-4 sm:mb-3">
          <div className="flex items-center gap-2">
            <Users className="h-5 w-5 text-[var(--chat-text-secondary)]" />
            <h3 className="text-xl sm:text-lg font-semibold text-[var(--chat-text)]">
              Participants ({chat.participants.length})
            </h3>
          </div>
          <Button
            variant="ghost"
            size="sm"
            className="h-10 w-10 p-0 min-h-[44px] min-w-[44px] sm:h-8 sm:w-8 sm:min-h-[32px] sm:min-w-[32px]"
            onClick={onClose}
          >
            <X className="h-4 w-4" />
          </Button>
        </div>

        {/* Search */}
        <div className="relative mb-4 sm:mb-3">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-[var(--chat-text-secondary)]" />
          <Input
            placeholder="Search participants..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className={cn(
              "pl-10 bg-[var(--chat-surface)] border-[var(--chat-border)]",
              // Mobile-friendly input size
              "h-12 text-base sm:h-10 sm:text-sm"
            )}
          />
        </div>

        {/* Add participant button */}
        {features.canManageParticipants && chat.type === 'group' && (
          <Button
            variant="outline"
            size="sm"
            className={cn(
              "w-full",
              // Mobile-friendly button size
              "h-12 text-base sm:h-10 sm:text-sm"
            )}
            onClick={() => setShowAddParticipant(true)}
          >
            <Plus className="h-4 w-4 mr-2" />
            Add Participant
          </Button>
        )}
      </div>

      {/* Participants list */}
      <ScrollArea className="flex-1">
        <div className="space-y-2 sm:space-y-1 p-3 sm:p-2">
          {filteredParticipants.length === 0 ? (
            <div className="text-center py-12 sm:py-8">
              <div className="space-y-3">
                <div className="w-16 h-16 mx-auto bg-[var(--chat-surface)] rounded-full flex items-center justify-center">
                  <Users className="h-8 w-8 text-[var(--chat-text-secondary)]" />
                </div>
                <div>
                  <p className="text-base sm:text-sm font-medium text-[var(--chat-text)] mb-1">
                    {searchQuery ? 'No participants found' : 'No participants'}
                  </p>
                  <p className="text-sm sm:text-xs text-[var(--chat-text-secondary)]">
                    {searchQuery
                      ? 'Try adjusting your search terms'
                      : 'Participants will appear here when added'
                    }
                  </p>
                </div>
              </div>
            </div>
          ) : (
            filteredParticipants.map(renderParticipant)
          )}
        </div>
      </ScrollArea>

      {/* Add participant section */}
      {renderAddParticipant()}

      {/* Chat info */}
      <div className="p-4 sm:p-4 border-t border-[var(--chat-border)] bg-[var(--chat-surface)]">
        <div className="space-y-3 sm:space-y-2 text-sm sm:text-xs text-[var(--chat-text-secondary)]">
          <div className="flex justify-between items-center">
            <span className="font-medium">Chat Type:</span>
            <Badge variant="secondary" className="capitalize text-xs">
              {chat.type}
            </Badge>
          </div>

          <div className="flex justify-between items-center">
            <span className="font-medium">Created:</span>
            <span className="text-[var(--chat-text)]">
              {new Date(chat.created_at).toLocaleDateString()}
            </span>
          </div>

          {chat.description && (
            <div className="space-y-2">
              <span className="block font-medium">Description:</span>
              <p className="text-[var(--chat-text)] text-sm sm:text-sm leading-relaxed p-3 bg-[var(--chat-background)] rounded-lg border border-[var(--chat-border)]">
                {chat.description}
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ChatParticipants;
