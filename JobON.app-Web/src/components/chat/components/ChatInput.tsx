import React, { useEffect, useState, useCallback, useRef } from 'react';
import { Controller, useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { ChatFeatures, ChatTheme, getMaxMessageLength, isFileAllowed } from '../config/roleConfigurations';
import { useSendMessage } from '@/hooks/useChatQueries';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Input } from '@/components/ui/input';
import {
  Send,
  Paperclip,
  Mic,
  MicOff,
  X,
  File,
  Image,
  Video,
  Loader2
} from 'lucide-react';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { cn } from '@/lib/utils';
import { useToast } from '@/hooks/use-toast';

interface ChatInputProps {
  chatId?: string;
  userRole: 'admin' | 'provider' | 'customer';
  features: ChatFeatures;
  theme: ChatTheme;
  onMessageSent?: (message: any) => void;
  replyToMessage?: any;
  onCancelReply?: () => void;
}

/**
 * ChatInput Component
 * 
 * Handles message composition with role-based features like file upload,
 * voice messages, emoji picker, and form validation.
 */
export const ChatInput: React.FC<ChatInputProps> = ({
  chatId,
  userRole,
  features,
  theme,
  onMessageSent,
  replyToMessage,
  onCancelReply,
}) => {
  const { toast } = useToast();
  const sendMessageMutation = useSendMessage();
  const [isTyping, setIsTyping] = useState(false);

  // Refs
  const fileInputRef = useRef<HTMLInputElement>(null);
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const typingTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Create message schema based on role features
  const messageSchema = z.object({
    message: z
      .string()
  });

  type MessageFormData = z.infer<typeof messageSchema>;

  // Form setup
  const {
    watch,
    handleSubmit,
    reset,
    control,
    getValues,
    formState: { errors },
  } = useForm<MessageFormData>({
    resolver: zodResolver(messageSchema),
    defaultValues: {
      message: '',
    },
  });

  // Handle typing state with debounce
  const handleTypingStart = useCallback(() => {
    setIsTyping(true);
    
    // Clear existing timeout
    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current);
    }
    
    // Set new timeout to stop typing after 1.5 seconds of inactivity
    typingTimeoutRef.current = setTimeout(() => {
      setIsTyping(false);
    }, 1500);
  }, []);

  // Clean up timeout on unmount
  useEffect(() => {
    return () => {
      if (typingTimeoutRef.current) {
        clearTimeout(typingTimeoutRef.current);
      }
    };
  }, []);

  // Handle form submission
  const onSubmit = async (data: MessageFormData) => {

    if (!chatId) {
      toast({
        title: 'Error',
        description: 'No chat selected',
        variant: 'destructive',
      });
      return;
    }

    try {
      const messageData = {
        type: 'text' as const,
        message: data.message.trim(),
        ...(replyToMessage && { reply_to: replyToMessage.id }),
      };

      // Use TanStack Query mutation for optimistic updates and better error handling
      await sendMessageMutation.mutateAsync({
        chatId,
        message: messageData
      });

      console.log('Message sent successfully');

      // Reset form
      reset();

      // Cancel reply if active
      if (onCancelReply) {
        onCancelReply();
      }

      // Callback
      if (onMessageSent) {
        onMessageSent(messageData);
      }

      // Focus back to textarea
      if (textareaRef.current) {
        textareaRef.current.focus();
      }
    } catch (error) {
      console.error('Failed to send message:', error);

      // Enhanced error handling with more specific messages
      const errorMessage = error instanceof Error
        ? error.message
        : 'Failed to send message. Please try again.';

      toast({
        title: 'Error',
        description: errorMessage,
        variant: 'destructive',
      });
    }
  };


  // Handle keyboard shortcuts
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSubmit(onSubmit)();
    }
  };
  const message = watch('message')
  // Render reply preview
  const renderReplyPreview = () => {
    if (!replyToMessage) return null;

    return (
      <div className="flex items-center justify-between p-2 bg-[var(--chat-surface)] border-t border-[var(--chat-border)]">
        <div className="flex-1 min-w-0">
          <div className="text-xs text-[var(--chat-text-secondary)] mb-1">
            Replying to {replyToMessage.user.name}
          </div>
          <div className="text-sm text-[var(--chat-text)] truncate">
            {replyToMessage.message}
          </div>
        </div>
        <Button
          type="button"
          variant="ghost"
          size="sm"
          className="h-6 w-6 p-0 flex-shrink-0"
          onClick={onCancelReply}
        >
          <X className="h-3 w-3" />
        </Button>
      </div>
    );
  };

  return (
    <div className="chat-input border-t border-[var(--chat-border)] bg-[var(--chat-background)]">
      {renderReplyPreview()}
      
      <form onSubmit={handleSubmit(onSubmit)} className="p-4">
        <div
          className={cn(
            'relative flex items-end space-x-2 rounded-lg border border-[var(--chat-border)] bg-[var(--chat-surface)] p-2',
            errors.message && 'border-red-500'
          )}
        >
          {/* Message input */}
          <Controller
            name="message"
            control={control}
            render={({ field }) => (
              <Textarea
                {...field}
                ref={textareaRef}
                placeholder={chatId ? "Type a message..." : "Select a chat to start messaging..."}
                className="flex-1 min-h-[40px] max-h-[120px] resize-none border-0 bg-transparent focus-visible:ring-0 focus-visible:ring-offset-0"
                onKeyDown={handleKeyDown}
                onChange={(e) => {
                  field.onChange(e); // This ensures the form state updates
                  handleTypingStart();
                }}
                disabled={sendMessageMutation.isPending || !chatId}
              />
            )}
          />


          {/* Send button */}
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  type="submit"
                  size="sm"
                  className="!h-8 !w-8 p-0 flex-shrink-0 !m-0"
                  disabled={sendMessageMutation.isPending  || !chatId}
                  onClick={() => console.log('Send button clicked', {
                    isPending: sendMessageMutation.isPending,
                    message: message,
                    chatId,
                    error: sendMessageMutation.error
                  })}
                >
                  {sendMessageMutation.isPending ? (
                    <Loader2 className="h-4 w-4 animate-spin" />
                  ) : (
                    <Send className="h-4 w-4" />
                  )}
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>
                  {!chatId
                    ? 'Select a chat to send messages'
                    : !message?.trim()
                    ? 'Type a message to send'
                    : 'Send message'
                  }
                </p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>

        {/* Error messages */}
        {errors.message && (
          <p className="text-sm text-red-500 mt-1">{errors.message.message}</p>
        )}
        {sendMessageMutation.error && (
          <p className="text-sm text-red-500 mt-1">
            {sendMessageMutation.error instanceof Error
              ? sendMessageMutation.error.message
              : 'Failed to send message. Please try again.'}
          </p>
        )}

        {/* Character count */}
        <div className="flex justify-between items-center mt-1 text-xs text-[var(--chat-text-secondary)]">
          <span>
            {getValues('message')?.length || 0}/{getMaxMessageLength(userRole)}
          </span>
        </div>
      </form>
    </div>
  );
};

export default ChatInput;
