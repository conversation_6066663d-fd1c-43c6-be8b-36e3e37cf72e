import React, { useEffect, useMemo } from 'react';
import { UniversalChatProps } from '@/types/chat';
import { useChat } from '@/hooks/useChat';
import {
  getChatFeatures,
  getChatTheme,
  getChatVariantConfig,
} from './config/roleConfigurations';
import  ChatContainer  from './components/ChatContainer';
import { cn } from '@/lib/utils';

/**
 * UniversalChat Component
 *
 * A reusable chat component that adapts to different user roles (admin, provider, customer)
 * with role-specific features, permissions, and theming.
 *
 * Features:
 * - Role-based permissions and features
 * - Multiple variants (compact, full, modal)
 * - Customizable themes
 * - Real-time messaging with WebSocket support
 * - File sharing and media support
 * - Responsive design
 * - External sidebar support for custom chat lists
 */
export const UniversalChat: React.FC<UniversalChatProps> = ({
                                                              userRole,
                                                              userId,
                                                              recipientId,
                                                              chatId,
                                                              variant = 'full',
                                                              features: customFeatures,
                                                              theme = 'default',
                                                              customTheme,
                                                              className,
                                                              externalSidebar = false,
                                                              showSidebar = true,
                                                              hideHeader = false,
                                                              fixedLayout = false, // New prop for fixed positioning
                                                              onChatSelect,
                                                              onMessageSent,
                                                              onError,
                                                            }) => {
  const { state, dispatch, initializeForRole } = useChat();

  // Get role-based configuration
  const roleFeatures = useMemo(() => getChatFeatures(userRole), [userRole]);
  const roleTheme = useMemo(() => getChatTheme(userRole), [userRole]);
  const variantConfig = useMemo(() => getChatVariantConfig(variant), [variant]);

  // Merge custom features with role-based features
  const finalFeatures = useMemo(() => ({
    ...roleFeatures,
    ...customFeatures,
  }), [roleFeatures, customFeatures]);

  // Override sidebar settings when using external sidebar
  const finalFeaturesWithSidebar = useMemo(() => ({
    ...finalFeatures,
    // Disable internal sidebar when external sidebar is used
    ...(externalSidebar && {
      showChatList: false,
      showSidebar: false,
    }),
    // Respect showSidebar prop when not using external sidebar
    ...(!externalSidebar && {
      showSidebar: showSidebar,
    }),
  }), [finalFeatures, externalSidebar, showSidebar]);

  // Determine final theme
  const finalTheme = useMemo(() => {
    let baseTheme = roleTheme;

    if (theme !== 'default') {
      switch (theme) {
        case 'admin':
          baseTheme = getChatTheme('admin');
          break;
        case 'provider':
          baseTheme = getChatTheme('provider');
          break;
        case 'customer':
          baseTheme = getChatTheme('customer');
          break;
      }
    }

    return {
      ...baseTheme,
      ...customTheme,
    };
  }, [roleTheme, theme, customTheme]);

  // Initialize chat for the specific role
  useEffect(() => {
    if (userId && userRole) {
      const currentUser = {
        id: userId,
        name: '', // This should be populated from user context
        role: {
          name: userRole
        }
      };
      //@ts-ignore
      initializeForRole(userRole, currentUser);
    }
  }, [userId, userRole, initializeForRole]);

  // Handle errors
  useEffect(() => {
    if (state.error && onError) {
      onError(state.error);
    }
  }, [state.error, onError]);

  // Generate CSS custom properties for theming
  const themeStyles = useMemo(() => ({
    '--chat-primary': finalTheme.primary,
    '--chat-secondary': finalTheme.secondary,
    '--chat-accent': finalTheme.accent,
    '--chat-background': finalTheme.background,
    '--chat-surface': finalTheme.surface,
    '--chat-text': finalTheme.text,
    '--chat-text-secondary': finalTheme.textSecondary,
    '--chat-border': finalTheme.border,
    '--chat-success': finalTheme.success,
    '--chat-warning': finalTheme.warning,
    '--chat-error': finalTheme.error,
  } as React.CSSProperties), [finalTheme]);

  // Generate variant styles - adjust when using external sidebar
  const variantStyles = useMemo(() => {
    const baseStyles = {
      height: variantConfig.height,
      width: variantConfig.width,
      borderRadius: variantConfig.borderRadius,
      boxShadow: variantConfig.shadow,
    };

    // When using external sidebar, remove width constraints to fill available space
    if (externalSidebar) {
      return {
        ...baseStyles,
        width: '100%',
        height: '100%',
      };
    }

    return baseStyles;
  }, [variantConfig, externalSidebar]);

  // Combine all styles
  const containerStyles = useMemo(() => ({
    ...themeStyles,
    ...variantStyles,
  }), [themeStyles, variantStyles]);

  // Handle chat selection
  const handleChatSelect = (selectedChatId: string) => {
    dispatch({ type: 'SELECT_CHAT', payload: selectedChatId });
    if (onChatSelect) {
      onChatSelect(selectedChatId);
    }
  };

  // Handle message sent
  const handleMessageSent = (message: any) => {
    if (onMessageSent) {
      onMessageSent(message);
    }
  };

  return (
      <div
          className={cn(
              'universal-chat',
              `universal-chat--${variant}`,
              `universal-chat--${userRole}`,
              'bg-[var(--chat-background)]',
              'border border-[var(--chat-border)]',
              'text-[var(--chat-text)]',
              'overflow-hidden',
              'flex flex-col',
              {
                'relative': variant === 'full',
                'fixed inset-0 z-50': variant === 'modal',
                'rounded-lg': variant !== 'full',
                // Remove border when using external sidebar to avoid double borders
                'border-0': externalSidebar,
              },
              className
          )}
          style={containerStyles}
          data-testid="universal-chat"
          data-role={userRole}
          data-variant={variant}
          data-external-sidebar={externalSidebar}
      >
        <ChatContainer
            userRole={userRole}
            userId={userId}
            recipientId={recipientId}
            chatId={chatId}
            variant={variant}
            features={finalFeaturesWithSidebar}
            theme={finalTheme}
            variantConfig={variantConfig}
            externalSidebar={externalSidebar}
            hideHeader={hideHeader}
            fixedLayout={fixedLayout} // Pass the fixedLayout prop
            onChatSelect={handleChatSelect}
            onMessageSent={handleMessageSent}
        />
      </div>
  );
};

// Export types for external use
export type { UniversalChatProps };

// Default export
export default UniversalChat;