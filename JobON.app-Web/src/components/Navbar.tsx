
import React, { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { Menu, X, User, LogOut, Wrench, Zap, Sparkles, Scissors, HelpCircle, Banknote, BookOpen, Search, Calendar } from 'lucide-react';
import { But<PERSON> } from "@/components/ui/button";
import { ThemeToggle } from './ThemeToggle';
import { Sheet, SheetContent } from "@/components/ui/sheet";
import { useIsMobile } from '@/hooks/use-mobile';
import { NavigationMenu, NavigationMenuContent, NavigationMenuItem, NavigationMenuList, NavigationMenuTrigger } from "@/components/ui/navigation-menu";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar.tsx";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuLabel, DropdownMenuSeparator, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/features/auth/hooks/useAuth';
import { MobileHamburgerMenu } from './mobile/MobileHamburgerMenu';

export const Navbar: React.FC = () => {
  const [isScrolled, setIsScrolled] = useState(false);
  const {
    isAuthenticated,
    user,
    logout,
    getDashboardRoute
  } = useAuth();
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const isMobile = useIsMobile();
  const navigate = useNavigate();
  const {
    toast
  } = useToast();

  // React Auth Kit hooks
  const signOut = logout;

  // Get user data from auth state
  const userName = user?.name || '';
  useEffect(() => {
    // Add scroll event listener
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 10);
    };
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);
  const handleAdminNavigation = () => {
    if (isAuthenticated) {
      // If user is authenticated, navigate to the appropriate dashboard based on role
      const userRole = typeof user?.role === 'string' ? user.role : user?.role?.name || 'customer';
      navigate(getDashboardRoute(user?.role));
    } else {
      // If not authenticated, navigate to auth page
      navigate('/auth');
    }
    setIsMobileMenuOpen(false);
  };
  const handleLogout = () => {
    signOut();
    setIsMobileMenuOpen(false);
    toast({
      title: "Logged out successfully",
      description: "You have been logged out of your account."
    });
  };

  // Get user initials for avatar fallback
  const getUserInitials = () => {
    if (!userName) return "U";
    return userName.split(' ').map(name => name.charAt(0).toUpperCase()).slice(0, 2).join('');
  };
  return <header className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${isScrolled ? 'bg-white dark:bg-gray-900 shadow-sm py-3' : 'bg-white dark:bg-gray-900 py-4'}`}>
      <div className="container mx-auto px-6 md:px-12">
        <div className="flex items-center justify-between">
          <Link to="/" className="flex items-center gap-2">
            <div className="relative">
              <img 
                src="/images/JobON Logo.png"
                alt="JobON Logo" 
                className="h-8 md:h-10 w-auto"
              />
            </div>
          </Link>

          <div className="hidden md:flex items-center justify-center flex-1 mx-4">
            <NavigationMenu className="px-4">
              <NavigationMenuList className="justify-center gap-3">
                <NavigationMenuItem>
                  <NavigationMenuTrigger className="px-4 py-2.5 text-foreground font-medium">Services</NavigationMenuTrigger>
                  <NavigationMenuContent>
                    <div className="grid grid-cols-2 gap-3 p-5 w-[420px] bg-popover">
                      <Link to="/services/plumbing" className="flex items-center gap-3 p-2.5 hover:bg-blue-50 rounded-md transition-colors">
                        <span className="p-1.5 rounded-full bg-blue-100 text-blue-600">
                          <Wrench className="h-5 w-5" />
                        </span>
                        <span className="font-medium text-gray-800 dark:text-gray-100">Plumbing</span>
                      </Link>
                      <Link to="/services/electrical" className="flex items-center gap-3 p-2.5 hover:bg-amber-50 rounded-md transition-colors">
                        <span className="p-1.5 rounded-full bg-amber-100 text-amber-600">
                          <Zap className="h-5 w-5" />
                        </span>
                        <span className="font-medium text-gray-800 dark:text-gray-100">Electrical</span>
                      </Link>
                      <Link to="/services/cleaning" className="flex items-center gap-3 p-2.5 hover:bg-cyan-50 rounded-md transition-colors">
                        <span className="p-1.5 rounded-full bg-cyan-100 text-cyan-600">
                          <Sparkles className="h-5 w-5" />
                        </span>
                        <span className="font-medium text-gray-800 dark:text-gray-100">Cleaning</span>
                      </Link>
                      <Link to="/services/landscaping" className="flex items-center gap-3 p-2.5 hover:bg-green-50 rounded-md transition-colors">
                        <span className="p-1.5 rounded-full bg-green-100 text-green-600">
                          <Scissors className="h-5 w-5" />
                        </span>
                        <span className="font-medium text-gray-800 dark:text-gray-100">Landscaping</span>
                      </Link>

                      {/*<Link to="/services/pest-control" className="flex items-center gap-3 p-2.5 hover:bg-red-50 rounded-md transition-colors">*/}
                      {/*  <span className="p-1.5 rounded-full bg-red-100 text-red-600">*/}
                      {/*    <Bug className="h-5 w-5" />*/}
                      {/*  </span>*/}
                      {/*  <span className="font-medium text-gray-800 dark:text-gray-100">Pest Control</span>*/}
                      {/*</Link>*/}
                      {/*<Link to="/services/handyman" className="flex items-center gap-3 p-2.5 hover:bg-orange-50 rounded-md transition-colors">*/}
                      {/*  <span className="p-1.5 rounded-full bg-orange-100 text-orange-600">*/}
                      {/*    <Hammer className="h-5 w-5" />*/}
                      {/*  </span>*/}
                      {/*  <span className="font-medium text-gray-800 dark:text-gray-100">Handyman</span>*/}
                      {/*</Link>*/}
                      {/*<Link to="/services/appliance-repair" className="flex items-center gap-3 p-2.5 hover:bg-purple-50 rounded-md transition-colors">*/}
                      {/*  <span className="p-1.5 rounded-full bg-purple-100 text-purple-600">*/}
                      {/*    <Laptop className="h-5 w-5" />*/}
                      {/*  </span>*/}
                      {/*  <span className="font-medium text-gray-800 dark:text-gray-100">Appliance Repair</span>*/}
                      {/*</Link>*/}
                      {/*<Link to="/services/hvac" className="flex items-center gap-3 p-2.5 hover:bg-indigo-50 rounded-md transition-colors">*/}
                      {/*  <span className="p-1.5 rounded-full bg-indigo-100 text-indigo-600">*/}
                      {/*    <Thermometer className="h-5 w-5" />*/}
                      {/*  </span>*/}
                      {/*  <span className="font-medium text-gray-800 dark:text-gray-100">HVAC</span>*/}
                      {/*</Link>*/}
                      {/*<Link to="/services/roofing" className="flex items-center gap-3 p-2.5 hover:bg-stone-50 rounded-md transition-colors">*/}
                      {/*  <span className="p-1.5 rounded-full bg-stone-100 text-stone-600">*/}
                      {/*    <Construction className="h-5 w-5" />*/}
                      {/*  </span>*/}
                      {/*  <span className="font-medium text-gray-800 dark:text-gray-100">Roofing</span>*/}
                      {/*</Link>*/}
                      {/*<Link to="/services/solar" className="flex items-center gap-3 p-2.5 hover:bg-yellow-50 rounded-md transition-colors">*/}
                      {/*  <span className="p-1.5 rounded-full bg-yellow-100 text-yellow-600">*/}
                      {/*    <Sun className="h-5 w-5" />*/}
                      {/*  </span>*/}
                      {/*  <span className="font-medium text-gray-800 dark:text-gray-100">Solar</span>*/}
                      {/*</Link>*/}
                    </div>
                  </NavigationMenuContent>
                </NavigationMenuItem>

                <NavigationMenuItem>
                  <NavigationMenuTrigger className="px-4 py-2.5 text-foreground font-medium">Resources</NavigationMenuTrigger>
                  <NavigationMenuContent>
                    <div className="flex flex-col p-4 w-[280px] bg-popover">
                      <Link to="/how-it-works" className="flex items-center gap-3 p-2.5 hover:bg-accent rounded-md transition-colors">
                        <HelpCircle className="h-5 w-5 text-primary" />
                        <span className="font-medium">How It Works</span>
                      </Link>
                      <Link to="/financing" className="flex items-center gap-3 p-2.5 hover:bg-accent rounded-md transition-colors">
                        <Banknote className="h-5 w-5 text-primary" />
                        <span className="font-medium">Financing</span>
                      </Link>
                      <Link to="/blog" className="flex items-center gap-3 p-2.5 hover:bg-accent rounded-md transition-colors">
                        <BookOpen className="h-5 w-5 text-primary" />
                        <span className="font-medium">Blog</span>
                      </Link>
                    </div>
                  </NavigationMenuContent>
                </NavigationMenuItem>

                <NavigationMenuItem>
                  <NavigationMenuTrigger className="px-4 py-2.5 text-foreground font-medium">For Providers</NavigationMenuTrigger>
                  <NavigationMenuContent>
                    <div className="grid grid-cols-3 gap-3 p-6 w-[800px] bg-popover">
                      <div className="col-span-3 mb-3 pb-2 border-b">
                        <span className="font-semibold text-foreground">Free Business Tools</span>
                      </div>

                      <Link to="/jobs" className="flex items-center gap-3 p-2.5 hover:bg-accent rounded-md transition-colors">
                        <Search className="h-5 w-5 text-primary flex-shrink-0" />
                        <span className="font-medium">Find Jobs</span>
                      </Link>
                      <Link to="/free-tools" className="flex items-center gap-3 p-2.5 hover:bg-accent rounded-md transition-colors">
                        <Wrench className="h-5 w-5 text-primary flex-shrink-0" />
                        <span className="font-medium">All Free Tools</span>
                      </Link>
                      <Link to="/for-providers" className="flex items-center gap-3 p-2.5 hover:bg-accent rounded-md transition-colors">
                        <Calendar className="h-5 w-5 text-primary flex-shrink-0" />
                        <span className="font-medium">Join Us</span>
                      </Link>
                    </div>
                  </NavigationMenuContent>
                </NavigationMenuItem>
              </NavigationMenuList>
            </NavigationMenu>
          </div>

          <div className="hidden md:flex items-center space-x-4">
            <ThemeToggle />
            {isAuthenticated ? <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" className="relative h-10 w-10 rounded-full">
                    <Avatar className="h-10 w-10">
                      <AvatarImage src="/placeholder.svg" alt={userName} />
                      <AvatarFallback className="bg-primary text-primary-foreground">
                        {getUserInitials()}
                      </AvatarFallback>
                    </Avatar>
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuLabel>My Account</DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem onClick={handleAdminNavigation}>
                    <User className="mr-2 h-4 w-4" />
                    Dashboard
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={handleLogout}>
                    <LogOut className="mr-2 h-4 w-4" />
                    Logout
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu> : <Button variant="outline" size="sm" className="text-foreground border-border" onClick={handleAdminNavigation}>
                <User className="mr-2 h-4 w-4" />
                <span className="text-foreground">Account</span>
              </Button>}
          </div>

          {/* Mobile Navigation */}
          <div className="md:hidden flex items-center space-x-4">
            <ThemeToggle />
            {isAuthenticated ? <Button variant="ghost" className="p-0 h-10 w-10" onClick={handleAdminNavigation}>
                <Avatar className="h-8 w-8">
                  <AvatarImage src="/placeholder.svg" alt={userName} />
                  <AvatarFallback className="bg-primary text-primary-foreground text-xs">
                    {getUserInitials()}
                  </AvatarFallback>
                </Avatar>
              </Button> : null}
            <Sheet open={isMobileMenuOpen} onOpenChange={setIsMobileMenuOpen}>
              <button 
                className="text-foreground p-2 rounded-md hover:bg-accent/50 transition-colors min-h-[44px] min-w-[44px] flex items-center justify-center" 
                onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
              >
                {isMobileMenuOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
              </button>
              <SheetContent side="right" className="w-[90vw] sm:max-w-md p-0">
                <MobileHamburgerMenu
                  onItemClick={() => setIsMobileMenuOpen(false)}
                  isAuthenticated={isAuthenticated}
                  onAuthClick={handleAdminNavigation}
                  onLogout={handleLogout}
                />
              </SheetContent>
            </Sheet>
          </div>
        </div>
      </div>
    </header>;
};
