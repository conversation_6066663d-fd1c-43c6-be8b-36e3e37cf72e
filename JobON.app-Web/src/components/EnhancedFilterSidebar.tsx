
import React, { useState, useEffect } from 'react';
import { Search, MapPin, Star, Check<PERSON><PERSON><PERSON>, Clock, Scissors, Wrench, Zap, Wind, Construction, Bug, Sun, Truck, Home } from 'lucide-react';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Slider } from '@/components/ui/slider';
import { Switch } from '@/components/ui/switch';
import { ScrollArea } from '@/components/ui/scroll-area';
import { useIsMobile } from '@/hooks/use-mobile';
import { useDebounce, useDebounceValue } from '@/hooks/use-debounce';
import { cn } from '@/lib/utils';
import { useGeolocation } from '@/hooks/use-geolocation';
interface ServiceCategory {
  id: string;
  name: string;
  icon: React.ReactNode;
}
interface FilterValues {
  searchQuery: string;
  zipCode: string;
  distance: number;
  minRating: number;
  availableNow: boolean;
  verifiedOnly: boolean;
  selectedServiceId: string | null;
}
interface EnhancedFilterSidebarProps {
  currentServiceId: string;
  onFilterChange: (values: FilterValues) => void;
  onServiceChange?: (serviceId: string) => void;
  className?: string;
  initialSearchQuery?: string;
  zipCode?: string;
}
export function EnhancedFilterSidebar({
  currentServiceId,
  onFilterChange,
  onServiceChange,
  className,
  initialSearchQuery = '',
  zipCode: externalZipCode
}: EnhancedFilterSidebarProps) {
  const isMobile = useIsMobile();

  // Filter states
  const [searchQuery, setSearchQuery] = useState(initialSearchQuery);
  const debouncedSearchQuery = useDebounceValue(searchQuery, 300);
  const [zipCode, setZipCode] = useState(externalZipCode || '');
  const debouncedZipCode = useDebounceValue(zipCode, 300);
  const [distance, setDistance] = useState(25);
  const [minRating, setMinRating] = useState(0);
  const [availableNow, setAvailableNow] = useState(false);
  const [verifiedOnly, setVerifiedOnly] = useState(false);
  const [selectedServiceId, setSelectedServiceId] = useState<string | null>(currentServiceId);
  const {
    zipCode: detectedZipCode,
    loading
  } = useGeolocation();

  // Set detected zipcode when available
  useEffect(() => {
    if (detectedZipCode && !zipCode) {
      setZipCode(detectedZipCode);
    }
  }, [detectedZipCode]);

  // Update local zipCode state when external zipCode prop changes
  useEffect(() => {
    if (externalZipCode !== undefined && externalZipCode !== zipCode) {
      setZipCode(externalZipCode);
    }
  }, [externalZipCode]);

  // Service categories
  const serviceCategories: ServiceCategory[] = [{
    id: 'cleaning',
    name: 'Cleaning',
    icon: <Scissors className="h-4 w-4" />
  }, {
    id: 'plumbing',
    name: 'Plumbing',
    icon: <Wrench className="h-4 w-4" />
  }, {
    id: 'electrical',
    name: 'Electrical',
    icon: <Zap className="h-4 w-4" />
  }, {
    id: 'landscaping',
    name: 'Landscaping',
    icon: <Scissors className="h-4 w-4" />
  },
  //   {
  //   id: 'handyman',
  //   name: 'Handyman',
  //   icon: <Home className="h-4 w-4" />
  // }, {
  //   id: 'roofing',
  //   name: 'Roofing',
  //   icon: <Construction className="h-4 w-4" />
  // }, {
  //   id: 'hvac',
  //   name: 'HVAC',
  //   icon: <Wind className="h-4 w-4" />
  // }, {
  //   id: 'solar',
  //   name: 'Solar',
  //   icon: <Sun className="h-4 w-4" />
  // }, {
  //   id: 'pest-control',
  //   name: 'Pest Control',
  //   icon: <Bug className="h-4 w-4" />
  // }, {
  //   id: 'moving',
  //   name: 'Moving Services',
  //   icon: <Truck className="h-4 w-4" />
  // }
  ];

  // Apply filters on state changes
  useEffect(() => {
    onFilterChange({
      searchQuery: debouncedSearchQuery,
      zipCode: debouncedZipCode,
      distance,
      minRating,
      availableNow,
      verifiedOnly,
      selectedServiceId
    });
  }, [debouncedSearchQuery, debouncedZipCode, distance, minRating, availableNow, verifiedOnly, selectedServiceId]);

  // Handle service category change
  const handleServiceSelect = (serviceId: string) => {
    if (serviceId === selectedServiceId) return;
    setSelectedServiceId(serviceId);
    if (onServiceChange) {
      onServiceChange(serviceId);
    }
  };

  // Calculate scroll height
  const scrollHeight = isMobile ? 'auto' : 'calc(100vh - 140px)';
  return <div className={cn("w-full sticky top-20", className)}>
    <div className="space-y-4 pb-4">
      {/* Search Filter */}
      <div className="relative">


      </div>

      {/* Service Categories */}
      <div className="space-y-2 bg-gray-50 dark:bg-gray-800/50 p-0 md:p-3 rounded-lg px-4">
        <h3 className="text-sm font-medium text-gray-900 dark:text-white px-2 py-1">Service Category</h3>
        <div className="md:space-y-1.5 pb-2 md:pb-0 flex gap-2 md:gap-0 md:block overflow-scroll md:overflow-visible scroll-smooth scrollbar-hide">
          {serviceCategories.map(category => <div key={category.id} onClick={() => handleServiceSelect(category.id)} className={cn("flex flex-col h-[80px] text-center md:text-start min-w-[90px] md:h-fit border md:border-0 md:flex-row justify-center items-center p-0 md:px-3 md:py-1.5 rounded-md cursor-pointer transition-colors duration-200", selectedServiceId === category.id ? 'bg-primary  text-white md:bg-primary/10 md:text-primary' : 'hover:bg-gray-100 dark:hover:bg-gray-800')}>
                  <span className={cn("mr-2 flex items-center justify-center", selectedServiceId === category.id ? "text-white md:text-primary" : "text-gray-500 dark:text-gray-400")}>
                    {category.icon}
                  </span>
            <span className="text-sm grow-0 md:grow ">
                    {category.name}
                  </span>
          </div>)}
        </div>
      </div>

      {/* ZIP Code and Distance */}
      <div className="space-y-2 bg-gray-50 dark:bg-gray-800/50 p-3 rounded-lg hidden md:block">
        <h3 className="text-sm font-medium text-gray-900 dark:text-white">Location</h3>
        <div className="space-y-3">
          <div className="relative">
            <MapPin className="absolute left-2 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input placeholder={loading ? "Detecting location..." : "ZIP Code"} value={zipCode} onChange={e => setZipCode(e.target.value)} className="pl-8" maxLength={5} />
          </div>

          <div className="space-y-2">
            <div className="flex justify-between text-xs text-gray-600">
              <span>Distance</span>
              <span>{distance} miles</span>
            </div>
            <Slider defaultValue={[25]} max={50} step={5} value={[distance]} onValueChange={value => setDistance(value[0])} />
            <div className="flex justify-between text-xs text-gray-500">
              <span>5mi</span>
              <span>50mi</span>
            </div>
          </div>
        </div>
      </div>

      {/* Rating Filter */}
      <div className="space-y-2 bg-gray-50 dark:bg-gray-800/50 p-3 rounded-lg hidden md:block">
        <h3 className="text-sm font-medium text-gray-900 dark:text-white">Minimum Rating</h3>
        <div className="space-y-2">
          <div className="flex items-center">
            <Slider defaultValue={[0]} max={5} step={1} value={[minRating]} onValueChange={value => setMinRating(value[0])} />
          </div>
          <div className="flex items-center justify-between text-xs text-gray-600">
                <span className="flex items-center">
                  {minRating > 0 ? minRating : "Any"}
                  {minRating > 0 && <Star className="ml-1 h-3 w-3 text-amber-400 fill-amber-400" />}
                </span>
            <span className="flex items-center">
                  5 <Star className="ml-1 h-3 w-3 text-amber-400 fill-amber-400" />
                </span>
          </div>
        </div>
      </div>

      {/* Availability Toggles */}
      <div className="space-y-3 bg-gray-50 dark:bg-gray-800/50 p-3 rounded-lg hidden md:block">
        <div className="flex items-center space-x-2">
          <Switch id="available-now" checked={availableNow} onCheckedChange={setAvailableNow} />
          <Label htmlFor="available-now" className="flex items-center text-sm text-gray-700 dark:text-gray-300">
            <Clock className="mr-1.5 h-3.5 w-3.5" />
            Available Now
          </Label>
        </div>


      </div>
    </div>
    </div>;
}
