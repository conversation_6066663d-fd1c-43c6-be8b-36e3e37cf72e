
import React from 'react';
import { ArrowUpRight } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useNavigate } from 'react-router-dom';

interface UpgradeBannerProps {
  message: string;
  ctaText?: string;
  variant?: 'default' | 'subtle';
}

export const UpgradeBanner: React.FC<UpgradeBannerProps> = ({
  message,
  ctaText = "Upgrade Now",
  variant = 'default'
}) => {
  const navigate = useNavigate();
  
  return (
    <div className={`w-full rounded-lg mb-6 ${
      variant === 'default' 
        ? 'bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-950/30 dark:to-indigo-950/30 border border-indigo-100 dark:border-indigo-900/50'
        : 'bg-blue-50/50 dark:bg-blue-950/10 border border-blue-100/50 dark:border-blue-900/30'
    } p-4 flex items-center justify-between`}>
      <p className="font-medium text-sm sm:text-base">{message}</p>
      <Button 
        size="sm"
        variant={variant === 'default' ? 'default' : 'outline'}
        className="whitespace-nowrap ml-4 flex-shrink-0" 
        onClick={() => navigate('/provider/plans')}
      >
        {ctaText}
        <ArrowUpRight className="ml-1 h-4 w-4" />
      </Button>
    </div>
  );
};
