
import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Check, X } from 'lucide-react';
import { cn } from '@/lib/utils';

interface SubscriptionFeature {
  included: boolean;
  text: string;
}

interface SubscriptionPlanCardProps {
  title: string;
  price: string;
  description: string;
  commission: string;
  features: SubscriptionFeature[];
  ctaText: string;
  onSelect: () => void;
  isCurrentPlan?: boolean;
  isPopular?: boolean;
  highlighted?: boolean;
  disabled?: boolean;
}

export const SubscriptionPlanCard: React.FC<SubscriptionPlanCardProps> = ({
  title,
  price,
  description,
  commission,
  features,
  ctaText,
  onSelect,
  isCurrentPlan = false,
  isPopular = false,
  disabled = false
}) => {
  return (
    <div className={cn(
      "relative flex flex-col h-full rounded-xl shadow-md border transition-all border-border",
      isCurrentPlan && "ring-2 ring-green-500"
    )}>
      {isPopular && (
        <Badge className="absolute -top-3 left-1/2 -translate-x-1/2 py-1 px-3">
          Most Popular
        </Badge>
      )}
      
      <div className="p-6 border-b border-border space-y-2">
        <h3 className="text-2xl font-bold">{title}</h3>
        <div className="flex items-baseline gap-2">
          <span className="text-3xl font-bold">{price}</span>
          {price !== "Free" && (
            <span className="text-muted-foreground">/month</span>
          )}
        </div>
        <p className="text-muted-foreground">{description}</p>
      </div>
      
      <div className="p-6 border-b border-border">
        <div className="flex items-center gap-2 mb-6">
          <span className={cn(
            "text-lg font-semibold", 
            isCurrentPlan ? "text-primary" : "text-gray-900 dark:text-gray-100"
          )}>
            {commission}
          </span>
          <span className="text-muted-foreground">commission</span>
        </div>
        
        <div className="space-y-4">
          {features.map((feature, idx) => (
            <div key={idx} className="flex gap-3 items-center">
              {feature.included ? (
                <Check className="h-5 w-5 text-green-500 flex-shrink-0" />
              ) : (
                <X className="h-5 w-5 text-gray-300 dark:text-gray-600 flex-shrink-0" />
              )}
              <span className={cn(
                "text-sm",
                !feature.included && "text-muted-foreground"
              )}>
                {feature.text}
              </span>
            </div>
          ))}
        </div>
      </div>
      
      <div className="p-6 mt-auto">
        <Button
          className="w-full"
          variant={isCurrentPlan ? "success" : "outline"}
          onClick={onSelect}
          disabled={disabled || isCurrentPlan}
        >
          {ctaText}
        </Button>
      </div>
    </div>
  );
};
