
import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { ArrowUpRight, ArrowDownRight, Minus } from 'lucide-react';
import { cn } from '@/lib/utils';

interface PerformanceMetricCardProps {
  title: string;
  value: string | number;
  description?: string;
  icon: React.ReactNode;
  trend?: {
    value: number; // positive for increase, negative for decrease
    label: string; // e.g., "vs last month"
  };
  progress?: {
    value: number; // 0-100
    target?: number;
    label?: string;
  };
  color?: 'blue' | 'green' | 'amber' | 'purple' | 'slate';
}

export const PerformanceMetricCard = ({
  title,
  value,
  description,
  icon,
  trend,
  progress,
  color = 'blue'
}: PerformanceMetricCardProps) => {
  
  const colorStyles = {
    blue: {
      icon: 'bg-blue-100 text-blue-600',
      progress: 'bg-blue-600'
    },
    green: {
      icon: 'bg-green-100 text-green-600',
      progress: 'bg-green-600'
    },
    amber: {
      icon: 'bg-amber-100 text-amber-600',
      progress: 'bg-amber-600'
    },
    purple: {
      icon: 'bg-purple-100 text-purple-600',
      progress: 'bg-purple-600'
    },
    slate: {
      icon: 'bg-slate-100 text-slate-600',
      progress: 'bg-slate-600'
    }
  };
  
  return (
    <Card className="h-full">
      <CardHeader className="p-4 pb-2">
        <div className="flex justify-between items-start">
          <CardTitle className="text-base font-semibold">{title}</CardTitle>
          <div className={cn("h-8 w-8 rounded-full flex items-center justify-center", colorStyles[color].icon)}>
            {icon}
          </div>
        </div>
      </CardHeader>
      <CardContent className="p-4">
        <div className="flex items-end gap-2 mb-1">
          <span className="text-2xl font-bold">{value}</span>
          {trend && (
            <div className="flex items-center space-x-1 mb-0.5">
              {trend.value > 0 ? (
                <ArrowUpRight className="h-4 w-4 text-green-500" />
              ) : trend.value < 0 ? (
                <ArrowDownRight className="h-4 w-4 text-red-500" />
              ) : (
                <Minus className="h-4 w-4 text-gray-400" />
              )}
              <span className={cn(
                "text-xs font-medium",
                trend.value > 0 ? "text-green-500" : 
                trend.value < 0 ? "text-red-500" : "text-gray-400"
              )}>
                {trend.value > 0 && "+"}
                {trend.value}% {trend.label}
              </span>
            </div>
          )}
        </div>
        
        {description && (
          <CardDescription className="text-sm">{description}</CardDescription>
        )}
        
        {progress && (
          <div className="mt-3">
            <div className="flex justify-between mb-1">
              <span className="text-xs text-muted-foreground">{progress.label || 'Progress'}</span>
              {progress.target && (
                <span className="text-xs font-medium">{progress.value}/{progress.target}</span>
              )}
            </div>
            <Progress 
              value={progress.value} 
              className={cn("h-1.5", colorStyles[color].progress)}
            />
          </div>
        )}
      </CardContent>
    </Card>
  );
};
