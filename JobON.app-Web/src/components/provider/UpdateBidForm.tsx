import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useToast } from '@/hooks/use-toast';
import { updateBid, getBidById } from '@/services/bidService';
import { UpdateBidRequest, Bid } from '@/types/bid';
import { validateBidUpdateData, BidValidationError, getFieldError } from '@/utils/bidValidation';
import { useAuth } from '@/features/auth/hooks/useAuth';
import { AlertCircle, DollarSign, FileText, Loader2 } from 'lucide-react';

interface UpdateBidFormProps {
  bidId: string;
  onBidUpdated?: (updatedBid: Bid) => void;
  onCancel?: () => void;
}

export const UpdateBidForm: React.FC<UpdateBidFormProps> = ({
  bidId,
  onBidUpdated,
  onCancel
}) => {
  const [originalBid, setOriginalBid] = useState<Bid | null>(null);
  const [formData, setFormData] = useState<UpdateBidRequest>({
    amount: undefined,
    description: undefined
  });
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [validationErrors, setValidationErrors] = useState<BidValidationError[]>([]);
  const [touched, setTouched] = useState<Record<string, boolean>>({});
  const { toast } = useToast();
  const { token } = useAuth();

  // Load the original bid data
  useEffect(() => {
    const loadBidData = async () => {
      setIsLoading(true);
      try {
        const response = await getBidById(bidId, token || undefined);
        
        if (response.isSuccess && response.data) {
          setOriginalBid(response.data);
          setFormData({
            amount: response.data.amount,
            description: response.data.description
          });
        } else {
          toast({
            title: 'Error',
            description: response.error || 'Failed to load bid data',
            variant: 'destructive'
          });
        }
      } catch (error) {
        toast({
          title: 'Error',
          description: 'An unexpected error occurred while loading bid data',
          variant: 'destructive'
        });
      } finally {
        setIsLoading(false);
      }
    };

    if (bidId) {
      loadBidData();
    }
  }, [bidId, toast]);

  const handleInputChange = (field: keyof UpdateBidRequest, value: string | number | undefined) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));

    // Mark field as touched
    setTouched(prev => ({
      ...prev,
      [field]: true
    }));

    // Validate on change for real-time feedback
    const updatedData = { ...formData, [field]: value };
    const validation = validateBidUpdateData(updatedData);
    setValidationErrors(validation.errors);
  };

  const handleBlur = (field: keyof UpdateBidRequest) => {
    setTouched(prev => ({
      ...prev,
      [field]: true
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!originalBid) {
      toast({
        title: 'Error',
        description: 'Original bid data not loaded',
        variant: 'destructive'
      });
      return;
    }

    // Mark all fields as touched for validation display
    setTouched({
      amount: true,
      description: true
    });

    // Check if any changes were made
    const hasChanges = 
      formData.amount !== originalBid.amount || 
      formData.description !== originalBid.description;

    if (!hasChanges) {
      toast({
        title: 'No Changes',
        description: 'No changes were made to the bid.',
        variant: 'default'
      });
      return;
    }

    // Prepare update data with only changed fields
    const updateData: UpdateBidRequest = {};
    if (formData.amount !== originalBid.amount) {
      updateData.amount = formData.amount;
    }
    if (formData.description !== originalBid.description) {
      updateData.description = formData.description;
    }

    // Comprehensive validation
    const validation = validateBidUpdateData(updateData);
    setValidationErrors(validation.errors);

    if (!validation.isValid) {
      toast({
        title: 'Validation Error',
        description: 'Please fix the errors below before submitting.',
        variant: 'destructive'
      });
      return;
    }

    setIsSubmitting(true);

    try {
      const response = await updateBid(bidId, updateData, token || undefined);

      if (response.isSuccess && response.data) {
        toast({
          title: 'Bid Updated Successfully',
          description: 'Your bid has been updated successfully.',
        });

        // Call the callback with updated bid data
        onBidUpdated?.(response.data);
      } else {
        toast({
          title: 'Update Failed',
          description: response.error || 'Failed to update bid. Please try again.',
          variant: 'destructive'
        });
      }
    } catch (error) {
      toast({
        title: 'Error',
        description: 'An unexpected error occurred. Please try again.',
        variant: 'destructive'
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  if (isLoading) {
    return (
      <Card className="w-full max-w-2xl mx-auto">
        <CardContent className="flex items-center justify-center py-8">
          <Loader2 className="h-6 w-6 animate-spin mr-2" />
          <span>Loading bid data...</span>
        </CardContent>
      </Card>
    );
  }

  if (!originalBid) {
    return (
      <Card className="w-full max-w-2xl mx-auto">
        <CardContent className="py-8">
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              Failed to load bid data. Please try again.
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle>Update Your Bid</CardTitle>
        <div className="text-sm text-gray-600">
          <p><strong>Job:</strong> {originalBid.job?.title || 'N/A'}</p>
          <p><strong>Service Type:</strong> {originalBid.job?.serviceType || 'N/A'}</p>
          {originalBid.job?.location && <p><strong>Location:</strong> {originalBid.job.location}</p>}
          <p><strong>Current Status:</strong> <span className="capitalize">{originalBid.status}</span></p>
        </div>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* General validation errors */}
          {validationErrors.length > 0 && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                Please fix the following errors:
                <ul className="mt-2 list-disc list-inside">
                  {validationErrors.map((error, index) => (
                    <li key={index}>{error.message}</li>
                  ))}
                </ul>
              </AlertDescription>
            </Alert>
          )}

          <div className="space-y-2">
            <Label htmlFor="amount" className="flex items-center gap-2">
              <DollarSign className="h-4 w-4" />
              Bid Amount ($)
            </Label>
            <Input
              id="amount"
              type="number"
              min="0"
              step="0.01"
              value={formData.amount || ''}
              onChange={(e) => handleInputChange('amount', parseFloat(e.target.value) || undefined)}
              onBlur={() => handleBlur('amount')}
              placeholder="Enter your bid amount"
              className={getFieldError(validationErrors, 'amount') && touched.amount ? 'border-red-500' : ''}
            />
            {getFieldError(validationErrors, 'amount') && touched.amount && (
              <p className="text-sm text-red-600">{getFieldError(validationErrors, 'amount')}</p>
            )}
            <p className="text-xs text-gray-500">
              Original amount: ${originalBid.amount}
            </p>
          </div>

          <div className="space-y-2">
            <Label htmlFor="description" className="flex items-center gap-2">
              <FileText className="h-4 w-4" />
              Bid Description
            </Label>
            <Textarea
              id="description"
              value={formData.description || ''}
              onChange={(e) => handleInputChange('description', e.target.value)}
              onBlur={() => handleBlur('description')}
              placeholder="Describe your approach, timeline, and what's included"
              className={`min-h-[120px] ${getFieldError(validationErrors, 'description') && touched.description ? 'border-red-500' : ''}`}
              maxLength={2000}
            />
            {getFieldError(validationErrors, 'description') && touched.description && (
              <p className="text-sm text-red-600">{getFieldError(validationErrors, 'description')}</p>
            )}
            <p className="text-xs text-gray-500">
              {(formData.description || '').length}/2000 characters (minimum 10 required)
            </p>
          </div>

          <div className="flex gap-3 justify-end">
            {onCancel && (
              <Button
                type="button"
                variant="outline"
                onClick={onCancel}
                disabled={isSubmitting}
              >
                Cancel
              </Button>
            )}
            <Button
              type="submit"
              disabled={isSubmitting}
              className="min-w-[120px]"
            >
              {isSubmitting ? (
                <>
                  <Loader2 className="h-4 w-4 animate-spin mr-2" />
                  Updating...
                </>
              ) : (
                'Update Bid'
              )}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  );
};
