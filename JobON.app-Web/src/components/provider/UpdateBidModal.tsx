import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useToast } from '@/hooks/use-toast';
import { updateProviderBid } from '@/services/bidService';
import { useAuth } from '@/features/auth/hooks/useAuth';
import { Loader2 } from 'lucide-react';

interface UpdateBidModalProps {
  isOpen: boolean;
  onClose: () => void;
  bidId: string;
  currentBid?: {
    amount: number;
    description: string;
    estimated_completion_time?: string;
  };
  onSuccess?: () => void;
}

export const UpdateBidModal: React.FC<UpdateBidModalProps> = ({
  isOpen,
  onClose,
  bidId,
  currentBid,
  onSuccess
}) => {
  const { toast } = useToast();
  const { token } = useAuth();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  const [formData, setFormData] = useState({
    amount: currentBid?.amount?.toString() || '',
    description: currentBid?.description || '',
    estimated_completion_time: currentBid?.estimated_completion_time || ''
  });

  // Reset form when modal opens/closes or currentBid changes
  useEffect(() => {
    if (isOpen && currentBid) {
      setFormData({
        amount: currentBid.amount?.toString() || '',
        description: currentBid.description || '',
        estimated_completion_time: currentBid.estimated_completion_time || ''
      });
      setError(null);
    }
  }, [isOpen, currentBid]);

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
    // Clear error when user starts typing
    if (error) {
      setError(null);
    }
  };

  const validateForm = () => {
    if (!formData.amount || parseFloat(formData.amount) <= 0) {
      setError('Please enter a valid amount greater than 0');
      return false;
    }
    
    if (!formData.description.trim()) {
      setError('Please enter a description for your bid');
      return false;
    }
    
    if (!formData.estimated_completion_time.trim()) {
      setError('Please enter an estimated completion time');
      return false;
    }
    
    return true;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const updateData = {
        amount: parseFloat(formData.amount),
        description: formData.description.trim(),
        estimated_completion_time: formData.estimated_completion_time.trim()
      };

      const response = await updateProviderBid(bidId, updateData, token);

      if (response.isSuccess) {
        toast({
          title: "Bid Updated",
          description: "Your bid has been successfully updated.",
        });
        onSuccess?.();
        onClose();
      } else {
        setError(response.error || 'Failed to update bid');
      }
    } catch (err) {
      console.error('Error updating bid:', err);
      setError('An unexpected error occurred. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    if (!loading) {
      onClose();
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Update Bid</DialogTitle>
          <DialogDescription>
            Update your bid details below. All fields are required.
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4">
          {error && (
            <Alert variant="destructive">
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          <div className="space-y-2">
            <Label htmlFor="amount">Bid Amount ($)</Label>
            <Input
              id="amount"
              type="number"
              step="0.01"
              min="0"
              placeholder="Enter bid amount"
              value={formData.amount}
              onChange={(e) => handleInputChange('amount', e.target.value)}
              disabled={loading}
              required
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="description">Description</Label>
            <Textarea
              id="description"
              placeholder="Describe your approach to this job..."
              value={formData.description}
              onChange={(e) => handleInputChange('description', e.target.value)}
              disabled={loading}
              rows={4}
              required
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="estimated_completion_time">Estimated Completion Time</Label>
            <Input
              id="estimated_completion_time"
              type="text"
              placeholder="e.g., 2024-01-15 10:00:00 or 3-5 days"
              value={formData.estimated_completion_time}
              onChange={(e) => handleInputChange('estimated_completion_time', e.target.value)}
              disabled={loading}
              required
            />
          </div>

          <div className="flex justify-end space-x-2 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={handleClose}
              disabled={loading}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={loading}
              className="bg-blue-600 hover:bg-blue-700"
            >
              {loading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Updating...
                </>
              ) : (
                'Update Bid'
              )}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
};
