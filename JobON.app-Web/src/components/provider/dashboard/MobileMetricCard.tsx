
import React from 'react';
import { cn } from "@/lib/utils";

type MetricCardColor = 'blue' | 'green' | 'amber' | 'purple' | 'slate';

interface MobileMetricCardProps {
  title: string;
  value: string;
  icon: React.ReactNode;
  color: MetricCardColor;
}

const getColorStyles = (color: MetricCardColor) => {
  const colorMap = {
    blue: {
      background: 'bg-blue-50',
      text: 'text-blue-600',
      iconBg: 'bg-blue-100',
      border: 'border-blue-100',
    },
    green: {
      background: 'bg-green-50',
      text: 'text-green-600',
      iconBg: 'bg-green-100',
      border: 'border-green-100',
    },
    amber: {
      background: 'bg-amber-50',
      text: 'text-amber-600',
      iconBg: 'bg-amber-100',
      border: 'border-amber-100',
    },
    purple: {
      background: 'bg-purple-50',
      text: 'text-purple-600',
      iconBg: 'bg-purple-100',
      border: 'border-purple-100',
    },
    slate: {
      background: 'bg-slate-50',
      text: 'text-slate-600',
      iconBg: 'bg-slate-100',
      border: 'border-slate-100',
    }
  };

  return colorMap[color];
};

export const MobileMetricCard = ({ title, value, icon, color }: MobileMetricCardProps) => {
  const colorStyles = getColorStyles(color);

  return (
    <div className={cn(
      "flex flex-col items-center justify-center p-4 rounded-xl shadow-sm border",
      colorStyles.background, 
      colorStyles.border
    )}>
      <div className={cn(
        "rounded-full p-2 mb-2",
        colorStyles.iconBg
      )}>
        <div className={colorStyles.text}>
          {icon}
        </div>
      </div>
      <p className={cn("text-lg font-bold", colorStyles.text)}>{value}</p>
      <p className="text-xs text-gray-500 text-center">{title}</p>
    </div>
  );
};
