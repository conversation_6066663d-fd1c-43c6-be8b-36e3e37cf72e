import React from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { FileText, Upload } from 'lucide-react';

interface BusinessCertificationDialogProps {
  isOpen: boolean;
  onClose: () => void;
  planName: string;
}

export const BusinessCertificationDialog: React.FC<BusinessCertificationDialogProps> = ({
  isOpen,
  onClose,
  planName,
}) => {
  const navigate = useNavigate();

  const handleConfirm = () => {
    onClose();
    // Navigate to profile page with hash to scroll to business certifications section
    navigate('/provider/profile#business-certifications');
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <div className="flex items-center gap-3 mb-2">
            <div className="p-2 bg-blue-100 rounded-full">
              <FileText className="h-5 w-5 text-blue-600" />
            </div>
            <DialogTitle className="text-lg font-semibold">
              Business Certification Required
            </DialogTitle>
          </div>
          <DialogDescription className="text-sm text-muted-foreground leading-relaxed">
            To upgrade to the <span className="font-medium text-foreground">{planName}</span> plan, 
            you need to upload your business certifications and licenses. This helps us verify 
            your credentials and ensures trust with customers.
          </DialogDescription>
        </DialogHeader>
        
        <div className="flex items-center gap-3 p-4 bg-muted/50 rounded-lg my-4">
          <Upload className="h-5 w-5 text-muted-foreground" />
          <div className="text-sm">
            <p className="font-medium">Required documents:</p>
            <p className="text-muted-foreground">Business licenses, certifications, or insurance documents</p>
          </div>
        </div>

        <DialogFooter className="flex-col sm:flex-row gap-2">
          <Button variant="outline" onClick={onClose} className="w-full sm:w-auto">
            Cancel
          </Button>
          <Button onClick={handleConfirm} className="w-full sm:w-auto">
            Upload Certifications
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};