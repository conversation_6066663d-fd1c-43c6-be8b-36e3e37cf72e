import React from 'react';
import { Outlet } from 'react-router-dom';
import { useIsMobile } from '@/hooks/use-mobile';
import { SessionNavBar } from '@/components/ui/session-navbar';
import { cn } from '@/lib/utils';
import Footer from '@/components/Footer';
import { MobileNavigation } from '@/components/MobileNavigation';
import { MobileFooter } from '@/components/MobileFooter';

/**
 * ProviderLayout Component
 *
 * This component serves as a wrapper for all provider routes, ensuring that
 * the sidebar and footer are consistently displayed across all provider pages.
 */
const ProviderLayout: React.FC = () => {
  const isMobile = useIsMobile();

  return (
    <div className="flex flex-col min-h-screen">
      {/* Main content area with sidebar */}
      <div className={cn(
        "flex-1 flex flex-col min-h-screen",
        !isMobile && "ml-[3.05rem]" // Default collapsed width for sidebar
      )}>
        {/* Sidebar - only shown on desktop */}
        {!isMobile && <SessionNavBar />}

        {/* Outlet renders the child route components */}
        <Outlet />
      </div>

      {/* Footer */}
      {isMobile ? (
        <>
          <MobileNavigation />
          <MobileFooter />
        </>
      ) : (
        <Footer />
      )}
    </div>
  );
};

export default ProviderLayout;
