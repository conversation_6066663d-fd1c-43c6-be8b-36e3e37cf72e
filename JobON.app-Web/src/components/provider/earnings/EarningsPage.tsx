
import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { DollarSign, TrendingUp, Calendar, Info, Filter } from 'lucide-react';
import { format } from 'date-fns';
import { useIsMobile } from '@/hooks/use-mobile';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { cn } from '@/lib/utils';

// Sample data for earnings
const earningsData = {
  currentMonth: 1250,
  yearToDate: 8750,
  commissionRate: 20,
  payouts: [
    {
      id: '1',
      date: new Date(2025, 3, 15),
      jobName: 'Kitchen Sink Repair',
      amount: 185.00,
      status: 'paid'
    },
    {
      id: '2', 
      date: new Date(2025, 3, 12),
      jobName: 'Bathroom Remodel',
      amount: 1250.00,
      status: 'paid'
    },
    {
      id: '3',
      date: new Date(2025, 3, 5),
      jobName: 'Pipe Replacement',
      amount: 375.50,
      status: 'pending'
    },
    {
      id: '4',
      date: new Date(2025, 2, 28),
      jobName: 'Faucet Installation',
      amount: 120.00,
      status: 'paid'
    },
    {
      id: '5',
      date: new Date(2025, 2, 20),
      jobName: 'Water Heater Repair',
      amount: 450.00,
      status: 'paid'
    }
  ]
};

export const EarningsPage = () => {
  const isMobile = useIsMobile();
  const [activeTab, setActiveTab] = useState('overview');

  return (
    <div className="space-y-6">
      {isMobile && (
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full mb-6">
          <TabsList className="grid w-full grid-cols-2 mb-4">
            <TabsTrigger value="overview" className="text-sm">Overview</TabsTrigger>
            <TabsTrigger value="history" className="text-sm">Payout History</TabsTrigger>
          </TabsList>
        </Tabs>
      )}

      {(!isMobile || activeTab === 'overview') && (
        <>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <Card className={cn(isMobile ? "shadow-md border-blue-100" : "")}>
              <CardHeader className={cn("pb-3", isMobile && "bg-gradient-to-r from-blue-50 to-indigo-50 border-b")}>
                <CardTitle className="text-sm font-medium text-muted-foreground">
                  Earnings This Month
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-baseline gap-2">
                  <div className="text-2xl font-bold">${earningsData.currentMonth.toLocaleString()}</div>
                  <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                    <TrendingUp className="h-3 w-3 mr-1" /> +12%
                  </Badge>
                </div>
                <p className="text-xs text-muted-foreground mt-1">
                  Compared to last month
                </p>
              </CardContent>
            </Card>

            <Card className={cn(isMobile ? "shadow-md border-blue-100" : "")}>
              <CardHeader className={cn("pb-3", isMobile && "bg-gradient-to-r from-blue-50 to-indigo-50 border-b")}>
                <CardTitle className="text-sm font-medium text-muted-foreground">
                  Year-To-Date Earnings
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-baseline">
                  <div className="text-2xl font-bold">${earningsData.yearToDate.toLocaleString()}</div>
                </div>
                <p className="text-xs text-muted-foreground mt-1">
                  <Calendar className="h-3 w-3 inline mr-1" /> Since January 2025
                </p>
              </CardContent>
            </Card>

            <Card className={cn(isMobile ? "shadow-md border-blue-100" : "")}>
              <CardHeader className={cn("pb-3", isMobile && "bg-gradient-to-r from-blue-50 to-indigo-50 border-b")}>
                <CardTitle className="text-sm font-medium text-muted-foreground">
                  Commission Rate
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-baseline">
                  <div className="text-2xl font-bold">{earningsData.commissionRate}%</div>
                </div>
                <div className="flex items-center gap-1 mt-1">
                  <p className="text-xs text-muted-foreground">
                    Based on your current plan
                  </p>
                  <Info className="h-3 w-3 text-muted-foreground cursor-help" />
                </div>
              </CardContent>
            </Card>
          </div>

          <Card className="border-amber-200 bg-amber-50/30 shadow-md">
            <CardContent className="p-4 flex flex-col sm:flex-row justify-between items-center gap-4">
              <div className="flex items-center gap-3">
                <div className="bg-amber-100 p-2 rounded-full">
                  <DollarSign className="h-5 w-5 text-amber-600" />
                </div>
                <div>
                  <h3 className="font-medium">Lower your commission by upgrading</h3>
                  <p className="text-sm text-muted-foreground">Pro plan: 15% • Elite plan: 12%</p>
                </div>
              </div>
              <Button className="whitespace-nowrap bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 w-full sm:w-auto">
                Upgrade Plan
              </Button>
            </CardContent>
          </Card>
        </>
      )}

      {(!isMobile || activeTab === 'history') && (
        <Card className={cn("border-blue-100 shadow-md", isMobile && "rounded-xl mb-20")}>
          <CardHeader className={cn("pb-3 border-b", isMobile && "bg-gradient-to-r from-blue-50 to-indigo-50")}>
            <div className="flex justify-between items-center">
              <div>
                <CardTitle>Payout History</CardTitle>
                <CardDescription>
                  View all your completed jobs and payment status
                </CardDescription>
              </div>
              {isMobile && (
                <Button variant="outline" size="sm" className="flex items-center gap-1">
                  <Filter className="h-4 w-4" />
                  <span>Filter</span>
                </Button>
              )}
            </div>
          </CardHeader>
          <CardContent className={cn("p-0", isMobile && "max-h-[450px] overflow-auto")}>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Date</TableHead>
                  <TableHead>Job</TableHead>
                  <TableHead className="text-right">Amount</TableHead>
                  <TableHead>Status</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {earningsData.payouts.map((payout) => (
                  <TableRow key={payout.id}>
                    <TableCell className="font-medium">
                      {format(payout.date, 'MMM d, yyyy')}
                    </TableCell>
                    <TableCell>{payout.jobName}</TableCell>
                    <TableCell className="text-right">${payout.amount.toFixed(2)}</TableCell>
                    <TableCell>
                      <Badge
                        variant="outline"
                        className={`${
                          payout.status === 'paid'
                            ? 'bg-green-50 text-green-700 border-green-200'
                            : 'bg-yellow-50 text-yellow-700 border-yellow-200'
                        }`}
                      >
                        {payout.status === 'paid' ? 'Paid' : 'Pending'}
                      </Badge>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardContent>
        </Card>
      )}
    </div>
  );
};
