import React, { useState, useRef, useEffect } from 'react';
import { Card } from '@/components/ui/card';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Search, Send, Paperclip, Image, Star, X, ArrowLeft } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { useUIHelpers } from '@/hooks/use-ui-helpers';
import { useToast } from '@/hooks/use-toast';
import { Sheet, SheetContent, SheetHeader, SheetTitle, SheetTrigger } from '@/components/ui/sheet';

// Mock conversation data
const mockConversations = [{
  id: 'conv1',
  customer: {
    name: '<PERSON>',
    avatar: '/placeholder.svg',
    initials: '<PERSON><PERSON>'
  },
  lastMessage: "What time will you arrive tomorrow?",
  timestamp: "10:45 AM",
  unread: true,
  jobTitle: "Main Line Drain Cleaning"
}, {
  id: 'conv2',
  customer: {
    name: '<PERSON>',
    avatar: '/placeholder.svg',
    initials: 'E<PERSON>'
  },
  lastMessage: "Perfect, thank you for the update!",
  timestamp: "Yesterday",
  unread: false,
  jobTitle: "Shower Valve Replacement"
}, {
  id: 'conv3',
  customer: {
    name: 'Sarah Miller',
    avatar: '/placeholder.svg',
    initials: 'SM'
  },
  lastMessage: "Can you give me an estimate for this job?",
  timestamp: "2 days ago",
  unread: false,
  jobTitle: "Bathroom Sink Repair"
}];

// Mock messages for active conversation
const mockMessages = [{
  id: 'msg1',
  sender: 'customer',
  content: "Hi John, I'm having an issue with my main drain line. There's water backing up in the basement sink when we use the washing machine.",
  timestamp: "Yesterday, 4:30 PM"
}, {
  id: 'msg2',
  sender: 'provider',
  content: "Hello Michael, thanks for reaching out. That sounds like a potential clog in your main line. I have availability to come check it out tomorrow or Thursday.",
  timestamp: "Yesterday, 4:45 PM"
}, {
  id: 'msg3',
  sender: 'customer',
  content: "Tomorrow would be great if possible. What time can you come?",
  timestamp: "Yesterday, 5:10 PM"
}, {
  id: 'msg4',
  sender: 'provider',
  content: "I can arrive between 10:00 AM and 12:00 PM tomorrow. Does that work for you?",
  timestamp: "Yesterday, 5:25 PM"
}, {
  id: 'msg5',
  sender: 'customer',
  content: "What time will you arrive tomorrow?",
  timestamp: "Today, 10:45 AM"
}];
export const MessagesInterface = () => {
  const {
    toast
  } = useToast();
  const [activeConversation, setActiveConversation] = useState(mockConversations[0]);
  const [messageText, setMessageText] = useState('');
  const [showMobileConversation, setShowMobileConversation] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const {
    isMobile
  } = useUIHelpers();

  // Scroll to bottom of messages when conversation changes
  useEffect(() => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({
        behavior: 'smooth'
      });
    }
  }, [activeConversation, showMobileConversation]);
  const handleSendMessage = () => {
    if (!messageText.trim()) return;

    // In a real app, this would send the message to an API
    toast({
      title: "Message sent",
      description: `Your message has been sent to ${activeConversation.customer.name}.`
    });
    setMessageText('');
  };
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };
  return <div className="space-y-6">
      <div>
        
        
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 h-[calc(100vh-240px)] min-h-[500px]">
        {/* Conversation List */}
        <Card className={`p-4 overflow-hidden flex flex-col h-full ${isMobile && showMobileConversation ? 'hidden' : ''}`}>
          <div className="mb-4">
            <div className="relative">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input placeholder="Search conversations..." className="pl-9" />
            </div>
          </div>
          
          <div className="flex-grow overflow-auto">
            {mockConversations.map(conversation => <div key={conversation.id} onClick={() => {
            setActiveConversation(conversation);
            if (isMobile) setShowMobileConversation(true);
          }} className={`flex items-start gap-3 p-3 cursor-pointer rounded-lg mb-2 ${activeConversation.id === conversation.id ? 'bg-primary/5 border border-primary/10' : 'hover:bg-muted'}`}>
                <Avatar>
                  <AvatarImage src={conversation.customer.avatar} alt={conversation.customer.name} />
                  <AvatarFallback>{conversation.customer.initials}</AvatarFallback>
                </Avatar>
                <div className="flex-grow min-w-0">
                  <div className="flex justify-between items-center">
                    <h4 className="font-medium truncate">{conversation.customer.name}</h4>
                    <span className="text-xs text-muted-foreground whitespace-nowrap">{conversation.timestamp}</span>
                  </div>
                  <p className="text-sm truncate text-muted-foreground">{conversation.lastMessage}</p>
                  <div className="mt-1 text-xs text-muted-foreground">{conversation.jobTitle}</div>
                </div>
                {conversation.unread && <Badge className="h-2.5 w-2.5 rounded-full p-0 ml-1" />}
              </div>)}
          </div>

          {isMobile && <MobileQuickActions />}
        </Card>
        
        {/* Message Thread */}
        <Card className={`p-4 md:col-span-2 flex flex-col h-full ${isMobile && !showMobileConversation ? 'hidden' : ''}`}>
          {activeConversation ? <>
              {/* Header */}
              <div className="flex items-center justify-between pb-4 border-b">
                <div className="flex items-center gap-3">
                  {isMobile && <Button variant="ghost" size="icon" className="mr-1" onClick={() => setShowMobileConversation(false)}>
                      <ArrowLeft className="h-4 w-4" />
                    </Button>}
                  <Avatar>
                    <AvatarImage src={activeConversation.customer.avatar} alt={activeConversation.customer.name} />
                    <AvatarFallback>{activeConversation.customer.initials}</AvatarFallback>
                  </Avatar>
                  <div>
                    <h4 className="font-medium">{activeConversation.customer.name}</h4>
                    <p className="text-xs text-muted-foreground">{activeConversation.jobTitle}</p>
                  </div>
                </div>
                <div className="flex gap-2">
                  <Sheet>
                    <SheetTrigger asChild>
                      <Button variant="outline" size="sm">View Job Details</Button>
                    </SheetTrigger>
                    <SheetContent side={isMobile ? "bottom" : "right"} className={isMobile ? "h-[80%]" : ""}>
                      <SheetHeader>
                        <SheetTitle>Job Details</SheetTitle>
                      </SheetHeader>
                      <div className="mt-4 space-y-4">
                        <h3 className="text-lg font-medium">{activeConversation.jobTitle}</h3>
                        <p className="text-sm text-muted-foreground">
                          Customer: {activeConversation.customer.name}
                        </p>
                        <div className="border-t pt-4 mt-4">
                          <h4 className="font-medium mb-2">Description</h4>
                          <p className="text-sm">
                            This job involves cleaning the main drainage line that has been clogged, causing water backup issues in the basement sink.
                          </p>
                        </div>
                      </div>
                    </SheetContent>
                  </Sheet>
                  
                  {!isMobile && <Button variant="outline" size="icon" onClick={() => {
                toast({
                  title: "Job marked as important",
                  description: "This job has been marked as important"
                });
              }}>
                      <Star className="h-4 w-4" />
                    </Button>}
                </div>
              </div>
              
              {/* Messages */}
              <div className="flex-grow overflow-auto p-4 space-y-4">
                {mockMessages.map(message => <div key={message.id} className={`flex ${message.sender === 'provider' ? 'justify-end' : 'justify-start'}`}>
                    <div className={`max-w-[80%] rounded-lg p-3 ${message.sender === 'provider' ? 'bg-primary text-primary-foreground' : 'bg-muted'}`}>
                      <p className="text-sm">{message.content}</p>
                      <span className="text-xs opacity-70 block text-right mt-1">
                        {message.timestamp}
                      </span>
                    </div>
                  </div>)}
                <div ref={messagesEndRef} />
              </div>
              
              {/* Input */}
              <div className="pt-4 border-t mt-auto">
                <div className="flex items-center gap-2">
                  <div className="flex-shrink-0">
                    <Sheet>
                      <SheetTrigger asChild>
                        <Button variant="outline" size="icon">
                          <Paperclip className="h-4 w-4" />
                        </Button>
                      </SheetTrigger>
                      <SheetContent side={isMobile ? "bottom" : "right"} className={isMobile ? "h-[50%]" : ""}>
                        <SheetHeader>
                          <SheetTitle>Attach Files</SheetTitle>
                        </SheetHeader>
                        <div className="grid gap-4 pt-4">
                          <Button variant="outline" className="justify-start">
                            <Image className="mr-2 h-4 w-4" />
                            Add Photos
                          </Button>
                          <Button variant="outline" className="justify-start">
                            <Paperclip className="mr-2 h-4 w-4" />
                            Add Documents
                          </Button>
                        </div>
                      </SheetContent>
                    </Sheet>
                  </div>
                  <Input value={messageText} onChange={e => setMessageText(e.target.value)} onKeyDown={handleKeyDown} placeholder="Type your message..." className="flex-grow" />
                  <Button size="icon" onClick={handleSendMessage} disabled={!messageText.trim()}>
                    <Send className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </> : <div className="flex items-center justify-center h-full">
              <div className="text-center">
                <h3 className="font-medium">Select a conversation</h3>
                <p className="text-sm text-muted-foreground">Choose a conversation from the list to start messaging</p>
              </div>
            </div>}
        </Card>
      </div>
    </div>;
};

// Mobile quick actions component for the conversation list
const MobileQuickActions = () => {
  return <div className="pt-3 mt-2 border-t grid grid-cols-3 gap-2">
      <Button variant="outline" size="sm" className="flex flex-col items-center py-3 h-auto">
        <Search className="h-4 w-4 mb-1" />
        <span className="text-xs">Search</span>
      </Button>
      <Button variant="outline" size="sm" className="flex flex-col items-center py-3 h-auto">
        <Star className="h-4 w-4 mb-1" />
        <span className="text-xs">Starred</span>
      </Button>
      <Button variant="outline" size="sm" className="flex flex-col items-center py-3 h-auto">
        <Paperclip className="h-4 w-4 mb-1" />
        <span className="text-xs">Files</span>
      </Button>
    </div>;
};