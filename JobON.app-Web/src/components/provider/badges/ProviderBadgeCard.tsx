
import React from 'react';
import { Card, CardContent, CardDescription, <PERSON><PERSON>ooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Star, Clock, Award, DollarSign, Briefcase, ThumbsUp } from 'lucide-react';
import { cn } from '@/lib/utils';

export type BadgeType = 
  | 'fast_responder' 
  | 'top_rated' 
  | 'consistent_closer' 
  | 'high_earner'
  | 'customer_favorite'
  | 'completion_pro';

interface BadgeConfig {
  name: string;
  icon: React.ElementType;
  color: string;
  description: string;
  criteria: string;
  backgroundColor: string;
}

export const badgeConfigs: Record<BadgeType, BadgeConfig> = {
  fast_responder: {
    name: 'Fast Responder',
    icon: Clock,
    color: 'text-blue-600',
    backgroundColor: 'bg-blue-50',
    description: 'Responds to leads quickly',
    criteria: 'Responds to leads within 1 hour'
  },
  top_rated: {
    name: 'Top Rated',
    icon: Star,
    color: 'text-amber-500',
    backgroundColor: 'bg-amber-50',
    description: 'Consistently high ratings',
    criteria: 'Average rating of 4.8+ stars'
  },
  consistent_closer: {
    name: 'Consistent Closer',
    icon: ThumbsUp,
    color: 'text-green-600',
    backgroundColor: 'bg-green-50',
    description: 'Frequently wins bids',
    criteria: 'Wins bids on over 50% of submitted quotes'
  },
  high_earner: {
    name: 'High Earner',
    icon: DollarSign,
    color: 'text-emerald-600',
    backgroundColor: 'bg-emerald-50',
    description: 'Top income performer',
    criteria: 'Earns $10,000+ on JobOn'
  },
  customer_favorite: {
    name: 'Customer Favorite',
    icon: Award,
    color: 'text-purple-600',
    backgroundColor: 'bg-purple-50',
    description: 'Highly requested by customers',
    criteria: 'Requested by name by 10+ customers'
  },
  completion_pro: {
    name: 'Completion Pro',
    icon: Briefcase,
    color: 'text-indigo-600',
    backgroundColor: 'bg-indigo-50',
    description: 'Completes jobs as scheduled',
    criteria: 'Completes 95%+ of jobs on schedule'
  }
};

export interface ProviderBadge {
  type: BadgeType;
  earned: boolean;
  progress?: number; // 0-100 progress towards earning the badge
  earnedDate?: string;
}

interface ProviderBadgeCardProps {
  badge: ProviderBadge;
}

export const ProviderBadgeCard = ({ badge }: ProviderBadgeCardProps) => {
  const config = badgeConfigs[badge.type];
  
  return (
    <Card className={cn(
      "overflow-hidden transition-all hover:shadow-md h-full",
      badge.earned ? config.backgroundColor : "bg-gray-50 opacity-75"
    )}>
      <CardHeader className="p-4 pb-2">
        <div className="flex justify-between items-start">
          <div className={cn(
            "h-10 w-10 rounded-full flex items-center justify-center",
            badge.earned ? `${config.color} bg-white shadow-sm` : "bg-gray-100 text-gray-400"
          )}>
            <config.icon className="h-5 w-5" />
          </div>
          {badge.earned && (
            <Badge variant="outline" className="bg-white">Earned</Badge>
          )}
        </div>
      </CardHeader>
      <CardContent className="p-4 pt-2">
        <CardTitle className="text-base mb-1">{config.name}</CardTitle>
        <CardDescription className="line-clamp-2">{config.description}</CardDescription>
      </CardContent>
      <CardFooter className="p-4 pt-0">
        {badge.earned ? (
          <p className="text-xs text-muted-foreground">
            {badge.earnedDate && `Earned on ${badge.earnedDate}`}
          </p>
        ) : (
          <div className="w-full">
            <div className="h-1.5 w-full bg-gray-100 rounded-full mb-1">
              <div 
                className="h-full bg-gray-300 rounded-full"
                style={{ width: `${badge.progress || 0}%` }}
              />
            </div>
            <p className="text-xs text-muted-foreground">
              {badge.progress || 0}% progress • {config.criteria}
            </p>
          </div>
        )}
      </CardFooter>
    </Card>
  );
};
