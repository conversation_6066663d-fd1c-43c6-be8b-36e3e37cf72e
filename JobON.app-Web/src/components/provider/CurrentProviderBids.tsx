import React, { useState, useEffect, useCallback } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useToast } from '@/hooks/use-toast';
import { getCurrentProviderBids } from '@/services/bidService';
import { Bid, BidFilters, BidSortOptions, BidStatus } from '@/types/bid';
import { useAuth } from '@/features/auth/hooks/useAuth';
import { Loader2, Eye, RefreshCw } from 'lucide-react';

/**
 * CurrentProviderBids Component
 * 
 * This component demonstrates the usage of the new getCurrentProviderBids function
 * which implements the GET /api/provider/bids endpoint as per task 20.
 * 
 * Key features:
 * - Uses authenticated provider automatically (no providerId needed)
 * - Supports pagination and status filtering
 * - Leverages existing authentication system
 */
export const CurrentProviderBids: React.FC = () => {
  const { token, isProvider, isAuthenticated } = useAuth();
  const { toast } = useToast();

  // State management
  const [bids, setBids] = useState<Bid[]>([]);
  const [loading, setLoading] = useState(false);
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [filters, setFilters] = useState<BidFilters>({});
  const [sort, setSort] = useState<BidSortOptions>({ field: 'submittedAt', direction: 'desc' });

  // Fetch bids using the new getCurrentProviderBids function
  const fetchBids = useCallback(async () => {
    if (!isAuthenticated || !isProvider) {
      toast({
        title: 'Access Denied',
        description: 'You must be logged in as a provider to view bids',
        variant: 'destructive'
      });
      return;
    }
    
    setLoading(true);
    try {
      const response = await getCurrentProviderBids(filters, sort, page, 10, token || undefined);
      
      if (response.isSuccess && response.data) {
        setBids(response.data.bids);
        setTotalPages(response.data.totalPages);
      } else {
        toast({
          title: 'Error',
          description: response.error || 'Failed to fetch bids',
          variant: 'destructive'
        });
      }
    } catch (error) {
      toast({
        title: 'Error',
        description: 'An unexpected error occurred',
        variant: 'destructive'
      });
    } finally {
      setLoading(false);
    }
  }, [isAuthenticated, isProvider, filters, sort, page, token, toast]);

  // Effect to fetch bids when component mounts or dependencies change
  useEffect(() => {
    fetchBids();
  }, [fetchBids]);

  // Handle status filter change
  const handleStatusFilter = (status: string) => {
    setFilters(prev => ({
      ...prev,
      status: status === 'all' ? undefined : status as BidStatus
    }));
    setPage(1); // Reset to first page when filtering
  };

  // Handle sort change
  const handleSortChange = (field: string) => {
    setSort(prev => ({
      field: field as BidSortOptions['field'],
      direction: prev.field === field && prev.direction === 'asc' ? 'desc' : 'asc'
    }));
    setPage(1); // Reset to first page when sorting
  };

  // Render bid status badge
  const renderStatusBadge = (status: BidStatus) => {
    const statusColors = {
      pending: 'bg-yellow-100 text-yellow-800',
      accepted: 'bg-green-100 text-green-800',
      rejected: 'bg-red-100 text-red-800',
      withdrawn: 'bg-gray-100 text-gray-800'
    };

    return (
      <Badge className={statusColors[status]}>
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </Badge>
    );
  };

  // Show access denied message if not authenticated or not a provider
  if (!isAuthenticated || !isProvider) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="text-center">
            <h3 className="text-lg font-semibold mb-2">Access Denied</h3>
            <p className="text-muted-foreground">
              You must be logged in as a provider to view your bids.
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            My Bids
            <Button
              variant="outline"
              size="sm"
              onClick={fetchBids}
              disabled={loading}
            >
              <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
              Refresh
            </Button>
          </CardTitle>
        </CardHeader>
        <CardContent>
          {/* Filters and Sort */}
          <div className="flex gap-4 mb-6">
            <Select onValueChange={handleStatusFilter}>
              <SelectTrigger className="w-48">
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Statuses</SelectItem>
                <SelectItem value="pending">Pending</SelectItem>
                <SelectItem value="accepted">Accepted</SelectItem>
                <SelectItem value="rejected">Rejected</SelectItem>
                <SelectItem value="withdrawn">Withdrawn</SelectItem>
              </SelectContent>
            </Select>

            <Select onValueChange={handleSortChange}>
              <SelectTrigger className="w-48">
                <SelectValue placeholder="Sort by" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="submittedAt">Date Submitted</SelectItem>
                <SelectItem value="amount">Amount</SelectItem>
                <SelectItem value="updatedAt">Last Updated</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Loading state */}
          {loading && (
            <div className="flex items-center justify-center py-8">
              <Loader2 className="h-8 w-8 animate-spin" />
              <span className="ml-2">Loading bids...</span>
            </div>
          )}

          {/* Bids list */}
          {!loading && bids.length > 0 && (
            <div className="space-y-4">
              {bids.map((bid) => (
                <Card key={bid.id} className="border-l-4 border-l-blue-500">
                  <CardContent className="p-4">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-2">
                          <h4 className="font-semibold">
                            {bid.job?.title || `Job #${bid.jobId}`}
                          </h4>
                          {renderStatusBadge(bid.status)}
                        </div>
                        <p className="text-sm text-muted-foreground mb-2">
                          {bid.description}
                        </p>
                        <div className="flex items-center gap-4 text-sm text-muted-foreground">
                          <span>Amount: ${bid.amount.toFixed(2)}</span>
                          <span>Submitted: {new Date(bid.submittedAt).toLocaleDateString()}</span>
                          {bid.updatedAt !== bid.submittedAt && (
                            <span>Updated: {new Date(bid.updatedAt).toLocaleDateString()}</span>
                          )}
                        </div>
                      </div>
                      <Button variant="outline" size="sm">
                        <Eye className="h-4 w-4 mr-2" />
                        View Details
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}

          {/* Empty state */}
          {!loading && bids.length === 0 && (
            <div className="text-center py-8">
              <h3 className="text-lg font-semibold mb-2">No bids found</h3>
              <p className="text-muted-foreground">
                You haven't submitted any bids yet, or no bids match your current filters.
              </p>
            </div>
          )}

          {/* Pagination */}
          {!loading && totalPages > 1 && (
            <div className="flex items-center justify-between mt-6">
              <Button
                variant="outline"
                onClick={() => setPage(p => Math.max(1, p - 1))}
                disabled={page === 1}
              >
                Previous
              </Button>
              <span className="text-sm text-muted-foreground">
                Page {page} of {totalPages}
              </span>
              <Button
                variant="outline"
                onClick={() => setPage(p => Math.min(totalPages, p + 1))}
                disabled={page === totalPages}
              >
                Next
              </Button>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};
