
import React from 'react';
import { Badge } from '@/components/ui/badge';
import { Clock, Check } from 'lucide-react';
import { cn } from '@/lib/utils';

export type NotificationType = 'lead' | 'bid' | 'appointment' | 'payout' | 'badge' | 'subscription';

export interface NotificationItem {
  id: string;
  type: NotificationType;
  title: string;
  message: string;
  timestamp: string;
  read: boolean;
  actionLink?: string;
  actionText?: string;
}

interface NotificationItemProps {
  notification: NotificationItem;
  onMarkAsRead: (id: string) => void;
}

export const NotificationItem = ({ notification, onMarkAsRead }: NotificationItemProps) => {
  const getNotificationBadge = (type: NotificationType) => {
    switch (type) {
      case 'lead':
        return <Badge variant="business">New Lead</Badge>;
      case 'bid':
        return <Badge variant="secondary">Bid Update</Badge>;
      case 'appointment':
        return <Badge variant="warning">Reminder</Badge>;
      case 'payout':
        return <Badge variant="success">Payout</Badge>;
      case 'badge':
        return <Badge>Achievement</Badge>;
      case 'subscription':
        return <Badge variant="success">Subscription</Badge>;
      default:
        return null;
    }
  };

  return (
    <div 
      className={cn(
        "p-4 border-b last:border-b-0 transition-colors hover:bg-muted/30",
        !notification.read && "bg-blue-50 dark:bg-blue-950/10"
      )}
    >
      <div className="flex justify-between items-start mb-1.5">
        <div>
          {getNotificationBadge(notification.type)}
        </div>
        <div className="flex items-center text-xs text-muted-foreground">
          <Clock className="h-3 w-3 mr-1" />
          {notification.timestamp}
          {!notification.read && (
            <button 
              onClick={() => onMarkAsRead(notification.id)}
              className="ml-2 p-1 rounded-full hover:bg-muted"
              title="Mark as read"
            >
              <Check className="h-3 w-3" />
            </button>
          )}
        </div>
      </div>
      
      <h4 className="text-sm font-semibold mb-1">{notification.title}</h4>
      <p className="text-sm text-muted-foreground mb-2">{notification.message}</p>
      
      {notification.actionLink && notification.actionText && (
        <a 
          href={notification.actionLink}
          className="text-sm text-primary font-medium inline-block"
        >
          {notification.actionText}
        </a>
      )}
    </div>
  );
};
