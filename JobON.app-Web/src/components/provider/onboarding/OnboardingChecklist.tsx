
import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { UserCheck, Mail, Calendar, FileCheck, X } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { useToast } from '@/hooks/use-toast';
import { useOnboardingNavigation } from '@/hooks/use-onboarding-navigation';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from '@/components/ui/dialog';
import { useAuth } from '@/features/auth/hooks/useAuth';

interface OnboardingTask {
  id: string;
  title: string;
  completed: boolean;
  icon: React.ElementType;
  description: string;
}

export const OnboardingChecklist = () => {
  const { toast } = useToast();
  const { handleTaskNavigation } = useOnboardingNavigation();
  const [selectedTask, setSelectedTask] = React.useState<OnboardingTask | null>(null);
  const [hidden, setHidden] = React.useState(false);
  const { user } = useAuth();
  const [tasks, setTasks] = React.useState<OnboardingTask[]>([
    {
      id: 'profile',
      title: 'Complete Business Profile',
      completed: false,
      icon: UserCheck,
      description: 'Add your business details, services offered, and areas served to help customers find you.'
    },
    {
      id: 'contact',
      title: 'Verify Contact Information',
      completed: false,
      icon: Mail,
      description: 'Verify your email and phone number to receive job alerts and customer messages.'
    },
    {
      id: 'calendar',
      title: 'Set Availability Calendar',
      completed: false,
      icon: Calendar,
      description: 'Set your working hours and blocked dates to manage job scheduling.'
    },
    {
      id: 'bid',
      title: 'Submit First Job Bid',
      completed: false,
      icon: FileCheck,
      description: 'Browse available jobs and submit your first bid to start winning work.'
    }
  ]);

  // Check localStorage on mount to see if the checklist was previously hidden in this session
  React.useEffect(() => {
    const checklistHidden = localStorage.getItem('onboardingChecklistHidden');
    if (checklistHidden === 'true') {
      setHidden(true);
    }
  }, []);

  const completedTasks = tasks.filter(task => task.completed).length;
  const progress = (completedTasks / tasks.length) * 100;

  const handleTaskClick = (task: OnboardingTask) => {
    if (task.completed) {
      setSelectedTask(task);
    } else {
      handleTaskNavigation(task.id);
      setTasks(prevTasks =>
        prevTasks.map(t =>
          t.id === task.id ? { ...t, completed: true } : t
        )
      );
    }
  };

  const handleCloseChecklist = () => {
    setHidden(true);
    // Store in localStorage that checklist is hidden for this session
    localStorage.setItem('onboardingChecklistHidden', 'true');
    
    toast({
      title: "Checklist hidden",
      description: "You can see this again when you next sign in.",
      variant: "default"
    });
  };

  React.useEffect(() => {
    if (progress === 100) {
      toast({
        title: "🎉 Onboarding Complete!",
        description: "You've earned a free Lead Credit as a reward!",
        variant: "default"
      });
    }
  }, [progress, toast]);

  if (hidden) {
    return null;
  }

  return (
    <>
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="text-lg font-semibold">Get Started with JobOn</CardTitle>
            <div className="flex items-center gap-2">
              <Badge variant="outline">{`${completedTasks}/${tasks.length}`} Complete</Badge>
              <button 
                onClick={handleCloseChecklist}
                className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
                aria-label="Close checklist"
              >
                <X className="h-4 w-4" />
              </button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="mb-4">
            <Progress value={progress} className="h-2" />
            <p className="text-sm text-muted-foreground mt-2">
              {progress === 100 
                ? "All tasks completed! 🎉" 
                : `${Math.round(progress)}% complete - finish setup to get more leads!`}
            </p>
          </div>
          <div className="space-y-4">
            {tasks.map((task) => (
              <div
                key={task.id}
                className="flex items-center gap-3 p-3 rounded-lg bg-secondary/10 transition-colors cursor-pointer hover:bg-secondary/20"
                onClick={() => handleTaskClick(task)}
                role="button"
                tabIndex={0}
              >
                <div className={`rounded-full p-1.5 ${
                  task.completed ? 'bg-green-100 text-green-600' : 'bg-secondary/20'
                }`}>
                  <task.icon className="h-4 w-4" />
                </div>
                <div className="flex-1">
                  <div className="font-medium">{task.title}</div>
                  <p className="text-sm text-muted-foreground">{task.description}</p>
                </div>
                {task.completed && (
                  <Badge variant="default" className="bg-green-600">
                    Completed
                  </Badge>
                )}
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      <Dialog open={!!selectedTask} onOpenChange={() => setSelectedTask(null)}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>{selectedTask?.title}</DialogTitle>
            <DialogDescription>
              This task is already completed! Would you like to review or update your information?
            </DialogDescription>
          </DialogHeader>
          <div className="flex justify-end space-x-2">
            <button
              className="px-4 py-2 bg-primary text-white rounded-md"
              onClick={() => {
                if (selectedTask) {
                  handleTaskNavigation(selectedTask.id);
                }
                setSelectedTask(null);
              }}
            >
              Review/Update
            </button>
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
};
