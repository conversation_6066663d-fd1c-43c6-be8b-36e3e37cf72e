
import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { MapPin, DollarSign, Calendar as CalendarIcon, ArrowLeft, ChevronLeft, ChevronRight, X, ZoomIn } from 'lucide-react';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog';
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { useForm } from 'react-hook-form';
import * as z from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { toast } from '@/hooks/use-toast';
import { useIsMobile } from '@/hooks/use-mobile';
import { apiService } from '@/services/api';
import { useAuth } from '@/features/auth/hooks/useAuth';
import { updateProviderBid, deleteProviderBid } from '@/services/bidService';

// Define lead data type based on API response
interface Lead {
  id: string;
  title: string;
  description: string;
  location: {
    city: string;
    state: string;
    fullAddress?: string;
    address?: string;
    zipCode?: string;
  };
  postedAt: string;
  budget?: number;
  category: string;
  urgent?: boolean;
  requestedDate?: string;
  customerName?: string;
  photos?: string[];
  service?: {
    category: string;
    tasks?: string[];
  };
  schedule?: {
    date: string;
    timePreference?: string;
  };
  assets?: Array<{
    url: string;
    uuid: string;
  }>;
  contact?: {
    fullName: string;
  };
  user?: {
    name: string;
  };
  createdAt: string;
  status: string;
  // Add bid-related fields
  provider_bid?: {
    id: number | string;
    amount: number;
    status: string;
    description: string;
    estimated_completion_time?: string;
  } | null;
  bids_summary?: {
    has_bid: boolean;
    bid_status: string | null;
    bid_amount: number | null;
  };
}

// Define bid form schema
const bidFormSchema = z.object({
  amount: z.string().min(1, "Amount is required").refine(val => !isNaN(Number(val)), {
    message: "Amount must be a valid number",
  }),
  message: z.string().min(10, "Message must be at least 10 characters"),
  availableDate: z.string().min(1, "Available date is required"),
  estimatedCompletionTime: z.string().min(1, "Estimated completion time is required"),
});

const updateBidFormSchema = z.object({
  amount: z.string().min(1, "Amount is required").refine(val => !isNaN(Number(val)), {
    message: "Amount must be a valid number",
  }),
  message: z.string().min(10, "Message must be at least 10 characters"),
  estimatedCompletionTime: z.string().min(1, "Estimated completion time is required"),
});

type BidFormValues = z.infer<typeof bidFormSchema>;
type UpdateBidFormValues = z.infer<typeof updateBidFormSchema>;

export const LeadDetailsView = ({ jobId }: { jobId: string }) => {
  const [lead, setLead] = useState<Lead | null>(null);
  const [loading, setLoading] = useState(true);
  const [bidDialogOpen, setBidDialogOpen] = useState(false);
  const [updateDialogOpen, setUpdateDialogOpen] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  const [updating, setUpdating] = useState(false);
  const [withdrawing, setWithdrawing] = useState(false);
  // Lightbox state
  const [lightboxOpen, setLightboxOpen] = useState(false);
  const [selectedPhotoIndex, setSelectedPhotoIndex] = useState(0);
  const navigate = useNavigate();
  const isMobile = useIsMobile();
  const { token } = useAuth();

  const form = useForm<BidFormValues>({
    resolver: zodResolver(bidFormSchema),
    defaultValues: {
      amount: "",
      message: "",
      availableDate: "",
      estimatedCompletionTime: "",
    },
  });

  const updateForm = useForm<UpdateBidFormValues>({
    resolver: zodResolver(updateBidFormSchema),
    defaultValues: {
      amount: "",
      message: "",
      estimatedCompletionTime: "",
    },
  });

  useEffect(() => {
    const fetchLeadDetails = async () => {
      if (!jobId || !token) {
        console.log('Missing jobId or token:', { jobId, token });
        return;
      }

      setLoading(true);

      try {
        const authToken = token || '';

        const response = await apiService(`/api/provider/job-bookings/${jobId}`, {
          method: 'GET',
          requiresAuth: true,
          headers: {
            'Authorization': authToken,
            'Content-Type': 'application/json'
          }
        });

        if (response.isSuccess && response.data) {
          //@ts-ignore
          const jobData = response.data.data || response.data;

          // Format relative time
          const formatRelativeTime = (dateString: string) => {
            const date = new Date(dateString);
            const now = new Date();
            const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);

            if (diffInSeconds < 60) return `${diffInSeconds} seconds ago`;
            const diffInMinutes = Math.floor(diffInSeconds / 60);
            if (diffInMinutes < 60) return `${diffInMinutes} ${diffInMinutes === 1 ? 'minute' : 'minutes'} ago`;
            const diffInHours = Math.floor(diffInMinutes / 60);
            if (diffInHours < 24) return `${diffInHours} ${diffInHours === 1 ? 'hour' : 'hours'} ago`;
            const diffInDays = Math.floor(diffInHours / 24);
            return `${diffInDays} ${diffInDays === 1 ? 'day' : 'days'} ago`;
          };

          // Map API response to Lead interface
          const mappedLead: Lead = {
            id: jobData.id,
            title: jobData.service?.category
              ? `${jobData.service.category}${jobData.service.tasks ? ` - ${jobData.service.tasks.join(', ')}` : ''}`
              : 'Service Request',
            description: jobData.description || 'No description provided',
            location: {
              city: jobData.location?.city || 'Unknown',
              state: jobData.location?.state || 'Unknown',
              fullAddress: jobData.location?.address || `${jobData.location?.city || 'Unknown'}, ${jobData.location?.state || 'Unknown'}`,
              address: jobData.location?.address,
              zipCode: jobData.location?.zipCode
            },
            postedAt: formatRelativeTime(jobData.createdAt),
            budget: typeof jobData.budget === 'number' ? jobData.budget : (jobData.budget ? parseInt(jobData.budget) : undefined),
            category: jobData.service?.category || 'General',
            urgent: jobData.status === 'urgent',
            requestedDate: jobData.schedule?.date,
            customerName: jobData.contact?.fullName || jobData.user?.name || 'Customer',
            photos: jobData.assets?.map((asset: any) => `https://dash.jobon.app/storage/${asset.url}`) || [],
            service: jobData.service,
            schedule: jobData.schedule,
            assets: jobData.assets,
            contact: jobData.contact,
            user: jobData.user,
            createdAt: jobData.createdAt,
            status: jobData.status,
            // Add bid-related fields from the provider-specific API response
            provider_bid: jobData.provider_bid || null,
            bids_summary: jobData.bids_summary || { has_bid: false, bid_status: null, bid_amount: null }
          };

          setLead(mappedLead);

          // Set default bid amount if budget exists
          if (mappedLead.budget) {
            form.setValue("amount", mappedLead.budget.toString());
          }

          // Populate update form if provider has an existing bid
          if (mappedLead.provider_bid) {
            updateForm.setValue("amount", mappedLead.provider_bid.amount.toString());
            updateForm.setValue("message", mappedLead.provider_bid.description);
            if (mappedLead.provider_bid.estimated_completion_time) {
              updateForm.setValue("estimatedCompletionTime", mappedLead.provider_bid.estimated_completion_time);
            }
          }
        } else {
          throw new Error(response.error || 'Failed to fetch lead details');
        }
      } catch (error) {
        console.error("Error fetching lead details:", error);
        toast({
          title: "Error",
          description: "Failed to load lead details. Please try again.",
          variant: "destructive"
        });
      } finally {
        setLoading(false);
      }
    };

    fetchLeadDetails();
  }, [jobId, token, form, toast]);

  const onSubmitBid = async (values: BidFormValues) => {
    if (!lead || !token) return;

    setSubmitting(true);

    try {
      // token from useAuthHeader already includes 'Bearer ' prefix
      const authToken = token || '';

      // Prepare bid data using new API format
      const bidData = {
        amount: parseFloat(values.amount),
        description: values.message,
        estimated_completion_time: values.estimatedCompletionTime
      };


      const response = await apiService(`/api/job-bookings/${lead.id}/bids`, {
        method: 'POST',
        requiresAuth: true,
        headers: {
          'Authorization': authToken,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(bidData)
      });

      if (response.isSuccess) {
        // Show success toast
        toast({
          title: "Bid submitted successfully!",
          description: "The customer will be notified of your offer.",
        });

        // Close dialog and redirect
        setBidDialogOpen(false);
        navigate('/provider/jobs?tab=leads');
      } else {
        throw new Error(response.error || 'Failed to submit bid');
      }
    } catch (error) {
      console.error("Error submitting bid:", error);
      toast({
        title: "Failed to submit bid",
        description: error instanceof Error ? error.message : "Please try again later.",
        variant: "destructive",
      });
    } finally {
      setSubmitting(false);
    }
  };

  const onUpdateBid = async (values: UpdateBidFormValues) => {
    if (!lead?.provider_bid || !token) return;

    setUpdating(true);

    try {
      const updateData = {
        amount: parseFloat(values.amount),
        description: values.message,
        estimated_completion_time: values.estimatedCompletionTime
      };

      const response = await updateProviderBid(
        lead.provider_bid.id.toString(),
        updateData,
        token
      );

      if (response.isSuccess) {
        toast({
          title: "Bid updated successfully!",
          description: "Your bid has been updated.",
        });

        setUpdateDialogOpen(false);
        // Refresh the lead details to get updated bid info
        window.location.reload();
      } else {
        throw new Error(response.error || 'Failed to update bid');
      }
    } catch (error) {
      console.error("Error updating bid:", error);
      toast({
        title: "Failed to update bid",
        description: error instanceof Error ? error.message : "Please try again later.",
        variant: "destructive",
      });
    } finally {
      setUpdating(false);
    }
  };

  const onWithdrawBid = async () => {
    if (!lead?.provider_bid || !token) return;

    setWithdrawing(true);

    try {
      const response = await deleteProviderBid(
        lead.provider_bid.id.toString(),
        token
      );

      if (response.isSuccess) {
        toast({
          title: "Bid withdrawn successfully!",
          description: "Your bid has been withdrawn.",
        });

        // Refresh the lead details to reflect the withdrawn bid
        window.location.reload();
      } else {
        throw new Error(response.error || 'Failed to withdraw bid');
      }
    } catch (error) {
      console.error("Error withdrawing bid:", error);
      toast({
        title: "Failed to withdraw bid",
        description: error instanceof Error ? error.message : "Please try again later.",
        variant: "destructive",
      });
    } finally {
      setWithdrawing(false);
    }
  };

  const handleGoBack = () => {
    navigate('/provider/jobs?tab=leads');
  };

  // Lightbox handlers
  const openLightbox = (index: number) => {
    setSelectedPhotoIndex(index);
    setLightboxOpen(true);
  };

  const closeLightbox = () => {
    setLightboxOpen(false);
  };

  const nextPhoto = () => {
    if (lead?.photos && selectedPhotoIndex < lead.photos.length - 1) {
      setSelectedPhotoIndex(selectedPhotoIndex + 1);
    }
  };

  const prevPhoto = () => {
    if (selectedPhotoIndex > 0) {
      setSelectedPhotoIndex(selectedPhotoIndex - 1);
    }
  };

  // Keyboard navigation for lightbox
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (!lightboxOpen) return;

      switch (event.key) {
        case 'Escape':
          closeLightbox();
          break;
        case 'ArrowLeft':
          prevPhoto();
          break;
        case 'ArrowRight':
          nextPhoto();
          break;
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [lightboxOpen, selectedPhotoIndex, lead?.photos]);

  // Loading state
  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center mb-4">
          <Button 
            variant="ghost" 
            size="sm" 
            className="gap-1" 
            onClick={handleGoBack}
          >
            <ArrowLeft className="h-4 w-4" />
            Back to Leads
          </Button>
        </div>
        <Card>
          <CardHeader>
            <Skeleton className="h-8 w-3/4 mb-2" />
            <Skeleton className="h-4 w-1/2" />
          </CardHeader>
          <CardContent className="space-y-4">
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-4 w-3/4" />
            <div className="flex flex-wrap gap-4 mt-4">
              <Skeleton className="h-24 w-24 rounded-md" />
              <Skeleton className="h-24 w-24 rounded-md" />
            </div>
          </CardContent>
          <CardFooter>
            <Skeleton className="h-10 w-32" />
          </CardFooter>
        </Card>
      </div>
    );
  }

  if (!lead) {
    return (
      <div className="space-y-6">
        <div className="flex items-center mb-4">
          <Button 
            variant="ghost" 
            size="sm" 
            className="gap-1" 
            onClick={handleGoBack}
          >
            <ArrowLeft className="h-4 w-4" />
            Back to Leads
          </Button>
        </div>
        <Card>
          <CardHeader>
            <CardTitle>Lead not found</CardTitle>
            <CardDescription>This lead may have been removed or is no longer available.</CardDescription>
          </CardHeader>
          <CardFooter>
            <Button onClick={handleGoBack}>Return to Leads</Button>
          </CardFooter>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6 pb-8">
      {/* Back button */}
      <div className={`flex items-center ${isMobile ? 'mb-2' : 'mb-4'}`}>
        <Button
          variant="ghost"
          size={isMobile ? "sm" : "default"}
          className="gap-1 hover:bg-gray-100 dark:hover:bg-gray-800"
          onClick={handleGoBack}
        >
          <ArrowLeft className={`${isMobile ? 'h-4 w-4' : 'h-5 w-5'}`} />
          <span className="font-medium">Back to Leads</span>
        </Button>
      </div>

      <Card className="border-l-4 border-l-primary shadow-sm">
        <CardHeader className={`${isMobile ? 'flex-col space-y-3' : 'flex-row items-start justify-between'}`}>
          <div className={isMobile ? 'w-full' : ''}>
            <div className="flex items-center gap-2 flex-wrap">
              <CardTitle className={`${isMobile ? 'text-lg' : 'text-xl'}`}>{lead.title}</CardTitle>
              {lead.urgent && (
                <Badge variant="destructive">Urgent</Badge>
              )}
            </div>
            <CardDescription className="mt-1.5">Posted {lead.postedAt}</CardDescription>
          </div>
          <Badge variant="outline" className={isMobile ? 'self-start' : ''}>{lead.category}</Badge>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Details section */}
          <div className={`grid gap-4 ${isMobile ? 'grid-cols-1' : 'sm:grid-cols-2 lg:grid-cols-3'}`}>
            <div className="flex flex-col space-y-1">
              <span className="text-sm font-medium text-gray-500">Location</span>
              <div className="flex items-center">
                <MapPin className="h-4 w-4 mr-1 text-gray-500 flex-shrink-0" />
                <span className="text-sm">{lead.location.fullAddress || `${lead.location.city}, ${lead.location.state}`}</span>
              </div>
            </div>
            {lead.requestedDate && (
              <div className="flex flex-col space-y-1">
                <span className="text-sm font-medium text-gray-500">Requested Date</span>
                <div className="flex items-center">
                  <CalendarIcon className="h-4 w-4 mr-1 text-gray-500 flex-shrink-0" />
                  <span className="text-sm">{new Date(lead.requestedDate).toLocaleDateString()}</span>
                </div>
              </div>
            )}
            {lead.budget && (
              <div className="flex flex-col space-y-1">
                <span className="text-sm font-medium text-gray-500">Budget</span>
                <div className="flex items-center">
                  <DollarSign className="h-4 w-4 mr-1 text-gray-500 flex-shrink-0" />
                  <span className="text-sm font-semibold">${lead.budget}</span>
                </div>
              </div>
            )}
          </div>

          {/* Description */}
          <div>
            <h3 className="text-lg font-medium mb-2">Description</h3>
            <p className="text-gray-700 whitespace-pre-line">{lead.description}</p>
          </div>

          {/* Photos */}
          {lead.photos && lead.photos.length > 0 && (
            <div>
              <h3 className="text-lg font-medium mb-3">Photos ({lead.photos.length})</h3>
              <div className={`grid gap-3 ${
                isMobile
                  ? 'grid-cols-2'
                  : lead.photos.length === 1
                    ? 'grid-cols-1 max-w-md'
                    : lead.photos.length === 2
                      ? 'grid-cols-2 max-w-2xl'
                      : 'grid-cols-2 sm:grid-cols-3 lg:grid-cols-4'
              }`}>
                {lead.photos.map((photo, index) => (
                  <div
                    key={index}
                    className="relative aspect-square rounded-lg overflow-hidden border border-gray-200 cursor-pointer group hover:shadow-md transition-all duration-200"
                    onClick={() => openLightbox(index)}
                  >
                    <img
                      src={photo}
                      alt={`Job photo ${index + 1}`}
                      className="object-cover w-full h-full group-hover:scale-105 transition-transform duration-200"
                    />
                    <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-200 flex items-center justify-center">
                      <ZoomIn className="h-6 w-6 text-white opacity-0 group-hover:opacity-100 transition-opacity duration-200" />
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </CardContent>
        <CardFooter className={`${isMobile ? "flex-col space-y-3 pt-6" : "flex-row gap-3 pt-6"}`}>
          {/* Conditional rendering based on bid status */}
          {!lead.bids_summary?.has_bid ? (
            // No bid exists - show Submit Bid button
            <Button
              onClick={() => setBidDialogOpen(true)}
              className={`${isMobile ? "w-full h-11" : "px-6"}`}
              size={isMobile ? "lg" : "default"}
            >
              Submit Bid
            </Button>
          ) : lead.bids_summary?.bid_status === 'requested' ? (
            // Bid exists with 'requested' status - show Update and Withdraw buttons
            <div className={`${isMobile ? "flex flex-col space-y-2 w-full" : "flex gap-2"}`}>
              <Button
                onClick={() => setUpdateDialogOpen(true)}
                className={`${isMobile ? "w-full h-11" : "px-6"}`}
                size={isMobile ? "lg" : "default"}
                disabled={updating}
              >
                {updating ? "Updating..." : "Update Bid"}
              </Button>
              <Button
                variant="outline"
                onClick={onWithdrawBid}
                className={`${isMobile ? "w-full h-11" : "px-6"}`}
                size={isMobile ? "lg" : "default"}
                disabled={withdrawing}
              >
                {withdrawing ? "Withdrawing..." : "Withdraw Bid"}
              </Button>
            </div>
          ) : (
            // Bid exists with other status - show status info
            <div className={`flex items-center gap-2 ${isMobile ? "w-full justify-center" : ""}`}>
              <Badge variant={
                lead.bids_summary?.bid_status === 'accepted' ? 'default' :
                lead.bids_summary?.bid_status === 'rejected' ? 'destructive' :
                'secondary'
              } className="px-3 py-1">
                Bid {lead.bids_summary?.bid_status}
              </Badge>
              {lead.bids_summary?.bid_amount && (
                <span className="text-sm font-medium text-muted-foreground">
                  ${lead.bids_summary.bid_amount}
                </span>
              )}
            </div>
          )}

          {isMobile && (
            <Button
              variant="outline"
              onClick={handleGoBack}
              className="w-full h-11"
              size="lg"
            >
              Back to Leads
            </Button>
          )}
        </CardFooter>
      </Card>

      {/* Photo Lightbox Dialog */}
      <Dialog open={lightboxOpen} onOpenChange={setLightboxOpen}>
        <DialogContent className="max-w-4xl w-full h-[90vh] p-0 bg-black/95">
          <div className="relative w-full h-full flex items-center justify-center">
            {/* Close button */}
            <Button
              variant="ghost"
              size="icon"
              className="absolute top-4 right-4 z-10 text-white hover:bg-white/20"
              onClick={closeLightbox}
            >
              <X className="h-6 w-6" />
            </Button>

            {/* Navigation buttons */}
            {lead.photos && lead.photos.length > 1 && (
              <>
                <Button
                  variant="ghost"
                  size="icon"
                  className="absolute left-4 top-1/2 -translate-y-1/2 z-10 text-white hover:bg-white/20 disabled:opacity-30"
                  onClick={prevPhoto}
                  disabled={selectedPhotoIndex === 0}
                >
                  <ChevronLeft className="h-8 w-8" />
                </Button>
                <Button
                  variant="ghost"
                  size="icon"
                  className="absolute right-4 top-1/2 -translate-y-1/2 z-10 text-white hover:bg-white/20 disabled:opacity-30"
                  onClick={nextPhoto}
                  disabled={selectedPhotoIndex === lead.photos.length - 1}
                >
                  <ChevronRight className="h-8 w-8" />
                </Button>
              </>
            )}

            {/* Photo */}
            {lead.photos && lead.photos[selectedPhotoIndex] && (
              <img
                src={lead.photos[selectedPhotoIndex]}
                alt={`Job photo ${selectedPhotoIndex + 1}`}
                className="max-w-full max-h-full object-contain"
              />
            )}

            {/* Photo counter */}
            {lead.photos && lead.photos.length > 1 && (
              <div className="absolute bottom-4 left-1/2 -translate-x-1/2 bg-black/50 text-white px-3 py-1 rounded-full text-sm">
                {selectedPhotoIndex + 1} of {lead.photos.length}
              </div>
            )}
          </div>
        </DialogContent>
      </Dialog>

      {/* Bid Dialog */}
      <Dialog open={bidDialogOpen} onOpenChange={setBidDialogOpen}>
        <DialogContent className={`${isMobile ? 'w-[95vw] max-w-[95vw]' : 'sm:max-w-[500px]'} max-h-[90vh] overflow-y-auto`}>
          <DialogHeader>
            <DialogTitle>Submit a Bid</DialogTitle>
            <DialogDescription>
              Enter your bid details for "{lead.title}".
            </DialogDescription>
          </DialogHeader>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmitBid)} className="space-y-4">
              <FormField
                control={form.control}
                name="amount"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Bid Amount ($)</FormLabel>
                    <FormControl>
                      <Input type="number" placeholder="Enter amount" {...field} />
                    </FormControl>
                    <FormDescription>
                      Customer's budget: ${lead.budget}
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <FormField
                control={form.control}
                name="availableDate"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>When can you start?</FormLabel>
                    <FormControl>
                      <Input type="date" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="estimatedCompletionTime"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Estimated Completion Time</FormLabel>
                    <FormControl>
                      <Input type="datetime-local" {...field} />
                    </FormControl>
                    <FormDescription>
                      When do you expect to complete this job?
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="message"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Message to Customer</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Introduce yourself and explain why you're a good fit for this job..."
                        className="min-h-[120px]"
                        {...field}
                      />
                    </FormControl>
                    <FormDescription>
                      Highlight your experience and approach to the job.
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <DialogFooter>
                <Button 
                  variant="outline" 
                  type="button" 
                  onClick={() => setBidDialogOpen(false)}
                  disabled={submitting}
                >
                  Cancel
                </Button>
                <Button type="submit" disabled={submitting}>
                  {submitting ? "Submitting..." : "Submit Bid"}
                </Button>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>

      {/* Update Bid Dialog */}
      <Dialog open={updateDialogOpen} onOpenChange={setUpdateDialogOpen}>
        <DialogContent className={`${isMobile ? 'w-[95vw] max-w-[95vw]' : 'sm:max-w-[500px]'} max-h-[90vh] overflow-y-auto`}>
          <DialogHeader>
            <DialogTitle>Update Your Bid</DialogTitle>
            <DialogDescription>
              Update your bid details for "{lead.title}".
            </DialogDescription>
          </DialogHeader>
          <Form {...updateForm}>
            <form onSubmit={updateForm.handleSubmit(onUpdateBid)} className="space-y-4">
              <FormField
                control={updateForm.control}
                name="amount"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Bid Amount ($)</FormLabel>
                    <FormControl>
                      <Input type="number" placeholder="Enter amount" {...field} />
                    </FormControl>
                    <FormDescription>
                      Customer's budget: ${lead.budget}
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={updateForm.control}
                name="estimatedCompletionTime"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Estimated Completion Time</FormLabel>
                    <FormControl>
                      <Input type="datetime-local" {...field} />
                    </FormControl>
                    <FormDescription>
                      When do you expect to complete this job?
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={updateForm.control}
                name="message"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Message to Customer</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Update your message to the customer..."
                        className="min-h-[120px]"
                        {...field}
                      />
                    </FormControl>
                    <FormDescription>
                      Highlight your experience and approach to the job.
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <DialogFooter>
                <Button
                  variant="outline"
                  type="button"
                  onClick={() => setUpdateDialogOpen(false)}
                  disabled={updating}
                >
                  Cancel
                </Button>
                <Button type="submit" disabled={updating}>
                  {updating ? "Updating..." : "Update Bid"}
                </Button>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>
    </div>
  );
};
