
import React, { useState } from 'react';
import { Slider } from "@/components/ui/slider";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { CheckCircle, Info } from 'lucide-react';

interface ServiceEstimate {
  tier: string;
  price: string;
  description: string;
  features: string[];
  recommended?: boolean;
}

interface ServiceEstimatorProps {
  serviceId: string;
  estimates: ServiceEstimate[];
}

export const ServiceEstimator: React.FC<ServiceEstimatorProps> = ({ 
  serviceId,
  estimates
}) => {
  const [projectSize, setProjectSize] = useState(1);
  const [urgency, setUrgency] = useState(1);
  
  // Map the slider values to user-friendly text
  const getSizeText = () => {
    if (projectSize < 1) return "Small";
    if (projectSize < 2) return "Medium";
    return "Large";
  };
  
  const getUrgencyText = () => {
    if (urgency < 1) return "Standard";
    if (urgency < 2) return "Priority";
    return "Emergency";
  };
  
  // Determine which service tier should be recommended based on selections
  const getRecommendedTier = () => {
    const combinedScore = projectSize + urgency;
    
    if (combinedScore < 1) return 0; // Basic/Economy
    if (combinedScore < 3) return 1; // Standard
    return 2; // Premium
  };
  
  const recommendedIndex = getRecommendedTier();
  
  return (
    <div className="bg-white dark:bg-gray-800 rounded-xl shadow-xl p-6 max-w-md mx-auto">
      <h3 className="text-xl font-bold mb-4 text-gray-900 dark:text-white">Get Instant Price Estimate</h3>
      
      <div className="space-y-6 mb-6">
        <div>
          <div className="flex justify-between mb-2">
            <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
              Project Size
            </label>
            <span className="text-sm font-medium text-primary">{getSizeText()}</span>
          </div>
          <Slider
            defaultValue={[1]}
            max={2}
            step={1}
            value={[projectSize]}
            onValueChange={(value) => setProjectSize(value[0])}
            className="mb-4"
          />
          <div className="flex justify-between text-xs text-gray-500 dark:text-gray-400">
            <span>Small</span>
            <span>Medium</span>
            <span>Large</span>
          </div>
        </div>
        
        <div>
          <div className="flex justify-between mb-2">
            <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
              Service Urgency
            </label>
            <span className="text-sm font-medium text-primary">{getUrgencyText()}</span>
          </div>
          <Slider
            defaultValue={[1]}
            max={2}
            step={1}
            value={[urgency]}
            onValueChange={(value) => setUrgency(value[0])}
            className="mb-4"
          />
          <div className="flex justify-between text-xs text-gray-500 dark:text-gray-400">
            <span>Standard</span>
            <span>Priority</span>
            <span>Emergency</span>
          </div>
        </div>
      </div>
      
      <div className="space-y-4 mt-8">
        {estimates.map((estimate, index) => {
          const isRecommended = index === recommendedIndex;
          return (
            <Card 
              key={estimate.tier} 
              className={`${isRecommended ? 'border-primary shadow-md ring-1 ring-primary' : 'border-gray-200 dark:border-gray-700'} transition-all hover:shadow-md relative`}
            >
              {isRecommended && (
                <div className="absolute -top-3 left-1/2 transform -translate-x-1/2 bg-primary text-white text-xs font-bold px-3 py-1 rounded-full">
                  Recommended
                </div>
              )}
              <CardContent className={`p-4 ${isRecommended ? 'pt-6' : 'pt-4'}`}>
                <div className="flex justify-between items-start">
                  <div>
                    <h4 className="font-semibold text-lg">{estimate.tier}</h4>
                    <p className="text-gray-500 dark:text-gray-400 text-sm">{estimate.description}</p>
                  </div>
                  <div className="text-right">
                    <p className="text-xl font-bold text-primary">{estimate.price}</p>
                  </div>
                </div>
                
                <div className="mt-4 space-y-2">
                  {estimate.features.map((feature, i) => (
                    <div key={i} className="flex items-start text-sm">
                      <CheckCircle className="h-4 w-4 text-primary mr-2 mt-0.5 flex-shrink-0" />
                      <span>{feature}</span>
                    </div>
                  ))}
                </div>
                
                <div className="mt-4">
                  <Button className={`w-full ${isRecommended ? 'bg-primary' : 'bg-primary/80'}`}>
                    Book {estimate.tier} Service
                  </Button>
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>
      
      <div className="mt-6 text-center text-sm text-gray-500 dark:text-gray-400 flex items-center justify-center">
        <Info className="h-4 w-4 mr-1" />
        <span>Prices are estimates and may vary based on specific requirements</span>
      </div>
    </div>
  );
};
