import React, { useState } from 'react';
import { Search, MapPin, Navigation, Star, Shield, Clock, CheckCheck } from 'lucide-react';
import { Button } from "@/components/ui/button";
import { SearchBar } from './SearchBar';
import { Briefcase, Trees, Wrench, Zap, Scissors, Fan, Bug, Hammer, Cog, Refrigerator, Building, Store, Home } from 'lucide-react';
import { Link, useLocation, useNavigate, useSearchParams } from 'react-router-dom';
import { StarRating } from './StarRating';
import { ServiceCard } from './ServiceCard';
import { useIsMobile } from '@/hooks/use-mobile';
import { categories } from './CategoryList';
import { AuroraBackground } from './ui/aurora-background';
import { motion } from 'framer-motion';
import { TextRotate } from './ui/text-rotate';
const serviceIcons = [{
  icon: <Wrench size={24} />,
  label: 'Plumbing',
  path: '/services/plumbing',
  bgColor: 'bg-blue-100',
  textColor: 'text-blue-600'
}, {
  icon: <Zap size={24} />,
  label: 'Electrical',
  path: '/services/electrical',
  bgColor: 'bg-amber-100',
  textColor: 'text-amber-600'
}, {
  icon: <Scissors size={24} />,
  label: 'Cleaning',
  path: '/services/cleaning',
  bgColor: 'bg-cyan-100',
  textColor: 'text-cyan-600'
},{
  icon: <Trees size={24} />,
  label: 'Landscaping',
  path: '/services/landscaping',
  bgColor: 'bg-green-100',
  textColor: 'text-green-600'
},
  // {
  //   icon: <Bug size={24} />,
  //   label: 'Pest Control',
  //   path: '/services/pest-control',
  //   bgColor: 'bg-red-100',
  //   textColor: 'text-red-600'
  // }, {
  //   icon: <Hammer size={24} />,
  //   label: 'Handyman',
  //   path: '/services/handyman',
  //   bgColor: 'bg-orange-100',
  //   textColor: 'text-orange-600'
  // }, {
  //   icon: <Refrigerator size={24} />,
  //   label: 'Appliance Repair',
  //   path: '/services/appliance-repair',
  //   bgColor: 'bg-purple-100',
  //   textColor: 'text-purple-600'
  // }, {
  //   icon: <Fan size={24} />,
  //   label: 'HVAC',
  //   path: '/services/hvac',
  //   bgColor: 'bg-indigo-100',
  //   textColor: 'text-indigo-600'
  // }
];
interface HeroProps {
  hideSearchBar?: boolean;
  hideServiceIcons?: boolean;
  hideButtons?: boolean;
  title?: React.ReactNode;
  subtitle?: React.ReactNode;
  showStats?: boolean;
  showLocationSearch?: boolean;
  searchBarHint?: string;
  serviceIconsHint?: string;
  serviceName?: string;
  professionalTitle?: string;
}
export const Hero: React.FC<HeroProps> = ({
  hideSearchBar = false,
  hideServiceIcons = false,
  hideButtons = false,
  title,
  subtitle,
  showStats = true,
  showLocationSearch = false,
  searchBarHint,
  serviceIconsHint,
  serviceName = '',
  professionalTitle = 'Professionals'
}) => {
  const location = useLocation();
  const navigate = useNavigate();
  const [searchParams, setSearchParams] = useSearchParams();
  const isJobsPage = location.pathname === '/jobs';
  const isServicePage = location.pathname.includes('/services/');
  const isMobile = useIsMobile();
  const handleServiceIconClick = (service: {
    label: string;
    path: string;
  }) => {
    if (isJobsPage) {
      searchParams.set('category', service.label);
      searchParams.set('page', '1');
      setSearchParams(searchParams);
      setTimeout(() => {
        const jobsGrid = document.querySelector('[data-jobs-grid]');
        if (jobsGrid) {
          jobsGrid.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
          });
        }
      }, 100);
    } else {
      navigate(service.path);
    }
  };
  const formattedTrustedUsers = "4.8M+";
  const [serviceType, setServiceType] = useState<'home' | 'office' | 'commercial'>('home');
  const rotatingWords = ["professional", "reliable", "trusted", "skilled", "experienced", "qualified", "certified"];
  const getProfessionalButtonText = () => {
    if (!serviceName) return "Browse Pros";
    return `Browse ${professionalTitle || `${serviceName} Pros`}`;
  };
  const heroContent = <>
      {!isMobile && <div className="mb-6 max-w-md mx-auto"></div>}

      <div className="flex flex-col items-center text-center">
        {isMobile && <div className="w-full">
            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden mb-5">
              <div className="relative h-52 overflow-hidden">
                <img src="/lovable-uploads/9b223582-543e-4d67-bdeb-38c5a3054a83.png" alt="Service professional with homeowner" className="w-full h-full object-cover object-center" style={{
                  objectPosition: "center 30%"
                }} />
                <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-black/40 to-transparent flex items-end">
                  <div className="p-4 text-white w-full">
                    <h1 className="text-2xl font-bold mb-1 text-left">
                      {isJobsPage ? "Find Jobs Near You" : "Find Local Service Pros"}
                    </h1>
                    <h2 className="text-lg font-medium text-blue-300 mb-2 text-left">
                      {isJobsPage ? <>
                          <span className="block">Work On Your Terms</span>
                          <span className="block">Apply with Ease</span>
                        </> : "Get Competitive Bids"}
                    </h2>
                    <div className="flex items-center space-x-2 mb-1">
                      <div className="flex">
                        {[1, 2, 3, 4, 5].map((_, i) => <Star key={i} className="h-3 w-3 text-amber-400 fill-amber-400" />)}
                      </div>
                      <span className="text-xs font-medium">4.9/5 · Rated by early users</span>
                    </div>
                  </div>
                </div>
              </div>

              <div className="p-4">
                <p className="text-sm text-gray-600 dark:text-gray-300 mb-4">
                  {isJobsPage ? "Thousands of jobs posted monthly" : "Thousands of users and growing"}
                </p>

                {isJobsPage && <Link to="/create-job" className="w-full block mb-3">
                    <Button variant="default" size="default" className="w-full rounded-md">
                      Apply Now
                    </Button>
                  </Link>}

                {!hideSearchBar && <div className="mb-3">
                    <SearchBar variant="standard" placeholder={isJobsPage ? "Search for jobs..." : "What service do you need?"} />
                  </div>}

                {!hideButtons && <div className="flex space-x-2">
                    <Link to="/create-job" className="flex-1">
                      <Button variant="default" size="default" className="w-full rounded-md">
                        Post a Job
                      </Button>
                    </Link>
                    <Link to={isJobsPage ? "/auth" : isServicePage && serviceName ? `/services/${serviceName.toLowerCase()}/professionals` : "/professionals"} className="flex-1">
                      <Button variant="outline" size="default" className="w-full rounded-md">
                        {isJobsPage ? "Sign Up" : getProfessionalButtonText()}
                      </Button>
                    </Link>
                  </div>}
              </div>
            </div>

            <div className="bg-white dark:bg-gray-800 rounded-xl shadow p-3 mb-4 flex items-center justify-between">
              <div className="flex items-center">
                <Shield className="h-8 w-8 text-green-500 mr-2" />
                <div className="text-left">
                  <span className="block text-xs text-gray-500 dark:text-gray-400">
                    {isJobsPage ? "Available" : "Verified"}
                  </span>
                  <span className="font-bold text-sm">
                    {isJobsPage ? "Jobs" : "Professionals"}
                  </span>
                </div>
              </div>
              <div className="w-px h-10 bg-gray-200 dark:bg-gray-700"></div>
              <div className="flex items-center">
                <Clock className="h-8 w-8 text-blue-500 mr-2" />
                <div className="text-left">
                  <span className="block text-xs text-gray-500 dark:text-gray-400">Response</span>
                  <span className="font-bold text-sm">&#60; 1hr</span>
                </div>
              </div>
              <div className="w-px h-10 bg-gray-200 dark:bg-gray-700"></div>
              <div className="flex items-center">
                <CheckCheck className="h-8 w-8 text-purple-500 mr-2" />
                <div className="text-left">
                  <span className="block text-xs text-gray-500 dark:text-gray-400">
                    {isJobsPage ? "Projects" : "Pros"}
                  </span>
                  <span className="font-bold text-sm">10K+</span>
                </div>
              </div>
            </div>

            <p className="text-sm text-center text-gray-600 dark:text-gray-400 mb-4">
              {isJobsPage ? "Most applications get responses in under 1 hour" : "Most projects get bids in under 1 hour"}
            </p>
          </div>}

        {!isMobile && <div className="flex flex-col lg:flex-row items-center justify-between w-full gap-8 z-10">
            <div className="max-w-2xl text-center lg:text-left space-y-6 md:space-y-8">
              <motion.div initial={{
            opacity: 0,
            y: 20
          }} animate={{
            opacity: 1,
            y: 0
          }} transition={{
            duration: 0.5,
            delay: 0.2
          }}>
                <h1 className="text-4xl md:text-5xl lg:text-7xl font-bold text-foreground leading-tight tracking-tight dark:text-white">
                  {title ? title : <React.Fragment>
                      <span className="text-gray-900 dark:text-white">
                        Find{' '}
                        <span className="inline-flex">
                          <TextRotate texts={rotatingWords} mainClassName="overflow-hidden text-primary dark:text-blue-300" staggerDuration={0.03} staggerFrom="last" rotationInterval={3000} transition={{
                      type: "spring",
                      damping: 30,
                      stiffness: 400
                    }} />
                        </span>
                      </span>
                      <br className="hidden md:inline" />
                      <span className="text-primary mt-1 block dark:text-blue-300">
                        {serviceType === 'home' ? 'Home service pros.' : serviceType === 'office' ? 'Office service pros.' : 'Commercial service pros.'}
                      </span>
                    </React.Fragment>}
                </h1>
              </motion.div>

              <motion.div initial={{
            opacity: 0,
            y: 20
          }} animate={{
            opacity: 1,
            y: 0
          }} transition={{
            duration: 0.5,
            delay: 0.4
          }}>
                <p className="text-lg md:text-2xl text-foreground max-w-2xl mx-auto lg:mx-0 leading-relaxed dark:text-gray-300">
                  {subtitle ? subtitle : <span className="space-y-2 md:space-y-3">
                      <span className="block">Find local service pros for all your 
                        {serviceType === 'home' ? ' home' : serviceType === 'office' ? ' office' : ' business'} 
                        needs</span>
                    </span>}
                </p>
              </motion.div>

              {!hideButtons && <motion.div className="pt-6 md:pt-10" initial={{
            opacity: 0,
            y: 20
          }} animate={{
            opacity: 1,
            y: 0
          }} transition={{
            duration: 0.5,
            delay: 0.6
          }}>
                  <div className="flex flex-col sm:flex-row gap-3 justify-center lg:justify-start">
                    <Link to="/create-job" className="w-full sm:w-auto">
                      <Button variant="default" size="lg" className="text-base md:text-lg font-medium w-full sm:w-auto px-4 md:px-8 py-6 h-auto rounded-xl shadow-lg hover:shadow-xl hover:translate-y-[-2px] transition-all">
                        Post a Project
                      </Button>
                    </Link>
                    <Link to={isServicePage && serviceName ? `/services/${serviceName.toLowerCase()}/professionals` : "/professionals"} className="w-full sm:w-auto">
                      <Button variant="secondary" size="lg" className="text-base md:text-lg font-medium w-full sm:w-auto px-4 md:px-8 py-6 h-auto rounded-xl border-2 border-gray-400 dark:border-gray-500 bg-gray-50 dark:bg-gray-800 text-gray-800 dark:text-white hover:bg-primary/10 dark:hover:bg-primary/20 hover:border-primary dark:hover:border-primary hover:text-primary dark:hover:text-primary-foreground shadow-lg hover:shadow-xl hover:translate-y-[-2px] transition-all">
                        {getProfessionalButtonText()}
                      </Button>
                    </Link>
                  </div>
                  <p className="text-xs md:text-sm text-muted-foreground mt-2 md:mt-4">
                    No credit card required • Most projects get bids in under 1 hour
                  </p>
                </motion.div>}
            </div>

            <motion.div className="w-full max-w-md" initial={{
          opacity: 0,
          scale: 0.95
        }} animate={{
          opacity: 1,
          scale: 1
        }} transition={{
          duration: 0.5,
          delay: 0.5
        }}>
              <div className="relative">
                <div className="absolute inset-0 bg-primary/10 dark:bg-primary/20 rounded-full filter blur-xl opacity-70"></div>
                <div className="relative bg-white dark:bg-gray-800 rounded-2xl p-5 shadow-lg">
                  <img src="/lovable-uploads/9b223582-543e-4d67-bdeb-38c5a3054a83.png" alt="Homeowner meeting with service professional" className="w-full h-auto rounded-xl object-cover aspect-[4/3]" />
                  <div className="mt-4 p-2">
                    <div className="flex justify-between items-center mb-2">
                      <div className="flex text-amber-400">
                        <StarRating rating={4.9} />
                      </div>
                      <span className="text-sm font-medium text-gray-500 dark:text-gray-400">156 reviews</span>
                    </div>
                    <h3 className="font-bold text-lg text-gray-900 dark:text-white">
                      {serviceType === 'home' ? 'Mike H.' : serviceType === 'office' ? 'Sarah J.' : 'Team Solutions Inc.'}
                    </h3>
                    <p className="text-sm text-gray-600 dark:text-gray-300">
                      {serviceType === 'home' ? 'Professional Handyman • Oakland, CA' : serviceType === 'office' ? 'Office Specialist • San Francisco, CA' : 'Commercial Services • Los Angeles, CA'}
                    </p>
                    <div className="mt-3 flex flex-wrap gap-2">
                      <span className="text-xs bg-blue-50 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 px-2 py-1 rounded-full">Repairs</span>
                      <span className="text-xs bg-green-50 dark:bg-green-900/30 text-green-700 dark:text-green-300 px-2 py-1 rounded-full">Installations</span>
                      <span className="text-xs bg-purple-50 dark:bg-purple-900/30 text-purple-700 dark:text-purple-300 px-2 py-1 rounded-full">Maintenance</span>
                    </div>
                  </div>
                </div>
              </div>
            </motion.div>
          </div>}
      </div>

      {!hideSearchBar && !isMobile && <motion.div className="mt-8" initial={{
      opacity: 0,
      y: 20
    }} animate={{
      opacity: 1,
      y: 0
    }} transition={{
      duration: 0.5,
      delay: 0.7
    }}>
          
        </motion.div>}

      {showStats && <div className={`${isMobile ? 'mt-2' : 'mt-8'} text-center`}>
          <p className="text-sm md:text-base font-medium text-gray-700 dark:text-gray-300">
            Thousands of users and growing
          </p>
          <div className="flex items-center justify-center mt-1">
            <span className="font-bold text-gray-900 dark:text-white mr-1">4.9/5</span>
            <div className="flex text-amber-400">
              <StarRating rating={4.9} />
            </div>
            <span className="ml-2 text-sm font-medium text-gray-600 dark:text-gray-400">
              Rated by early users
            </span>
          </div>
          {!isMobile && <p className="text-sm mt-1 text-gray-600 dark:text-gray-400">
              Most projects get bids in under 1 hour
            </p>}
        </div>}

      {!hideServiceIcons && <motion.div className="mt-6 md:mt-8 z-10" initial={{
      opacity: 0
    }} animate={{
      opacity: 1
    }} transition={{
      duration: 0.5,
      delay: 0.9
    }}>
          <h3 className="text-center text-lg md:text-xl font-semibold mb-4 text-gray-900 dark:text-white">
            {isJobsPage ? "Popular Job Categories" : "Popular Services"}
          </h3>

          <div className={`grid grid-cols-4 ${isMobile ? 'gap-2' : 'sm:grid-cols-4 lg:grid-cols-4 gap-4 md:gap-6'}`}>
            {serviceIcons.map((service, index) => {
          const categoryMatch = categories.find(cat => cat.title === service.label);
          const bgColor = service.bgColor || 'bg-primary/5';
          const textColor = service.textColor || 'text-primary';
          return <div key={index} onClick={() => handleServiceIconClick(service)} className={`flex flex-col items-center text-center group hover:scale-105 transition-transform cursor-pointer ${isJobsPage ? 'hover:cursor-pointer' : ''}`}>
                  <div className={`${isMobile ? 'w-12 h-12' : 'w-16 h-16 md:w-20 md:h-20'} flex items-center justify-center ${bgColor} dark:bg-opacity-70 rounded-full mb-2 md:mb-3 group-hover:bg-opacity-80 transition-colors`}>
                    {React.cloneElement(service.icon as React.ReactElement, {
                className: `${isMobile ? 'h-6 w-6' : 'h-8 w-8'} ${textColor} dark:opacity-90`,
                strokeWidth: 1.5
              })}
                  </div>
                  <span className={`${isMobile ? 'text-xs' : 'text-sm md:text-base'} font-medium ${textColor} dark:opacity-90 group-hover:opacity-100 transition-colors`}>
                    {service.label}
                  </span>
                </div>;
        })}
          </div>
          {serviceIconsHint && <p className="mt-3 text-center text-sm text-muted-foreground">{serviceIconsHint}</p>}
        </motion.div>}
    </>;
  return <>
      <AuroraBackground className="h-auto py-12 md:py-14 overflow-hidden flex flex-col justify-center">
        <div className="container mx-auto px-4 md:px-8 lg:px-16 relative z-10 pt-0 md:pt-10">
          {heroContent}
        </div>
      </AuroraBackground>

      {showStats && !isMobile && <section className="w-full py-12 md:py-16 relative overflow-hidden bg-gray-50 dark:bg-gray-900">
          <div className="absolute inset-0 bg-gradient-to-r from-blue-50/80 to-indigo-50/80 dark:from-blue-900/20 dark:to-blue-900/20 z-0"></div>

          {!isMobile && <React.Fragment>
              <div className="absolute top-0 right-0 w-64 h-64 bg-primary/5 dark:bg-primary/10 rounded-full filter blur-2xl"></div>
              <div className="absolute bottom-0 left-1/4 w-48 h-48 bg-yellow-400/10 dark:bg-blue-400/10 rounded-full filter blur-2xl"></div>
              <div className="absolute top-1/2 left-1/3 w-32 h-32 bg-green-400/10 dark:bg-teal-400/10 rounded-full filter blur-xl"></div>
              <div className="absolute inset-0 opacity-10 bg-[radial-gradient(#444_1px,transparent_1px)] [background-size:16px_16px] dark:bg-[radial-gradient(#ccc_1px,transparent_1px)]"></div>
            </React.Fragment>}

          <div className="max-w-7xl mx-auto px-4 md:px-6 relative z-10">
            <div className="text-center mb-8 md:mb-10">
              <h2 className="text-xl md:text-3xl font-bold text-gray-900 dark:text-white">
                Why People Choose JobON
              </h2>
              <div className="h-1 w-16 md:w-24 bg-primary dark:bg-blue-400 mx-auto mt-2 md:mt-3 rounded-full"></div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 md:gap-8 py-4 md:py-6">
              <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-100 dark:border-gray-700 p-4 md:p-6 flex flex-col items-center text-center space-y-2 md:space-y-3 hover:shadow-md transition-all duration-300">
                <div className="flex text-amber-400 mb-1 md:mb-2">
                  <StarRating rating={5} size={isMobile ? "sm" : "lg"} />
                </div>
                <h3 className="text-lg md:text-xl font-semibold text-gray-900 dark:text-white">Customer reviews</h3>
                <p className="text-xs md:text-sm text-gray-600 dark:text-gray-300">Verified ratings from our early users</p>
              </div>

              <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-100 dark:border-gray-700 p-4 md:p-6 flex flex-col items-center text-center space-y-2 md:space-y-3 hover:shadow-md transition-all duration-300">
                <span className="text-2xl md:text-3xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-blue-600 to-blue-400 dark:from-blue-400 dark:to-blue-200 mb-1 md:mb-2">10,000+</span>
                <h3 className="text-lg md:text-xl font-semibold text-gray-900 dark:text-white">People trust JobON</h3>
                <p className="text-xs md:text-sm text-gray-600 dark:text-gray-300">Across all service categories nationwide</p>
              </div>

              <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-100 dark:border-gray-700 p-4 md:p-6 flex flex-col items-center text-center space-y-2 md:space-y-3 hover:shadow-md transition-all duration-300">
                <div className="w-10 h-10 md:w-14 md:h-14 rounded-full bg-green-50 dark:bg-green-900/30 flex items-center justify-center mb-1 md:mb-2">
                  <CheckCheck className="h-6 w-6 md:h-8 md:w-8 text-green-600 dark:text-green-400" />
                </div>
                <h3 className="text-lg md:text-xl font-semibold text-gray-900 dark:text-white">Competitive quotes</h3>
                <p className="text-xs md:text-sm text-gray-600 dark:text-gray-300">Compare bids from multiple service professionals</p>
              </div>
            </div>
          </div>
        </section>}
    </>;
};
