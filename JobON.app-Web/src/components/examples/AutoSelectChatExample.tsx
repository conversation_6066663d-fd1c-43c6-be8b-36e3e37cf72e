import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { MessageSquare, Users, ExternalLink } from 'lucide-react';

/**
 * Example component demonstrating the auto-select chat functionality
 * 
 * This shows how to create links that will automatically open specific chats
 * when navigating to message pages.
 */
export const AutoSelectChatExample: React.FC = () => {
  // Example user IDs (these would come from your actual data)
  const exampleUsers = [
    { id: '123', name: '<PERSON> Provider', role: 'provider' },
    { id: '456', name: '<PERSON>er', role: 'customer' },
    { id: '789', name: '<PERSON> Contractor', role: 'provider' },
  ];

  /**
   * Generate message link with auto-select functionality
   */
  const generateMessageLink = (userId: string, userRole: 'admin' | 'provider' | 'customer') => {
    return `/${userRole}/messages?userId=${userId}`;
  };

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <MessageSquare className="h-5 w-5" />
            Auto-Select Chat Feature
          </CardTitle>
          <CardDescription>
            Demonstration of automatic chat selection when navigating to message pages with user ID parameters.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Feature Description */}
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <h3 className="font-semibold text-blue-900 mb-2">How it works:</h3>
            <ul className="text-sm text-blue-800 space-y-1">
              <li>• Navigate to message pages with <code className="bg-blue-100 px-1 rounded">?userId=123</code> parameter</li>
              <li>• The system automatically finds and opens the chat with that user</li>
              <li>• Works across all message pages: /admin/messages, /customer/messages, /provider/messages</li>
              <li>• URL parameter is cleaned up after selection for better UX</li>
            </ul>
          </div>

          {/* URL Examples */}
          <div>
            <h3 className="font-semibold mb-3">URL Examples:</h3>
            <div className="space-y-2 font-mono text-sm bg-gray-50 p-4 rounded-lg">
              <div>/admin/messages?userId=123</div>
              <div>/customer/messages?userId=456</div>
              <div>/provider/messages?userId=789</div>
            </div>
          </div>

          {/* Interactive Examples */}
          <div>
            <h3 className="font-semibold mb-3">Try it out:</h3>
            <div className="grid gap-4 md:grid-cols-3">
              {exampleUsers.map((user) => (
                <Card key={user.id} className="border-2 hover:border-primary/50 transition-colors">
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between mb-3">
                      <div>
                        <h4 className="font-medium">{user.name}</h4>
                        <Badge variant={user.role === 'provider' ? 'success' : 'business'} className="text-xs">
                          {user.role}
                        </Badge>
                      </div>
                      <Users className="h-4 w-4 text-muted-foreground" />
                    </div>
                    
                    <div className="space-y-2">
                      {/* Admin Message Link */}
                      <Button
                        variant="outline"
                        size="sm"
                        className="w-full justify-between"
                        onClick={() => {
                          const link = generateMessageLink(user.id, 'admin');
                          window.open(link, '_blank');
                        }}
                      >
                        Admin Chat
                        <ExternalLink className="h-3 w-3" />
                      </Button>
                      
                      {/* Customer Message Link */}
                      <Button
                        variant="outline"
                        size="sm"
                        className="w-full justify-between"
                        onClick={() => {
                          const link = generateMessageLink(user.id, 'customer');
                          window.open(link, '_blank');
                        }}
                      >
                        Customer Chat
                        <ExternalLink className="h-3 w-3" />
                      </Button>
                      
                      {/* Provider Message Link */}
                      <Button
                        variant="outline"
                        size="sm"
                        className="w-full justify-between"
                        onClick={() => {
                          const link = generateMessageLink(user.id, 'provider');
                          window.open(link, '_blank');
                        }}
                      >
                        Provider Chat
                        <ExternalLink className="h-3 w-3" />
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>

          {/* Implementation Notes */}
          <div className="bg-green-50 border border-green-200 rounded-lg p-4">
            <h3 className="font-semibold text-green-900 mb-2">Implementation Notes:</h3>
            <ul className="text-sm text-green-800 space-y-1">
              <li>• The <code className="bg-green-100 px-1 rounded">useAutoSelectChat</code> hook handles all the logic</li>
              <li>• Works with existing ChatContext and UniversalChat components</li>
              <li>• Handles edge cases like user not found or multiple chats</li>
              <li>• Provides manual selection methods for programmatic use</li>
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default AutoSelectChatExample;
