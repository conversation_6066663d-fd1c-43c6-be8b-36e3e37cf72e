import React, { useEffect } from 'react';
import { useParams, useSearchParams, useNavigate } from 'react-router-dom';
import { useGeolocation } from '@/hooks/use-geolocation';
import ProfessionalsPage from './ProfessionalsPage';

interface ServiceConfig {
  serviceName: string;
  pageTitle: string;
  pageDescription: string;
  gradientBackground: string;
  rotatingWords?: string[];
}

const serviceConfigs: Record<string, ServiceConfig> = {
  // General professionals (empty serviceId)
  '': {
    serviceName: 'professionals',
    pageTitle: 'Find Trusted Service Professionals Near You | Compare Free Quotes',
    pageDescription: 'Easily find licensed and vetted professionals for plumbing, cleaning, HVAC, electrical, landscaping, and more. Compare bids, read real reviews, and connect with pros near you on JobON.',
    gradientBackground: 'linear-gradient(to bottom, #eff6ff 0%, #ffffff 100%)'
  },
  hvac: {
    serviceName: 'HVAC Technicians',
    pageTitle: 'Expert HVAC Technicians Near You | Heating & Cooling Pros',
    pageDescription: 'Keep your home comfortable year-round. Find trusted HVAC pros, compare quotes, and get quick service for repair, installation, and maintenance.',
    gradientBackground: 'linear-gradient(to bottom, #fafafa 0%, #ffffff 100%)'
  },
  plumbing: {
    serviceName: 'Plumbers',
    pageTitle: 'Top-Rated Plumbers Near You | Compare Local Plumbing Bids',
    pageDescription: 'Find licensed plumbers near you for repairs, installations, or emergencies. Compare bids, check reviews, and book trusted professionals through JobON today.',
    gradientBackground: 'linear-gradient(to bottom, #f0f9ff 0%, #ffffff 100%)'
  },
  electrical: {
    serviceName: 'Electricians',
    pageTitle: 'Licensed Electricians Near You | Fast & Reliable Electrical Services',
    pageDescription: 'Connect with skilled electricians for home repairs, installations, upgrades, and emergency electrical services in your area.',
    gradientBackground: 'linear-gradient(to bottom, #f5f3ff 0%, #ffffff 100%)'
  },
  cleaning: {
    serviceName: 'Cleaning Professionals',
    pageTitle: 'Top-Rated Cleaning Services Near You | Book Professional Cleaners',
    pageDescription: 'Find reliable and thorough cleaning professionals for your home or business. Compare services, read reviews, and hire the best cleaners in your area.',
    gradientBackground: 'linear-gradient(to bottom, #ecfdf5 0%, #ffffff 100%)'
  },
  handyman: {
    serviceName: 'Handymen',
    pageTitle: 'Skilled Handymen Near You | Fix, Repair, Install & Maintain',
    pageDescription: 'Find versatile handymen for all your home repair and maintenance needs. Compare experts, check availability, and book skilled professionals today.',
    gradientBackground: 'linear-gradient(to bottom, #f8fafc 0%, #ffffff 100%)'
  },
  landscaping: {
    serviceName: 'Landscapers',
    pageTitle: 'Professional Landscapers Near You | Transform Your Outdoor Space',
    pageDescription: 'Connect with expert landscapers to design, maintain, and enhance your property. Browse portfolios, get quotes, and hire the right professionals for your project.',
    gradientBackground: 'linear-gradient(to bottom, #f0fdf4 0%, #ffffff 100%)'
  },
  'pest-control': {
    serviceName: 'Pest Control Experts',
    pageTitle: 'Pest Control Professionals Near You | Effective & Safe Solutions',
    pageDescription: 'Eliminate unwanted pests with professional pest control services. Find experts who provide safe, effective treatments for your home or business.',
    gradientBackground: 'linear-gradient(to bottom, #fffbeb 0%, #ffffff 100%)'
  },
  'appliance-repair': {
    serviceName: 'Appliance Repair Technicians',
    pageTitle: 'Appliance Repair Experts Near You | Fast & Reliable Service',
    pageDescription: 'Get quick service for all your household appliance repairs. Find certified technicians who can fix refrigerators, washing machines, ovens, and more.',
    gradientBackground: 'linear-gradient(to bottom, #f1f5f9 0%, #ffffff 100%)'
  },
  roofing: {
    serviceName: 'Roofers',
    pageTitle: 'Professional Roofers Near You | Repair, Replace & Install',
    pageDescription: 'Find experienced roofing contractors for repairs, replacements, and installations. Compare quotes from qualified professionals in your area.',
    gradientBackground: 'linear-gradient(to bottom, #fef2f2 0%, #ffffff 100%)'
  },
  solar: {
    serviceName: 'Solar Installers',
    pageTitle: 'Solar Installation Experts Near You | Clean Energy Solutions',
    pageDescription: 'Connect with certified solar professionals to install, maintain, and optimize your solar energy system. Get clean energy solutions for your home or business.',
    gradientBackground: 'linear-gradient(to bottom, #fffbeb 0%, #ffffff 100%)'
  }
};

const UnifiedProfessionalsPage = () => {
  const { serviceId } = useParams<{ serviceId?: string }>();
  const { zipCode: detectedZipCode, loading } = useGeolocation();
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  
  // Use empty string for general professionals page
  const currentServiceId = serviceId || '';
  
  // Auto-apply detected zipcode if not present in URL
  useEffect(() => {
    if (detectedZipCode && !loading && !searchParams.has('zip')) {
      const newSearchParams = new URLSearchParams(searchParams);
      // newSearchParams.set('zip', detectedZipCode);
      navigate(`?${newSearchParams.toString()}`, { replace: true });
    }
  }, [detectedZipCode, loading, searchParams, navigate]);

  // Get configuration for current service (or general professionals)
  const config = serviceConfigs[currentServiceId];
  
  if (!config) {
    // Redirect to 404 for unknown services
    navigate('/not-found', { replace: true });
    return null;
  }

  return (
    <ProfessionalsPage
      serviceId={currentServiceId}
      serviceName={config.serviceName}
      pageTitle={config.pageTitle}
      pageDescription={config.pageDescription}
      gradientBackground={config.gradientBackground}
      rotatingWords={config.rotatingWords}
    />
  );
};

export default UnifiedProfessionalsPage;