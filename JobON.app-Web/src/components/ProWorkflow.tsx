
import React, { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { BriefcaseIcon, CheckCircle, UserPlus, FileText, MessageSquare, Search, ArrowRight } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

type Step = {
  id: number;
  title: string;
  description: string;
  icon: React.ReactNode;
  action: string;
  route: string;
};

export const ProWorkflow = ({ onComplete }: { onComplete?: () => void }) => {
  const [currentStep, setCurrentStep] = useState(0);
  const { toast } = useToast();
  const navigate = useNavigate();

  // Define the workflow steps
  const steps: Step[] = [
    {
      id: 1,
      title: "Complete Your Profile",
      description: "Add your skills, experience, and upload any certifications to stand out to potential clients.",
      icon: <UserPlus className="h-8 w-8 text-primary" />,
      action: "Complete Profile",
      route: "/profile"
    },
    {
      id: 2,
      title: "Browse Available Jobs",
      description: "Explore jobs in your area that match your skills and expertise.",
      icon: <Search className="h-8 w-8 text-primary" />,
      action: "Find Jobs",
      route: "/jobs"
    },
    {
      id: 3,
      title: "Make Your First Offer",
      description: "Select a job and submit a competitive offer. Remember, your bid will be reviewed within 24-48 hours.",
      icon: <FileText className="h-8 w-8 text-primary" />,
      action: "Make an Offer",
      route: "/jobs"
    },
    {
      id: 4,
      title: "Connect With Clients",
      description: "Once your offer is accepted, communicate directly with clients through our secure messaging system.",
      icon: <MessageSquare className="h-8 w-8 text-primary" />,
      action: "Learn More",
      route: "/how-it-works"
    }
  ];

  const handleComplete = () => {
    // Mark the workflow as completed in localStorage
    localStorage.setItem('proWorkflowCompleted', 'true');
    
    toast({
      title: "You're all set!",
      description: "You now know the basics of finding and bidding on jobs. Good luck!",
    });
    
    if (onComplete) {
      onComplete();
    }
  };

  const handleNext = () => {
    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1);
    } else {
      handleComplete();
    }
  };

  const handleGoTo = (route: string) => {
    navigate(route);
  };

  return (
    <div className="container max-w-4xl mx-auto px-4 py-8">
      <Card className="border-2 border-primary/10 shadow-lg">
        <CardHeader className="text-center">
          <div className="flex items-center justify-center mb-4">
            <BriefcaseIcon className="h-10 w-10 text-primary" />
          </div>
          <CardTitle className="text-2xl">Professional Workflow Guide</CardTitle>
          <CardDescription>Follow these steps to start earning with JobON</CardDescription>
        </CardHeader>
        <CardContent>
          {/* Progress tracker */}
          <div className="flex items-center justify-between mb-8 px-4">
            {steps.map((step, index) => (
              <React.Fragment key={step.id}>
                <div 
                  className={`flex flex-col items-center ${
                    index <= currentStep ? 'text-primary' : 'text-muted-foreground'
                  }`}
                >
                  <div className={`w-8 h-8 rounded-full flex items-center justify-center border-2 ${
                    index < currentStep 
                      ? 'bg-primary border-primary text-white' 
                      : index === currentStep 
                        ? 'border-primary text-primary' 
                        : 'border-muted-foreground text-muted-foreground'
                  }`}>
                    {index < currentStep ? <CheckCircle className="h-4 w-4" /> : (index + 1)}
                  </div>
                  <span className="text-xs mt-1 hidden md:block">{`Step ${index + 1}`}</span>
                </div>
                {index < steps.length - 1 && (
                  <div className={`flex-1 h-1 mx-2 ${
                    index < currentStep ? 'bg-primary' : 'bg-muted'
                  }`} />
                )}
              </React.Fragment>
            ))}
          </div>

          {/* Current step details */}
          <div className="text-center px-4 md:px-12 py-6 mb-6">
            <div className="flex justify-center mb-6">
              {steps[currentStep].icon}
            </div>
            <h3 className="text-xl font-bold mb-3">{steps[currentStep].title}</h3>
            <p className="text-muted-foreground">{steps[currentStep].description}</p>
          </div>

          <Separator className="my-6" />

          {/* Steps summary */}
          <div className="grid gap-4">
            {steps.map((step, index) => (
              <div 
                key={step.id} 
                className={`flex items-center p-3 rounded-lg ${
                  index === currentStep ? 'bg-primary/10 border border-primary/20' : ''
                }`}
              >
                <div className={`w-8 h-8 rounded-full flex items-center justify-center mr-4 ${
                  index < currentStep 
                    ? 'bg-primary text-white' 
                    : index === currentStep 
                      ? 'border-2 border-primary text-primary' 
                      : 'border-2 border-muted-foreground text-muted-foreground'
                }`}>
                  {index < currentStep ? <CheckCircle className="h-4 w-4" /> : (index + 1)}
                </div>
                <div className="flex-1">
                  <h4 className="font-medium">{step.title}</h4>
                </div>
                {index === currentStep && (
                  <Button 
                    variant="outline" 
                    size="sm" 
                    className="ml-2"
                    onClick={() => handleGoTo(step.route)}
                  >
                    {step.action}
                  </Button>
                )}
              </div>
            ))}
          </div>
        </CardContent>
        <CardFooter className="flex justify-between border-t px-6 py-4">
          <Button 
            variant="outline"
            onClick={() => setCurrentStep(Math.max(0, currentStep - 1))}
            disabled={currentStep === 0}
          >
            Back
          </Button>
          <Button onClick={handleNext}>
            {currentStep < steps.length - 1 ? (
              <>
                Next Step <ArrowRight className="ml-2 h-4 w-4" />
              </>
            ) : (
              'Complete Guide'
            )}
          </Button>
        </CardFooter>
      </Card>
    </div>
  );
};
