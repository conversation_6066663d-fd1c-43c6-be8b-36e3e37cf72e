
import React from 'react';
import { Link } from 'react-router-dom';
import { FileText, ImageIcon, Calculator, BookOpen, ArrowRight } from 'lucide-react';
import { Badge } from '@/components/ui/badge';

interface ToolCardProps {
  icon: React.ReactNode;
  iconBgColor: string;
  iconColor: string;
  title: string;
  description: string;
  linkText: string;
  linkUrl: string;
  badgeColor: string;
  badgeTextColor: string;
  badgeText?: string;
}

const ToolCard: React.FC<ToolCardProps> = ({
  icon,
  iconBgColor,
  iconColor,
  title,
  description,
  linkText,
  linkUrl,
  badgeColor,
  badgeTextColor,
  badgeText,
}) => {
  return (
    <div className="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 shadow-sm hover:shadow-md transition-all overflow-hidden">
      <div className="p-6">
        <div className="flex justify-between items-start mb-6">
          <div className={`flex items-center justify-center w-12 h-12 rounded-xl ${iconBgColor}`}>
            {React.cloneElement(icon as React.ReactElement, { 
              className: `h-6 w-6 ${iconColor}` 
            })}
          </div>
          {badgeText && (
            <Badge 
              variant="outline" 
              className={`${badgeColor} ${badgeTextColor} border-none text-sm`}
            >
              {badgeText}
            </Badge>
          )}
        </div>
        
        <h3 className="font-semibold text-xl mb-2 text-gray-900 dark:text-gray-100">{title}</h3>
        <p className="text-base text-gray-600 dark:text-gray-300 mb-6">{description}</p>
        
        <Link 
          to={linkUrl}
          className="text-primary hover:text-primary/80 text-base font-medium flex items-center"
        >
          {linkText}
          <ArrowRight className="w-5 h-5 ml-2" />
        </Link>
      </div>
    </div>
  );
};

export const RecentTools: React.FC = () => {
  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
      <ToolCard 
        icon={<FileText />}
        iconBgColor="bg-amber-100 dark:bg-amber-900/30"
        iconColor="text-amber-600 dark:text-amber-400"
        title="Work Order Template"
        description="Professional work orders for your service jobs"
        linkText="Use template"
        linkUrl="/free-tools/work-order-template"
        badgeColor="bg-amber-50 dark:bg-amber-900/30"
        badgeTextColor="text-amber-600 dark:text-amber-400"
        badgeText="New"
      />
      
      <ToolCard 
        icon={<ImageIcon />}
        iconBgColor="bg-blue-100 dark:bg-blue-900/30"
        iconColor="text-blue-600 dark:text-blue-400"
        title="Before/After Editor"
        description="Showcase your work with before/after images"
        linkText="Use editor"
        linkUrl="/free-tools/image-editor"
        badgeColor="bg-blue-50 dark:bg-blue-900/30"
        badgeTextColor="text-blue-600 dark:text-blue-400"
        badgeText="New"
      />
      
      <ToolCard 
        icon={<Calculator />}
        iconBgColor="bg-green-100 dark:bg-green-900/30"
        iconColor="text-green-600 dark:text-green-400"
        title="Profit Margin Calculator"
        description="Calculate your real profit on every job"
        linkText="Use calculator"
        linkUrl="/free-tools/profit-calculator"
        badgeColor="bg-green-50 dark:bg-green-900/30"
        badgeTextColor="text-green-600 dark:text-green-400"
        badgeText="New"
      />
      
      <ToolCard 
        icon={<BookOpen />}
        iconBgColor="bg-purple-100 dark:bg-purple-900/30"
        iconColor="text-purple-600 dark:text-purple-400"
        title="Marketing Guide"
        description="Simple strategies to grow your service business"
        linkText="Read guide"
        linkUrl="/free-tools/marketing-guide"
        badgeColor="bg-purple-50 dark:bg-purple-900/30"
        badgeTextColor="text-purple-600 dark:text-purple-400"
        badgeText="New"
      />
    </div>
  );
};
