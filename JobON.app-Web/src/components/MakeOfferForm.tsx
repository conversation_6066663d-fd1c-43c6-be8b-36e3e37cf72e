
import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Textarea } from '@/components/ui/textarea';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { AlertCircle, SendIcon } from 'lucide-react';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { useToast } from '@/hooks/use-toast';

const offerSchema = z.object({
  price: z
    .string()
    .refine((val) => !isNaN(Number(val)), {
      message: "Price must be a number",
    })
    .refine((val) => Number(val) > 0, {
      message: "Price must be greater than 0",
    }),
  message: z
    .string()
    .min(30, { message: "Message must be at least 30 characters" })
    .max(500, { message: "Message must not exceed 500 characters" }),
  availableDate: z.string().min(1, { message: "Please select an available date" }),
});

type OfferFormValues = z.infer<typeof offerSchema>;

interface MakeOfferFormProps {
  jobId: string;
  jobTitle: string;
  originalPrice: number;
  onSubmitSuccess?: () => void;
}

export const MakeOfferForm = ({ jobId, jobTitle, originalPrice, onSubmitSuccess }: MakeOfferFormProps) => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [verificationRequired, setVerificationRequired] = useState(false);
  const { toast } = useToast();
  
  const form = useForm<OfferFormValues>({
    resolver: zodResolver(offerSchema),
    defaultValues: {
      price: originalPrice.toString(),
      message: "",
      availableDate: "",
    },
  });

  const onSubmit = (values: OfferFormValues) => {
    setIsSubmitting(true);
    
    // Check if the user account is verified (in a real app, this would come from your backend)
    const isUserVerified = localStorage.getItem('isVerified') === 'true';
    
    if (!isUserVerified) {
      setVerificationRequired(true);
      setIsSubmitting(false);
      return;
    }
    
    // Simulate sending the offer to the backend
    setTimeout(() => {
      setIsSubmitting(false);
      
      toast({
        title: "Offer submitted successfully!",
        description: "The client will be notified of your offer.",
      });
      
      if (onSubmitSuccess) {
        onSubmitSuccess();
      }
    }, 1500);
  };

  const handleVerificationRequest = () => {
    toast({
      title: "Verification requested",
      description: "Your account verification is now in progress. This typically takes 24-48 hours.",
    });
    
    // In a real app, this would call your backend to initiate verification
    localStorage.setItem('verificationRequested', 'true');
    
    setVerificationRequired(false);
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>Make an Offer</CardTitle>
        <CardDescription>Submit your proposal for "{jobTitle}"</CardDescription>
      </CardHeader>
      <CardContent>
        {verificationRequired ? (
          <div className="space-y-4">
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertTitle>Verification Required</AlertTitle>
              <AlertDescription>
                Your professional account needs to be verified before you can submit offers.
                Verification typically takes 24-48 hours and helps ensure quality service for our clients.
              </AlertDescription>
            </Alert>
            <Button 
              onClick={handleVerificationRequest} 
              className="w-full"
            >
              Request Verification
            </Button>
          </div>
        ) : (
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              <FormField
                control={form.control}
                name="price"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Your Price ($)</FormLabel>
                    <FormControl>
                      <Input type="number" {...field} />
                    </FormControl>
                    <FormDescription>
                      Offer a competitive price. The client's budget is ${originalPrice}.
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <FormField
                control={form.control}
                name="availableDate"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>When can you start?</FormLabel>
                    <FormControl>
                      <Input type="date" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <FormField
                control={form.control}
                name="message"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Message to Client</FormLabel>
                    <FormControl>
                      <Textarea 
                        placeholder="Introduce yourself and explain why you're a good fit for this job..."
                        className="min-h-[120px]"
                        {...field}
                      />
                    </FormControl>
                    <FormDescription>
                      Highlight your experience, skills, and approach to the job.
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <Button type="submit" className="w-full" disabled={isSubmitting}>
                {isSubmitting ? (
                  "Submitting Offer..."
                ) : (
                  <>
                    <SendIcon className="mr-2 h-4 w-4" />
                    Submit Offer
                  </>
                )}
              </Button>
            </form>
          </Form>
        )}
      </CardContent>
    </Card>
  );
};
