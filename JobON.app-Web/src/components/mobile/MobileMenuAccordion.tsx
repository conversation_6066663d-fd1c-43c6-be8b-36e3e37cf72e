
import React from 'react';
import { 
  Grid3x3, TreePine, Users, Search, Wrench, Calendar,
  Info, CreditCard, BookOpen, LifeBuoy, Package, Settings
} from 'lucide-react';
import { 
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger
} from '@/components/ui/accordion';
import { NavigationMenuItem } from '../NavigationMenuItem';

interface MobileMenuAccordionProps {
  onItemClick: () => void;
}

export const MobileMenuAccordion: React.FC<MobileMenuAccordionProps> = ({ onItemClick }) => {
  return (
    <Accordion type="multiple" className="space-y-2 w-full">
      {/* Services Section */}
      <AccordionItem value="services" className="border rounded-lg overflow-hidden px-0">
        <AccordionTrigger className="px-4 py-3 hover:bg-accent/50 hover:no-underline">
          <div className="flex items-center gap-3">
            <div className="section-icon">
              <Grid3x3 className="h-5 w-5 text-blue-500" />
            </div>
            <span className="font-medium">Services</span>
          </div>
        </AccordionTrigger>
        <AccordionContent className="px-1 py-2 space-y-1">
          <NavigationMenuItem 
            to="/services/landscaping"
            icon={<TreePine className="h-4 w-4 text-green-500" />}
            label="Landscaping"
            onClick={onItemClick}
            badge="Popular"
          />

          <NavigationMenuItem 
            to="/professionals/landscaping"
            icon={<Users className="h-4 w-4 text-blue-500" />}
            label="Find Landscapers"
            onClick={onItemClick}
            isNested
          />
        </AccordionContent>
      </AccordionItem>

      {/* Providers Section */}
      <AccordionItem value="providers" className="border rounded-lg overflow-hidden px-0">
        <AccordionTrigger className="px-4 py-3 hover:bg-accent/50 hover:no-underline">
          <div className="flex items-center gap-3">
            <div className="section-icon">
              <Users className="h-5 w-5 text-purple-500" />
            </div>
            <span className="font-medium">For Providers</span>
          </div>
        </AccordionTrigger>
        <AccordionContent className="px-1 py-2 space-y-1">
          <NavigationMenuItem 
            to="/for-providers"
            icon={<Package className="h-4 w-4 text-purple-500" />}
            label="Provider Solutions"
            onClick={onItemClick}
          />

          <NavigationMenuItem 
            to="/jobs"
            icon={<Search className="h-4 w-4 text-blue-500" />}
            label="Find Jobs"
            onClick={onItemClick}
            badge="New"
          />

          <NavigationMenuItem 
            to="/free-tools"
            icon={<Wrench className="h-4 w-4 text-amber-500" />}
            label="Free Business Tools"
            onClick={onItemClick}
          />

          <NavigationMenuItem 
            to="/for-providers"
            icon={<Calendar className="h-4 w-4 text-green-500" />}
            label="Join Us"
            onClick={onItemClick}
          />
        </AccordionContent>
      </AccordionItem>

      {/* Resources Section */}
      <AccordionItem value="resources" className="border rounded-lg overflow-hidden px-0">
        <AccordionTrigger className="px-4 py-3 hover:bg-accent/50 hover:no-underline">
          <div className="flex items-center gap-3">
            <div className="section-icon">
              <LifeBuoy className="h-5 w-5 text-cyan-500" />
            </div>
            <span className="font-medium">Resources</span>
          </div>
        </AccordionTrigger>
        <AccordionContent className="px-1 py-2 space-y-1">
          <NavigationMenuItem 
            to="/how-it-works"
            icon={<Info className="h-4 w-4 text-blue-500" />}
            label="How It Works"
            onClick={onItemClick}
          />

          <NavigationMenuItem 
            to="/financing"
            icon={<CreditCard className="h-4 w-4 text-green-500" />}
            label="Financing"
            onClick={onItemClick}
          />

          <NavigationMenuItem 
            to="/blog"
            icon={<BookOpen className="h-4 w-4 text-amber-500" />}
            label="Blog"
            onClick={onItemClick}
          />
        </AccordionContent>
      </AccordionItem>
    </Accordion>
  );
};
