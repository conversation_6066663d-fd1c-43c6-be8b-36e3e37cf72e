
import React from "react";
import { ImageUploader } from "@/components/ImageUploader";
import { Button } from "@/components/ui/button";
import { ChevronLeft, ArrowRight, Upload, X } from "lucide-react";

interface MobilePhotoUploadStepProps {
  photoURLs: string[];
  onPhotoChange: (images: string[], files?: File[]) => void;
  onNext: () => void;
  onBack: () => void;
}

export const MobilePhotoUploadStep: React.FC<MobilePhotoUploadStepProps> = ({
  photoURLs,
  onPhotoChange,
  onNext,
  onBack,
}) => {
  return (
    <div className="space-y-6 pb-28">
      <div className="text-center mb-4">
        <h2 className="text-xl font-semibold">Upload Photos or Video (optional)</h2>
        <p className="text-gray-500 mt-2">
          Photos help service providers understand your needs better
        </p>
      </div>
      <ImageUploader
        onImagesSelected={onPhotoChange}
        currentImages={photoURLs}
        maxImages={10}
        multiple={true}
      />
      {/* Fixed footer with Back and Continue/Skip buttons (mobile) */}
      <div className=" bottom-0 left-0 w-full bg-white dark:bg-gray-900 border-t border-gray-200 dark:border-gray-800 p-4 z-10">
        <div className="flex flex-col max-w-md mx-auto">
          {photoURLs.length === 0 && (
            <p className="text-red-500 text-sm mb-2 text-center">Please upload at least one photo</p>
          )}
          <div className="flex justify-between items-center">
            <Button className={"h-12"} variant="outline" onClick={onBack}>
              <ChevronLeft className="h-4 w-4 mr-2" />
              Back
            </Button>
            <Button
              onClick={onNext}
              className="w-fit text-[14px]"
              variant="default"
              size="mobile"
              disabled={photoURLs.length === 0}
            >
              {photoURLs.length > 0 ? 'Continue' : 'Skip This Step'}
              <ArrowRight className="h-4 w-4 ml-2" />
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};
