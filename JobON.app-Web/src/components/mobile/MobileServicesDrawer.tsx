
import React from 'react';
import { Link } from 'react-router-dom';
import { DrawerContent } from '@/components/ui/drawer';
import { categories } from '../CategoryList';
import { X } from 'lucide-react';
import { Button } from '@/components/ui/button';

interface MobileServicesDrawerProps {
  onClose?: () => void;
}

export const MobileServicesDrawer: React.FC<MobileServicesDrawerProps> = ({ onClose }) => {
  return (
    <div className="max-h-[85vh] px-2 py-6 focus:outline-none">
      <div className="px-4 pb-2 mb-2 border-b flex justify-between items-center">
        <h3 className="text-xl font-bold">Services</h3>
        {onClose && (
          <Button variant="ghost" size="sm" onClick={onClose} className="h-8 w-8 p-0">
            <X className="h-5 w-5" />
          </Button>
        )}
      </div>
      <div className="grid grid-cols-2 gap-3 px-2 pb-16 max-h-[65vh] overflow-y-auto">
        {categories.map((category) => {
          const categoryId = category.id;
          return (
            <Link
              key={categoryId}
              to={`/services/${categoryId}`}
              onClick={onClose}
              className={`flex flex-col items-center p-3 rounded-lg border ${categoryId === 'cleaning' ? 'bg-primary/10 border-primary/30' : category.borderColor} ${category.color} transition-colors`}
            >
              <div className={`w-12 h-12 rounded-full flex items-center justify-center mb-2 ${categoryId === 'cleaning' ? 'bg-primary/20' : category.iconBgColor || 'bg-primary/10'}`}>
                {React.cloneElement(category.icon as React.ReactElement, { 
                  className: `h-6 w-6 ${category.iconColor || 'text-primary'}`,
                  strokeWidth: 1.5 
                })}
              </div>
              <span className="text-sm font-medium text-center">{category.title}</span>
            </Link>
          );
        })}
      </div>
    </div>
  );
};
