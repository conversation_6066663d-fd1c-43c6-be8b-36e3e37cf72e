
import React from 'react';
import { Heart, MapPin, Star, MessageSquare } from 'lucide-react';
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarImage, AvatarFallback } from "@/components/ui/avatar";
import { ProviderVerifiedBadge } from "@/components/provider/ProviderVerifiedBadge";
import { cn } from "@/lib/utils";
import { DataType } from "@/types/common";

interface MobileProviderCardProps {
  provider: DataType;
  onViewProfile: () => void;
  onMessage?: () => void;
  className?: string;
}

export function MobileProviderCard({
  provider,
  onViewProfile,
  onMessage,
  className
}: MobileProviderCardProps) {
  const [isFavorite, setIsFavorite] = React.useState(false);

  const calculateAverageRating = (reviews?: Array<{ rating: string }>): number => {
    if (!reviews || reviews.length === 0) return 0;
    const sum = reviews.reduce((total, review) => {
      const rating = review?.rating ? parseInt(review.rating) : 0;
      return total + rating;
    }, 0);
    return parseFloat((sum / reviews.length).toFixed(1));
  };

  // Determine provider tier based on rating and jobs completed
  const getProviderTier = (): 'elite' | 'pro' | null => {
    const averageRating = calculateAverageRating(provider?.reviews);
    const jobsCompleted = provider?.reviews?.length || 0;
    
    if (averageRating >= 4.8 && jobsCompleted >= 100) return 'elite';
    if (averageRating >= 4.5 && jobsCompleted >= 50) return 'pro';
    return null;
  };

  const jobsCompleted = provider?.reviews?.length || 234; // Fallback for demo
  const averageRating = calculateAverageRating(provider?.reviews);
  const reviewCount = provider?.reviews?.length || 0;
  const tier = getProviderTier();

  const handleMessage = () => {
    if (onMessage) {
      onMessage();
    } else {
      console.log('Message functionality not implemented');
    }
  };

  return (
    <div
      className={cn(
        "relative w-full",
        "border border-gray-200 dark:border-gray-700 rounded-xl",
        "bg-white dark:bg-gray-800 p-4 shadow-sm",
        "font-sans",
        className
      )}
    >
      <div className="flex gap-4">
        {/* Left - Enhanced Avatar */}
        <div className="flex-shrink-0 relative">
          <Avatar className="w-20 h-20 rounded-xl shadow-md border-2 border-gray-100 dark:border-gray-700">
            <AvatarImage
              src={provider?.photos?.[0] || "/images/avatar-default.svg"}
              alt={provider?.name}
              className="object-cover"
            />
            <AvatarFallback className="text-lg font-semibold bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300">
              {provider?.name?.[0]}
            </AvatarFallback>
          </Avatar>
          
          {/* Tier Badge Overlay */}
          {tier && (
            <div className="absolute -top-1 -right-1">
              <ProviderVerifiedBadge plan={tier} size="sm" />
            </div>
          )}
        </div>

        {/* Right - Content */}
        <div className="flex-1 min-w-0 flex flex-col">
          <div className="flex items-start justify-between mb-2">
            <div className="flex-1 min-w-0 pr-2">
              <div className="flex items-center gap-2 mb-1 min-w-0">
                <h3 className="font-bold text-lg text-gray-900 dark:text-white truncate flex-1 min-w-0">
                  {provider?.name}
                </h3>
                {tier && (
                  <Badge 
                    variant={tier === 'elite' ? 'warning' : 'business'}
                    className="text-xs font-medium px-2 py-0.5 flex-shrink-0"
                  >
                    {tier === 'elite' ? 'Elite' : 'Pro'}
                  </Badge>
                )}
              </div>
            </div>
            
            <button 
              onClick={() => setIsFavorite(!isFavorite)}
              className="text-red-500 dark:text-red-400 active:text-red-600 dark:active:text-red-500 transition-all active:scale-95 min-h-[32px] min-w-[32px] flex items-center justify-center flex-shrink-0"
            >
              <Heart 
                className={cn("h-5 w-5", isFavorite && "fill-current text-red-500 dark:text-red-400")} 
              />
            </button>
          </div>

          <div className="flex items-center mb-2">
            <Star className="h-4 w-4 text-amber-400 fill-current" />
            <span className="ml-1 font-semibold text-sm text-gray-900 dark:text-white">
              {averageRating}
            </span>
            <span className="mx-1 text-gray-400 dark:text-gray-500">·</span>
            <span className="text-xs text-gray-600 dark:text-gray-400">
              {reviewCount} reviews
            </span>
          </div>

          <div className="text-xs text-gray-500 dark:text-gray-400 mb-2">
            {jobsCompleted} jobs completed
          </div>

          <div className="text-xs text-gray-600 dark:text-gray-400 mb-3 truncate">
            {provider?.fullAddress || provider?.address}
          </div>
        </div>
      </div>
        {/* Dual Action Buttons */}
        <div className="mt-auto">
            <div className="flex gap-2">
                <Button
                    onClick={handleMessage}
                    variant="outline"
                    type="button"
                    className="flex-1 border-[#2263eb] dark:border-[#3b82f6] text-[#2263eb] dark:text-[#3b82f6] hover:bg-[#2263eb]/10 dark:hover:bg-[#3b82f6]/10 active:bg-[#2263eb]/20 dark:active:bg-[#3b82f6]/20 font-medium h-9 text-xs px-2 bg-white dark:bg-gray-800"
                >
                    <MessageSquare className="h-3 w-3 mr-1" />
                    Message
                </Button>

                <Button
                    onClick={onViewProfile}
                    type="button"
                    className="flex-1 bg-[#2263eb] dark:bg-[#3b82f6] active:bg-[#2263eb]/80 dark:active:bg-[#3b82f6]/80 md:hover:bg-[#2263eb]/90 dark:md:hover:bg-[#3b82f6]/90 text-white font-medium h-9 text-xs px-2"
                >
                    View Profile
                </Button>
            </div>
        </div>
    </div>
  );
}
