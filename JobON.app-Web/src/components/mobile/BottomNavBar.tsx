
import React from 'react';
import { NavLink, Link, useNavigate } from 'react-router-dom';
import { Home, Search, PlusCircle, Heart, User } from 'lucide-react';
import { setPreviousPage } from '@/utils/navigationUtils';
import { useAuth } from '@/features/auth/hooks/useAuth';

interface BottomNavBarProps {
  onExploreClick?: () => void;
}

export const BottomNavBar: React.FC<BottomNavBarProps> = ({ onExploreClick }) => {
  const { isAuthenticated } = useAuth();
  const navigate = useNavigate();
  
  const handleProfileClick = (event: React.MouseEvent) => {
    event.preventDefault();
    if (isAuthenticated) {
      navigate('/profile');
    } else {
      setPreviousPage(); // Store current page for redirection after login
      navigate('/auth');
    }
  };
  
  return (
    <div className="fixed bottom-0 left-0 right-0 bg-white dark:bg-gray-900 border-t border-gray-200 dark:border-gray-800 h-20 z-50">
      <div className="h-full max-w-lg mx-auto grid grid-cols-5">
        <NavLink
          to="/"
          className={({ isActive }) =>
            `flex flex-col items-center justify-center text-xs min-h-[48px] min-w-[48px] transition-all active:scale-95 ${
              isActive 
                ? 'text-primary font-medium' 
                : 'text-gray-500 dark:text-gray-400'
            }`
          }
        >
          <Home className="h-6 w-6 mb-1" />
          <span>Home</span>
        </NavLink>
        
        {/* Changed to button for explore to show services drawer */}
        <button
          onClick={onExploreClick}
          className="flex flex-col items-center justify-center text-xs text-gray-500 dark:text-gray-400 min-h-[48px] min-w-[48px] transition-all active:scale-95"
        >
          <Search className="h-6 w-6 mb-1" />
          <span>Explore</span>
        </button>
        
        <PostJobButton />
        
        <NavLink
          to="/saved"
          className={({ isActive }) =>
            `flex flex-col items-center justify-center text-xs min-h-[48px] min-w-[48px] transition-all active:scale-95 ${
              isActive 
                ? 'text-primary font-medium' 
                : 'text-gray-500 dark:text-gray-400'
            }`
          }
        >
          <Heart className="h-6 w-6 mb-1" />
          <span>Saved</span>
        </NavLink>
        
        <button
          onClick={handleProfileClick}
          className="flex flex-col items-center justify-center text-xs text-gray-500 dark:text-gray-400 min-h-[48px] min-w-[48px] transition-all active:scale-95"
        >
          <User className="h-6 w-6 mb-1" />
          <span>Profile</span>
        </button>
      </div>
    </div>
  );
};

const PostJobButton = () => {
  const navigate = useNavigate();
  
  const handleClick = () => {
    setPreviousPage(); // Set the previous page in the cookie
    navigate('/create-job'); // Navigate to create-job page
  };
  
  return (
    <div 
      onClick={handleClick}
      className="flex flex-col items-center justify-center relative cursor-pointer min-h-[48px] min-w-[48px] transition-all active:scale-95"
    >
      <div className="bg-primary text-white rounded-full p-3 -mt-8 shadow-lg border-4 border-white dark:border-gray-900">
        <PlusCircle className="h-6 w-6" />
      </div>
      <span className="text-xs mt-3 font-medium text-primary">Post Job</span>
    </div>
  );
};
