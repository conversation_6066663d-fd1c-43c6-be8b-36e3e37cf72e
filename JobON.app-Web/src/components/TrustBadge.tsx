
import React from 'react';
import { Users, Clock, Award } from 'lucide-react';

export const TrustBadge = () => {
  return (
    <div className="trust-badge-container bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
      <div className="trust-badge-inner flex flex-wrap items-center justify-center gap-4 md:gap-8">
        <div className="flex items-center">
          <div className="p-2 bg-blue-100 dark:bg-blue-900/30 rounded-full mr-3">
            <Users className="h-5 w-5 text-blue-600 dark:text-blue-400" />
          </div>
          <div>
            <p className="font-medium text-sm">5000+</p>
            <p className="text-xs text-gray-500 dark:text-gray-400">Verified Professionals</p>
          </div>
        </div>
        
        <div className="flex items-center">
          <div className="p-2 bg-green-100 dark:bg-green-900/30 rounded-full mr-3">
            <Award className="h-5 w-5 text-green-600 dark:text-green-400" />
          </div>
          <div>
            <p className="font-medium text-sm">90%</p>
            <p className="text-xs text-gray-500 dark:text-gray-400">Customer Satisfaction</p>
          </div>
        </div>
        
        <div className="flex items-center">
          <div className="p-2 bg-amber-100 dark:bg-amber-900/30 rounded-full mr-3">
            <Clock className="h-5 w-5 text-amber-600 dark:text-amber-400" />
          </div>
          <div>
            <p className="font-medium text-sm">Fast Response</p>
            <p className="text-xs text-gray-500 dark:text-gray-400">Within 24 hours</p>
          </div>
        </div>
      </div>
    </div>
  );
};
