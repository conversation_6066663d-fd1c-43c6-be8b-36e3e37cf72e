
import React, { useState } from 'react';
import { Di<PERSON>, Dialog<PERSON>ontent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { BusinessType } from '@/pages/Business';
import CreateBusinessForm from './CreateBusinessForm';

interface CreateBusinessModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
  business?: BusinessType;
  isEditing?: boolean;
}

const CreateBusinessModal: React.FC<CreateBusinessModalProps> = ({
  isOpen,
  onClose,
  onSuccess,
  business,
  isEditing = false
}) => {
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-hidden p-0">
        <DialogHeader className="p-6 pb-0">
          <DialogTitle className="text-xl font-semibold">
            {isEditing ? 'Edit Business' : 'Create New Business'}
          </DialogTitle>
        </DialogHeader>
        
        <div className="px-6">
          <CreateBusinessForm
            onClose={onClose}
            onSuccess={onSuccess}
            business={business}
            isEditing={isEditing}
          />
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default CreateBusinessModal;
