
import React from 'react';
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { ImageUploader } from "@/components/ImageUploader";
import { MapPin, Clock, DollarSign, ArrowRight, Image, Video } from 'lucide-react';
import { Link, useNavigate } from 'react-router-dom';
import { useAuth } from '@/features/auth/hooks/useAuth';

interface JobListingProps {
  id: string;
  title: string;
  description: string;
  budget?: number;  // Made optional with ?
  location: string;
  dueDate: string;
  images?: string[];
  videoUrl?: string;
  category: string;
  status: 'open' | 'in-progress' | 'completed';
}

const JobListing: React.FC<JobListingProps> = ({
  id,
  title,
  description,
  location,
  dueDate,
  images,
  videoUrl,
  category,
  status
}) => {
  const navigate = useNavigate();
  const { isProvider, isAuthenticated } = useAuth();

  const handlePlaceBid = () => {
    if (!isAuthenticated) {
      navigate('/auth');
      return;
    }

    if (!isProvider) {
      navigate('/provider-signup');
      return;
    }

    // Navigate to job details where they can place a bid
    navigate(`/job/${id}`);
  };
  return (
    <Card className="w-full bg-white shadow-sm hover:shadow-md transition-shadow duration-200">
      <div className="p-6">
        <div className="flex justify-between items-start mb-4">
          <div>
            <h3 className="text-xl font-semibold text-gray-900 mb-2">{title}</h3>
            <div className="flex items-center gap-4 text-sm text-gray-600 mb-4">
              <div className="flex items-center gap-1">
                <MapPin className="h-4 w-4" />
                {location}
              </div>
              <div className="flex items-center gap-1">
                <Clock className="h-4 w-4" />
                {dueDate}
              </div>
            </div>
          </div>
          <Badge variant={
            status === 'open' ? 'success' :
            status === 'in-progress' ? 'secondary' :
            'secondary'
          }>
            {status}
          </Badge>
        </div>

        <p className="text-gray-600 mb-4 line-clamp-2">{description}</p>

        {(images?.length || videoUrl) && (
          <div className="mb-4">
            <div className="flex gap-2 overflow-x-auto pb-2">
              {images?.map((image, index) => (
                <div key={index} className="relative min-w-[100px] h-[100px] rounded-lg overflow-hidden group">
                  <img 
                    src={image} 
                    alt={`Project image ${index + 1}`}
                    className="w-full h-full object-cover"
                  />
                  <div className="absolute inset-0 bg-black/40 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center">
                    <Image className="w-5 h-5 text-white" />
                  </div>
                </div>
              ))}
              {videoUrl && (
                <div className="relative min-w-[100px] h-[100px] rounded-lg overflow-hidden bg-gray-100 flex items-center justify-center group">
                  <Video className="w-6 h-6 text-gray-400 group-hover:text-gray-600 transition-colors" />
                </div>
              )}
            </div>
          </div>
        )}

        <div className="flex flex-wrap gap-2 mb-4">
          <Badge variant="outline">{category}</Badge>
        </div>

        <div className="flex justify-between items-center">
          <Button asChild variant="outline" size="sm">
            <Link to={`/job/${id}`}>
              View Details
              <ArrowRight className="ml-2 h-4 w-4" />
            </Link>
          </Button>
          <Button size="sm" onClick={handlePlaceBid}>
            Place Bid
          </Button>
        </div>
      </div>
    </Card>
  );
};

export default JobListing;
