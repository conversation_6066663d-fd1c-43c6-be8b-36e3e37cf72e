
import React from 'react';
import { Layout } from './Layout';
import { Link } from 'react-router-dom';
import { Button } from "./ui/button";
import { StarRating } from './StarRating';
import { CheckCircle, Shield, Award, Clock, BadgeCheck, Users, User, Handshake, Clipboard<PERSON>heck, MessageSquare, ThumbsUp } from 'lucide-react';
import { Separator } from "@/components/ui/separator";
import { Card, CardContent } from "./ui/card";
import { TestimonialsCarousel } from './TestimonialsCarousel';
import { ServiceEstimator } from './ServiceEstimator';
import { ServiceNeeds } from './ServiceNeeds';
import { useIsMobile } from '@/hooks/use-mobile';
import { AuroraBackground } from './ui/aurora-background';
import { SEO } from './SEO';

interface ServicePageProps {
  serviceId: string;
  title: string;
  subtitle: string;
  description: string;
  heroImage: string;
  benefits: {
    title: string;
    description: string;
    icon: React.ReactNode;
  }[];
  faqs: {
    question: string;
    answer: string;
  }[];
  commercialSpecific?: boolean;
  estimates?: {
    tier: string;
    price: string;
    description: string;
    features: string[];
    recommended?: boolean;
  }[];
  commonNeeds?: Array<{
    icon: React.ReactNode;
    label: string;
  }>;
  hideEstimator?: boolean;
  hideHero?: boolean;
  customCta?: React.ReactNode;
  showNeedsAfterHero?: boolean;
  professionalTitle?: string;
  toolPage?: boolean;
  seoTitle?: string;
  seoDescription?: string;
  serviceImages?: string[];
}

export const ServicePageTemplate: React.FC<ServicePageProps> = ({
  serviceId,
  title,
  subtitle,
  description,
  heroImage,
  benefits,
  faqs,
  commercialSpecific = false,
  estimates = [],
  commonNeeds = [],
  hideEstimator = false,
  hideHero = false,
  customCta,
  showNeedsAfterHero = false,
  professionalTitle = "Professionals",
  toolPage = false,
  seoTitle,
  seoDescription,
  serviceImages = []
}) => {
  const isMobile = useIsMobile();
  const defaultEstimates = [
    {
      tier: "Basic",
      price: "$80-120",
      description: "Essential service for simple needs",
      features: ["Licensed professionals", "90-day warranty", "Standard response time", "Basic service package"]
    },
    {
      tier: "Standard",
      price: "$150-250",
      description: "Our most popular option",
      features: ["Licensed & insured professionals", "1-year warranty", "Priority scheduling", "Comprehensive service", "Detailed inspection"]
    },
    {
      tier: "Premium",
      price: "$280-450",
      description: "Comprehensive solution for complex needs",
      features: ["Master-level professionals", "3-year extended warranty", "Emergency service available", "Complete system inspection", "Preventative maintenance", "Priority customer support"]
    }
  ];
  const serviceEstimates = estimates.length > 0 ? estimates : defaultEstimates;

  const defaultSeoDescription = `Compare bids from local ${serviceId} pros. JobON makes hiring fast, affordable, and protected—so the job gets done right, every time.`;

  // Default plumbing service images if none are provided
  const defaultImages = [
    'https://images.unsplash.com/photo-1606800052052-a08af7148866?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1770&q=80',
    'https://images.unsplash.com/photo-1575517111839-3a3843ee7c5b?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=2070&q=80',
    'https://images.unsplash.com/photo-1606800052052-a08af7148866?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1770&q=80'
  ];

  // Use provided images or default to the fallback images
  const displayImages = serviceImages.length > 0 ? serviceImages : defaultImages;

  return (
    <Layout>
      {/*<SEO */}
      {/*  title={seoTitle || `${title} With Bids – Fast, Trusted & Protected`}*/}
      {/*  description={seoDescription || defaultSeoDescription}*/}
      {/*  localBusinessSchema={true}*/}
      {/*  serviceType={title}*/}
      {/*  serviceSlug={serviceId}*/}
      {/*/>*/}

      {!hideHero && 
        <AuroraBackground className={`h-auto py-0 overflow-visible ${toolPage ? 'bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-gray-900 dark:to-gray-800' : ''}`}>
          <section className="relative pt-16 md:pt-24 pb-8 md:pb-20">
            <div className="absolute inset-0 bg-cover bg-center z-0 opacity-40" style={{
              backgroundImage: `url(${heroImage})`,
              filter: 'brightness(0.5)'
            }}></div>
            <div className="absolute inset-0 bg-gradient-to-r from-primary/50 to-transparent z-0"></div>

            <div className="container mx-auto px-4 md:px-12 relative z-10">
              <div className="flex flex-col md:flex-row items-center md:items-start">
                {isMobile && !hideEstimator && (
                  <div className="w-full mb-6">
                    <ServiceEstimator serviceId={serviceId} estimates={serviceEstimates} />
                  </div>
                )}

                <div className="w-full md:w-1/2 md:pr-16 text-white">
                  <h1 className={`text-2xl md:text-5xl lg:text-6xl font-bold text-white leading-tight ${toolPage ? 'md:text-6xl lg:text-7xl' : ''} dark:text-white dark:drop-shadow-lg`}>
                    <span className="block">Trusted {title}</span>
                    <span className="text-blue-300 bg-gradient-to-r from-blue-300 to-blue-100 bg-clip-text text-transparent">Services That Deliver</span>
                  </h1>
                  <p className={`text-lg md:text-2xl mb-3 md:mb-6 text-white/90 ${toolPage ? 'md:text-3xl' : ''} dark:text-white dark:drop-shadow-md`}>
                    {subtitle}
                  </p>
                  <p className={`text-sm md:text-lg mb-4 md:mb-8 text-white/80 max-w-lg ${toolPage ? 'md:text-xl' : ''} dark:text-gray-200 dark:drop-shadow-md`}>
                    {description}
                  </p>

                  <div className="flex flex-col sm:flex-row gap-3 mb-4 md:mb-8">
                    <Link to="/create-job">
                      <Button size={isMobile ? "default" : "lg"} className={`${isMobile ? 'text-sm py-5 font-semibold' : 'text-lg py-6'} font-medium px-4 md:px-6 h-auto rounded-xl shadow-lg hover:shadow-xl hover:translate-y-[-2px] transition-all w-full sm:w-auto ${toolPage ? 'bg-gradient-to-r from-primary to-blue-600 hover:from-primary/90 hover:to-blue-500' : ''}`}>
                        Post a Project
                      </Button>
                    </Link>
                    <Button size={isMobile ? "default" : "lg"} variant="outline" className={`${isMobile ? 'text-sm py-5 font-semibold' : 'text-lg py-6'} font-medium px-4 md:px-6 h-auto rounded-xl border-white text-white hover:bg-white/10 shadow-lg hover:shadow-xl hover:translate-y-[-2px] transition-all w-full sm:w-auto`}>
                      View Pricing
                    </Button>
                  </div>

                  <div className="flex flex-wrap items-center gap-2 md:gap-6 text-white/90">
                    <div className="flex items-center gap-1">
                      <StarRating rating={4.8} size="sm" />
                      <span className={`text-xs md:text-sm font-medium ml-1 ${toolPage ? 'md:text-base' : ''} text-white dark:text-white`}>4.8/5</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <Shield className={`w-3 h-3 md:w-4 md:h-4 ${toolPage ? 'md:w-5 md:h-5' : ''}`} />
                      <span className={`text-xs md:text-sm font-medium ${toolPage ? 'md:text-base' : ''} text-white dark:text-white`}>Licensed & Insured</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <Clock className={`w-3 h-3 md:w-4 md:h-4 ${toolPage ? 'md:w-5 md:h-5' : ''}`} />
                      <span className={`text-xs md:text-sm font-medium ${toolPage ? 'md:text-base' : ''} text-white dark:text-white`}>24/7 Service</span>
                    </div>
                  </div>
                </div>

                {!isMobile && !hideEstimator && 
                  <div className="w-full md:w-1/2 mt-6 md:mt-0">
                    {serviceId === 'plumbing' ? (
                      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-4 md:p-6">
                        <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                          <div className="col-span-1 md:col-span-1 rounded-lg overflow-hidden aspect-[3/4]">
                            <img 
                              src={displayImages[0]} 
                              alt="Professional plumbing repair" 
                              className="w-full h-full object-cover"
                            />
                          </div>
                          <div className="col-span-1 md:col-span-1 rounded-lg overflow-hidden aspect-[3/4]">
                            <img 
                              src={displayImages[1]} 
                              alt="Plumbing installation services" 
                              className="w-full h-full object-cover"
                            />
                          </div>
                          <div className="col-span-2 md:col-span-1 rounded-lg overflow-hidden aspect-[3/2] md:aspect-[3/4]">
                            <img 
                              src={displayImages[2]} 
                              alt="Water heater repair and maintenance" 
                              className="w-full h-full object-cover"
                            />
                          </div>
                        </div>
                        <div className="mt-4 bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                          <h3 className="font-semibold text-lg mb-2 dark:text-white">Get Your Free Estimate</h3>
                          <p className="text-sm text-gray-600 dark:text-gray-300 mb-4">Enter your ZIP code to compare quotes from licensed professionals</p>
                          <div className="flex shadow-lg rounded-xl overflow-hidden">
                            <div className="flex-1 flex items-center bg-white dark:bg-gray-600 p-3">
                              <input
                                type="text"
                                placeholder="Enter your ZIP code"
                                className="w-full bg-transparent border-none focus:outline-none text-gray-900 dark:text-white placeholder:text-gray-500 dark:placeholder:text-gray-300"
                              />
                            </div>
                            <Button 
                              type="submit" 
                              className="px-6 h-auto rounded-none bg-primary hover:bg-primary/90 text-white transition-all"
                            >
                              <span>Compare Quotes</span>
                            </Button>
                          </div>
                        </div>
                      </div>
                    ) : (
                      <ServiceEstimator serviceId={serviceId} estimates={serviceEstimates} />
                    )}
                  </div>
                }
              </div>
            </div>
          </section>
        </AuroraBackground>}

      {showNeedsAfterHero && commonNeeds.length > 0 && <section className="py-8 md:py-16 bg-white dark:bg-gray-800">
          <div className="container mx-auto px-4 md:px-12">
            <div className="text-center mb-4 md:mb-10">
              <h2 className="text-xl md:text-4xl font-bold mb-1 md:mb-4 text-black dark:text-white">
                Common Professional {title} Needs
              </h2>
              <p className="text-xs md:text-lg text-gray-800 dark:text-gray-300 max-w-2xl mx-auto font-medium">
                Select from our most requested {serviceId} services below
              </p>
            </div>

            <ServiceNeeds serviceId={serviceId} needs={commonNeeds} estimates={serviceEstimates} />
          </div>
        </section>}

      <section className={`py-8 md:py-20 bg-white dark:bg-gray-800 ${toolPage ? 'bg-gradient-to-b from-white to-blue-50 dark:from-gray-800 dark:to-gray-900' : ''}`}>
        <div className="container mx-auto px-4 md:px-12">
          <div className="text-center mb-4 md:mb-12">
            <h2 className="text-xl md:text-4xl font-bold mb-1 md:mb-4 text-black dark:text-white">
              Why Choose Our {title} Service
            </h2>
            <p className="text-xs md:text-lg text-gray-800 dark:text-gray-300 max-w-2xl mx-auto font-medium">
              We provide reliable, professional service you can trust
            </p>
          </div>

          {isMobile ? <div className="grid grid-cols-2 gap-2">
              {benefits.map((benefit, index) => <div key={index} className="bg-white dark:bg-gray-800 rounded-lg p-2 shadow-sm border border-gray-100 dark:border-gray-700 hover-scale flex flex-col items-center text-center">
                  <div className="w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center mb-1">
                    {React.cloneElement(benefit.icon as React.ReactElement, {
                className: "h-4 w-4 text-primary"
              })}
                  </div>
                  <h3 className="text-xs font-semibold mb-0.5 text-black dark:text-white">{benefit.title}</h3>
                </div>)}
            </div> : <div className="grid grid-cols-3 gap-3 md:gap-8">
              {benefits.slice(0, 3).map((benefit, index) => <div key={index} className="bg-white dark:bg-gray-800 rounded-xl p-4 md:p-6 shadow-sm border border-gray-100 dark:border-gray-700 hover-scale">
                  <div className="w-10 h-10 md:w-12 md:h-12 rounded-full bg-primary/10 flex items-center justify-center mb-3 md:mb-4">
                    {React.cloneElement(benefit.icon as React.ReactElement, {
                className: "h-5 w-5 md:h-6 md:w-6 text-primary"
              })}
                  </div>
                  <h3 className="text-base md:text-xl font-semibold mb-1 md:mb-2">{benefit.title}</h3>
                  <p className="text-xs md:text-base text-gray-600 dark:text-gray-300">{benefit.description}</p>
                </div>)}
              {benefits.slice(3).map((benefit, index) => <div key={index} className="bg-white dark:bg-gray-800 rounded-xl p-4 md:p-6 shadow-sm border border-gray-100 dark:border-gray-700 hover-scale">
                  <div className="w-10 h-10 md:w-12 md:h-12 rounded-full bg-primary/10 flex items-center justify-center mb-3 md:mb-4">
                    {React.cloneElement(benefit.icon as React.ReactElement, {
                className: "h-5 w-5 md:h-6 md:w-6 text-primary"
              })}
                  </div>
                  <h3 className="text-base md:text-xl font-semibold mb-1 md:mb-2">{benefit.title}</h3>
                  <p className="text-xs md:text-base text-gray-600 dark:text-gray-300">{benefit.description}</p>
                </div>)}
            </div>}
        </div>
      </section>

      <section className="py-8 md:py-20 bg-white dark:bg-gray-800">
        <div className="container mx-auto px-4 md:px-12">
          <div className="text-center mb-4 md:mb-16">
            <h2 className="text-xl md:text-4xl font-bold mb-1 md:mb-4">
              How Our {title} Service Works
            </h2>
            <p className="text-xs md:text-lg text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
              A simple, hassle-free process to get your project done right
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-3 md:gap-8">
            <div className="text-center bg-gray-50 dark:bg-gray-700 p-3 md:p-6 rounded-xl">
              <div className="w-10 h-10 md:w-16 md:h-16 rounded-full bg-primary flex items-center justify-center text-white text-xl md:text-2xl font-bold mx-auto mb-2 md:mb-4">
                <ClipboardCheck className="h-5 w-5 md:h-8 md:w-8 text-white" strokeWidth={2} />
              </div>
              <h3 className="text-sm md:text-xl font-semibold mb-1 md:mb-2">Submit Your Request</h3>
              <p className="text-xs md:text-base text-gray-600 dark:text-gray-300">
                Fill out our simple form with details about your {serviceId} project needs
              </p>
            </div>

            <div className="text-center bg-gray-50 dark:bg-gray-700 p-3 md:p-6 rounded-xl">
              <div className="w-10 h-10 md:w-16 md:h-16 rounded-full bg-primary flex items-center justify-center text-white text-xl md:text-2xl font-bold mx-auto mb-2 md:mb-4">
                <MessageSquare className="h-5 w-5 md:h-8 md:w-8 text-white" strokeWidth={2} />
              </div>
              <h3 className="text-sm md:text-xl font-semibold mb-1 md:mb-2">Get Multiple Quotes</h3>
              <p className="text-xs md:text-base text-gray-600 dark:text-gray-300">
                Receive competitive bids from our network of licensed professionals
              </p>
            </div>

            <div className="text-center bg-gray-50 dark:bg-gray-700 p-3 md:p-6 rounded-xl">
              <div className="w-10 h-10 md:w-16 md:h-16 rounded-full bg-primary flex items-center justify-center text-white text-xl md:text-2xl font-bold mx-auto mb-2 md:mb-4">
                <ThumbsUp className="h-5 w-5 md:h-8 md:w-8 text-white" strokeWidth={2} />
              </div>
              <h3 className="text-sm md:text-xl font-semibold mb-1 md:mb-2">Book with Confidence</h3>
              <p className="text-xs md:text-base text-gray-600 dark:text-gray-300">
                Choose the best pro for your job and schedule service on your terms
              </p>
            </div>
          </div>
        </div>
      </section>

      <section className="py-8 md:py-20 bg-gray-50 dark:bg-gray-900">
        <div className="container mx-auto px-4 md:px-12">
          <div className="text-center mb-4 md:mb-16">
            <h2 className="text-xl md:text-4xl font-bold mb-1 md:mb-4">
              What Our Customers Say
            </h2>
            <p className="text-xs md:text-lg text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
              Read reviews from {commercialSpecific ? 'businesses' : 'homeowners'} who trusted our {serviceId} services
            </p>
          </div>

          <TestimonialsCarousel />
        </div>
      </section>

      {!showNeedsAfterHero && commonNeeds.length > 0 && <section className="py-8 md:py-16 bg-white dark:bg-gray-800">
          <div className="container mx-auto px-4 md:px-12">
            <div className="text-center mb-4 md:mb-10">
              <h2 className="text-xl md:text-4xl font-bold mb-1 md:mb-4 text-black dark:text-white">
                Common {title} Needs
              </h2>
              <p className="text-xs md:text-lg text-gray-800 dark:text-gray-300 max-w-2xl mx-auto font-medium">
                Select from our most requested {serviceId} services below
              </p>
            </div>

            <ServiceNeeds serviceId={serviceId} needs={commonNeeds} estimates={serviceEstimates} />
          </div>
        </section>}

      <section className={`py-8 md:py-16 bg-primary text-white ${toolPage ? 'bg-gradient-to-r from-primary to-blue-600' : ''}`}>
        <div className="container mx-auto px-4 md:px-12 text-center">
          <h2 className={`text-xl md:text-4xl font-bold mb-1 md:mb-4 ${toolPage ? 'md:text-5xl' : ''} text-white`}>
            Ready to Get Started with Your {title} Project?
          </h2>
          <p className={`text-xs md:text-xl text-white/90 mb-3 md:mb-8 max-w-2xl mx-auto ${toolPage ? 'md:text-2xl' : ''} text-white dark:text-white`}>
            Join thousands of satisfied customers who trust JobON for their {serviceId} needs
          </p>

          {customCta ? customCta : <div className="flex flex-col sm:flex-row justify-center gap-2 md:gap-4">
              <Link to="/create-job">
                <Button size={isMobile ? "default" : "lg"} className={`${isMobile ? 'text-sm py-2' : 'text-lg py-6'} font-medium px-4 md:px-6 h-auto rounded-xl bg-white text-primary hover:bg-white/90 shadow-lg hover:shadow-xl hover:translate-y-[-2px] transition-all w-full sm:w-auto ${toolPage ? 'text-xl py-7' : ''}`}>
                  Post a Project
                </Button>
              </Link>
              <Link to={`/professionals?service=${serviceId}`}>
                <Button size={isMobile ? "default" : "lg"} variant="outline" className={`${isMobile ? 'text-sm py-2' : 'text-lg py-6'} font-medium px-4 md:px-6 h-auto rounded-xl border-white text-white hover:bg-white/10 shadow-lg hover:shadow-xl hover:translate-y-[-2px] transition-all w-full sm:w-auto ${toolPage ? 'text-xl py-7' : ''}`}>
                  Browse {professionalTitle}
                </Button>
              </Link>
            </div>}
        </div>
      </section>
    </Layout>
  );
};
