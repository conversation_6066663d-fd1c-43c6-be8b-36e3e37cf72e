import React from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>alog<PERSON><PERSON><PERSON>, DialogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Phone, Globe, Mail, MapPin, Clock, Star, X } from "lucide-react";
import { useMediaQuery } from "@/hooks/use-media-query";
import { BusinessType } from '@/pages/Business';

interface BusinessDetailsDialogProps {
  business: BusinessType | null;
  isOpen: boolean;
  onClose: () => void;
}

const BusinessDetailsDialog: React.FC<BusinessDetailsDialogProps> = ({
  business,
  isOpen,
  onClose,
}) => {
  const isMobile = useMediaQuery("(max-width: 768px)");

  if (!business) return null;

  const getBusinessInitials = (name: string) => {
    const words = name.split(' ');
    if (words.length >= 2) {
      return (words[0][0] + words[1][0]).toUpperCase();
    }
    return name.charAt(0).toUpperCase();
  };

  const renderBusinessHours = () => {
    if (!business.hours || typeof business.hours !== 'object') {
      return <p className="text-sm text-gray-500">Hours not available</p>;
    }

    const daysOrder = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'] as const;
    const dayNames: Record<typeof daysOrder[number], string> = {
      monday: 'Monday',
      tuesday: 'Tuesday', 
      wednesday: 'Wednesday',
      thursday: 'Thursday',
      friday: 'Friday',
      saturday: 'Saturday',
      sunday: 'Sunday'
    };

    return (
      <div className="space-y-1">
        {daysOrder.map((dayKey) => {
          const dayName = dayNames[dayKey];
          const hours = (business.hours as Record<string, string>)[dayKey] || 'Closed';
          return (
            <div key={dayKey} className="flex justify-between text-sm">
              <span className="font-medium">{dayName}</span>
              <span className="text-gray-600">{hours}</span>
            </div>
          );
        })}
      </div>
    );
  };

  const renderPhotos = () => {
    if (!business.photos || business.photos.length === 0) {
      return <p className="text-sm text-gray-500">No photos available</p>;
    }

    return (
      <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
        {business.photos.map((photo: string, index: number) => (
          <div key={index} className="aspect-square rounded-lg overflow-hidden bg-gray-100">
            <img
              src={photo}
              alt={`Business photo ${index + 1}`}
              className="w-full h-full object-cover"
              onError={(e) => {
                const target = e.target as HTMLImageElement;
                target.src = '/placeholder.svg';
              }}
            />
          </div>
        ))}
      </div>
    );
  };

  const renderReviews = () => {
    if (!business.reviews || business.reviews.length === 0) {
      return <p className="text-sm text-gray-500">No reviews available</p>;
    }

    const averageRating = business.reviews.reduce((acc: number, review: { text: string; rating: string; author: string; date: string }) => acc + parseFloat(review.rating || '0'), 0) / business.reviews.length;

    return (
      <div className="space-y-4">
        <div className="flex items-center gap-2">
          <div className="flex items-center">
            {[1, 2, 3, 4, 5].map((star) => (
              <Star
                key={star}
                className={`h-4 w-4 ${
                  star <= averageRating ? 'text-yellow-400 fill-current' : 'text-gray-300'
                }`}
              />
            ))}
          </div>
          <span className="text-sm font-medium">{averageRating.toFixed(1)} out of 5</span>
          <span className="text-sm text-gray-500">({business.reviews.length} reviews)</span>
        </div>

        <div className="space-y-3">
          {business.reviews.map((review: { text: string; rating: string; author: string; date: string }, index: number) => (
            <div key={index} className="border rounded-lg p-3">
              <div className="flex items-center justify-between mb-2">
                <span className="font-medium text-sm">{review.author || 'Anonymous'}</span>
                <div className="flex items-center">
                  {[1, 2, 3, 4, 5].map((star) => (
                    <Star
                      key={star}
                      className={`h-3 w-3 ${
                        star <= parseFloat(review.rating || '0') ? 'text-yellow-400 fill-current' : 'text-gray-300'
                      }`}
                    />
                  ))}
                </div>
              </div>
              <p className="text-sm text-gray-600">{review.text}</p>
              {review.date && (
                <p className="text-xs text-gray-400 mt-1">{review.date}</p>
              )}
            </div>
          ))}
        </div>
      </div>
    );
  };

  if (isMobile) {
    return (
      <Dialog open={isOpen} onOpenChange={onClose}>
        <DialogContent className="h-[90vh] p-0 gap-0">
          <div className="flex flex-col h-full">
            {/* Header */}
            <div className="p-4 border-b">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <Avatar className="h-12 w-12">
                    <AvatarImage src={business.photos?.[0]} alt={business.name} />
                    <AvatarFallback>{getBusinessInitials(business.name)}</AvatarFallback>
                  </Avatar>
                  <div>
                    <h2 className="font-bold text-lg">{business.name}</h2>
                    <p className="text-sm text-gray-600">{business.category}</p>
                  </div>
                </div>
                <Button variant="ghost" size="icon" onClick={onClose}>
                  <X className="h-5 w-5" />
                </Button>
              </div>
            </div>

            {/* Content */}
            <ScrollArea className="flex-1">
              <div className="p-4 space-y-6">
                {/* Contact Information */}
                <Card>
                  <CardHeader>
                    <CardTitle className="text-base">Contact Information</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <div className="flex items-center gap-2">
                      <MapPin className="h-4 w-4 text-gray-500" />
                      <span className="text-sm">{business.address}</span>
                    </div>
                    {business.phone && (
                      <div className="flex items-center gap-2">
                        <Phone className="h-4 w-4 text-gray-500" />
                        <span className="text-sm">{business.phone}</span>
                      </div>
                    )}
                    {business.email && (
                      <div className="flex items-center gap-2">
                        <Mail className="h-4 w-4 text-gray-500" />
                        <span className="text-sm">{business.email}</span>
                      </div>
                    )}
                    {business.website && (
                      <div className="flex items-center gap-2">
                        <Globe className="h-4 w-4 text-gray-500" />
                        <a 
                          href={business.website} 
                          target="_blank" 
                          rel="noopener noreferrer"
                          className="text-sm text-blue-600 hover:underline"
                        >
                          {business.website}
                        </a>
                      </div>
                    )}
                  </CardContent>
                </Card>

                {/* Business Hours */}
                <Card>
                  <CardHeader>
                    <CardTitle className="text-base flex items-center gap-2">
                      <Clock className="h-4 w-4" />
                      Business Hours
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    {renderBusinessHours()}
                  </CardContent>
                </Card>

                {/* Services */}
                {business.services && business.services.length > 0 && (
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-base">Services</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="flex flex-wrap gap-2">
                        {business.services.map((service, index) => (
                          <Badge key={index} variant="secondary">
                            {service}
                          </Badge>
                        ))}
                      </div>
                    </CardContent>
                  </Card>
                )}

                {/* Photos */}
                <Card>
                  <CardHeader>
                    <CardTitle className="text-base">Photos</CardTitle>
                  </CardHeader>
                  <CardContent>
                    {renderPhotos()}
                  </CardContent>
                </Card>

                {/* Reviews */}
                <Card>
                  <CardHeader>
                    <CardTitle className="text-base">Reviews</CardTitle>
                  </CardHeader>
                  <CardContent>
                    {renderReviews()}
                  </CardContent>
                </Card>
              </div>
            </ScrollArea>
          </div>
        </DialogContent>
      </Dialog>
    );
  }

  // Desktop version
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] p-0 gap-0">
        <DialogHeader className="p-6 pb-0">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <Avatar className="h-16 w-16">
                <AvatarImage src={business.photos?.[0]} alt={business.name} />
                <AvatarFallback>{getBusinessInitials(business.name)}</AvatarFallback>
              </Avatar>
              <div>
                <DialogTitle className="text-2xl">{business.name}</DialogTitle>
                <p className="text-gray-600">{business.category}</p>
                <p className="text-sm text-gray-500">{business.location}</p>
              </div>
            </div>
            <Button variant="ghost" size="icon" onClick={onClose}>
              <X className="h-5 w-5" />
            </Button>
          </div>
        </DialogHeader>

        <ScrollArea className="max-h-[calc(90vh-120px)]">
          <div className="p-6 pt-4">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Left Column */}
              <div className="space-y-6">
                {/* Contact Information */}
                <Card>
                  <CardHeader>
                    <CardTitle>Contact Information</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <div className="flex items-center gap-2">
                      <MapPin className="h-4 w-4 text-gray-500" />
                      <span>{business.address}</span>
                    </div>
                    {business.phone && (
                      <div className="flex items-center gap-2">
                        <Phone className="h-4 w-4 text-gray-500" />
                        <span>{business.phone}</span>
                      </div>
                    )}
                    {business.email && (
                      <div className="flex items-center gap-2">
                        <Mail className="h-4 w-4 text-gray-500" />
                        <span>{business.email}</span>
                      </div>
                    )}
                    {business.website && (
                      <div className="flex items-center gap-2">
                        <Globe className="h-4 w-4 text-gray-500" />
                        <a 
                          href={business.website} 
                          target="_blank" 
                          rel="noopener noreferrer"
                          className="text-blue-600 hover:underline"
                        >
                          {business.website}
                        </a>
                      </div>
                    )}
                  </CardContent>
                </Card>

                {/* Business Hours */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Clock className="h-4 w-4" />
                      Business Hours
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    {renderBusinessHours()}
                  </CardContent>
                </Card>

                {/* Services */}
                {business.services && business.services.length > 0 && (
                  <Card>
                    <CardHeader>
                      <CardTitle>Services</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="flex flex-wrap gap-2">
                        {business.services.map((service, index) => (
                          <Badge key={index} variant="secondary">
                            {service}
                          </Badge>
                        ))}
                      </div>
                    </CardContent>
                  </Card>
                )}
              </div>

              {/* Right Column */}
              <div className="space-y-6">
                {/* Photos */}
                <Card>
                  <CardHeader>
                    <CardTitle>Photos</CardTitle>
                  </CardHeader>
                  <CardContent>
                    {renderPhotos()}
                  </CardContent>
                </Card>

                {/* Reviews */}
                <Card>
                  <CardHeader>
                    <CardTitle>Reviews</CardTitle>
                  </CardHeader>
                  <CardContent>
                    {renderReviews()}
                  </CardContent>
                </Card>
              </div>
            </div>
          </div>
        </ScrollArea>
      </DialogContent>
    </Dialog>
  );
};

export default BusinessDetailsDialog;
