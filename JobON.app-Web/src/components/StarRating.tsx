
import React from 'react';
import { Star } from 'lucide-react';

type RatingSize = 'sm' | 'md' | 'lg';

interface StarRatingProps {
  rating: number;
  size?: RatingSize;
  showCount?: boolean;
}

export const StarRating: React.FC<StarRatingProps> = ({ 
  rating, 
  size = 'md',
  showCount = false
}) => {
  const fullStars = Math.floor(rating);
  const hasHalfStar = rating % 1 !== 0;
  
  const getStarSize = () => {
    switch (size) {
      case 'sm': return 'h-3 w-3';
      case 'lg': return 'h-5 w-5';
      case 'md':
      default: return 'h-4 w-4';
    }
  };

  return (
    <div className="flex items-center">
      {[...Array(5)].map((_, index) => {
        if (index < fullStars) {
          // Full star
          return (
            <Star
              key={index}
              className={`${getStarSize()} text-amber-400 fill-amber-400 mr-0.5`}
              strokeWidth={1.5}
            />
          );
        } else if (index === fullStars && hasHalfStar) {
          // Half star (you might want to use a specific half-star icon here)
          return (
            <div key={index} className="relative mr-0.5">
              <Star
                className={`${getStarSize()} text-gray-300 fill-gray-300`}
                strokeWidth={1.5}
              />
              <div className="absolute top-0 left-0 overflow-hidden w-1/2">
                <Star
                  className={`${getStarSize()} text-amber-400 fill-amber-400`}
                  strokeWidth={1.5}
                />
              </div>
            </div>
          );
        } else {
          // Empty star
          return (
            <Star
              key={index}
              className={`${getStarSize()} text-gray-300 mr-0.5`}
              strokeWidth={1.5}
            />
          );
        }
      })}
      
      {showCount && (
        <span className="ml-2 text-sm font-medium text-gray-500 dark:text-gray-400">
          {rating.toFixed(1)}
        </span>
      )}
    </div>
  );
};
