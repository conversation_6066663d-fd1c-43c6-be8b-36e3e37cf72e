
import React from 'react';
import { Link } from 'react-router-dom';
import { Calendar, MessageSquare, User, Clock } from 'lucide-react';
import { Card, CardContent } from "@/components/ui/card";
import { formatDate } from "@/lib/utils";
import { useIsMobile } from '@/hooks/use-mobile';

export interface BlogPost {
  id: string;
  title: string;
  excerpt: string;
  category: string;
  slug: string;
  date: string;
  author: string;
  commentCount: number;
  image: string;
}

interface BlogCardProps {
  post: BlogPost;
  compact?: boolean;
}

export const BlogCard: React.FC<BlogCardProps> = ({ post, compact = false }) => {
  const isMobile = useIsMobile();
  
  // On mobile, never use compact mode unless explicitly requested
  const shouldUseCompact = isMobile ? false : compact;
  
  return (
    <Card className="overflow-hidden border-none shadow-md hover:shadow-xl transition-all duration-300 group h-full">
      <Link to={`/blog/${post.slug}`} className="block">
        <div className={`${shouldUseCompact ? 'aspect-[16/9]' : 'aspect-[3/2]'} w-full overflow-hidden`}>
          <img 
            src={post.image} 
            alt={post.title} 
            className="w-full h-full object-cover transition-transform duration-500 group-hover:scale-110"
          />
        </div>
      </Link>
      <CardContent className={`${shouldUseCompact ? 'p-4' : 'p-6'} bg-white dark:bg-gray-800 h-full flex flex-col`}>
        <div className="space-y-2">
          <div className="text-xs sm:text-sm text-primary font-medium">
            <Link to={`/category/${post.category.toLowerCase().replace(' ', '-')}`} className="hover:underline">
              {post.category}
            </Link>
          </div>
          
          <Link to={`/blog/${post.slug}`} className="block">
            <h3 className={`${shouldUseCompact ? 'text-base' : 'text-xl'} font-bold leading-tight mb-2 group-hover:text-primary transition-colors ${shouldUseCompact ? 'line-clamp-2' : ''}`}>
              {post.title.replace('&amp;', '&')}
            </h3>
          </Link>
          
          <p className={`text-muted-foreground line-clamp-2 text-xs sm:text-sm`}>
            {shouldUseCompact 
              ? post.excerpt.substring(0, 70) + (post.excerpt.length > 70 ? '...' : '') 
              : post.excerpt}
          </p>
          
          {!shouldUseCompact && (
            <div className="flex items-center pt-3 border-t border-gray-100 dark:border-gray-700 mt-3 text-xs sm:text-sm text-muted-foreground">
              <div className="flex items-center gap-1">
                <Clock className="h-3.5 w-3.5" />
                <span>{Math.ceil(post.excerpt.length / 20)} min read</span>
              </div>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};
