import React, {useState} from 'react';
import { Avatar, AvatarFallback, AvatarImage } from "./ui/avatar";
import { Card, CardContent } from "./ui/card";
import { Badge } from "./ui/badge";
import { StarRating } from './StarRating';
import { Shield, Clock, Award, Handshake, MapPin, Briefcase, CheckCheck, MessageSquare, Contact, Heart, Eye } from 'lucide-react';
import { Button } from './ui/button';
import { useIsMobile } from '@/hooks/use-mobile';
import { MessageProviderDialog } from './MessageProviderDialog';
import {useNavigate} from "react-router-dom";

export interface Provider {
    fullAddress: string;
    id: string;
    name: string;
    avatar: string;
    rating: number;
    reviewCount: number;
    responseTime: string;
    badges: Array<'verified' | 'topRated' | 'fastResponse' | 'mostHired'>;
    specialty: string;
    phone: string;
    email: string|null;
    category: string;
    isFeatured?: boolean;
}

interface ProviderCardProps {
    provider: Provider;
    badgeType?: 'verified' | 'topRated' | 'fastResponse' | 'mostHired';
    hourlyRate?: number;
    jobsCompleted?: number;
    distance?: string;
    listView?: boolean;
    onClick?: () => void;
    onViewProfile?: () => void;
    className?: string;
}

const stockProfileImages = [
    "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?q=80&w=1974&auto=format&fit=crop",
    "https://images.unsplash.com/photo-1494790108377-be9c29b29330?q=80&w=1974&auto=format&fit=crop",
    "https://images.unsplash.com/photo-1500648767791-00dcc994a43e?q=80&w=1974&auto=format&fit=crop",
    "https://images.unsplash.com/photo-1534528741775-53994a69daeb?q=80&w=1964&auto=format&fit=crop",
    "https://images.unsplash.com/photo-1539571696357-5a69c17a67c6?q=80&w=1974&auto=format&fit=crop",
    "https://images.unsplash.com/photo-1517841905240-472988babdf9?q=80&w=1974&auto=format&fit=crop",
    "https://images.unsplash.com/photo-1506794778202-cad84cf45f1d?q=80&w=1974&auto=format&fit=crop",
    "https://images.unsplash.com/photo-1507101105822-7472b28e22ac?q=80&w=1974&auto=format&fit=crop",
    "https://images.unsplash.com/photo-1531746020798-e6953c6e8e04?q=80&w=1964&auto=format&fit=crop",
    "https://images.unsplash.com/photo-1531123897727-8f129e1688ce?q=80&w=1974&auto=format=crop"
];

const getStockImage = (providerId: string) => {
    const idLastChar = providerId.charAt(providerId.length - 1);
    const numericValue = parseInt(idLastChar, 16) || 0;
    return stockProfileImages[numericValue % stockProfileImages.length];
};

const sampleReviews = [
    "Excellent service! They were on time and very professional.",
    "Great work ethic. Finished the job ahead of schedule.",
    "Very knowledgeable and reasonably priced. Would hire again!",
    "Did an amazing job with our lawn. Looks better than ever.",
    "Responsive and detail-oriented. I'm very satisfied.",
    "Went above and beyond what was expected. Highly recommend!",
    "Fair pricing and quality workmanship. No complaints.",
    "Very pleasant to work with and did a fantastic job."
];

const getReviewsForProvider = (providerId: string, count: number = 2) => {
    const idSum = providerId.split('').reduce((sum, char) => sum + char.charCodeAt(0), 0);
    const startIndex = idSum % (sampleReviews.length - count);

    return sampleReviews.slice(startIndex, startIndex + count);
};

export const ProviderCard: React.FC<ProviderCardProps> = ({
                                                              provider,
                                                              badgeType,
                                                              hourlyRate,
                                                              jobsCompleted,
                                                              distance,
                                                              listView = false,
                                                              onClick,
                                                              onViewProfile,
                                                              className = ''
                                                          }) => {
    const isMobile = useIsMobile();
    const navigate = useNavigate();
    const [isMessageDialogOpen, setIsMessageDialogOpen] = useState(false);

    const getBadgeIcon = (type: 'verified' | 'topRated' | 'fastResponse' | 'mostHired') => {
        switch (type) {
            case 'verified':
                return <Shield className="h-4 w-4 mr-1.5" />;
            case 'fastResponse':
                return <Clock className="h-4 w-4 mr-1.5 text-blue-500" />;
            case 'topRated':
                return <Award className="h-4 w-4 mr-1.5 text-yellow-500" />;
            case 'mostHired':
                return <Handshake className="h-4 w-4 mr-1.5 text-green-500" />;
        }
    };

    const getBadgeText = (type: 'verified' | 'topRated' | 'fastResponse' | 'mostHired') => {
        switch (type) {
            case 'verified':
                return 'Verified';
            case 'fastResponse':
                return 'Fast Response';
            case 'topRated':
                return 'Top Rated';
            case 'mostHired':
                return 'Most Hired';
        }
    };

    const handleMessageClick = (e: React.MouseEvent) => {
        e.stopPropagation();
        setIsMessageDialogOpen(true);
    };

    const profileImage = getStockImage(provider.id);
    const reviews = getReviewsForProvider(provider.id);
    const firstLetter = provider.name.charAt(0);

    if (isMobile) {
        return (
            <Card 
                className={`w-full max-w-[398px] h-auto overflow-hidden transition-all duration-200 hover:shadow-lg border border-gray-100 rounded-xl shadow-md bg-white dark:bg-gray-800 cursor-pointer ${className}`} 
                onClick={onClick}
            >
                <CardContent className="p-3">
                    <div className="flex items-start gap-3">
                        <div className="relative flex-shrink-0">
                            <Avatar className="h-14 w-14 border-2 border-primary/10">
                                <AvatarImage src={profileImage} alt={provider.name} />
                                <AvatarFallback>{provider.name.charAt(0)}</AvatarFallback>
                            </Avatar>

                            {provider.badges.includes('verified') && (
                                <div className="absolute -top-1 -right-1 bg-[#0f7c61] rounded-full p-0.5 shadow-sm">
                                    <CheckCheck className="h-3 w-3 text-white" />
                                </div>
                            )}
                        </div>

                        <div className="flex-1 min-w-0">
                            <div className="flex items-start justify-between mb-1">
                                <h3 className="font-semibold text-base truncate text-gray-900 dark:text-white pr-2">{provider.name}</h3>
                                <button className="text-gray-400 hover:text-red-500 transition-colors flex-shrink-0">
                                    <Heart className="h-4 w-4" />
                                </button>
                            </div>

                            <div className="flex items-center mb-1">
                                <StarRating rating={provider.rating} size="sm" />
                                <span className="text-sm text-gray-600 dark:text-gray-300 ml-1">({provider.reviewCount})</span>
                            </div>

                            <div className="text-sm text-gray-600 dark:text-gray-300 mb-2">
                                <div className="flex items-center">
                                    <MapPin className="h-3.5 w-3.5 text-gray-400 mr-1 flex-shrink-0" />
                                    <span className="truncate">{distance}</span>
                                </div>
                            </div>

                            <div className="flex flex-wrap gap-1 mb-3">
                                {provider.badges
                                    .filter(badge => badge !== 'verified')
                                    .slice(0, 2)
                                    .map((badge) => (
                                        <Badge
                                            key={badge}
                                            variant="secondary"
                                            className="px-2 py-0.5 text-xs flex items-center"
                                        >
                                            {getBadgeIcon(badge)}
                                            <span>{getBadgeText(badge)}</span>
                                        </Badge>
                                    ))}
                            </div>

                            <Button 
                                size="sm"
                                className="w-full h-9 bg-[#2263eb] hover:bg-[#2263eb]/90 text-white text-sm"
                                onClick={onViewProfile || onClick}
                                type="button"
                            >
                                View Profile
                            </Button>
                        </div>
                    </div>
                </CardContent>
            </Card>
        );
    }

    if (listView) {
        return (
            <>
                <Card className={`overflow-hidden transition-all duration-200 hover:shadow-lg border bg-white dark:bg-gray-800 cursor-pointer mb-4 ${className}`} onClick={onClick}>
                    <CardContent className="p-4">
                        <div className="flex items-start gap-4">
                            <div className="relative flex-shrink-0">
                                <div className="rounded-full bg-gray-100 h-20 w-20 flex items-center justify-center text-2xl font-semibold text-gray-500">
                                    {firstLetter}
                                </div>

                                {provider.badges.includes('verified') && (
                                    <div className="absolute top-0 right-0 bg-green-600 rounded-full p-1">
                                        <CheckCheck className="h-4 w-4 text-white" />
                                    </div>
                                )}
                            </div>

                            <div className="flex-grow min-w-0">
                                <div className="flex items-center justify-between mb-1">
                                    <div className="flex items-center space-x-2">
                                        <h3 className="font-semibold text-xl text-gray-900 dark:text-white truncate">{provider.name}</h3>
                                        <button className="text-gray-400 hover:text-red-500 transition-colors">
                                            <Heart className="h-5 w-5" />
                                        </button>
                                    </div>
                                </div>
                                
                                <p className="text-sm text-gray-700 dark:text-gray-300 mb-1">{provider.specialty}</p>
                                <p className="text-sm text-gray-600 dark:text-gray-400 mb-2 truncate">{provider.fullAddress}</p>

                                <div className="flex items-center mb-2">
                                    <div className="text-yellow-400 text-lg mr-1">★</div>
                                    <span className="font-semibold text-base mr-1">{provider.rating}</span>
                                    <span className="text-gray-500 text-sm">• {provider.reviewCount} reviews</span>
                                </div>

                                <div className="flex flex-wrap gap-2 mb-2">
                                    {provider.badges.map((badge) => {
                                        if (badge === 'verified') return null;

                                        let icon;
                                        const text = getBadgeText(badge);
                                        let bgColor = 'bg-gray-100';
                                        let textColor = 'text-gray-800';

                                        if (badge === 'topRated') {
                                            icon = <Award className="h-4 w-4 text-yellow-500 mr-1" />;
                                            bgColor = 'bg-yellow-50';
                                            textColor = 'text-yellow-700';
                                        } else if (badge === 'fastResponse') {
                                            icon = <Clock className="h-4 w-4 text-blue-500 mr-1" />;
                                            bgColor = 'bg-blue-50';
                                            textColor = 'text-blue-700';
                                        } else if (badge === 'mostHired') {
                                            icon = <Handshake className="h-4 w-4 text-green-500 mr-1" />;
                                            bgColor = 'bg-green-50';
                                            textColor = 'text-green-700';
                                        }

                                        return (
                                            <div key={badge} className={`flex items-center ${bgColor} ${textColor} px-3 py-1 rounded-full text-sm`}>
                                                {icon}
                                                <span>{text}</span>
                                            </div>
                                        );
                                    })}
                                </div>
                            </div>

                            <div className="flex flex-col items-end gap-2 flex-shrink-0">
                                <div className="flex items-center text-gray-600 text-sm">
                                    <MapPin className="h-4 w-4 mr-1" />
                                    <span>{distance?.includes('away') ? distance : `${distance} away`}</span>
                                </div>

                                <div className="flex items-center text-gray-600 text-sm">
                                    <Clock className="h-4 w-4 mr-1" />
                                    <span>Responds under {provider.responseTime.includes('min') ? provider.responseTime : `${provider.responseTime.replace('<', '')}`}</span>
                                </div>

                                <div className="flex items-center text-gray-600 text-sm">
                                    <Briefcase className="h-4 w-4 mr-1" />
                                    <span>{jobsCompleted} projects completed</span>
                                </div>

                                <div className="flex gap-2 mt-2">
                                    <Button
                                        variant="secondary"
                                        size="sm"
                                        className="flex items-center gap-1"
                                        onClick={onViewProfile || onClick}
                                        type="button"
                                    >
                                        <Eye className="h-4 w-4" />
                                        View
                                    </Button>
                                    <Button
                                        variant="default"
                                        size="sm"
                                        className="flex items-center gap-1 bg-blue-600 hover:bg-blue-700"
                                        onClick={handleMessageClick}
                                    >
                                        <MessageSquare className="h-4 w-4" />
                                        Message
                                    </Button>
                                </div>
                            </div>
                        </div>
                    </CardContent>
                </Card>

                <MessageProviderDialog
                    isOpen={isMessageDialogOpen}
                    onClose={() => setIsMessageDialogOpen(false)}
                    provider={provider}
                />
            </>
        );
    }

    return (
        <Card className={`overflow-hidden hover:shadow-lg transition-all duration-200 border-0 shadow-md h-full bg-white dark:bg-gray-800 cursor-pointer ${className}`} onClick={onClick}>
            <CardContent className="p-5">
                <div className="flex flex-col items-center text-center">
                    <div className="relative">
                        <Avatar className="h-44 w-44 mb-4 border-2 border-primary/10">
                            <AvatarImage src={profileImage} alt={provider.name} />
                            <AvatarFallback>{provider.name.charAt(0)}</AvatarFallback>
                        </Avatar>

                        {provider.badges.includes('verified') && (
                            <div className="absolute -top-2 left-1/2 transform -translate-x-1/2 bg-[#0f7c61] rounded-full p-1 shadow-sm">
                                <CheckCheck className="h-5 w-5 text-white" />
                            </div>
                        )}
                    </div>

                    <h3 className="font-semibold text-lg mb-2 text-gray-900 dark:text-white">{provider.name}</h3>

                    <div className="flex items-center justify-center mb-2">
                        <StarRating rating={provider.rating} size="sm" />
                        <span className="text-sm text-gray-600 dark:text-gray-300 ml-2">({provider.reviewCount})</span>
                    </div>

                    <p className="text-sm text-gray-700 dark:text-gray-300 mb-3 px-2">{provider.specialty}</p>

                    <div className="w-full grid grid-cols-2 gap-2 mb-4">
                        <div className="bg-gray-50 dark:bg-gray-800/50 p-2 rounded-md flex flex-col items-center border border-gray-100 dark:border-gray-700">
                            <div className="flex items-center gap-1 text-gray-800 dark:text-gray-200 mb-1">
                                <Briefcase className="h-3.5 w-3.5 text-amber-600" />
                                <span className="font-medium text-sm">Projects</span>
                            </div>
                            <span className="text-base font-semibold">{jobsCompleted}</span>
                        </div>

                        <div className="bg-gray-50 dark:bg-gray-800/50 p-2 rounded-md flex flex-col items-center border border-gray-100 dark:border-gray-700">
                            <div className="flex items-center gap-1 text-gray-800 dark:text-gray-200 mb-1">
                                <MapPin className="h-3.5 w-3.5 text-purple-600" />
                                <span className="font-medium text-sm">Distance</span>
                            </div>
                            <span className="text-base font-semibold">{distance}</span>
                        </div>
                    </div>

                    <div className="w-full mb-4 p-3 bg-gray-50 dark:bg-gray-800/50 rounded-md border border-gray-100 dark:border-gray-700">
                        <p className="text-sm text-gray-700 dark:text-gray-300 italic">"{reviews[0]}"</p>
                    </div>

                    <div className="flex flex-wrap gap-1.5 justify-center mb-4">
                        {provider.badges
                            .filter(badge => badge !== 'verified')
                            .map((badge) => (
                                <Badge
                                    key={badge}
                                    variant={badge as never}
                                    className="px-2 py-0.5 text-xs flex items-center gap-1"
                                >
                                    {getBadgeIcon(badge)}
                                    <span>{getBadgeText(badge)}</span>
                                </Badge>
                            ))}
                    </div>

                    <Button className="w-full text-sm flex items-center justify-center gap-1" onClick={onViewProfile || onClick} type="button">
                        <Eye className="h-4 w-4" />
                        View Profile
                    </Button>
                </div>
            </CardContent>
        </Card>
    );
};
