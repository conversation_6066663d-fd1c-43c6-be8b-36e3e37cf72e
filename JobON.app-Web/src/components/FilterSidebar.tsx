
import React, { useState } from 'react';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { Button } from '@/components/ui/button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { categories } from '@/components/CategoryList';
import { Separator } from '@/components/ui/separator';

interface FilterState {
  categories: string[];
  locations: string[];
  salary: [number, number];
  tags: string[];
  service?: string | null;
}

interface FilterSidebarProps {
  onFilterChange: (filters: FilterState) => void;
  serviceType?: string;
  jobsPerPage?: number;
  onJobsPerPageChange?: (value: string) => void;
}

export const FilterSidebar: React.FC<FilterSidebarProps> = ({ 
  onFilterChange, 
  serviceType = "all",
  jobsPerPage = 10,
  onJobsPerPageChange
}) => {
  const [filters, setFilters] = useState<FilterState>({
    categories: [],
    locations: [],
    salary: [0, 100000],
    tags: [],
    service: null
  });

  const handleCategoryChange = (categoryId: string, checked: boolean) => {
    const newCategories = checked
      ? [...filters.categories, categoryId]
      : filters.categories.filter(id => id !== categoryId);
    
    const newFilters = { ...filters, categories: newCategories };
    setFilters(newFilters);
    onFilterChange(newFilters);
  };

  const clearFilters = () => {
    const clearedFilters = {
      categories: [],
      locations: [],
      salary: [0, 100000] as [number, number],
      tags: [],
      service: null
    };
    setFilters(clearedFilters);
    onFilterChange(clearedFilters);
  };

  return (
    <div className="w-full h-fit max-h-[calc(100vh-8rem)] overflow-y-auto bg-white dark:bg-gray-900 rounded-lg border border-gray-200 dark:border-gray-700">
      <div className="p-6 space-y-4">
        {/* Jobs Per Page */}
        {onJobsPerPageChange && (
          <>
            <div className="space-y-3">
              <Label className="text-sm font-semibold text-gray-900 dark:text-white">
                Jobs per page
              </Label>
              <Select value={jobsPerPage.toString()} onValueChange={onJobsPerPageChange}>
                <SelectTrigger className="w-full">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent className="z-50 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700">
                  <SelectItem value="6">6</SelectItem>
                  <SelectItem value="10">10</SelectItem>
                  <SelectItem value="12">12</SelectItem>
                  <SelectItem value="18">18</SelectItem>
                  <SelectItem value="24">24</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <Separator />
          </>
        )}

        {/* Categories */}
        <div className="space-y-4">
          <Label className="text-sm font-semibold text-gray-900 dark:text-white">
            Filter by Category
          </Label>
          <div className="space-y-3">
            {categories.slice(0, 8).map((category) => (
              <div key={category.id} className="flex items-center space-x-3">
                <Checkbox
                  id={category.id}
                  checked={filters.categories.includes(category.id)}
                  onCheckedChange={(checked) => 
                    handleCategoryChange(category.id, checked as boolean)
                  }
                  className="rounded"
                />
                <Label 
                  htmlFor={category.id} 
                  className="text-sm text-gray-700 dark:text-gray-300 cursor-pointer font-normal"
                >
                  {category.title}
                </Label>
              </div>
            ))}
          </div>
        </div>

        <Separator />

        {/* Clear Filters */}
        <Button 
          variant="outline" 
          onClick={clearFilters}
          className="w-full"
        >
          Clear All Filters
        </Button>
      </div>
    </div>
  );
};
