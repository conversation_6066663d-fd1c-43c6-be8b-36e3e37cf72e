
import React from 'react';
import { Helmet } from 'react-helmet-async';
import { YoastSEOData } from '@/services/wordpressApi';

// Helper function to decode HTML entities
const decodeHtmlEntities = (text: string): string => {
  if (!text) return '';

  // First decode
  const textarea = document.createElement('textarea');
  textarea.innerHTML = text;
  let decoded = textarea.value;

  // Check if there are still HTML entities that need decoding (specifically &amp;)
  if (decoded.includes('&amp;')) {
    // Second decode for nested entities
    textarea.innerHTML = decoded;
    decoded = textarea.value;
  }

  return decoded;
};

interface SEOProps {
  title: string;
  description: string;
  schema?: Record<string, any>;
  canonicalUrl?: string;
  image?: string;
  localBusinessSchema?: boolean;
  serviceType?: string;
  serviceSlug?: string;
  yoastSEO?: YoastSEOData;
}

export const SEO: React.FC<SEOProps> = ({ 
  title, 
  description, 
  schema, 
  canonicalUrl,
  image = "https://lovable.dev/opengraph-image-p98pqg.png",
  localBusinessSchema = false,
  serviceType = "",
  serviceSlug = "",
  yoastSEO
}) => {
  const siteUrl = "https://jobon.app";

  // Use Yoast SEO data if available, otherwise use custom data
  // Decode HTML entities in titles and descriptions to fix special character issues
  const seoTitle = yoastSEO?.title ? decodeHtmlEntities(yoastSEO.title) : title;
  const seoDescription = yoastSEO?.description ? decodeHtmlEntities(yoastSEO.description) : description;
  const seoCanonical = yoastSEO?.canonical || (canonicalUrl ? `${siteUrl}${canonicalUrl}` : siteUrl);
  const seoImage = yoastSEO?.opengraphImage || image;

  // For OpenGraph and Twitter, prioritize Yoast data
  const ogTitle = yoastSEO?.opengraphTitle ? decodeHtmlEntities(yoastSEO.opengraphTitle) : seoTitle;
  const ogDescription = yoastSEO?.opengraphDescription ? decodeHtmlEntities(yoastSEO.opengraphDescription) : seoDescription;
  const ogType = yoastSEO?.opengraphType || "website";

  const twitterTitle = yoastSEO?.twitterTitle ? decodeHtmlEntities(yoastSEO.twitterTitle) : ogTitle;
  const twitterDescription = yoastSEO?.twitterDescription ? decodeHtmlEntities(yoastSEO.twitterDescription) : ogDescription;
  const twitterImage = yoastSEO?.twitterImage || seoImage;

  // For backward compatibility
  const fullTitle = seoTitle ? `${seoTitle} | JobON` : "JobON | Help around the home at your fingertips";
  const fullUrl = seoCanonical;
  const logoUrl = "https://www.jobon.app/images/logo.png";

  // Generate LocalBusiness schema if needed
  const generateLocalBusinessSchema = () => {
    if (!localBusinessSchema || !serviceType || !serviceSlug) return null;

    return {
      "@context": "https://schema.org",
      "@type": "LocalBusiness",
      "name": "JobON",
      "url": `https://www.jobon.app/services/${serviceSlug}`,
      "image": logoUrl,
      "logo": logoUrl,
      "description": `Compare bids from local ${serviceType} pros. JobON makes hiring fast, affordable, and protected—so the job gets done right, every time.`,
      "areaServed": {
        "@type": "Country",
        "name": "United States"
      },
      "serviceType": serviceType,
      "brand": {
        "@type": "Brand",
        "name": "JobON"
      },
      "hasMap": "https://www.jobon.app/locations",
      "aggregateRating": {
        "@type": "AggregateRating",
        "ratingValue": "4.8",
        "reviewCount": "1540"
      },
      "sameAs": [
        "https://www.facebook.com/JobONapp",
        "https://www.instagram.com/JobONapp",
        "https://www.linkedin.com/company/jobonapp"
      ]
    };
  };

  const localBusinessSchemaData = generateLocalBusinessSchema();

  return (
    <Helmet>
      {/* Basic Meta Tags */}
      <title>{fullTitle}</title>
      <meta name="description" content={seoDescription} />

      {/* Canonical URL */}
      <link rel="canonical" href={fullUrl} />

      {/* Robot Meta Tags from Yoast if available */}
      {yoastSEO?.metaRobotsNoindex && (
        <meta name="robots" content={`${yoastSEO.metaRobotsNoindex},${yoastSEO.metaRobotsNofollow}`} />
      )}

      {/* OpenGraph Tags for Social Sharing */}
      <meta property="og:title" content={ogTitle} />
      <meta property="og:description" content={ogDescription} />
      <meta property="og:url" content={fullUrl} />
      <meta property="og:type" content={ogType} />
      <meta property="og:image" content={seoImage} />

      {/* Twitter Card Tags */}
      <meta name="twitter:card" content="summary_large_image" />
      <meta name="twitter:site" content="@jobon_app" />
      <meta name="twitter:title" content={twitterTitle} />
      <meta name="twitter:description" content={twitterDescription} />
      <meta name="twitter:image" content={twitterImage} />

      {/* Schema.org JSON-LD - Prioritize Yoast schema if available */}
      {(yoastSEO?.schema || schema) && (
        <script type="application/ld+json">
          {JSON.stringify(yoastSEO?.schema || schema)}
        </script>
      )}

      {/* LocalBusiness Schema.org JSON-LD if applicable and no Yoast schema */}
      {!yoastSEO?.schema && localBusinessSchemaData && (
        <script type="application/ld+json">
          {JSON.stringify(localBusinessSchemaData)}
        </script>
      )}
    </Helmet>
  );
};
