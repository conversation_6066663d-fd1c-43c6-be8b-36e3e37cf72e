
import { useState, useEffect } from 'react';
import { Moon, Sun } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useToast } from '@/hooks/use-toast';
import { useIsMobile } from '@/hooks/use-mobile';

export const ThemeToggle: React.FC = () => {
  const [theme, setTheme] = useState<'light' | 'dark'>('light');
  const { toast } = useToast();
  const isMobile = useIsMobile();

  // Initialize theme on component mount
  useEffect(() => {
    // Check if user has a preference stored
    const savedTheme = localStorage.getItem('theme');
    
    // Set the initial theme state - default to light theme
    if (savedTheme === 'dark') {
      setTheme('dark');
      document.documentElement.classList.add('dark');
    } else {
      // Always default to light if no saved preference or if saved as light
      setTheme('light');
      document.documentElement.classList.remove('dark');
      // Ensure light theme is saved to localStorage
      localStorage.setItem('theme', 'light');
    }
  }, []);

  const toggleTheme = () => {
    if (theme === 'dark') {
      // Switch to light theme
      document.documentElement.classList.remove('dark');
      document.documentElement.classList.add('light');
      localStorage.setItem('theme', 'light');
      setTheme('light');
      
      // Only show toast on desktop
      if (!isMobile) {
        toast({
          title: "Light mode activated",
          description: "The application theme has been switched to light mode.",
        });
      }
    } else {
      // Switch to dark theme
      document.documentElement.classList.remove('light');
      document.documentElement.classList.add('dark');
      localStorage.setItem('theme', 'dark');
      setTheme('dark');
      
      // Only show toast on desktop
      if (!isMobile) {
        toast({
          title: "Dark mode activated",
          description: "The application theme has been switched to dark mode.",
        });
      }
    }
  };

  return (
    <Button 
      variant="ghost" 
      size="icon" 
      onClick={toggleTheme}
      className="rounded-full hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors"
      aria-label="Toggle theme"
    >
      {theme === 'dark' ? (
        <Sun className="h-5 w-5 text-yellow-400" />
      ) : (
        <Moon className="h-5 w-5 text-gray-700" />
      )}
    </Button>
  );
};
