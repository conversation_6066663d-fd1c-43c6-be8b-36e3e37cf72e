
import React, { useState, useRef, useEffect, useCallback } from 'react';
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent } from "@/components/ui/card";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Send } from "lucide-react";
import { useToast } from "@/hooks/use-toast";

interface MessageContent {
  type: string;
  data: string;
}

export interface Message {
  id: string;
  content: MessageContent[];
  sender: 'user' | 'bot';
  timestamp: Date;
}

export interface ChatBotProps {
  onClose: () => void;
  initialMessage?: string;
}

export const ChatBot: React.FC<ChatBotProps> = ({ 
  onClose,
  initialMessage 
}) => {
  const [messages, setMessages] = useState<Message[]>(() => {
    if (initialMessage) {
      return [{
        id: Math.random().toString(36).substring(7),
        content: [{ type: 'text', data: initialMessage }],
        sender: 'user',
        timestamp: new Date(),
      }];
    }
    return [];
  });
  const [input, setInput] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const chatContainerRef = useRef<HTMLDivElement>(null);
  const typingTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const { toast } = useToast();

  useEffect(() => {
    // Scroll to bottom on message change
    scrollToBottom();
  }, [messages]);

  // Clean up typing timeout on unmount
  useEffect(() => {
    return () => {
      if (typingTimeoutRef.current) {
        clearTimeout(typingTimeoutRef.current);
      }
    };
  }, []);

  const handleSend = () => {
    if (input.trim() === '') return;

    const newMessage: Message = {
      id: Math.random().toString(36).substring(7),
      content: [{ type: 'text', data: input }],
      sender: 'user',
      timestamp: new Date(),
    };

    setMessages([...messages, newMessage]);
    setInput('');

    // Simulate bot response after a short delay
    setTimeout(() => {
      const botResponse: Message = {
        id: Math.random().toString(36).substring(7),
        content: [{ type: 'text', data: `Echo: ${input}` }],
        sender: 'bot',
        timestamp: new Date(),
      };
      setMessages(prevMessages => [...prevMessages, botResponse]);
    }, 500);
  };

  const scrollToBottom = () => {
    if (chatContainerRef.current) {
      chatContainerRef.current.scrollTop = chatContainerRef.current.scrollHeight;
    }
  };

  return (
    <Card className="w-full h-[500px] flex flex-col">
      <CardContent className="flex-grow flex flex-col">
        <div className="font-bold mb-4">Chat with us</div>
        <ScrollArea ref={chatContainerRef} className="flex-grow mb-4">
          <div className="space-y-2">
            {messages.map((message) => (
              <div
                key={message.id}
                className={`p-3 rounded-md ${message.sender === 'user' ? 'bg-gray-100 text-right' : 'bg-blue-100'
                  }`}
              >
                <div className="text-sm font-medium">
                  {message.sender === 'user' ? 'You' : 'Bot'}
                </div>
                <div className="text-sm">{message.content[0].data}</div>
                <div className="text-xs text-gray-500 mt-1">
                  {message.timestamp.toLocaleTimeString()}
                </div>
              </div>
            ))}
          </div>
        </ScrollArea>
        <div className="flex items-center">
          <Input
            type="text"
            placeholder="Type your message..."
            value={input}
            onChange={(e) => setInput(e.target.value)}
            onKeyDown={(e) => {
              if (e.key === 'Enter') {
                handleSend();
              }
            }}
            className="flex-grow mr-2"
          />
          <Button onClick={handleSend}><Send className="h-4 w-4 mr-2" />Send</Button>
        </div>
      </CardContent>
    </Card>
  );
};
