
import React, { useState, useEffect } from 'react';
import { Layout } from '@/components/Layout';
import { BlogSidebar } from '@/components/BlogSidebar';
import { BlogPost } from '@/data/blogPosts';
import { ArrowLeft, Share2, Facebook, Twitter, Linkedin, Link2 } from 'lucide-react';
import { Link } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Helmet } from 'react-helmet-async';
import { toast } from "@/components/ui/use-toast";

interface BlogArticleTemplateProps {
  post: BlogPost;
  relatedPosts: BlogPost[];
  children: React.ReactNode;
}

export const BlogArticleTemplate: React.FC<BlogArticleTemplateProps> = ({ 
  post, 
  relatedPosts,
  children 
}) => {
  const [showFloatingShare, setShowFloatingShare] = useState(false);

  // Calculate estimated reading time
  const wordsPerMinute = 200;
  const text = React.Children.toArray(children)
    .filter(child => typeof child === 'string')
    .join(' ');
  const words = text.trim().split(/\s+/).length;
  const readingTime = Math.max(1, Math.ceil(words / wordsPerMinute));

  // Handle scroll to show/hide floating share buttons
  useEffect(() => {
    const handleScroll = () => {
      // Show floating share after scrolling past 300px instead of 400px to make it appear sooner
      if (window.scrollY > 300) {
        setShowFloatingShare(true);
      } else {
        setShowFloatingShare(false);
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const handleShare = (platform: string) => {
    const url = window.location.href;
    const title = post.title;
    
    let shareUrl = '';
    
    switch (platform) {
      case 'facebook':
        shareUrl = `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(url)}`;
        break;
      case 'twitter':
        shareUrl = `https://twitter.com/intent/tweet?text=${encodeURIComponent(title)}&url=${encodeURIComponent(url)}`;
        break;
      case 'linkedin':
        shareUrl = `https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(url)}`;
        break;
      case 'copy':
        navigator.clipboard.writeText(url)
          .then(() => toast({
            title: "Link copied!",
            description: "The article link has been copied to your clipboard",
            duration: 3000,
          }))
          .catch(err => console.error('Failed to copy: ', err));
        return;
      default:
        return;
    }
    
    window.open(shareUrl, '_blank', 'width=600,height=450');
  };

  return (
    <Layout>
      {/* SEO Optimization */}
      <Helmet>
        <title>{post.title} | Lovable Blog</title>
        <meta name="description" content={post.excerpt || `Read about ${post.title} and learn valuable insights about home services.`} />
        <meta property="og:title" content={post.title} />
        <meta property="og:description" content={post.excerpt || `Read about ${post.title} and learn valuable insights about home services.`} />
        <meta property="og:image" content={post.image} />
        <meta property="og:type" content="article" />
        <meta property="article:published_time" content={post.date} />
        <meta property="article:author" content={post.author} />
        <meta property="article:section" content={post.category} />
        <meta name="twitter:card" content="summary_large_image" />
      </Helmet>
      
      {/* Floating Share Buttons - Now with more visibility and different styling */}
      {showFloatingShare && (
        <div className="fixed left-4 top-1/3 z-40 flex flex-col gap-2 bg-white dark:bg-gray-800 p-3 rounded-full shadow-lg border-2 border-primary/20 animate-fade-in">
          <div className="text-xs font-semibold text-center text-gray-500 dark:text-gray-400 mb-1">Share</div>
          <button 
            onClick={() => handleShare('facebook')}
            className="p-2 rounded-full hover:bg-blue-50 text-blue-600 hover:scale-110 transition-all duration-200"
            aria-label="Share on Facebook"
          >
            <Facebook size={24} />
          </button>
          <button 
            onClick={() => handleShare('twitter')}
            className="p-2 rounded-full hover:bg-sky-50 text-sky-500 hover:scale-110 transition-all duration-200"
            aria-label="Share on Twitter"
          >
            <Twitter size={24} />
          </button>
          <button 
            onClick={() => handleShare('linkedin')}
            className="p-2 rounded-full hover:bg-blue-50 text-blue-700 hover:scale-110 transition-all duration-200"
            aria-label="Share on LinkedIn"
          >
            <Linkedin size={24} />
          </button>
          <button 
            onClick={() => handleShare('copy')}
            className="p-2 rounded-full hover:bg-gray-50 text-gray-700 hover:scale-110 transition-all duration-200"
            aria-label="Copy link"
          >
            <Link2 size={24} />
          </button>
        </div>
      )}
      
      <div className="py-14 bg-white dark:bg-gray-900">
        <div className="container mx-auto px-4 max-w-7xl">
          {/* Back to Blog Link */}
          <Link 
            to="/blog" 
            className="group inline-flex items-center gap-2 text-gray-600 dark:text-gray-400 hover:text-primary dark:hover:text-primary transition-colors mb-10"
          >
            <ArrowLeft size={18} className="transition-transform group-hover:-translate-x-1" />
            <span className="font-medium">Back to Blog</span>
          </Link>
          
          <div className="grid grid-cols-1 lg:grid-cols-12 gap-12">
            {/* Main Content */}
            <div className="lg:col-span-8">
              <article className="prose prose-lg dark:prose-invert max-w-none">
                {/* Category Badge */}
                <div className="not-prose mb-6">
                  <span className="inline-block py-1 px-3 rounded-full bg-primary/10 text-primary font-medium text-sm">
                    {post.category}
                  </span>
                </div>
                
                {/* Article Header - Removed date, author, and comments */}
                <header className="mb-8">
                  <h1 className="text-3xl md:text-4xl lg:text-5xl font-bold mb-6 leading-tight text-gray-900 dark:text-white">
                    {post.title}
                  </h1>
                  
                  <div className="flex flex-wrap items-center text-sm text-gray-600 dark:text-gray-400 pb-6 border-b border-gray-100 dark:border-gray-800">
                    <span className="text-gray-500 dark:text-gray-400">
                      {readingTime} min read
                    </span>
                  </div>
                </header>
                
                {/* Featured Image */}
                <figure className="not-prose mb-10">
                  <img 
                    src={post.image} 
                    alt={post.title}
                    className="w-full h-auto rounded-xl object-cover shadow-sm"
                    style={{ maxHeight: '600px' }}
                  />
                </figure>
                
                {/* Social Sharing Buttons */}
                <div className="not-prose mb-10 p-6 bg-gray-50 dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 relative overflow-hidden">
                  <div className="absolute top-0 left-0 w-1 h-full bg-primary"></div>
                  <div className="flex items-center justify-between flex-wrap gap-4">
                    <div className="text-base font-medium text-gray-700 dark:text-gray-300 flex items-center">
                      <Share2 size={20} className="mr-2 text-primary" /> 
                      <span>Share this article:</span>
                    </div>
                    <div className="flex flex-wrap gap-2">
                      <Button
                        onClick={() => handleShare('facebook')}
                        variant="outline"
                        size="lg"
                        className="bg-white dark:bg-transparent hover:bg-blue-50 text-blue-600 border-blue-200 flex items-center gap-2"
                      >
                        <Facebook size={18} /> Facebook
                      </Button>
                      <Button
                        onClick={() => handleShare('twitter')}
                        variant="outline"
                        size="lg"
                        className="bg-white dark:bg-transparent hover:bg-sky-50 text-sky-500 border-sky-200 flex items-center gap-2"
                      >
                        <Twitter size={18} /> Twitter
                      </Button>
                      <Button
                        onClick={() => handleShare('linkedin')}
                        variant="outline"
                        size="lg"
                        className="bg-white dark:bg-transparent hover:bg-blue-50 text-blue-700 border-blue-200 flex items-center gap-2"
                      >
                        <Linkedin size={18} /> LinkedIn
                      </Button>
                      <Button
                        onClick={() => handleShare('copy')}
                        variant="outline"
                        size="lg"
                        className="bg-white dark:bg-transparent hover:bg-gray-50 text-gray-700 border-gray-200 flex items-center gap-2"
                      >
                        <Link2 size={18} /> Copy Link
                      </Button>
                    </div>
                  </div>
                </div>
                
                {/* Article Content - Children will be rendered here */}
                <div className="mt-10 space-y-6">
                  {children}
                </div>
              </article>
              
              {/* Author Bio - Kept but with only role without personal details */}
              <div className="mt-16 p-8 bg-gray-50 dark:bg-gray-800 rounded-xl shadow-sm">
                <h3 className="text-xl font-bold mb-4">About the Author</h3>
                <div>
                  <p className="text-gray-600 dark:text-gray-400 leading-relaxed">
                    Home service expert with over 10 years of experience in helping homeowners find quality professionals
                    for their home improvement and maintenance needs. Passionate about creating better living spaces and
                    empowering homeowners with knowledge.
                  </p>
                </div>
              </div>
              
              {/* Article Tags */}
              <div className="mt-10 pt-6 border-t border-gray-100 dark:border-gray-800">
                <div className="flex flex-wrap gap-2">
                  {['Home Services', 'Professional Tips', 'Homeowner Guide'].map(tag => (
                    <span key={tag} className="px-3 py-1 bg-gray-100 dark:bg-gray-800 rounded-full text-sm text-gray-700 dark:text-gray-300">
                      {tag}
                    </span>
                  ))}
                </div>
              </div>
              
              {/* Bottom Share Buttons (Mobile Friendly) */}
              <div className="mt-12 mb-8 p-5 bg-gray-50 dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
                <h4 className="text-lg font-medium mb-4 flex items-center"><Share2 className="mr-2 text-primary" /> Share this article</h4>
                <div className="grid grid-cols-2 gap-3">
                  <Button
                    onClick={() => handleShare('facebook')}
                    variant="outline"
                    size="lg"
                    className="bg-white dark:bg-transparent hover:bg-blue-50 text-blue-600 border-blue-200"
                  >
                    <Facebook size={18} className="mr-1" /> Facebook
                  </Button>
                  <Button
                    onClick={() => handleShare('twitter')}
                    variant="outline"
                    size="lg"
                    className="bg-white dark:bg-transparent hover:bg-sky-50 text-sky-500 border-sky-200"
                  >
                    <Twitter size={18} className="mr-1" /> Twitter
                  </Button>
                  <Button
                    onClick={() => handleShare('linkedin')}
                    variant="outline"
                    size="lg"
                    className="bg-white dark:bg-transparent hover:bg-blue-50 text-blue-700 border-blue-200"
                  >
                    <Linkedin size={18} className="mr-1" /> LinkedIn
                  </Button>
                  <Button
                    onClick={() => handleShare('copy')}
                    variant="outline"
                    size="lg"
                    className="bg-white dark:bg-transparent hover:bg-gray-50 text-gray-700 border-gray-200"
                  >
                    <Link2 size={18} className="mr-1" /> Copy Link
                  </Button>
                </div>
              </div>
              
              {/* Comments Section - Removed as requested */}
            </div>
            
            {/* Sidebar */}
            <div className="lg:col-span-4">
              <div className="sticky top-24">
                <BlogSidebar relatedPosts={relatedPosts} />
              </div>
            </div>
          </div>
        </div>
      </div>
    </Layout>
  );
};
