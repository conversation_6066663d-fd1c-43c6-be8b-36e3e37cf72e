
import React, { useState, useEffect } from 'react';
import { MapPin, ChevronDown, Search, Filter } from 'lucide-react';
import { Button } from './ui/button';
import { Input } from './ui/input';
import { useIsMobile } from '@/hooks/use-mobile';
import { useGeolocation } from '@/hooks/use-geolocation';

interface StickySearchBarProps {
  onFilterClick: () => void;
  onSearch: (zipCode: string, service: string) => void;
  initialZipCode?: string;
  initialService?: string;
}

export const StickySearchBar = ({ 
  onFilterClick, 
  onSearch,
  initialZipCode = '', 
  initialService = 'All Services'
}: StickySearchBarProps) => {
  const [zipCode, setZipCode] = useState(initialZipCode);
  const [service, setService] = useState(initialService);
  const [isScrolled, setIsScrolled] = useState(false);
  const isMobile = useIsMobile();
  const { zipCode: detectedZipCode, loading } = useGeolocation();

  // Set detected zipcode when available
  useEffect(() => {
    if (detectedZipCode && !zipCode) {
      console.log("Using detected zipcode in sticky bar:", detectedZipCode);
      setZipCode(detectedZipCode);
    }
  }, [detectedZipCode, zipCode]);

  // Handle scroll effect for shadow
  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 50);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const handleSearchClick = () => {
    onSearch(zipCode, service);
  };

  return (
    <div className={`sticky top-0 z-30 bg-white dark:bg-gray-900 py-2 border-b border-gray-100 dark:border-gray-800 transition-shadow duration-300 ${
      isScrolled ? 'shadow-md' : ''
    }`}>
      <div className="container mx-auto px-4">
        <div className="flex flex-col md:flex-row gap-2">
          <div className="relative flex-1">
            <div className="relative flex items-center bg-gray-100 dark:bg-gray-800 rounded-lg p-1">
              <div className="relative min-w-[120px] md:min-w-[180px]">
                <select 
                  className="appearance-none bg-transparent pl-2 md:pl-3 pr-6 md:pr-8 py-1.5 md:py-2 w-full focus:outline-none text-sm md:text-base"
                  value={service}
                  onChange={(e) => setService(e.target.value)}
                >
                  <option>All Services</option>
                  <option>Plumbing</option>
                  <option>Electrical</option>
                  <option>HVAC</option>
                  <option>Handyman</option>
                  <option>Landscaping</option>
                  <option>Roofing</option>
                  <option>Solar</option>
                  <option>Pest Control</option>
                </select>
                <ChevronDown className="absolute right-1 md:right-2 top-1/2 transform -translate-y-1/2 h-3 md:h-4 w-3 md:w-4 text-gray-500 pointer-events-none" />
              </div>
              
              <div className="h-6 w-px bg-gray-300 dark:bg-gray-600 mx-1" />
              
              <div className="flex-1 flex items-center pl-2">
                <MapPin className="h-3 md:h-4 w-3 md:w-4 text-gray-500 mr-1" />
                <Input
                  type="text"
                  placeholder={loading ? "Detecting location..." : "ZIP code"}
                  value={zipCode}
                  onChange={(e) => setZipCode(e.target.value)}
                  className="border-0 bg-transparent focus-visible:ring-0 focus-visible:ring-offset-0 p-0 h-8 md:h-9 placeholder:text-gray-500 text-sm md:text-base"
                />
              </div>
              
              <Button 
                variant="default" 
                className="ml-1 md:ml-2 bg-primary hover:bg-primary/90" 
                size={isMobile ? "sm" : "default"}
                onClick={handleSearchClick}
              >
                <Search className="h-3 md:h-4 w-3 md:w-4 mr-1" />
                <span className="text-xs md:text-sm">Search</span>
              </Button>
            </div>
          </div>
          
          {isMobile && (
            <Button 
              variant="outline" 
              size="icon" 
              onClick={onFilterClick}
              className="ml-auto h-8 w-8 md:h-10 md:w-10 rounded-full"
            >
              <Filter className="h-4 md:h-5 w-4 md:w-5" />
            </Button>
          )}
        </div>
      </div>
    </div>
  );
};
