
import React from 'react';
import { Shield, Award, CheckCircle, User, Clock, ThumbsUp } from 'lucide-react';
import { cn } from '@/lib/utils';
import { TestimonialsCarousel } from './TestimonialsCarousel';
import {useIsMobile} from "@/hooks/use-mobile.tsx";

interface TrustAuthorityProps {
  className?: string;
}

export const TrustAuthority: React.FC<TrustAuthorityProps> = ({ className }) => {
  const trustBadges = [
    {
      icon: <Shield className="h-8 w-8 text-primary" />,
      title: "Verified Professionals",
      description: "All landscapers pass our rigorous screening process",
      badgeClass: "trusted"
    },
    {
      icon: <Award className="h-8 w-8 text-primary" />,
      title: "Satisfaction Guarantee",
      description: "We stand behind the quality of every job",
      badgeClass: "satisfaction"
    },
    {
      icon: <CheckCircle className="h-8 w-8 text-primary" />,
      title: "Insured & Bonded",
      description: "Every professional carries proper insurance",
      badgeClass: "insured"
    }
  ];

  const testimonials = [
    {
      quote: "The landscaper I hired through JobON transformed my backyard completely. Professional, on-time, and exceeded expectations.",
      author: "<PERSON>",
      location: "Hayward, CA",
      rating: 5,
      image: "https://images.unsplash.com/photo-1494790108377-be9c29b29330?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=256&q=80"
    },
    {
      quote: "JobON connected me with a landscaper who understood exactly what I wanted. Fair pricing and excellent communication throughout.",
      author: "Michael D.",
      location: "Oakland, CA",
      rating: 5,
      image: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=256&q=80"
    }
  ];

  const humanTouchItems = [
    {
      icon: <User className="h-6 w-6 text-primary" />,
      title: "Personal Support",
      description: "Get matched with the perfect professional for your specific needs",
      badgeClass: "support"
    },
    {
      icon: <Clock className="h-6 w-6 text-primary" />,
      title: "Quick Response",
      description: "Most landscapers respond within 24 hours of your request",
      badgeClass: "response"
    },
    {
      icon: <ThumbsUp className="h-6 w-6 text-primary" />,
      title: "Real Customer Experiences",
      description: "Authentic reviews from real customers in your neighborhood",
      badgeClass: "pros"
    }
  ];
  const isMobile = useIsMobile();

  // Mobile-optimized badges for homepage
  const mobileTrustBadges = [
    {
      icon: <Shield className="h-6 w-6 text-blue-600" />,
      title: "Verified Pros",
      value: "10K+",
      bgColor: "bg-blue-50 dark:bg-blue-950/30",
      borderColor: "border-blue-100 dark:border-blue-900"
    },
    {
      icon: <Award className="h-6 w-6 text-green-600" />,
      title: "Satisfied Customers",
      value: "50K+",
      bgColor: "bg-green-50 dark:bg-green-950/30",
      borderColor: "border-green-100 dark:border-green-900"
    },
    {
      icon: <CheckCircle className="h-6 w-6 text-purple-600" />,
      title: "Jobs Completed",
      value: "100K+",
      bgColor: "bg-purple-50 dark:bg-purple-950/30",
      borderColor: "border-purple-100 dark:border-purple-900"
    }
  ];

  return (
    <div className={cn("py-10 px-0 md:px-10 flex flex-col gap-10 bg-background", className)}>
      {/* Mobile Trust Badges Section - Only shown on mobile */}
      {isMobile && (
        <section className="px-4">
          <div className="text-center mb-6">
            <h2 className="text-xl font-bold mb-2">Trusted by Thousands</h2>
            <p className="text-sm text-muted-foreground">
              Join our growing community of satisfied customers
            </p>
          </div>
          
          <div className="grid grid-cols-3 gap-3">
            {mobileTrustBadges.map((badge, index) => (
              <div 
                key={index} 
                className={cn(
                  "rounded-xl p-4 text-center border",
                  badge.bgColor,
                  badge.borderColor
                )}
              >
                <div className="flex justify-center mb-2">
                  {badge.icon}
                </div>
                <div className="text-lg font-bold text-gray-900 dark:text-white mb-1">
                  {badge.value}
                </div>
                <p className="text-xs text-gray-600 dark:text-gray-400 leading-tight">
                  {badge.title}
                </p>
              </div>
            ))}
          </div>
        </section>
      )}

      {/* Authority Badges Section */}
      <section className="mb-4 hidden md:block">
        <div className="text-center mb-8">
          <h2 className="text-2xl md:text-3xl font-bold mb-2">Why Choose Our Landscaping Professionals</h2>
          <p className="text-muted-foreground max-w-2xl mx-auto">
            We carefully select and verify every professional to ensure you receive the highest quality service
          </p>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {trustBadges.map((badge, index) => (
            <div 
              key={index} 
              className="flex flex-col items-center text-center p-6 border rounded-lg shadow-sm bg-white dark:bg-gray-800 hover:shadow-md transition-shadow"
            >
              <div className={`mb-4 p-3 bg-primary/10 rounded-full trust-badge-icon ${badge.badgeClass}`}>
                {badge.icon}
              </div>
              <h3 className="font-semibold text-lg mb-2 trust-badge-text">{badge.title}</h3>
              <p className="text-muted-foreground">{badge.description}</p>
            </div>
          ))}
        </div>
      </section>

      {/* Human Touch Section */}
      <section className="mb-8 bg-gray-50 dark:bg-gray-900 p-6 rounded-xl hidden md:block">
        <div className="text-center mb-8">
          <h2 className="text-2xl md:text-3xl font-bold mb-2">The JobON Difference</h2>
          <p className="text-muted-foreground max-w-2xl mx-auto">
            We believe in connecting you with real people who care about your home as much as you do
          </p>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {humanTouchItems.map((item, index) => (
            <div key={index} className="flex flex-col md:flex-row items-center md:items-start gap-4 p-4">
              <div className={`flex-shrink-0 p-2 bg-primary/10 rounded-full trust-badge-icon ${item.badgeClass}`}>
                {item.icon}
              </div>
              <div className="text-center md:text-left">
                <h3 className="font-semibold mb-1 trust-badge-text">{item.title}</h3>
                <p className="text-muted-foreground text-sm">{item.description}</p>
              </div>
            </div>
          ))}
        </div>
      </section>

      {/* Real Customer Testimonials */}
      <section className="mb-4 hidden md:block">
        <div className="text-center mb-8">
          <h2 className="text-2xl md:text-3xl font-bold mb-2">Hear From Our Customers</h2>
          <p className="text-muted-foreground max-w-2xl mx-auto">
            Real testimonials from satisfied homeowners in your area
          </p>
        </div>

        {/* Add the TestimonialsCarousel component here */}
        <TestimonialsCarousel />
      </section>

      {
        isMobile &&
        <section className="mb-4">
          <div className="text-center mb-8">
            <h2 className="text-2xl md:text-3xl font-bold mb-2">Real Users. Real Savings.</h2>
            <p className="text-muted-foreground max-w-2xl mx-auto">
              See what our customers are saying about their experience
            </p>
          </div>

          {/* Add the TestimonialsCarousel component here */}
          <TestimonialsCarousel />
        </section>
      }
      
      {/* Trust Seal Footer - Enhanced for better dark mode visibility */}
      <section className="border-t pt-6 hidden md:block">
        <div className="flex flex-wrap justify-center items-center gap-8">
          <div className="flex items-center gap-2 trust-badge-item">
            <Shield className="h-5 w-5 text-primary" />
            <span className="text-sm font-medium trust-badge-text">Secure Payments</span>
          </div>
          <div className="flex items-center gap-2 trust-badge-item">
            <Award className="h-5 w-5 text-primary" />
            <span className="text-sm font-medium trust-badge-text">Quality Guaranteed</span>
          </div>
          <div className="flex items-center gap-2 trust-badge-item">
            <CheckCircle className="h-5 w-5 text-primary" />
            <span className="text-sm font-medium trust-badge-text">Vetted Professionals</span>
          </div>
        </div>
      </section>
    </div>
  );
};
