import React, { useState, useEffect, useCallback } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { ScrollArea } from '@/components/ui/scroll-area';
import { useToast } from '@/hooks/use-toast';
import { getProviderBids, getJobBids } from '@/services/bidService';
import { Bid } from '@/types/bid';
import { useAuth } from '@/features/auth/hooks/useAuth';
import {
  Bell,
  BellRing,
  Check,
  X,
  Eye,
  Clock,
  DollarSign,
  User,
  Loader2,
  MoreHorizontal
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';

interface BidNotification {
  id: string;
  type: 'new_bid' | 'bid_accepted' | 'bid_rejected' | 'bid_withdrawn';
  bid: Bid;
  timestamp: string;
  read: boolean;
}

interface BidNotificationsProps {
  userRole: 'customer' | 'provider' | 'admin';
  userId?: string;
  onBidSelect?: (bid: Bid) => void;
  className?: string;
}

export const BidNotifications: React.FC<BidNotificationsProps> = ({
  userRole,
  userId,
  onBidSelect,
  className
}) => {
  const [notifications, setNotifications] = useState<BidNotification[]>([]);
  const [loading, setLoading] = useState(false);
  const [unreadCount, setUnreadCount] = useState(0);
  const [isOpen, setIsOpen] = useState(false);
  const { toast } = useToast();
  const { user: currentUser } = useAuth();
  const effectiveUserId = userId || currentUser?.id;

  // Simulate fetching notifications based on recent bid activity
  const fetchNotifications = useCallback(async () => {
    if (!effectiveUserId) return;
    
    setLoading(true);
    try {
      let bids: Bid[] = [];
      
      if (userRole === 'provider') {
        // Get provider's bids to check for status changes
        const response = await getProviderBids(effectiveUserId, {}, { field: 'updatedAt', direction: 'desc' }, 1, 20);
        if (response.isSuccess && response.data) {
          bids = response.data.bids;
        }
      } else if (userRole === 'customer') {
        // For customers, we would need to get bids for their jobs
        // This would require a different API endpoint or job IDs
        // For now, we'll simulate with empty array
        bids = [];
      }

      // Convert bids to notifications (simplified simulation)
      const mockNotifications: BidNotification[] = bids.map((bid, index) => {
        let type: BidNotification['type'] = 'new_bid';
        
        if (bid.status === 'accepted') type = 'bid_accepted';
        else if (bid.status === 'rejected') type = 'bid_rejected';
        else if (bid.status === 'withdrawn') type = 'bid_withdrawn';
        
        return {
          id: `notification-${bid.id}-${index}`,
          type,
          bid,
          timestamp: bid.updatedAt,
          read: Math.random() > 0.3 // Simulate some read/unread notifications
        };
      });

      setNotifications(mockNotifications);
      setUnreadCount(mockNotifications.filter(n => !n.read).length);
    } catch (error) {
      console.error('Failed to fetch notifications:', error);
    } finally {
      setLoading(false);
    }
  }, [effectiveUserId, userRole]);

  useEffect(() => {
    fetchNotifications();
    
    // Set up polling for new notifications
    const interval = setInterval(fetchNotifications, 30000); // Poll every 30 seconds
    
    return () => clearInterval(interval);
  }, [fetchNotifications]);

  const markAsRead = (notificationId: string) => {
    setNotifications(prev => 
      prev.map(notification => 
        notification.id === notificationId 
          ? { ...notification, read: true }
          : notification
      )
    );
    setUnreadCount(prev => Math.max(0, prev - 1));
  };

  const markAllAsRead = () => {
    setNotifications(prev => 
      prev.map(notification => ({ ...notification, read: true }))
    );
    setUnreadCount(0);
  };

  const deleteNotification = (notificationId: string) => {
    setNotifications(prev => {
      const notification = prev.find(n => n.id === notificationId);
      const filtered = prev.filter(n => n.id !== notificationId);
      
      if (notification && !notification.read) {
        setUnreadCount(count => Math.max(0, count - 1));
      }
      
      return filtered;
    });
  };

  const getNotificationIcon = (type: BidNotification['type']) => {
    switch (type) {
      case 'new_bid':
        return <DollarSign className="h-4 w-4 text-blue-600" />;
      case 'bid_accepted':
        return <Check className="h-4 w-4 text-green-600" />;
      case 'bid_rejected':
        return <X className="h-4 w-4 text-red-600" />;
      case 'bid_withdrawn':
        return <Clock className="h-4 w-4 text-gray-600" />;
      default:
        return <Bell className="h-4 w-4" />;
    }
  };

  const getNotificationMessage = (notification: BidNotification): string => {
    const { type, bid } = notification;
    const providerName = bid.provider ? `${bid.provider.firstName} ${bid.provider.lastName}` : 'A provider';
    const jobTitle = bid.job?.title || 'a job';
    
    switch (type) {
      case 'new_bid':
        return userRole === 'customer' 
          ? `${providerName} submitted a bid for "${jobTitle}"`
          : `You submitted a bid for "${jobTitle}"`;
      case 'bid_accepted':
        return userRole === 'provider'
          ? `Your bid for "${jobTitle}" was accepted`
          : `You accepted ${providerName}'s bid for "${jobTitle}"`;
      case 'bid_rejected':
        return userRole === 'provider'
          ? `Your bid for "${jobTitle}" was rejected`
          : `You rejected ${providerName}'s bid for "${jobTitle}"`;
      case 'bid_withdrawn':
        return userRole === 'customer'
          ? `${providerName} withdrew their bid for "${jobTitle}"`
          : `You withdrew your bid for "${jobTitle}"`;
      default:
        return 'Bid notification';
    }
  };

  const formatTimeAgo = (timestamp: string): string => {
    const now = new Date();
    const time = new Date(timestamp);
    const diffInMinutes = Math.floor((now.getTime() - time.getTime()) / (1000 * 60));
    
    if (diffInMinutes < 1) return 'Just now';
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
    if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}h ago`;
    return `${Math.floor(diffInMinutes / 1440)}d ago`;
  };

  const formatCurrency = (amount: number): string => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  return (
    <Popover open={isOpen} onOpenChange={setIsOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="ghost"
          size="sm"
          className={`relative ${className}`}
        >
          {unreadCount > 0 ? (
            <BellRing className="h-5 w-5" />
          ) : (
            <Bell className="h-5 w-5" />
          )}
          {unreadCount > 0 && (
            <Badge 
              variant="destructive" 
              className="absolute -top-1 -right-1 h-5 w-5 flex items-center justify-center p-0 text-xs"
            >
              {unreadCount > 99 ? '99+' : unreadCount}
            </Badge>
          )}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-96 p-0" align="end">
        <Card className="border-0 shadow-lg">
          <CardHeader className="pb-3">
            <div className="flex items-center justify-between">
              <CardTitle className="text-lg">Bid Notifications</CardTitle>
              {unreadCount > 0 && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={markAllAsRead}
                  className="text-xs"
                >
                  Mark all read
                </Button>
              )}
            </div>
          </CardHeader>
          <CardContent className="p-0">
            <ScrollArea className="h-96">
              {loading ? (
                <div className="flex justify-center items-center h-32">
                  <Loader2 className="h-6 w-6 animate-spin" />
                </div>
              ) : notifications.length === 0 ? (
                <div className="text-center py-8 px-4">
                  <Bell className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                  <p className="text-sm text-gray-500">No notifications yet</p>
                </div>
              ) : (
                <div className="space-y-1">
                  {notifications.map((notification) => (
                    <div
                      key={notification.id}
                      className={`p-4 border-b border-gray-100 hover:bg-gray-50 transition-colors ${
                        !notification.read ? 'bg-blue-50' : ''
                      }`}
                    >
                      <div className="flex items-start gap-3">
                        <div className="flex-shrink-0 mt-1">
                          {getNotificationIcon(notification.type)}
                        </div>
                        
                        <div className="flex-1 min-w-0">
                          <div className="flex items-start justify-between gap-2">
                            <div className="flex-1">
                              <p className={`text-sm ${
                                !notification.read ? 'font-medium' : 'text-gray-700'
                              }`}>
                                {getNotificationMessage(notification)}
                              </p>
                              <div className="flex items-center gap-2 mt-1">
                                <span className="text-xs text-gray-500">
                                  {formatTimeAgo(notification.timestamp)}
                                </span>
                                <span className="text-xs font-medium text-green-600">
                                  {formatCurrency(notification.bid.amount)}
                                </span>
                              </div>
                            </div>
                            
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button variant="ghost" size="sm" className="h-6 w-6 p-0">
                                  <MoreHorizontal className="h-4 w-4" />
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="end">
                                {onBidSelect && (
                                  <DropdownMenuItem
                                    onClick={() => {
                                      onBidSelect(notification.bid);
                                      markAsRead(notification.id);
                                      setIsOpen(false);
                                    }}
                                  >
                                    <Eye className="h-4 w-4 mr-2" />
                                    View Details
                                  </DropdownMenuItem>
                                )}
                                {!notification.read && (
                                  <DropdownMenuItem
                                    onClick={() => markAsRead(notification.id)}
                                  >
                                    <Check className="h-4 w-4 mr-2" />
                                    Mark as Read
                                  </DropdownMenuItem>
                                )}
                                <DropdownMenuItem
                                  onClick={() => deleteNotification(notification.id)}
                                  className="text-red-600"
                                >
                                  <X className="h-4 w-4 mr-2" />
                                  Delete
                                </DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </div>
                          
                          {/* Provider/Customer info */}
                          {notification.bid.provider && userRole === 'customer' && (
                            <div className="flex items-center gap-2 mt-2">
                              <Avatar className="h-6 w-6">
                                <AvatarImage src={notification.bid.provider.avatar} />
                                <AvatarFallback>
                                  <User className="h-3 w-3" />
                                </AvatarFallback>
                              </Avatar>
                              <span className="text-xs text-gray-600">
                                {notification.bid.provider.firstName} {notification.bid.provider.lastName}
                              </span>
                              {notification.bid.provider.rating && (
                                <span className="text-xs text-yellow-600">
                                  ★ {notification.bid.provider.rating.toFixed(1)}
                                </span>
                              )}
                            </div>
                          )}
                        </div>
                        
                        {!notification.read && (
                          <div className="flex-shrink-0">
                            <div className="w-2 h-2 bg-blue-600 rounded-full"></div>
                          </div>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </ScrollArea>
            
            {notifications.length > 0 && (
              <div className="p-3 border-t border-gray-100">
                <Button
                  variant="ghost"
                  size="sm"
                  className="w-full text-xs"
                  onClick={() => setIsOpen(false)}
                >
                  View All Notifications
                </Button>
              </div>
            )}
          </CardContent>
        </Card>
      </PopoverContent>
    </Popover>
  );
};