
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, Lock, ArrowRight } from "lucide-react";
import { <PERSON><PERSON> } from "./button";
import { Badge } from "./badge";
import { ProjectCarousel } from "./project-carousel";
import { cn } from "@/lib/utils";
import { Rating } from '@smastrom/react-rating'
import '@smastrom/react-rating/style.css'
import {DataType, ProviderData} from "@/types/common.ts";
import { useIsMobile } from "@/hooks/use-mobile";
import { MobileProviderCard } from "@/components/mobile/MobileProviderCard";

interface ProviderCardProps {
  provider: DataType;
  onViewProfile: () => void;
  className?: string;
}

export function ProviderCard({
  provider,
  onViewProfile,
  className,
}: ProviderCardProps) {
  const isMobile = useIsMobile();

  if (isMobile) {
    return (
      <MobileProviderCard
        provider={provider}
        onViewProfile={onViewProfile}
        className={className}
      />
    );
  }

  function calculateAverageRating(reviews?: Array<{ rating: string }>): number {
    if (!reviews || reviews.length === 0) return 0;

    const sum = reviews.reduce((total, review) => {
      // Handle case where rating might be missing or invalid
      const rating = review?.rating ? parseInt(review.rating) : 0;
      return total + rating;
    }, 0);
    return parseFloat((sum / reviews.length).toFixed(1));
  }

  return (
      <div
          className={cn(
              "group relative bg-white dark:bg-gray-800 rounded-xl p-4",
              "border border-gray-200 dark:border-gray-700",
              "shadow-sm hover:shadow-md transition-shadow duration-200",
              "w-full flex gap-4",
              className
          )}
      >
        <div className="flex-grow flex gap-4">
          {/* Left Section - Avatar */}
          <div className="flex-shrink-0">
            <img
                src={"/images/avatar-default.svg"}
                alt={provider?.name}
                className="w-16 h-16 rounded-full object-cover border-2 border-gray-100"
            />
          </div>

          {/* Middle Section - Content */}
          <div className="flex-1 min-w-0">
            <div className="flex items-start justify-between mb-1">
              <h3 className="text-lg font-bold text-gray-900 dark:text-gray-100 leading-tight text-wrap">
                {provider?.name}
              </h3>
            </div>
            
            <p className="text-sm text-gray-600 dark:text-gray-400 mb-1">
              {`Licensed ${provider?.category} Technician`}
            </p>
            
            <p className="text-sm text-gray-600 dark:text-gray-400 mb-2 truncate">
              {provider.address ? provider?.address : provider?.location}
            </p>

            <div className="flex items-center mb-2">
              <div className="flex items-center text-amber-400">
                <Rating
                    style={{ maxWidth: 80 }}
                    value={calculateAverageRating(provider?.reviews)}
                    readOnly
                />
              </div>
              <span className="ml-2 font-medium text-sm">{calculateAverageRating(provider?.reviews)}</span>
              <span className="mx-1.5 text-gray-400">·</span>
              <span className="text-sm text-gray-600">{provider?.reviews?.length || 0} reviews</span>
            </div>

            {provider?.reviews?.length > 0 && provider?.reviews[0]?.text && (
                <blockquote className="text-sm text-gray-600 dark:text-gray-400 italic line-clamp-2">
                  "{provider?.reviews[0]?.text}"
                </blockquote>
            )}
          </div>

          {/* Right Section - Image and Button */}
          <div className="flex flex-col items-end gap-3 flex-shrink-0">
            {provider?.photos && provider.photos.length > 0 && (
                <ProjectCarousel images={provider.photos[0]} className="h-20 w-32 rounded-lg" />
            )}

            <Button
                onClick={onViewProfile}
                className="flex items-center gap-2 transition-all bg-[#2263eb] hover:bg-[#2263eb]/90 h-9 px-4 text-white text-sm"
                type={"button"}
            >
              View Profile
              <ArrowRight className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>
  );
}
