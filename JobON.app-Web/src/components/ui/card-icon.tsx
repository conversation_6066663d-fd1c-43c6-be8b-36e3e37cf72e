
import React from 'react';
import { cn } from "@/lib/utils";

interface CardIconProps extends React.HTMLAttributes<HTMLDivElement> {
  color?: string;
  children: React.ReactNode;
}

export function CardIcon({ color, children, className, ...props }: CardIconProps) {
  return (
    <div 
      className={cn(
        "flex items-center justify-center rounded-full p-2", 
        color || "bg-primary/10", 
        className
      )} 
      {...props}
    >
      {children}
    </div>
  );
}
