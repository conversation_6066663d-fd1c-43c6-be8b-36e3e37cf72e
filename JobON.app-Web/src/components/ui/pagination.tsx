
import * as React from "react"
import { ChevronLeft, ChevronRight, MoreHorizontal } from "lucide-react"
import { cn } from "@/lib/utils"
import { ButtonProps, buttonVariants } from "@/components/ui/button"

// Define the total number of pages to show before using ellipsis
const SIBLING_COUNT = 1;
const BOUNDARY_COUNT = 1;

// Helper to generate page numbers array
const range = (start: number, end: number) => {
  const length = end - start + 1;
  return Array.from({ length }, (_, i) => start + i);
};

const Pagination = ({
  className,
  totalItems,
  itemsPerPage = 10,
  currentPage = 1,
  onPageChange,
  ...props
}: React.ComponentProps<"nav"> & {
  totalItems: number;
  itemsPerPage?: number;
  currentPage?: number;
  onPageChange: (page: number) => void;
}) => {
  const totalPages = Math.ceil(totalItems / itemsPerPage);

  // Generate page numbers to display
  const getPageNumbers = () => {
    // If there are 7 or fewer pages, show all
    if (totalPages <= 7) {
      return range(1, totalPages);
    }

    // Calculate start and end pages
    const startPages = range(1, BOUNDARY_COUNT);
    const endPages = range(totalPages - BOUNDARY_COUNT + 1, totalPages);
    
    const siblingStart = Math.max(
      currentPage - SIBLING_COUNT,
      BOUNDARY_COUNT + 1
    );
    
    const siblingEnd = Math.min(
      currentPage + SIBLING_COUNT,
      totalPages - BOUNDARY_COUNT
    );

    // Determine if we need ellipsis at start and end
    const showStartEllipsis = siblingStart > BOUNDARY_COUNT + 1;
    const showEndEllipsis = siblingEnd < totalPages - BOUNDARY_COUNT;

    // Generate the final array
    const itemList = [];

    // Add start pages
    itemList.push(...startPages);
    
    // Add start ellipsis if needed
    if (showStartEllipsis) {
      itemList.push(-1); // Use -1 to represent ellipsis
    } else if (BOUNDARY_COUNT < siblingStart - 1) {
      itemList.push(BOUNDARY_COUNT + 1);
    }
    
    // Add sibling pages around current
    itemList.push(...range(siblingStart, siblingEnd));
    
    // Add end ellipsis if needed
    if (showEndEllipsis) {
      itemList.push(-2); // Use -2 to represent ellipsis
    } else if (siblingEnd + 1 < totalPages - BOUNDARY_COUNT + 1) {
      itemList.push(siblingEnd + 1);
    }
    
    // Add end pages
    itemList.push(...endPages);
    
    return itemList;
  };

  const pages = getPageNumbers();

  return (
    <nav
      role="navigation"
      aria-label="pagination"
      className={cn("mx-auto pb-4 flex w-full justify-center", className)}
      {...props}
    >
      <PaginationContent>
        <PaginationItem>
          <PaginationPrevious 
            href="#" 
            onClick={(e) => {
              e.preventDefault();
              if (currentPage > 1) {
                onPageChange(currentPage - 1);
              }
            }}
            aria-disabled={currentPage === 1}
            tabIndex={currentPage === 1 ? -1 : undefined}
            className={currentPage === 1 ? "pointer-events-none opacity-50" : ""}
          />
        </PaginationItem>

        {pages.map((page, index) => {
          // Render ellipsis
          if (page < 0) {
            return (
              <PaginationItem key={`ellipsis-${index}`}>
                <div className="flex h-9 w-9 items-center justify-center">
                  <MoreHorizontal className="h-4 w-4" />
                </div>
              </PaginationItem>
            );
          }

          // Render page number
          return (
            <PaginationItem key={`page-${page}`}>
              <PaginationLink
                href="#"
                onClick={(e) => {
                  e.preventDefault();
                  onPageChange(page);
                }}
                isActive={page === currentPage}
                aria-current={page === currentPage ? "page" : undefined}
              >
                {page}
              </PaginationLink>
            </PaginationItem>
          );
        })}

        <PaginationItem>
          <PaginationNext
            href="#"
            onClick={(e) => {
              e.preventDefault();
              if (currentPage < totalPages) {
                onPageChange(currentPage + 1);
              }
            }}
            aria-disabled={currentPage === totalPages}
            tabIndex={currentPage === totalPages ? -1 : undefined}
            className={currentPage === totalPages ? "pointer-events-none opacity-50" : ""}
          />
        </PaginationItem>
      </PaginationContent>
    </nav>
  );
};

const PaginationContent = React.forwardRef<
  HTMLUListElement,
  React.ComponentProps<"ul">
>(({ className, ...props }, ref) => (
  <ul
    ref={ref}
    className={cn("flex flex-row items-center gap-1", className)}
    {...props}
  />
));
PaginationContent.displayName = "PaginationContent";

const PaginationItem = React.forwardRef<
  HTMLLIElement,
  React.ComponentProps<"li">
>(({ className, ...props }, ref) => (
  <li ref={ref} className={cn("", className)} {...props} />
));
PaginationItem.displayName = "PaginationItem";

type PaginationLinkProps = {
  isActive?: boolean;
} & React.ComponentProps<"a">;

const PaginationLink = React.forwardRef<HTMLAnchorElement, PaginationLinkProps>(
  ({ className, isActive, ...props }, ref) => (
    <a
      ref={ref}
      aria-current={isActive ? "page" : undefined}
      className={cn(
        buttonVariants({
          variant: isActive ? "default" : "ghost",
        }),
        "h-9 w-9 w-fit",
        className
      )}
      {...props}
    />
  )
);
PaginationLink.displayName = "PaginationLink";

type PaginationButtonProps = {
  isActive?: boolean;
} & ButtonProps;

const PaginationButton = React.forwardRef<HTMLButtonElement, PaginationButtonProps>(
  ({ className, isActive, ...props }, ref) => (
    <button
      ref={ref}
      className={cn(
        buttonVariants({
          variant: isActive ? "outline" : "ghost",
          size: "icon",
        }),
        "h-9 w-9",
        className
      )}
      {...props}
    />
  )
);
PaginationButton.displayName = "PaginationButton";

const PaginationPrevious = React.forwardRef<HTMLAnchorElement, PaginationLinkProps>(
  ({ className, ...props }, ref) => (
    <PaginationLink
      ref={ref}
      aria-label="Go to previous page"
      className={cn("gap-1 pr-2.5", className)}
      {...props}
    >
      <ChevronLeft className="h-4 w-4" />
      <span>Previous</span>
    </PaginationLink>
  )
);
PaginationPrevious.displayName = "PaginationPrevious";

const PaginationNext = React.forwardRef<HTMLAnchorElement, PaginationLinkProps>(
  ({ className, ...props }, ref) => (
    <PaginationLink
      ref={ref}
      aria-label="Go to next page"
      className={cn("gap-1 pl-2.5", className)}
      {...props}
    >
      <span>Next</span>
      <ChevronRight className="h-4 w-4 w-fit" />
    </PaginationLink>
  )
);
PaginationNext.displayName = "PaginationNext";

// Add PaginationEllipsis component that's missing
const PaginationEllipsis = ({
  className,
  ...props
}: React.HTMLAttributes<HTMLSpanElement>) => (
  <span
    className={cn("flex h-9 w-9 items-center justify-center", className)}
    {...props}
  >
    <MoreHorizontal className="h-4 w-4" />
    <span className="sr-only">More pages</span>
  </span>
);
PaginationEllipsis.displayName = "PaginationEllipsis";

export {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationButton,
  PaginationPrevious,
  PaginationNext,
};
