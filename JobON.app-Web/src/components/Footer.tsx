import React from 'react';
import { Link } from 'react-router-dom';
import { FacebookIcon, TwitterIcon, InstagramIcon, Globe } from 'lucide-react';
import ResourcesExpander from './ResourcesExpander';
const Footer = () => {
  return <footer className="bg-white dark:bg-gray-900 fixed bottom-0 left-0 right-0 z-50 h-16 border-t border-gray-200 dark:border-gray-800">
      <div className="container mx-auto px-4 py-3">
        <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
          <div className="flex flex-col md:flex-row md:items-center space-y-4 md:space-y-0">
            <p className="text-sm text-gray-600 dark:text-gray-400">
              © {new Date().getFullYear()} JobON. All rights reserved.
            </p>
            <div className="flex space-x-4 mt-2 md:mt-0 md:ml-8">
              <Link to="/terms" className="text-sm text-gray-600 dark:text-gray-400 hover:underline">Terms</Link>
              <Link to="/privacy" className="text-sm text-gray-600 dark:text-gray-400 hover:underline">Privacy</Link>
              <Link to="/sitemap" className="text-sm text-gray-600 dark:text-gray-400 hover:underline">Sitemap</Link>
            </div>
          </div>

          <div className="flex items-center space-x-4">
            <button className="flex items-center text-sm text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white">


            </button>

            <div className="relative">
              <ResourcesExpander />
            </div>

            <div className="flex space-x-4">
              <a href="https://facebook.com" className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300" target="_blank" rel="noopener noreferrer">
                <FacebookIcon className="h-5 w-5" />
              </a>
              <a href="https://twitter.com" className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300" target="_blank" rel="noopener noreferrer">
                <TwitterIcon className="h-5 w-5" />
              </a>
              <a href="https://instagram.com" className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300" target="_blank" rel="noopener noreferrer">
                <InstagramIcon className="h-5 w-5" />
              </a>
            </div>
          </div>
        </div>
      </div>
    </footer>;
};
export default Footer;
