
import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { BlogPost as WPBlogPost, fetchPopularPosts } from '@/services/wordpressApi';
import { Card, CardContent } from "@/components/ui/card";
import { Mail, Building, Loader2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';

// Extend the BlogPost type to include views
interface BlogPost extends WPBlogPost {
  views?: number;
}

interface BlogSidebarProps {
  relatedPosts: WPBlogPost[];
}

export const BlogSidebar: React.FC<BlogSidebarProps> = ({ relatedPosts }) => {
  const [popularPosts, setPopularPosts] = useState<BlogPost[]>([]);
  const [loadingPopular, setLoadingPopular] = useState(true);
  const [errorPopular, setErrorPopular] = useState<string | null>(null);

  useEffect(() => {
    const loadPopularPosts = async () => {
      try {
        setLoadingPopular(true);
        setErrorPopular(null);
        const posts = await fetchPopularPosts(4);
        setPopularPosts(posts);
      } catch (err) {
        console.error('Error loading popular posts:', err);
        setErrorPopular('Failed to load popular articles');
      } finally {
        setLoadingPopular(false);
      }
    };

    loadPopularPosts();
  }, []);

  return (
    <div className="space-y-8 sticky top-20">
      {/* Newsletter Signup - Above Related Posts */}
      <Card>
        <CardContent className="p-5">
          <div className="text-center mb-3">
            <Mail className="inline-block mb-2" size={24} />
            <h3 className="text-lg font-semibold">Subscribe to Our Newsletter</h3>
            <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
              Get the latest home and business improvement tips delivered to your inbox
            </p>
          </div>
          <form className="space-y-3">
            <Input
              type="email"
              placeholder="Your email address"
              className="w-full"
            />
            <Button type="submit" className="w-full">
              Subscribe
            </Button>
          </form>
        </CardContent>
      </Card>

      {/* Related Posts */}
      <div className="bg-white dark:bg-gray-900 border border-gray-100 dark:border-gray-800 rounded-xl shadow-sm overflow-hidden">
        <div className="bg-primary/10 py-3 px-4 border-b border-gray-100 dark:border-gray-800">
          <h3 className="text-xl font-bold text-gray-900 dark:text-white">Related Articles</h3>
        </div>
        <div className="divide-y divide-gray-100 dark:divide-gray-800">
          {relatedPosts.map(post => (
            <Link to={`/blog/${post.slug}`} key={post.id} className="block group hover:bg-gray-50 dark:hover:bg-gray-800/50 transition-colors">
              <div className="p-4 flex gap-4 items-center">
                <div className="w-24 h-20 rounded-md overflow-hidden flex-shrink-0 border border-gray-100 dark:border-gray-700">
                  <img 
                    src={post.image} 
                    alt={post.title}
                    className="w-full h-full object-cover transition-transform group-hover:scale-105"
                  />
                </div>
                <div className="flex-1">
                  <h4 className="font-semibold text-base line-clamp-2 group-hover:text-primary transition-colors mb-1" dangerouslySetInnerHTML={{ __html: post.title }} />
                  <p className="text-sm text-gray-600 dark:text-gray-400 line-clamp-2">
                    {post.excerpt}
                  </p>
                </div>
              </div>
            </Link>
          ))}
        </div>
        {relatedPosts.length > 0 && (
          <div className="p-3 bg-gray-50 dark:bg-gray-800/50 text-center">
            <Link to="/blog" className="text-primary hover:underline text-sm font-medium">
              View all articles
            </Link>
          </div>
        )}
      </div>

      {/* Popular Articles */}
      <Card>
        <CardContent className="p-0 overflow-hidden">
          <div className="bg-primary/10 py-3 px-4 border-b border-gray-100 dark:border-gray-800">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-bold text-gray-900 dark:text-white">Popular Articles</h3>
              <Link to="/blog/business-resources" className="text-primary hover:underline text-xs flex items-center">
                <Building className="h-3.5 w-3.5 mr-1" />
                Business Resources
              </Link>
            </div>
          </div>

          {loadingPopular ? (
            <div className="p-5 flex justify-center items-center">
              <Loader2 className="h-8 w-8 animate-spin text-primary" />
            </div>
          ) : errorPopular ? (
            <div className="p-5">
              <p className="text-red-500 text-center">{errorPopular}</p>
            </div>
          ) : (
            <>
              <div className="divide-y divide-gray-100 dark:divide-gray-800">
                {popularPosts.map((post) => (
                  <Link to={`/blog/${post.slug}`} key={post.id} className="block group hover:bg-gray-50 dark:hover:bg-gray-800/50 transition-colors">
                    <div className="p-4 flex gap-3 items-center">
                      <div className="w-20 h-16 rounded-md overflow-hidden flex-shrink-0 border border-gray-100 dark:border-gray-700">
                        <img 
                          src={post.image} 
                          alt={post.title}
                          className="w-full h-full object-cover transition-transform group-hover:scale-105"
                        />
                      </div>
                      <div className="flex-1">
                        <h4 className="font-semibold text-sm line-clamp-2 group-hover:text-primary transition-colors" dangerouslySetInnerHTML={{ __html: post.title }} />
                        {/* Only show views if available */}
                        {post.views && (
                          <div className="text-xs text-primary mt-1.5 font-medium">
                            {post.views.toLocaleString()} views
                          </div>
                        )}
                      </div>
                    </div>
                  </Link>
                ))}
              </div>
              <div className="p-3 bg-gray-50 dark:bg-gray-800/50 text-center">
                <Link to="/blog" className="text-primary hover:underline text-sm font-medium">
                  See all articles
                </Link>
              </div>
            </>
          )}
        </CardContent>
      </Card>
    </div>
  );
};
