
import React from 'react';
import {
  <PERSON>,
  Card<PERSON>ontent,
  CardDescription,
  <PERSON><PERSON>oot<PERSON>,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Upload } from "lucide-react";

export const ProfilePhotoCard = () => {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Profile Photo</CardTitle>
        <CardDescription>Your public profile photo</CardDescription>
      </CardHeader>
      <CardContent className="flex justify-center">
        <div className="text-center">
          <Avatar className="w-24 h-24 mx-auto">
            <AvatarImage src="https://github.com/shadcn.png" alt="Profile" />
            <AvatarFallback className="text-2xl">JD</AvatarFallback>
          </Avatar>
        </div>
      </CardContent>
      <CardFooter className="flex justify-center">
        <Button variant="outline" className="w-full">
          <Upload className="h-4 w-4 mr-2" /> Upload New Photo
        </Button>
      </CardFooter>
    </Card>
  );
};
