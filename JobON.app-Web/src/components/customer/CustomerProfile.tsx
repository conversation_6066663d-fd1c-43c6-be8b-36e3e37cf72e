
import React, { useState, useEffect } from 'react';
import { useToast } from "@/hooks/use-toast";
import { useIsMobile } from "@/hooks/use-mobile";
import { useAuth } from "@/features/auth/hooks/useAuth";
import { ProfilePhotoCard } from './ProfilePhotoCard';
import { PersonalInfoCard } from './PersonalInfoCard';
import { AddressesCard } from './AddressesCard';
import { AddressDialog, AddressFormValues } from './AddressDialog';
import { DeleteAddressDialog } from './DeleteAddressDialog';
import { Address } from './AddressCard';

export function CustomerProfile() {
  // Get user data from auth context
  const { user: authUser } = useAuth();

  // Local state for user data
  const [user, setUser] = useState({
    firstName: "",
    lastName: "",
    email: "",
    phoneNumber: "",
    addresses: [
      {
        id: "addr1",
        type: "Home",
        street: "123 Main Street",
        city: "Pleasantville",
        state: "CA",
        zipCode: "94123"
      }
    ]
  });

  // Update local state when auth user changes
  useEffect(() => {
    if (authUser) {
      // Split the name into first and last name
      const nameParts = authUser.name ? authUser.name.split(' ') : ['', ''];
      const firstName = nameParts[0] || '';
      const lastName = nameParts.length > 1 ? nameParts.slice(1).join(' ') : '';

      setUser(prev => ({
        ...prev,
        firstName,
        lastName,
        email: authUser.email || '',
        phoneNumber: prev.phoneNumber // Keep existing phone number as it might not be in auth state
      }));
    }
  }, [authUser]);

  // Dialog states
  const [isAddressDialogOpen, setIsAddressDialogOpen] = useState(false);
  const [isEditMode, setIsEditMode] = useState(false);
  const [currentAddressId, setCurrentAddressId] = useState<string | null>(null);
  const [deleteConfirmOpen, setDeleteConfirmOpen] = useState(false);
  const [addressToDelete, setAddressToDelete] = useState<string | null>(null);

  // Toast notifications
  const { toast } = useToast();

  // Mobile detection
  const isMobile = useIsMobile();

  // Reset form and dialog state
  const resetFormAndState = () => {
    setIsEditMode(false);
    setCurrentAddressId(null);
  };

  // Open dialog for adding new address
  const openAddDialog = () => {
    resetFormAndState();
    setIsAddressDialogOpen(true);
  };

  // Open dialog for editing address
  const openEditDialog = (address: Address) => {
    setIsEditMode(true);
    setCurrentAddressId(address.id);
    setIsAddressDialogOpen(true);
  };

  // Open confirm dialog for deleting address
  const openDeleteConfirm = (addressId: string) => {
    setAddressToDelete(addressId);
    setDeleteConfirmOpen(true);
  };

  // Handle address deletion
  const handleDeleteAddress = () => {
    if (addressToDelete) {
      setUser({
        ...user,
        addresses: user.addresses.filter(address => address.id !== addressToDelete)
      });

      toast({
        title: "Address Deleted",
        description: "The address has been removed from your profile.",
      });

      setDeleteConfirmOpen(false);
      setAddressToDelete(null);
    }
  };

  // Handle form submission
  const handleAddressSubmit = (values: AddressFormValues) => {
    if (isEditMode && currentAddressId) {
      // Update existing address
      setUser({
        ...user,
        addresses: user.addresses.map(address =>
          address.id === currentAddressId ?
          {
            ...address,
            type: values.type,
            street: values.street,
            city: values.city,
            state: values.state,
            zipCode: values.zipCode
          } : address
        )
      });

      toast({
        title: "Address Updated",
        description: `Your ${values.type} address has been updated successfully.`,
      });
    } else {
      // Add new address
      const newAddress: Address = {
        id: `addr${user.addresses.length + 1}`,
        type: values.type,
        street: values.street,
        city: values.city,
        state: values.state,
        zipCode: values.zipCode
      };

      setUser({
        ...user,
        addresses: [...user.addresses, newAddress]
      });

      toast({
        title: "Address Added",
        description: `Your ${values.type} address has been saved successfully.`,
      });
    }

    // Close dialog
    setIsAddressDialogOpen(false);
  };

  // Get current address values for edit mode
  const getCurrentAddressValues = () => {
    if (isEditMode && currentAddressId) {
      const currentAddress = user.addresses.find(address => address.id === currentAddressId);
      if (currentAddress) {
        return {
          type: currentAddress.type,
          street: currentAddress.street,
          city: currentAddress.city,
          state: currentAddress.state,
          zipCode: currentAddress.zipCode
        };
      }
    }
    return undefined;
  };

  // Handle personal info update
  const handlePersonalInfoUpdate = (data: { firstName: string; lastName: string; email: string; phoneNumber: string }) => {
    setUser(prev => ({
      ...prev,
      ...data
    }));
  };

  return (
    <div className="space-y-6">
      <h1 className="text-2xl md:text-3xl font-bold">Profile</h1>
      <p className="text-muted-foreground">Manage your personal information</p>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {/* Profile Photo Card */}
        <ProfilePhotoCard />

        {/* Personal Information Card */}
        <PersonalInfoCard
          firstName={user.firstName}
          lastName={user.lastName}
          email={user.email}
          phoneNumber={user.phoneNumber}
          onUpdate={handlePersonalInfoUpdate}
        />

        {/* Addresses Card */}
        <AddressesCard
          addresses={user.addresses}
          onAddAddress={openAddDialog}
          onEditAddress={openEditDialog}
          onDeleteAddress={openDeleteConfirm}
        />
      </div>

      {/* Add/Edit Address Dialog */}
      <AddressDialog
        isOpen={isAddressDialogOpen}
        onOpenChange={setIsAddressDialogOpen}
        isEditMode={isEditMode}
        initialValues={getCurrentAddressValues()}
        onSubmit={handleAddressSubmit}
        isMobile={isMobile}
      />

      {/* Delete Confirmation Dialog */}
      <DeleteAddressDialog
        open={deleteConfirmOpen}
        onOpenChange={setDeleteConfirmOpen}
        onDelete={handleDeleteAddress}
      />
    </div>
  );
}
