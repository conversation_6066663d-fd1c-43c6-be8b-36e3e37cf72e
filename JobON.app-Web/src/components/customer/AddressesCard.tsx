
import React from 'react';
import {
  <PERSON>,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { MapPin } from "lucide-react";
import { AddressCard, Address } from './AddressCard';

interface AddressesCardProps {
  addresses: Address[];
  onAddAddress: () => void;
  onEditAddress: (address: Address) => void;
  onDeleteAddress: (addressId: string) => void;
}

export const AddressesCard = ({ addresses, onAddAddress, onEditAddress, onDeleteAddress }: AddressesCardProps) => {
  return (
    <Card className="md:col-span-3">
      <CardHeader className="flex flex-row items-center justify-between">
        <div>
          <CardTitle>Saved Addresses</CardTitle>
          <CardDescription>Manage your service locations</CardDescription>
        </div>
        <Button onClick={onAddAddress}>
          <MapPin className="h-4 w-4 mr-2" /> Add New Address
        </Button>
      </CardHeader>
      <CardContent>
        {addresses.length === 0 ? (
          <div className="text-center py-8">
            <MapPin className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
            <h3 className="text-lg font-medium">No saved addresses</h3>
            <p className="text-sm text-muted-foreground">
              Add your first address to make scheduling services easier.
            </p>
          </div>
        ) : (
          <div className="space-y-4">
            {addresses.map((address) => (
              <AddressCard 
                key={address.id} 
                address={address} 
                onEdit={onEditAddress}
                onDelete={onDeleteAddress}
              />
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
};
