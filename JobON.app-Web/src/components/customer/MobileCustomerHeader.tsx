
import React from 'react';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { useAuth } from '@/features/auth/hooks/useAuth';

interface MobileCustomerHeaderProps {
  title: string;
}

function getInitials(name?: string) {
  if (!name) return 'U';
  return name.split(' ').map(n => n[0]).join('').toUpperCase().slice(0, 2);
}

export function MobileCustomerHeader({ title }: MobileCustomerHeaderProps) {
  const { user } = useAuth();

  return (
    <div className="fixed top-0 left-0 right-0 z-50 bg-white/95 backdrop-blur-sm dark:bg-gray-900/95 border-b border-gray-200 dark:border-gray-800">
      <div className="flex items-center justify-between px-4 py-3 min-h-[60px] max-w-screen-xl mx-auto">
        <div className="flex items-center gap-3 min-w-0 flex-1">
          <Avatar className="h-8 w-8 flex-shrink-0">
            <AvatarImage src={user?.avatar} alt={user?.name || 'User'} />
            <AvatarFallback className="text-xs bg-blue-100 text-blue-700">
              {getInitials(user?.name)}
            </AvatarFallback>
          </Avatar>
          <div className="min-w-0 flex-1">
            <h1 className="text-lg font-semibold truncate text-gray-900 dark:text-white">{title}</h1>
          </div>
        </div>
      </div>
    </div>
  );
}
