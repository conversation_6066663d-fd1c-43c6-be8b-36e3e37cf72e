
import React, { useState } from 'react';
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { MessageSquare, Clock, Users, ArrowRight, MoreHorizontal, Edit, Trash } from "lucide-react";
import { Link } from "react-router-dom";
import { useSwipeGesture } from '@/hooks/use-swipe-gesture';
import { cn } from '@/lib/utils';
import { useToast } from '@/hooks/use-toast';

interface JobType {
  id: string;
  title: string;
  provider: string;
  status: string;
  date: string;
  messages: number;
  bids?: number;
}

interface MobileJobCardProps {
  job: JobType;
  onReschedule?: (jobId: string) => void;
  onCancel?: (jobId: string) => void;
}

export function MobileJobCard({ job, onReschedule, onCancel }: MobileJobCardProps) {
  const [isActionsVisible, setIsActionsVisible] = useState(false);
  const { toast } = useToast();
  
  const handleActionToggle = () => {
    setIsActionsVisible(!isActionsVisible);
  };
  
  const handleReschedule = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (onReschedule) {
      onReschedule(job.id);
    } else {
      toast({
        title: "Reschedule requested",
        description: "This would open the reschedule dialog"
      });
    }
    setIsActionsVisible(false);
  };
  
  const handleCancel = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (onCancel) {
      onCancel(job.id);
    } else {
      toast({
        title: "Cancellation requested",
        description: "This would open the cancellation confirmation dialog"
      });
    }
    setIsActionsVisible(false);
  };

  useSwipeGesture({
    onSwipeLeft: () => setIsActionsVisible(true),
    onSwipeRight: () => setIsActionsVisible(false)
  });

  // Helper function to determine status-based styling
  const getStatusStyles = (status: string) => {
    switch (status) {
      case 'In Progress':
        return {
          bgColor: 'bg-blue-50',
          indicatorColor: 'bg-blue-500',
          textColor: 'text-blue-700',
          badgeBg: 'bg-blue-100',
          badgeText: 'text-blue-700',
        };
      case 'Scheduled':
        return {
          bgColor: 'bg-purple-50',
          indicatorColor: 'bg-purple-500',
          textColor: 'text-purple-700',
          badgeBg: 'bg-purple-100',
          badgeText: 'text-purple-700',
        };
      case 'Awaiting Provider':
        return {
          bgColor: 'bg-amber-50',
          indicatorColor: 'bg-amber-500',
          textColor: 'text-amber-700',
          badgeBg: 'bg-amber-100',
          badgeText: 'text-amber-700',
        };
      default:
        return {
          bgColor: 'bg-gray-50',
          indicatorColor: 'bg-gray-500',
          textColor: 'text-gray-700',
          badgeBg: 'bg-gray-100',
          badgeText: 'text-gray-700',
        };
    }
  };

  const styles = getStatusStyles(job.status);

  return (
    <div className="relative overflow-hidden rounded-lg">
      {/* Swipe actions revealed behind the card */}
      <div className="absolute inset-y-0 right-0 flex items-center bg-gray-100 px-3 rounded-r-lg">
        <div className="flex gap-2">
          <Button 
            size="sm" 
            variant="ghost" 
            className="h-9 w-9 p-0 bg-amber-100 text-amber-700 rounded-full"
            onClick={handleReschedule}
          >
            <Edit className="h-4 w-4" />
          </Button>
          <Button 
            size="sm" 
            variant="ghost" 
            className="h-9 w-9 p-0 bg-red-100 text-red-700 rounded-full"
            onClick={handleCancel}
          >
            <Trash className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* Main card that slides to reveal actions */}
      <Card 
        className={cn(
          "cursor-pointer transition-all duration-200 ease-in-out border shadow-sm relative",
          styles.bgColor,
          {
            "translate-x-[-100px]": isActionsVisible,
          }
        )}
      >
        {/* Status indicator strip */}
        <div className={cn("absolute left-0 top-0 bottom-0 w-1.5", styles.indicatorColor)}></div>
        
        <Link to={`/customer/jobs/${job.id}/${job.status === "Awaiting Provider" ? 'bids' : 'manage'}`}>
          <div className="p-4 pl-6">
            <div className="flex justify-between items-start mb-2">
              <h3 className="text-lg font-medium leading-tight">{job.title}</h3>
              <Badge className={cn(styles.badgeBg, styles.badgeText, "font-medium")}>
                {job.status}
              </Badge>
            </div>
            
            <div className="space-y-2 text-sm">
              <div className="flex items-center text-muted-foreground">
                <Users className="h-3.5 w-3.5 mr-1.5" />
                <span>{job.provider}</span>
              </div>
              
              <div className="flex items-center text-muted-foreground">
                <Clock className="h-3.5 w-3.5 mr-1.5" />
                <span>{job.date}</span>
              </div>
              
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  {job.messages > 0 && (
                    <div className="flex items-center">
                      <MessageSquare className="h-3.5 w-3.5 mr-1 text-blue-500" />
                      <span className="text-blue-600 font-medium text-xs">{job.messages}</span>
                    </div>
                  )}
                  {job.status === 'Awaiting Provider' && job.bids && (
                    <div className="text-xs font-medium text-primary">
                      {job.bids} bids received
                    </div>
                  )}
                </div>
                
                <Button 
                  size="sm" 
                  variant="ghost" 
                  className={cn("p-0 h-7", styles.textColor)}
                  onClick={(e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    handleActionToggle();
                  }}
                >
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>
        </Link>
      </Card>
    </div>
  );
}
