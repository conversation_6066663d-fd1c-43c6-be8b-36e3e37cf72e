
import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  <PERSON>Content,
  <PERSON><PERSON><PERSON><PERSON>,
  Card<PERSON>itle
} from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { FileText, MessageSquare, Users, Clock, ArrowRight, Loader2 } from "lucide-react";
import { Link } from "react-router-dom";
import { apiService } from "@/services/api";
import { useAuth } from "@/features/auth/hooks/useAuth";
import { useToast } from "@/hooks/use-toast";

// Define interfaces for API response and job data
interface JobBooking {
  jobId: string;
  projectCode: string;
  createdAt: string;
  status: string;
  jobType: string;
  property: {
    type: string;
  };
  service: {
    category: string;
    tasks: string[];
    customTask: string | null;
  };
  schedule: {
    date: string;
    timePreference: string;
  };
  location: {
    address: string;
    city: string;
    state: string;
    zipCode: string;
  };
  contact: {
    fullName: string;
    email: string;
    phone: string;
  };
  user: {
    id: number;
    name: string;
    email: string;
  };
}

interface PaginationData {
  current_page: number;
  per_page: number;
  total: number;
  last_page: number;
}

interface ApiResponse {
  success: boolean;
  data: JobBooking[];
  pagination: PaginationData;
}

interface ActiveJob {
  id: string;
  title: string;
  provider: string;
  status: string;
  date: string;
  messages: number;
  bids: number;
}

export function CustomerActiveJobs() {
  const [activeJobs, setActiveJobs] = useState<ActiveJob[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [pagination, setPagination] = useState<PaginationData>({
    current_page: 1,
    per_page: 15,
    total: 0,
    last_page: 1
  });

  const { token } = useAuth();
  const { toast } = useToast();

  // Function to fetch job bookings from the API
  const fetchMyJobBookings = async () => {
    setIsLoading(true);
    setError(null);

    // Get the token without 'Bearer ' prefix
    const authToken = token ? token.replace('Bearer ', '') : '';

    if (!authToken) {
      setError('Authentication token is missing. Please log in again.');
      setIsLoading(false);
      toast({
        title: 'Authentication Error',
        description: 'You need to be logged in to view your jobs.',
        variant: 'destructive'
      });
      return;
    }

    try {
      const response = await apiService<ApiResponse>('/api/job-bookings/my-job-bookings', {
        method: 'GET',
        requiresAuth: true,
        headers: {
          'Authorization': `Bearer ${authToken}`
        }
      });

      if (response.isSuccess && response.data) {
        // Transform API response to match component's expected format
        const transformedJobs = response.data.data.map(job => ({
          id: job.jobId,
          title: job.service.tasks.join(", ") || job.service.category,
          provider: job.jobType === 'send_bids' ? 'Pending Selection' : (job.contact?.fullName || 'Unknown Provider'),
          status: mapStatusToDisplayStatus(job.status),
          date: formatDate(job.createdAt, job.status),
          messages: 0, // Assuming this would come from a different API
          bids: 0 // Assuming this would come from a different API
        }));

        setActiveJobs(transformedJobs);
        setPagination(response.data.pagination);
      } else {
        throw new Error(response.error || 'Failed to fetch job bookings');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred while fetching job bookings';
      setError(errorMessage);
      toast({
        title: 'Error',
        description: errorMessage,
        variant: 'destructive'
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Helper function to map API status to display status
  const mapStatusToDisplayStatus = (status: string): string => {
    const statusMap: Record<string, string> = {
      'pending': 'Awaiting Provider',
      'confirmed': 'Scheduled',
      'in_progress': 'In Progress',
      'completed': 'Completed',
      'cancelled': 'Cancelled'
    };

    return statusMap[status] || status;
  };

  // Helper function to format date based on status
  const formatDate = (dateString: string, status: string): string => {
    const date = new Date(dateString);
    const formattedDate = date.toLocaleDateString('en-US', { month: 'short', day: 'numeric', year: 'numeric' });

    if (status === 'pending') {
      return `Posted ${formattedDate}`;
    } else if (status === 'confirmed') {
      return `Starting ${formattedDate}`;
    } else if (status === 'in_progress') {
      return `Started ${formattedDate}`;
    }

    return formattedDate;
  };

  // Fetch job bookings when component mounts
  useEffect(() => {
    fetchMyJobBookings();
  }, []);

  return (
    <div className="space-y-4 sm:space-y-6">
      <div className="flex flex-col sm:flex-row sm:justify-between sm:items-start gap-4">
        <div className="flex-1 min-w-0">
          <h1 className="text-xl sm:text-2xl lg:text-3xl font-bold leading-tight">
            Active Jobs {!isLoading && pagination.total > 0 && `(${pagination.total})`}
          </h1>
          <p className="text-sm sm:text-base text-muted-foreground mt-1">
            Manage your ongoing service requests
          </p>
        </div>
        <Button asChild className="sm:w-auto w-full sm:flex-shrink-0">
          <Link to="/create-job">
            <FileText className="mr-2 h-4 w-4" />
            <span className="hidden sm:inline">Post a New Job</span>
            <span className="sm:hidden">Post Job</span>
          </Link>
        </Button>
      </div>

      {isLoading ? (
        <div className="flex justify-center items-center py-12">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
          <span className="ml-3 text-lg">Loading your active jobs...</span>
        </div>
      ) : error ? (
        <Card className="border-red-200 bg-red-50">
          <CardContent className="py-8 text-center">
            <h3 className="text-lg font-medium text-red-700">Error loading jobs</h3>
            <p className="text-sm text-red-600 mb-4">
              {error}
            </p>
            <Button onClick={fetchMyJobBookings}>
              Try Again
            </Button>
          </CardContent>
        </Card>
      ) : activeJobs.length === 0 ? (
        <Card className="border-dashed">
          <CardContent className="py-8 text-center">
            <div className="flex justify-center mb-4">
              <FileText className="h-12 w-12 text-muted-foreground" />
            </div>
            <h3 className="text-lg font-medium">No active jobs</h3>
            <p className="text-sm text-muted-foreground mb-4">
              You don't have any active jobs at the moment. Post a new job to get started.
            </p>
            <Button asChild>
              <Link to="/create-job">
                <FileText className="mr-2 h-4 w-4" /> Post a Job
              </Link>
            </Button>
          </CardContent>
        </Card>
      ) : (
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-2 xl:grid-cols-3 2xl:grid-cols-4 gap-4 sm:gap-6">
          {activeJobs.map(job => (
            <Card key={job.id} className="flex flex-col h-full hover:shadow-md transition-shadow duration-200">
              <CardHeader className="pb-3">
                <div className="flex items-start justify-between gap-2">
                  <CardTitle className="text-base sm:text-lg font-semibold line-clamp-2 flex-1">
                    {job.title}
                  </CardTitle>
                  <span className={`text-xs px-2 py-1 rounded-full font-medium flex-shrink-0 ${
                    job.status === 'In Progress' ? 'bg-blue-100 text-blue-700' :
                    job.status === 'Scheduled' ? 'bg-purple-100 text-purple-700' :
                    job.status === 'Awaiting Provider' ? 'bg-amber-100 text-amber-700' :
                    'bg-gray-100 text-gray-700'
                  }`}>
                    {job.status}
                  </span>
                </div>
              </CardHeader>
              <CardContent className="flex-grow px-4 pb-3">
                <div className="space-y-3">
                  <div className="flex items-center text-sm text-gray-600">
                    <Users className="h-4 w-4 mr-2 text-muted-foreground flex-shrink-0" />
                    <span className="truncate">{job.provider}</span>
                  </div>
                  <div className="flex items-center text-sm text-gray-600">
                    <Clock className="h-4 w-4 mr-2 text-muted-foreground flex-shrink-0" />
                    <span className="truncate">{job.date}</span>
                  </div>
                  {(job.messages > 0 || job.status === 'Awaiting Provider') && (
                    <div className="flex items-center gap-4 pt-1">
                      {job.messages > 0 && (
                        <div className="flex items-center text-sm text-blue-600">
                          <MessageSquare className="h-4 w-4 mr-1" />
                          <span className="font-medium">{job.messages}</span>
                        </div>
                      )}
                      {job.status === 'Awaiting Provider' && (
                        <div className="text-sm text-primary font-medium">
                          {job.bids} bids received
                        </div>
                      )}
                    </div>
                  )}
                </div>
              </CardContent>
              <div className="px-4 pb-4">
                <Button
                  variant={job.status === "Awaiting Provider" ? "default" : "outline"}
                  className="w-full justify-between text-sm h-9"
                  asChild
                >
                  <Link to={`/customer/jobs/${job.id}/${job.status === "Awaiting Provider" ? 'bids' : 'manage'}`}>
                    <span className="truncate">
                      {job.status === "Awaiting Provider" ? "View Bids" : "Manage Job"}
                    </span>
                    <ArrowRight className="h-4 w-4 flex-shrink-0 ml-2" />
                  </Link>
                </Button>
              </div>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
}
