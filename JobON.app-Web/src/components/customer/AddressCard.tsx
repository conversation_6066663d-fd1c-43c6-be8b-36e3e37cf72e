
import React from 'react';
import { MapPin, Edit, Trash2 } from "lucide-react";
import { Button } from "@/components/ui/button";

export interface Address {
  id: string;
  type: string;
  street: string;
  city: string;
  state: string;
  zipCode: string;
}

interface AddressCardProps {
  address: Address;
  onEdit: (address: Address) => void;
  onDelete: (addressId: string) => void;
}

export const AddressCard = ({ address, onEdit, onDelete }: AddressCardProps) => {
  return (
    <div className="p-4 border rounded-lg flex justify-between items-start">
      <div>
        <div className="flex items-center gap-2">
          <MapPin className="h-4 w-4 text-muted-foreground" />
          <h3 className="font-medium">{address.type}</h3>
        </div>
        <p className="text-sm mt-1">{address.street}</p>
        <p className="text-sm">{address.city}, {address.state} {address.zipCode}</p>
      </div>
      <div className="flex gap-2">
        <Button 
          variant="outline" 
          size="sm" 
          onClick={() => onEdit(address)}
          className="flex items-center"
        >
          <Edit className="h-4 w-4 mr-1" />
          Edit
        </Button>
        <Button 
          variant="outline" 
          size="sm" 
          onClick={() => onDelete(address.id)}
          className="flex items-center text-red-500 border-red-200 hover:bg-red-50 hover:text-red-600"
        >
          <Trash2 className="h-4 w-4 mr-1" />
          Delete
        </Button>
      </div>
    </div>
  );
};
