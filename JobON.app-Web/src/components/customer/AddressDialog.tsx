
import React from 'react';
import { <PERSON><PERSON>, <PERSON>alogContent, DialogDescription, <PERSON><PERSON><PERSON>ooter, <PERSON><PERSON><PERSON>eader, DialogTitle } from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { useAddressAutocomplete } from "@/hooks/use-address-autocomplete";
import { z } from "zod";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";

// Address schema for form validation
const addressSchema = z.object({
  type: z.string().min(1, "Address nickname is required"),
  street: z.string().min(1, "Street address is required"),
  city: z.string().min(1, "City is required"),
  state: z.string().min(1, "State is required"),
  zipCode: z.string().min(5, "Valid ZIP code is required").max(10),
});

export type AddressFormValues = z.infer<typeof addressSchema>;

interface AddressDialogProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  isEditMode: boolean;
  initialValues?: AddressFormValues;
  onSubmit: (values: AddressFormValues) => void;
  isMobile: boolean;
}

export const AddressDialog = ({ 
  isOpen, 
  onOpenChange, 
  isEditMode, 
  initialValues, 
  onSubmit,
  isMobile
}: AddressDialogProps) => {
  const form = useForm<AddressFormValues>({
    resolver: zodResolver(addressSchema),
    defaultValues: initialValues || {
      type: "",
      street: "",
      city: "",
      state: "",
      zipCode: "",
    },
  });

  // Address autocomplete
  const { query, setQuery, suggestions, isLoading, handleSelectAddress } = useAddressAutocomplete({});

  // Reset form state
  const resetFormAndState = () => {
    form.reset();
    setQuery("");
  };

  // Handle address selection from autocomplete
  const onSelectAddress = (address: any) => {
    handleSelectAddress(address);
    form.setValue("street", address.address);
    form.setValue("city", address.city);
    form.setValue("state", address.state);
    form.setValue("zipCode", address.zipCode);
    setQuery(address.address);
  };

  // Handle form submission
  const handleSubmit = (values: AddressFormValues) => {
    onSubmit(values);
    resetFormAndState();
    onOpenChange(false);
  };

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className={`sm:max-w-md ${isMobile ? 'p-4' : 'p-6'}`}>
        <DialogHeader>
          <DialogTitle className="text-center sm:text-left">
            {isEditMode ? "Edit Address" : "Add New Address"}
          </DialogTitle>
          <DialogDescription className="text-center sm:text-left">
            {isEditMode ? "Update your address information" : "Add a new service location to your account"}
          </DialogDescription>
        </DialogHeader>
        
        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
            {/* Address Nickname Field */}
            <FormField
              control={form.control}
              name="type"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Address Nickname</FormLabel>
                  <FormControl>
                    <Input {...field} placeholder="Home, Work, Vacation, etc." />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            
            {/* Street Address Field */}
            <FormField
              control={form.control}
              name="street"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Street Address</FormLabel>
                  <FormControl>
                    <Input
                      {...field}
                      placeholder="Enter your full address"
                      value={query}
                      onChange={(e) => {
                        setQuery(e.target.value);
                        field.onChange(e);
                      }}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            
            {/* Address Suggestions */}
            {suggestions.length > 0 && (
              <div className="border rounded-md overflow-hidden mb-2">
                <ul className="max-h-48 overflow-auto">
                  {suggestions.map((address, index) => (
                    <li 
                      key={index}
                      className="p-2 text-sm cursor-pointer hover:bg-accent"
                      onClick={() => onSelectAddress(address)}
                    >
                      {address.address}
                    </li>
                  ))}
                </ul>
              </div>
            )}
            
            {/* Hidden fields for city, state, zip */}
            <div className="hidden">
              <FormField
                control={form.control}
                name="city"
                render={({ field }) => (
                  <FormItem>
                    <FormControl>
                      <Input {...field} />
                    </FormControl>
                  </FormItem>
                )}
              />
              
              <FormField
                control={form.control}
                name="state"
                render={({ field }) => (
                  <FormItem>
                    <FormControl>
                      <Input {...field} />
                    </FormControl>
                  </FormItem>
                )}
              />
              
              <FormField
                control={form.control}
                name="zipCode"
                render={({ field }) => (
                  <FormItem>
                    <FormControl>
                      <Input {...field} />
                    </FormControl>
                  </FormItem>
                )}
              />
            </div>
            
            <DialogFooter className={isMobile ? 'flex-col space-y-2' : ''}>
              <Button 
                type="button" 
                variant="outline" 
                onClick={() => {
                  onOpenChange(false);
                  resetFormAndState();
                }}
                className={isMobile ? 'w-full' : ''}
              >
                Cancel
              </Button>
              <Button 
                type="submit"
                className={isMobile ? 'w-full' : ''}
                disabled={isLoading}
              >
                {isLoading ? "Loading..." : isEditMode ? "Update Address" : "Save Address"}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
};
