
import {
  Sidebar,
  SidebarContent,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarHeader,
  SidebarFooter
} from "@/components/ui/sidebar";
import {
  LayoutDashboard,
  Calendar,
  MessageSquare,
  User,
  Settings,
  ClipboardList,
  CheckSquare,
  CreditCard,
  Star,
  Shield,
  Gift,
  LogOut,
  UserCircle,
  ChevronsUpDown
} from "lucide-react";
import { Link, useLocation, useNavigate } from "react-router-dom";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { useAuth } from "@/features/auth/hooks/useAuth";
import { Button } from "@/components/ui/button";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuSeparator, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { useToast } from "@/hooks/use-toast";
import { Separator } from "@/components/ui/separator";

const menuItems = [
  {
    title: "Dashboard",
    url: "/customer/dashboard",
    icon: LayoutDashboard,
    tab: undefined,
  },
  {
    title: "Active Jobs",
    url: "/customer/dashboard?tab=active-jobs",
    icon: ClipboardList,
    tab: "active-jobs",
  },
  {
    title: "Completed Jobs",
    url: "/customer/dashboard?tab=completed-jobs",
    icon: CheckSquare,
    tab: "completed-jobs",
  },
  {
    title: "Messages",
    url: "/customer/dashboard?tab=messages",
    icon: MessageSquare,
    tab: "messages",
  },
  {
    title: "Calendar",
    url: "/customer/dashboard?tab=calendar",
    icon: Calendar,
    tab: "calendar",
  },
  {
    title: "Payments",
    url: "/customer/dashboard?tab=payments",
    icon: CreditCard,
    tab: "payments",
  },
  {
    title: "Reviews",
    url: "/customer/dashboard?tab=reviews",
    icon: Star,
    tab: "reviews",
  },
  {
    title: "Rewards",
    url: "/customer/dashboard?tab=rewards",
    icon: Shield,
    tab: "rewards",
  },
  {
    title: "Referrals",
    url: "/customer/dashboard?tab=referrals",
    icon: Gift,
    tab: "referrals",
  },
  {
    title: "Profile",
    url: "/customer/dashboard?tab=profile",
    icon: User,
    tab: "profile",
  },
  {
    title: "Settings",
    url: "/customer/dashboard?tab=settings",
    icon: Settings,
    tab: "settings",
  }
];

function getTabFromSearch(search: string) {
  const params = new URLSearchParams(search);
  return params.get("tab");
}

function getInitials(name?: string) {
  if (!name) return 'U';
  return name.split(' ').map(n => n[0]).join('').toUpperCase().slice(0, 2);
}

export function CustomerSidebar() {
  const location = useLocation();
  const currentTab = getTabFromSearch(location.search);
  const { user, logout } = useAuth();
  const navigate = useNavigate();
  const { toast } = useToast();

  const handleLogout = () => {
    logout(); // This will handle token removal and navigation
    toast({
      title: "Logged out successfully",
      description: "You have been logged out of your account."
    });
  };

  return (
    <Sidebar>
      <SidebarHeader className="p-4 border-b bg-gradient-to-br from-blue-50 to-white dark:from-gray-800 dark:to-gray-900">
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" className="w-full justify-start gap-3 h-auto p-2">
              <Avatar className="h-10 w-10 border-2 border-white shadow-sm">
                <AvatarImage src={user?.avatar} alt={user?.name || 'User'} />
                <AvatarFallback className="text-sm">
                  {getInitials(user?.name)}
                </AvatarFallback>
              </Avatar>
              <div className="flex-1 min-w-0 text-left">
                <p className="text-sm font-medium truncate">{user?.name || 'User'}</p>
                <p className="text-xs text-muted-foreground truncate capitalize">{user?.role?.name}</p>
              </div>
              <ChevronsUpDown className="h-4 w-4 text-muted-foreground" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="start" className="w-56">
            <DropdownMenuItem asChild className="flex items-center gap-2">
              <Link to="/customer/dashboard?tab=profile">
                <UserCircle className="h-4 w-4" /> View Profile
              </Link>
            </DropdownMenuItem>
            <DropdownMenuItem asChild className="flex items-center gap-2">
              <Link to="/customer/dashboard?tab=settings">
                <Settings className="h-4 w-4" /> Account Settings
              </Link>
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem onClick={handleLogout} className="flex items-center gap-2">
              <LogOut className="h-4 w-4" /> Sign out
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </SidebarHeader>
      
      <SidebarContent>
        <SidebarGroup>
          <SidebarGroupContent>
            <SidebarMenu className="gap-1">
              {menuItems.slice(0, 5).map((item) => {
                const isDashboard =
                  item.tab === undefined &&
                  location.pathname === "/customer/dashboard" &&
                  (!location.search || !getTabFromSearch(location.search));
                const isTab =
                  item.tab &&
                  location.pathname === "/customer/dashboard" &&
                  currentTab === item.tab;

                // Special handling for Active Jobs - also active when viewing job details
                const isActiveJobsPage =
                  item.tab === "active-jobs" &&
                  (location.pathname.includes("/customer/jobs/") ||
                   location.pathname.startsWith("/customer/jobs/") ||
                   (location.pathname === "/customer/dashboard" && currentTab === "active-jobs"));

                return (
                  <SidebarMenuItem key={item.title}>
                    <SidebarMenuButton
                      asChild
                      isActive={Boolean(isDashboard || isTab || isActiveJobsPage)}
                      className="h-10 transition-colors hover:bg-blue-50 hover:text-blue-700 dark:hover:bg-blue-900/30 dark:hover:text-blue-300 data-[active=true]:bg-blue-50 data-[active=true]:text-blue-700 dark:data-[active=true]:bg-blue-900/30 dark:data-[active=true]:text-blue-300 data-[active=true]:font-medium"
                    >
                      <Link to={item.url}>
                        <item.icon className="w-5 h-5" />
                        <span className="text-sm font-medium">{item.title}</span>
                      </Link>
                    </SidebarMenuButton>
                  </SidebarMenuItem>
                );
              })}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>

        <Separator className="my-2" />

        <SidebarGroup>
          <SidebarGroupContent>
            <SidebarMenu className="gap-1">
              {menuItems.slice(5, -2).map((item) => {
                const isTab =
                  item.tab &&
                  location.pathname === "/customer/dashboard" &&
                  currentTab === item.tab;

                return (
                  <SidebarMenuItem key={item.title}>
                    <SidebarMenuButton
                      asChild
                      isActive={Boolean(isTab)}
                      className="h-10 transition-colors hover:bg-blue-50 hover:text-blue-700 dark:hover:bg-blue-900/30 dark:hover:text-blue-300 data-[active=true]:bg-blue-50 data-[active=true]:text-blue-700 dark:data-[active=true]:bg-blue-900/30 dark:data-[active=true]:text-blue-300 data-[active=true]:font-medium"
                    >
                      <Link to={item.url}>
                        <item.icon className="w-5 h-5" />
                        <span className="text-sm font-medium">{item.title}</span>
                      </Link>
                    </SidebarMenuButton>
                  </SidebarMenuItem>
                );
              })}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>
      </SidebarContent>

      <SidebarFooter className="p-4 border-t bg-gradient-to-br from-white to-blue-50 dark:from-gray-900 dark:to-gray-800">
        <SidebarMenu>
          {menuItems.slice(-2).map((item) => {
            const isTab =
              item.tab &&
              location.pathname === "/customer/dashboard" &&
              currentTab === item.tab;

            return (
              <SidebarMenuItem key={item.title}>
                <SidebarMenuButton
                  asChild
                  isActive={Boolean(isTab)}
                  className="h-10 mb-2 transition-colors hover:bg-blue-50 hover:text-blue-700 dark:hover:bg-blue-900/30 dark:hover:text-blue-300 data-[active=true]:bg-blue-50 data-[active=true]:text-blue-700 dark:data-[active=true]:bg-blue-900/30 dark:data-[active=true]:text-blue-300 data-[active=true]:font-medium"
                >
                  <Link to={item.url}>
                    <item.icon className="w-5 h-5" />
                    <span className="text-sm font-medium">{item.title}</span>
                  </Link>
                </SidebarMenuButton>
              </SidebarMenuItem>
            );
          })}
        </SidebarMenu>
        <Button
          variant="outline"
          className="w-full py-2 text-sm gap-3 font-medium rounded-md shadow-sm hover:shadow flex items-center justify-center"
          onClick={handleLogout}
        >
          <LogOut className="h-4 w-4" />
          <span>Sign Out</span>
        </Button>
      </SidebarFooter>
    </Sidebar>
  );
}
