
import React, { useState } from 'react';
import { useIsMobile } from "@/hooks/use-mobile";
import { MobileCustomerSettings } from './MobileCustomerSettings';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>onte<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  TabsTrigger
} from "@/components/ui/tabs";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Separator } from "@/components/ui/separator";
import { 
  Lock, 
  Bell, 
  Mail, 
  MessageSquare, 
  CreditCard, 
  ShieldAlert,
  AlertTriangle,
  Loader2
} from "lucide-react";
import { apiService } from '@/services/api';
import { useToast } from '@/hooks/use-toast';
import {useAuth} from "@/features/auth/hooks/useAuth.ts";

export function CustomerSettings() {
  const isMobile = useIsMobile();
  const { toast } = useToast();
  const { token } = useAuth();

  const [passwordForm, setPasswordForm] = useState({
    current_password: '',
    password: '',
    password_confirmation: ''
  });

  // Form validation errors
  const [errors, setErrors] = useState<{
    current_password?: string;
    password?: string;
    password_confirmation?: string;
  }>({});

  // Loading state
  const [isLoading, setIsLoading] = useState(false);

  // Handle input change
  const handlePasswordChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { id, value } = e.target;

    // Map the input id to the form field name
    const fieldMap: Record<string, keyof typeof passwordForm> = {
      'currentPassword': 'current_password',
      'newPassword': 'password',
      'confirmPassword': 'password_confirmation'
    };

    const field = fieldMap[id];

    setPasswordForm(prev => ({
      ...prev,
      [field]: value
    }));

    // Clear error for this field when user types
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: undefined
      }));
    }
  };

  // Validate form
  const validatePasswordForm = () => {
    const newErrors: typeof errors = {};

    // Check required fields
    if (!passwordForm.current_password) {
      newErrors.current_password = 'Current password is required';
    }

    if (!passwordForm.password) {
      newErrors.password = 'New password is required';
    }

    if (!passwordForm.password_confirmation) {
      newErrors.password_confirmation = 'Please confirm your new password';
    }

    // Check if passwords match
    if (passwordForm.password && 
        passwordForm.password_confirmation && 
        passwordForm.password !== passwordForm.password_confirmation) {
      newErrors.password_confirmation = 'Passwords do not match';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle form submission
  const handleUpdatePassword = async () => {
    // Validate form
    if (!validatePasswordForm()) {
      return;
    }

    setIsLoading(true);

    if (!token) {
      toast({
        title: "Authentication Error",
        description: "You are not authenticated. Please log in again.",
        variant: "destructive",
      });
      setIsLoading(false);
      return;
    }

    try {
      const response = await apiService('/api/account/change-password', {
        method: 'PUT',
        headers: {
          'Authorization': token, // token is now confirmed to be a string
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        },
        body: passwordForm
      });

      if (response.isSuccess) {
        toast({
          title: "Success",
          description: "Password successfully updated",
          variant: "default",
        });

        // Reset form
        setPasswordForm({
          current_password: '',
          password: '',
          password_confirmation: ''
        });
      } else {
        toast({
          title: "Error",
          description: response.error || "Failed to update password. Please try again",
          variant: "destructive",
        });
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to update password. Please try again",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  // If on a mobile device, show the mobile-optimized version
  if (isMobile) {
    return <MobileCustomerSettings />;
  }

  // If not on a mobile device, show the desktop version
  return (
    <div className="space-y-6">
      <h1 className="text-2xl md:text-3xl font-bold">Settings</h1>
      <p className="text-muted-foreground">Manage your account preferences</p>

      <Tabs defaultValue="account" className="space-y-4">
        <TabsList>
          <TabsTrigger value="account">Account</TabsTrigger>
          <TabsTrigger value="notifications">Notifications</TabsTrigger>
          <TabsTrigger value="privacy">Privacy & Security</TabsTrigger>
        </TabsList>

        <TabsContent value="account">
          {/* Password Section */}
          <Card className="mb-6">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Lock className="h-5 w-5" />
                Password
              </CardTitle>
              <CardDescription>Change your account password</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="currentPassword">Current Password</Label>
                <Input 
                  id="currentPassword" 
                  type="text"
                  value={passwordForm.current_password}
                  onChange={handlePasswordChange}
                  disabled={isLoading}
                />
                {errors.current_password && (
                  <p className="text-sm text-red-500">{errors.current_password}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="newPassword">New Password</Label>
                <Input 
                  id="newPassword" 
                  type="text"
                  value={passwordForm.password}
                  onChange={handlePasswordChange}
                  disabled={isLoading}
                />
                {errors.password && (
                  <p className="text-sm text-red-500">{errors.password}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="confirmPassword">Confirm New Password</Label>
                <Input 
                  id="confirmPassword" 
                  type="text"
                  value={passwordForm.password_confirmation}
                  onChange={handlePasswordChange}
                  disabled={isLoading}
                />
                {errors.password_confirmation && (
                  <p className="text-sm text-red-500">{errors.password_confirmation}</p>
                )}
              </div>
            </CardContent>
            <CardFooter>
              <Button 
                className="ml-auto" 
                onClick={handleUpdatePassword}
                disabled={isLoading}
              >
                {isLoading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Loading...
                  </>
                ) : (
                  "Update Password"
                )}
              </Button>
            </CardFooter>
          </Card>

          {/* Payment Methods Section */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <CreditCard className="h-5 w-5" />
                Payment Methods
              </CardTitle>
              <CardDescription>Manage your saved payment methods</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex items-center justify-between p-4 border rounded-lg">
                <div className="flex items-center gap-3">
                  <div className="bg-slate-100 p-2 rounded">
                    <CreditCard className="h-5 w-5" />
                  </div>
                  <div>
                    <h4 className="font-medium">Visa ending in 4242</h4>
                    <p className="text-sm text-muted-foreground">Expires 04/2026</p>
                  </div>
                </div>
                <Button variant="outline" size="sm">Remove</Button>
              </div>

              <Button variant="outline" className="mt-4">
                <CreditCard className="h-4 w-4 mr-2" /> Add New Payment Method
              </Button>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="notifications">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Bell className="h-5 w-5" />
                Notification Preferences
              </CardTitle>
              <CardDescription>Control how you receive notifications</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Email Notifications */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium flex items-center gap-2">
                  <Mail className="h-4 w-4" /> Email Notifications
                </h3>
                <Separator />

                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <div>
                      <Label className="text-base" htmlFor="email-new-bids">New Bids</Label>
                      <p className="text-sm text-muted-foreground">Receive notifications when a provider bids on your job</p>
                    </div>
                    <Switch id="email-new-bids" defaultChecked />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <Label className="text-base" htmlFor="email-messages">New Messages</Label>
                      <p className="text-sm text-muted-foreground">Receive notifications for new messages from providers</p>
                    </div>
                    <Switch id="email-messages" defaultChecked />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <Label className="text-base" htmlFor="email-appointments">Appointment Reminders</Label>
                      <p className="text-sm text-muted-foreground">Receive reminders about upcoming scheduled appointments</p>
                    </div>
                    <Switch id="email-appointments" defaultChecked />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <Label className="text-base" htmlFor="email-marketing">Marketing & Promotions</Label>
                      <p className="text-sm text-muted-foreground">Receive special offers and promotions</p>
                    </div>
                    <Switch id="email-marketing" />
                  </div>
                </div>
              </div>

              {/* SMS Notifications */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium flex items-center gap-2">
                  <MessageSquare className="h-4 w-4" /> SMS Notifications
                </h3>
                <Separator />

                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <div>
                      <Label className="text-base" htmlFor="sms-new-bids">New Bids</Label>
                      <p className="text-sm text-muted-foreground">Receive text messages when a provider bids on your job</p>
                    </div>
                    <Switch id="sms-new-bids" />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <Label className="text-base" htmlFor="sms-messages">New Messages</Label>
                      <p className="text-sm text-muted-foreground">Receive text notifications for new messages from providers</p>
                    </div>
                    <Switch id="sms-messages" />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <Label className="text-base" htmlFor="sms-appointments">Appointment Reminders</Label>
                      <p className="text-sm text-muted-foreground">Receive text reminders about upcoming scheduled appointments</p>
                    </div>
                    <Switch id="sms-appointments" defaultChecked />
                  </div>
                </div>
              </div>
            </CardContent>
            <CardFooter>
              <Button className="ml-auto">Save Preferences</Button>
            </CardFooter>
          </Card>
        </TabsContent>

        <TabsContent value="privacy">
          <Card className="mb-6">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <ShieldAlert className="h-5 w-5" />
                Privacy Settings
              </CardTitle>
              <CardDescription>Control your data and privacy options</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <Label className="text-base" htmlFor="profile-visibility">Profile Visibility</Label>
                  <p className="text-sm text-muted-foreground">Allow providers to see your profile information</p>
                </div>
                <Switch id="profile-visibility" defaultChecked />
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <Label className="text-base" htmlFor="data-collection">Data Collection</Label>
                  <p className="text-sm text-muted-foreground">Allow us to collect usage data to improve our services</p>
                </div>
                <Switch id="data-collection" defaultChecked />
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <Label className="text-base" htmlFor="third-party">Third-Party Sharing</Label>
                  <p className="text-sm text-muted-foreground">Allow us to share information with trusted partners</p>
                </div>
                <Switch id="third-party" />
              </div>
            </CardContent>
            <CardFooter>
              <Button className="ml-auto">Save Privacy Settings</Button>
            </CardFooter>
          </Card>

          {/* Account Actions */}
          <Card className="border-red-100">
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-red-500">
                <AlertTriangle className="h-5 w-5" />
                Account Actions
              </CardTitle>
              <CardDescription>Manage your account status</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <h3 className="font-medium">Deactivate Account</h3>
                <p className="text-sm text-muted-foreground">
                  Temporarily deactivate your account. You can reactivate at any time by signing back in.
                </p>
                <Button variant="outline" className="text-amber-500 border-amber-200 hover:bg-amber-50">
                  Deactivate Account
                </Button>
              </div>

              <Separator />

              <div className="space-y-2">
                <h3 className="font-medium">Delete Account</h3>
                <p className="text-sm text-muted-foreground">
                  Permanently delete your account and all associated data. This action cannot be undone.
                </p>
                <Button variant="outline" className="text-red-500 border-red-200 hover:bg-red-50">
                  Delete Account
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
