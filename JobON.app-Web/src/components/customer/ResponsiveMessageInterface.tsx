
import React from 'react';
import { CustomerMessages } from './CustomerMessages';
import { MobileMessagingInterface } from './MobileMessagingInterface';
import { useIsMobile } from '@/hooks/use-mobile';
import { MobileCustomerLayout } from './MobileCustomerLayout';

export function ResponsiveMessageInterface() {
  const isMobile = useIsMobile();
  
  // If not on mobile, use the regular customer messages component
  if (!isMobile) {
    return <CustomerMessages />;
  }
  
  // On mobile, the header will be handled by MobileCustomerLayout in the CustomerDashboard component
  // So we just return the MobileMessagingInterface here
  return <MobileMessagingInterface />;
}
