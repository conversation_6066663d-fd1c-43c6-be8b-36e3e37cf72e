import React, { useState, useEffect, useCallback } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { useToast } from '@/hooks/use-toast';
import { getJobBids } from '@/services/bidService';
import { Bid, BidFilters, BidSortOptions } from '@/types/bid';
import { Job } from '@/types/jobs';
import { useBidStatusUpdate } from '@/hooks/useBidStatusUpdate';
import { Star, MapPin, Calendar, User, Check, X, Loader2 } from 'lucide-react';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';

interface BidManagementProps {
  job: Job;
  onBidAccepted?: (bid: Bid) => void;
  onBidRejected?: (bid: Bid) => void;
}

export const BidManagement: React.FC<BidManagementProps> = ({
  job,
  onBidAccepted,
  onBidRejected
}) => {
  const [bids, setBids] = useState<Bid[]>([]);
  const [loading, setLoading] = useState(true);
  const [actionLoading, setActionLoading] = useState<string | null>(null);
  const [sort, setSort] = useState<BidSortOptions>({ field: 'amount', direction: 'asc' });
  const { toast } = useToast();

  // Use the new bid status update hook
  const { updateStatus, isLoading: statusUpdateLoading } = useBidStatusUpdate({
    onSuccess: (updatedBid) => {
      // Update the bid in the local state
      setBids(prevBids =>
        prevBids.map(bid =>
          bid.id === updatedBid.id ? updatedBid : bid
        )
      );

      // Call the appropriate callback
      if (updatedBid.status === 'accepted') {
        onBidAccepted?.(updatedBid);
      } else if (updatedBid.status === 'rejected') {
        onBidRejected?.(updatedBid);
      }
    }
  });

  const fetchBids = useCallback(async () => {
    setLoading(true);
    try {
      const response = await getJobBids(job.id, {}, sort, 1, 50);
      
      if (response.isSuccess && response.data) {
        setBids(response.data.bids);
      } else {
        toast({
          title: 'Error',
          description: response.error || 'Failed to fetch bids',
          variant: 'destructive'
        });
      }
    } catch (error) {
      toast({
        title: 'Error',
        description: 'An unexpected error occurred',
        variant: 'destructive'
      });
    } finally {
      setLoading(false);
    }
  }, [job.id, sort, toast]);

  useEffect(() => {
    fetchBids();
  }, [fetchBids]);

  const handleAcceptBid = async (bid: Bid) => {
    await updateStatus(bid.id, 'accepted');
  };

  const handleRejectBid = async (bid: Bid) => {
    await updateStatus(bid.id, 'rejected');
  };

  const handleSortChange = (field: BidSortOptions['field']) => {
    setSort(prev => ({
      field,
      direction: prev.field === field && prev.direction === 'asc' ? 'desc' : 'asc'
    }));
  };

  const getBidStatusColor = (status: string): string => {
    switch (status) {
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'accepted': return 'bg-green-100 text-green-800';
      case 'rejected': return 'bg-red-100 text-red-800';
      case 'withdrawn': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const formatDate = (dateString: string): string => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        className={`h-4 w-4 ${
          i < Math.floor(rating) ? 'text-yellow-400 fill-current' : 'text-gray-300'
        }`}
      />
    ));
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Job Info */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Calendar className="h-5 w-5" />
            {job.title}
          </CardTitle>
          <div className="text-sm text-gray-600 space-y-1">
            <p className="flex items-center gap-2">
              <MapPin className="h-4 w-4" />
              {job.location}
            </p>
            <p>Service Type: {job.serviceType}</p>
            {job.scheduledDate && (
              <p>Scheduled: {formatDate(job.scheduledDate)}</p>
            )}
          </div>
        </CardHeader>
      </Card>

      {/* Sort Controls */}
      <Card>
        <CardHeader>
          <CardTitle>Bids Received ({bids.length})</CardTitle>
          <div className="flex gap-2">
            <Button
              variant={sort.field === 'amount' ? 'default' : 'outline'}
              size="sm"
              onClick={() => handleSortChange('amount')}
            >
              Sort by Amount {sort.field === 'amount' && (sort.direction === 'asc' ? '↑' : '↓')}
            </Button>
            <Button
              variant={sort.field === 'submittedAt' ? 'default' : 'outline'}
              size="sm"
              onClick={() => handleSortChange('submittedAt')}
            >
              Sort by Date {sort.field === 'submittedAt' && (sort.direction === 'asc' ? '↑' : '↓')}
            </Button>
          </div>
        </CardHeader>
      </Card>

      {/* Bid List */}
      <div className="space-y-4">
        {bids.length === 0 ? (
          <Card>
            <CardContent className="text-center py-8">
              <p className="text-gray-500">No bids received yet</p>
            </CardContent>
          </Card>
        ) : (
          bids.map((bid) => (
            <Card key={bid.id} className="hover:shadow-md transition-shadow">
              <CardContent className="p-6">
                <div className="flex justify-between items-start mb-4">
                  {/* Provider Info */}
                  <div className="flex items-start gap-4 flex-1">
                    <Avatar className="h-12 w-12">
                      <AvatarImage src={bid.provider?.avatar} />
                      <AvatarFallback>
                        <User className="h-6 w-6" />
                      </AvatarFallback>
                    </Avatar>
                    <div className="flex-1">
                      <h3 className="font-semibold text-lg">
                        {bid.provider?.firstName} {bid.provider?.lastName}
                      </h3>
                      {bid.provider?.businessName && (
                        <p className="text-gray-600 text-sm">
                          {bid.provider.businessName}
                        </p>
                      )}
                      <div className="flex items-center gap-2 mt-1">
                        <div className="flex items-center">
                          {renderStars(bid.provider?.rating || 0)}
                        </div>
                        <span className="text-sm text-gray-600">
                          ({bid.provider?.rating?.toFixed(1) || '0.0'})
                        </span>
                      </div>
                      {bid.provider?.specialty && bid.provider.specialty.length > 0 && (
                        <div className="flex flex-wrap gap-1 mt-2">
                          {bid.provider.specialty.map((spec, index) => (
                            <Badge key={index} variant="secondary" className="text-xs">
                              {spec}
                            </Badge>
                          ))}
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Bid Amount and Status */}
                  <div className="text-right ml-4">
                    <div className="text-3xl font-bold text-green-600 mb-2">
                      ${bid.amount.toFixed(2)}
                    </div>
                    <Badge className={getBidStatusColor(bid.status)}>
                      {bid.status.charAt(0).toUpperCase() + bid.status.slice(1)}
                    </Badge>
                  </div>
                </div>

                {/* Bid Description */}
                <div className="mb-4">
                  <h4 className="font-medium mb-2">Proposal:</h4>
                  <p className="text-gray-700 text-sm leading-relaxed">
                    {bid.description}
                  </p>
                </div>

                {/* Actions and Date */}
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-500">
                    Submitted: {formatDate(bid.submittedAt)}
                  </span>
                  
                  {bid.status === 'pending' && (
                    <div className="flex gap-2">
                      <AlertDialog>
                        <AlertDialogTrigger asChild>
                          <Button
                            variant="outline"
                            size="sm"
                            disabled={actionLoading === bid.id || statusUpdateLoading}
                            className="text-red-600 hover:text-red-700"
                          >
                            <X className="h-4 w-4 mr-1" />
                            Reject
                          </Button>
                        </AlertDialogTrigger>
                        <AlertDialogContent>
                          <AlertDialogHeader>
                            <AlertDialogTitle>Reject Bid</AlertDialogTitle>
                            <AlertDialogDescription>
                              Are you sure you want to reject this bid from {bid.provider?.firstName}? This action cannot be undone.
                            </AlertDialogDescription>
                          </AlertDialogHeader>
                          <AlertDialogFooter>
                            <AlertDialogCancel>Cancel</AlertDialogCancel>
                            <AlertDialogAction
                              onClick={() => handleRejectBid(bid)}
                              className="bg-red-600 hover:bg-red-700"
                            >
                              Reject Bid
                            </AlertDialogAction>
                          </AlertDialogFooter>
                        </AlertDialogContent>
                      </AlertDialog>

                      <AlertDialog>
                        <AlertDialogTrigger asChild>
                          <Button
                            size="sm"
                            disabled={actionLoading === bid.id || statusUpdateLoading}
                            className="bg-green-600 hover:bg-green-700"
                          >
                            {(actionLoading === bid.id || statusUpdateLoading) ? (
                              <Loader2 className="h-4 w-4 mr-1 animate-spin" />
                            ) : (
                              <Check className="h-4 w-4 mr-1" />
                            )}
                            Accept
                          </Button>
                        </AlertDialogTrigger>
                        <AlertDialogContent>
                          <AlertDialogHeader>
                            <AlertDialogTitle>Accept Bid</AlertDialogTitle>
                            <AlertDialogDescription>
                              Are you sure you want to accept this bid from {bid.provider?.firstName} for ${bid.amount.toFixed(2)}? This will automatically reject all other bids for this job.
                            </AlertDialogDescription>
                          </AlertDialogHeader>
                          <AlertDialogFooter>
                            <AlertDialogCancel>Cancel</AlertDialogCancel>
                            <AlertDialogAction
                              onClick={() => handleAcceptBid(bid)}
                              className="bg-green-600 hover:bg-green-700"
                            >
                              Accept Bid
                            </AlertDialogAction>
                          </AlertDialogFooter>
                        </AlertDialogContent>
                      </AlertDialog>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          ))
        )}
      </div>
    </div>
  );
};