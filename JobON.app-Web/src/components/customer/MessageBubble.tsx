import React from 'react';
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { format, formatDistanceToNow } from 'date-fns';

interface MessageBubbleProps {
  content: string;
  timestamp: string;
  isCustomer: boolean;
  status?: 'sent' | 'delivered' | 'read';
  showAvatar?: boolean;
  senderAvatar?: string;
  senderInitials: string;
  isFirstInGroup?: boolean;
  isLastInGroup?: boolean;
}

export const MessageBubble: React.FC<MessageBubbleProps> = ({
  content,
  timestamp,
  isCustomer,
  status,
  showAvatar = false,
  senderAvatar,
  senderInitials,
  isFirstInGroup = true,
  isLastInGroup = true
}) => {
  // Helper function to format the timestamp
  const formatMessageTime = (timestamp: string) => {
    try {
      // Try to parse as date if it's a date string
      if (timestamp.includes(',')) {
        const date = new Date(timestamp);
        if (!isNaN(date.getTime())) {
          // If it's today, just show time
          const today = new Date();
          if (date.toDateString() === today.toDateString()) {
            return format(date, 'h:mm a');
          }
          // If it's within the last 7 days, show day name and time
          const weekAgo = new Date();
          weekAgo.setDate(weekAgo.getDate() - 7);
          if (date > weekAgo) {
            return format(date, 'EEE, h:mm a');
          }
          // Otherwise show date and time
          return format(date, 'MMM d, h:mm a');
        }
      }
      return timestamp; // Return as is if not a full date
    } catch (error) {
      return timestamp; // Fallback to original string
    }
  };

  // Determine bubble style
  const bubbleClassName = `
    px-3 py-2 max-w-[85%]
    ${isCustomer 
      ? 'bg-blue-600 text-white' 
      : 'bg-gray-100 dark:bg-gray-800 text-foreground'}
    ${isFirstInGroup && isLastInGroup 
      ? isCustomer 
        ? 'rounded-t-lg rounded-l-lg rounded-br-lg' 
        : 'rounded-t-lg rounded-r-lg rounded-bl-lg'
      : isFirstInGroup 
        ? isCustomer 
          ? 'rounded-t-lg rounded-l-lg' 
          : 'rounded-t-lg rounded-r-lg' 
        : isLastInGroup 
          ? isCustomer 
            ? 'rounded-l-lg rounded-br-lg' 
            : 'rounded-r-lg rounded-bl-lg'
          : isCustomer 
            ? 'rounded-l-lg' 
            : 'rounded-r-lg'}
  `;

  return (
    <div className={`flex ${isCustomer ? 'justify-end' : 'justify-start'} mb-1`}>
      {!isCustomer && showAvatar && (
        <Avatar className="h-6 w-6 mr-1 mt-1 flex-shrink-0">
          {senderAvatar && (
            <AvatarImage src={senderAvatar} alt="Avatar" />
          )}
          <AvatarFallback className="text-[10px] bg-gradient-to-br from-indigo-400 to-purple-500 text-white">
            {senderInitials}
          </AvatarFallback>
        </Avatar>
      )}
      
      <div className={bubbleClassName}>
        <p className="text-sm whitespace-pre-wrap break-words">{content}</p>
        <div className={`flex items-center justify-end gap-1 mt-1 text-xs ${isCustomer ? 'text-blue-100' : 'text-gray-500 dark:text-gray-400'}`}>
          <span>{formatMessageTime(timestamp)}</span>
          {isCustomer && status && (
            <span>
              {status === 'sent' ? '✓' : 
               status === 'delivered' ? '✓✓' : 
               '✓✓'}
            </span>
          )}
        </div>
      </div>
      
      {isCustomer && showAvatar && (
        <Avatar className="h-6 w-6 ml-1 mt-1 flex-shrink-0">
          <AvatarFallback className="text-[10px] bg-gradient-to-br from-blue-500 to-indigo-500 text-white">
            YOU
          </AvatarFallback>
        </Avatar>
      )}
    </div>
  );
};
