
import React from 'react';
import {
  <PERSON>,
  CardContent,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Search, CreditCard, Download } from "lucide-react";
import { useIsMobile } from "@/hooks/use-mobile";
import { MobileCustomerPayments } from "./MobileCustomerPayments";

export function CustomerPayments() {
  const isMobile = useIsMobile();
  
  // If on a mobile device, show the mobile-optimized version
  if (isMobile) {
    return <MobileCustomerPayments />;
  }
  
  // Mock payments data
  const payments = [
    {
      id: "pay1",
      date: "Apr 15, 2025",
      job: "Bathroom Remodel",
      provider: "Deluxe Renovations",
      amount: "$750.00",
      status: "Paid"
    },
    {
      id: "pay2",
      date: "Apr 8, 2025",
      job: "Lawn Service",
      provider: "Green Thumb Landscaping",
      amount: "$120.00",
      status: "Paid"
    },
    {
      id: "pay3",
      date: "Apr 2, 2025",
      job: "Plumbing Repair",
      provider: "Mike's Plumbing",
      amount: "$350.00",
      status: "Paid"
    },
    {
      id: "pay4",
      date: "Mar 25, 2025",
      job: "HVAC Maintenance",
      provider: "Cool Air Systems",
      amount: "$275.00",
      status: "Refunded"
    },
    {
      id: "pay5",
      date: "Mar 18, 2025",
      job: "Electrical Wiring",
      provider: "Elite Electricians",
      amount: "$425.00",
      status: "Paid"
    }
  ];
  
  // Calculate total spending
  const totalSpending = payments
    .filter(payment => payment.status === "Paid")
    .reduce((total, payment) => total + parseFloat(payment.amount.replace('$', '')), 0);

  return (
    <div className="space-y-6">
      <h1 className="text-2xl md:text-3xl font-bold">Payments</h1>
      <p className="text-muted-foreground">View your payment history</p>
      
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card className="md:col-span-3">
          <CardHeader className="flex flex-row items-center justify-between">
            <CardTitle>Payment History</CardTitle>
            <div className="relative w-full md:w-64">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
              <Input placeholder="Search payments..." className="pl-10" />
            </div>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Date</TableHead>
                  <TableHead>Job</TableHead>
                  <TableHead>Provider</TableHead>
                  <TableHead>Amount</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead className="w-12">Receipt</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {payments.map(payment => (
                  <TableRow key={payment.id}>
                    <TableCell>{payment.date}</TableCell>
                    <TableCell>{payment.job}</TableCell>
                    <TableCell>{payment.provider}</TableCell>
                    <TableCell>{payment.amount}</TableCell>
                    <TableCell>
                      <Badge variant="outline" className={
                        payment.status === "Paid" 
                          ? "bg-green-100 text-green-800 hover:bg-green-100"
                          : "bg-red-100 text-red-800 hover:bg-red-100"
                      }>
                        {payment.status}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <Download className="h-4 w-4 cursor-pointer text-muted-foreground hover:text-foreground" />
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <CreditCard className="h-5 w-5" />
              Payment Summary
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <div className="text-sm text-muted-foreground">Total Spending</div>
                <div className="text-2xl font-bold">${totalSpending.toFixed(2)}</div>
              </div>
              
              <div>
                <div className="text-sm text-muted-foreground">Transactions</div>
                <div className="text-xl font-medium">{payments.length}</div>
              </div>
              
              <div className="pt-4 border-t">
                <div className="text-sm font-medium">Payment Methods</div>
                <ul className="mt-2 space-y-1">
                  <li className="text-sm flex items-center">
                    <CreditCard className="h-3 w-3 mr-2" />
                    Visa ending in 4242
                  </li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
