
import React from 'react';
import { Calendar } from "@/components/ui/calendar";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { CustomerAppointments } from './CustomerAppointments';
import { CalendarDays, Clock, MapPin } from 'lucide-react';
import { Badge } from "@/components/ui/badge";

export function CustomerCalendar() {
  const [date, setDate] = React.useState<Date | undefined>(new Date());
  
  return (
    <div className="space-y-6 p-6 bg-gray-50 min-h-screen">
      {/* Header Section */}
      <div className="flex items-center justify-between">
        <div className="space-y-2">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-blue-600 rounded-lg">
              <CalendarDays className="h-6 w-6 text-white" />
            </div>
            <div>
              <h1 className="text-3xl font-bold text-gray-900">
                Calendar
              </h1>
              <p className="text-gray-600">Manage your appointments and scheduled services</p>
            </div>
          </div>
        </div>
        <div className="hidden md:flex items-center gap-2">
          <Badge variant="secondary" className="bg-blue-100 text-blue-700">
            <Clock className="h-3 w-3 mr-1" />
            3 Upcoming
          </Badge>
        </div>
      </div>

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Calendar Card */}
        <Card className="lg:col-span-1 border-0 shadow-lg bg-white">
          <CardHeader className="pb-4 bg-blue-600 text-white rounded-t-lg">
            <CardTitle className="flex items-center gap-2 text-white">
              <CalendarDays className="h-5 w-5" />
              Select Date
            </CardTitle>
          </CardHeader>
          <CardContent className="p-6">
            <Calendar 
              mode="single" 
              selected={date} 
              onSelect={setDate} 
              className="rounded-md border-0 w-full mx-auto"
              classNames={{
                months: "flex flex-col w-full",
                month: "space-y-4 w-full",
                caption: "flex justify-center pt-1 relative items-center mb-4",
                caption_label: "text-lg font-semibold text-gray-800",
                nav: "space-x-1 flex items-center",
                nav_button: "h-8 w-8 bg-transparent p-0 opacity-70 hover:opacity-100 hover:bg-blue-50 rounded-lg transition-all",
                nav_button_previous: "absolute left-1",
                nav_button_next: "absolute right-1",
                table: "w-full border-collapse",
                head_row: "flex w-full mb-2",
                head_cell: "text-gray-600 rounded-md w-full font-medium text-sm flex-1 text-center py-2",
                row: "flex w-full mt-1",
                cell: "relative flex-1 text-center p-0 focus-within:relative focus-within:z-20",
                day: "h-10 w-full font-normal text-sm flex items-center justify-center rounded-lg transition-all hover:bg-blue-50 hover:text-blue-700 focus:bg-blue-50 focus:text-blue-700 aria-selected:opacity-100",
                day_selected: "bg-blue-600 text-white hover:bg-blue-700 focus:bg-blue-700",
                day_today: "bg-blue-100 text-blue-700 font-semibold",
                day_outside: "text-gray-400 opacity-50 aria-selected:bg-gray-100 aria-selected:text-gray-500 aria-selected:opacity-30",
                day_disabled: "text-gray-300 opacity-30 cursor-not-allowed",
                day_range_middle: "aria-selected:bg-blue-50 aria-selected:text-blue-700",
                day_hidden: "invisible",
              }}
            />
            <div className="mt-6 p-4 bg-blue-50 rounded-lg border border-blue-100">
              <p className="text-sm text-gray-700 text-center font-medium">
                {date ? (
                  <>
                    <span className="text-gray-600">Selected:</span>{" "}
                    <span className="font-semibold text-blue-600">
                      {date.toLocaleDateString('en-US', { 
                        weekday: 'long', 
                        year: 'numeric', 
                        month: 'long', 
                        day: 'numeric' 
                      })}
                    </span>
                  </>
                ) : (
                  'Select a date to view appointments'
                )}
              </p>
            </div>
          </CardContent>
        </Card>
        
        {/* Appointments Section */}
        <div className="lg:col-span-2">
          <CustomerAppointments />
        </div>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card className="border-0 shadow-md bg-white hover:shadow-lg transition-shadow">
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-green-100 rounded-lg">
                <CalendarDays className="h-5 w-5 text-green-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600">This Month</p>
                <p className="text-xl font-bold text-green-600">3 Services</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card className="border-0 shadow-md bg-white hover:shadow-lg transition-shadow">
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-orange-100 rounded-lg">
                <Clock className="h-5 w-5 text-orange-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600">Pending</p>
                <p className="text-xl font-bold text-orange-600">1 Approval</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card className="border-0 shadow-md bg-white hover:shadow-lg transition-shadow">
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-purple-100 rounded-lg">
                <MapPin className="h-5 w-5 text-purple-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600">Next Visit</p>
                <p className="text-xl font-bold text-purple-600">Apr 25</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
