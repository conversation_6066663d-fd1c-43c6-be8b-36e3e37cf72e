
import React from 'react';
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Gift, Copy, Trophy } from 'lucide-react';
import { useToast } from "@/hooks/use-toast";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { useIsMobile } from "@/hooks/use-mobile";
import { MobileCustomerReferrals } from './MobileCustomerReferrals';

type ReferralActivity = {
  friendName: string;
  date: string;
  amount: number;
};

export function CustomerReferrals() {
  const isMobile = useIsMobile();
  
  // If on a mobile device, show the mobile-optimized version
  if (isMobile) {
    return <MobileCustomerReferrals />;
  }
  
  const { toast } = useToast();
  const [showRewardDialog, setShowRewardDialog] = React.useState(false);
  const [rewardDialogContent, setRewardDialogContent] = React.useState({
    title: '',
    description: '',
    amount: 0
  });

  // In a real app, these would come from user profile stored in state/context
  // For now using mock data
  const firstName = "John";   // Would come from user profile
  const lastName = "Doe";     // Would come from user profile
  
  // Generate a referral code based on user info
  const generateReferralCode = () => {
    // Combine firstName and lastName
    let baseCode = `${firstName.toLowerCase()}${lastName.toLowerCase()}`;
    
    // Replace special characters
    baseCode = baseCode.replace(/[^a-z0-9]/g, '');
    
    // In a real app, we would check for duplicates in the database
    // and append a number if necessary
    // Here we'll simulate with a random number to demonstrate
    const userCount = Math.floor(Math.random() * 3); // Simulate 0-2 duplicates
    
    if (userCount > 0) {
      baseCode = `${baseCode}${userCount}`;
    }
    
    return baseCode;
  };
  
  const referralCode = generateReferralCode();
  const referralLink = `https://jobon.com/ref/${referralCode}`;
  
  const [referralStats, setReferralStats] = React.useState({
    totalReferred: 7,
    totalEarned: 350,
  });

  // Recent activity - Would come from API
  const [recentActivity, setRecentActivity] = React.useState<ReferralActivity[]>([
    { friendName: "Sarah T.", date: "2025-04-21", amount: 50 },
    { friendName: "Michael R.", date: "2025-04-20", amount: 50 },
    { friendName: "Emma P.", date: "2025-04-19", amount: 50 },
  ]);

  const copyReferralLink = () => {
    navigator.clipboard.writeText(referralLink);
    toast({
      title: "Referral Link Copied!",
      description: "Share it with your friends to earn rewards.",
    });
  };

  // Calculate progress percentage for the next milestone
  const getNextMilestone = () => {
    if (referralStats.totalReferred < 3) {
      return { target: 3, progress: (referralStats.totalReferred / 3) * 100 };
    } else if (referralStats.totalReferred < 5) {
      return { target: 5, progress: (referralStats.totalReferred / 5) * 100 };
    } else {
      const nextTarget = Math.ceil(referralStats.totalReferred / 5) * 5;
      return {
        target: nextTarget,
        progress: (referralStats.totalReferred / nextTarget) * 100
      };
    }
  };

  // Get badge status based on referral count
  const getBadges = () => {
    return {
      starter: referralStats.totalReferred >= 1,
      expert: referralStats.totalReferred >= 3,
      champion: referralStats.totalReferred >= 5,
    };
  };

  const nextMilestone = getNextMilestone();
  const badges = getBadges();

  return (
    <div className="space-y-6">
      <h1 className="text-2xl font-bold">Referrals</h1>

      {/* Main Referral Card */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Gift className="h-5 w-5 text-primary" />
            Invite Friends & Earn Unlimited Rewards
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <p className="text-lg">
            Get $50 JobON Credit for every friend who completes their first job. No limits - invite as many friends as you want!
          </p>

          <div className="flex gap-2">
            <Input value={referralLink} readOnly className="font-mono" />
            <Button 
              onClick={copyReferralLink} 
              variant="outline"
              className="bg-gradient-to-r from-blue-50 to-blue-100 hover:from-blue-100 hover:to-blue-200 text-blue-600 border-blue-200"
            >
              <Copy className="h-4 w-4 mr-2" />
              Copy Link
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Progress Tracker */}
      <Card>
        <CardContent className="pt-6 space-y-6">
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span>Progress to next milestone ({nextMilestone.target} referrals)</span>
              <span>{referralStats.totalReferred} / {nextMilestone.target}</span>
            </div>
            <Progress value={nextMilestone.progress} className="h-2 bg-slate-100" />
          </div>

          <div className="flex gap-4 flex-wrap">
            {badges.starter && (
              <Badge className="bg-gradient-to-r from-blue-50 to-blue-100 hover:from-blue-100 hover:to-blue-200 text-blue-600 border border-blue-200 font-medium">
                🌟 Referral Starter
              </Badge>
            )}
            {badges.expert && (
              <Badge className="bg-gradient-to-r from-indigo-50 to-indigo-100 hover:from-indigo-100 hover:to-indigo-200 text-indigo-600 border border-indigo-200 font-medium">
                ⭐ Referral Expert
              </Badge>
            )}
            {badges.champion && (
              <Badge className="bg-gradient-to-r from-purple-50 to-purple-100 hover:from-purple-100 hover:to-purple-200 text-purple-600 border border-purple-200 font-medium">
                🏆 Referral Champion
              </Badge>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Stats Grid */}
      <div className="grid gap-6 md:grid-cols-2">
        <Card>
          <CardContent className="pt-6">
            <div className="text-center">
              <h3 className="text-lg font-medium mb-2">Total Friends Referred</h3>
              <p className="text-3xl font-bold">{referralStats.totalReferred}</p>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="text-center">
              <h3 className="text-lg font-medium mb-2">Total Credits Earned</h3>
              <p className="text-3xl font-bold">${referralStats.totalEarned}</p>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Recent Activity */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Trophy className="h-5 w-5 text-primary" />
            Recent Referral Activity
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {recentActivity.map((activity, index) => (
              <div key={index} className="flex justify-between items-center py-2 border-b last:border-0">
                <div>
                  <p className="font-medium">{activity.friendName} completed their first job</p>
                  <p className="text-sm text-muted-foreground">
                    {new Date(activity.date).toLocaleDateString()}
                  </p>
                </div>
                <Badge className="bg-gradient-to-r from-slate-50 to-blue-50 text-blue-600 border border-blue-100">
                  +${activity.amount} earned
                </Badge>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Reward Dialog */}
      <Dialog open={showRewardDialog} onOpenChange={setShowRewardDialog}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle className="text-center text-xl">{rewardDialogContent.title}</DialogTitle>
            <DialogDescription className="text-center">
              {rewardDialogContent.description}
            </DialogDescription>
          </DialogHeader>
          <div className="flex flex-col items-center py-6">
            <div className="bg-blue-50 p-4 rounded-full mb-4">
              <Gift className="h-12 w-12 text-blue-600" />
            </div>
            <p className="text-3xl font-bold mb-2">${rewardDialogContent.amount}</p>
            <p className="text-center text-muted-foreground">
              Added to your wallet balance!
            </p>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}
