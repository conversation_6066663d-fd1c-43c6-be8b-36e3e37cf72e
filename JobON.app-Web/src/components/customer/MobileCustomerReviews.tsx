import React from 'react';
import { 
  <PERSON>, 
  <PERSON><PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON>er
} from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Star, PlusCircle } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { cn } from "@/lib/utils";

interface Review {
  id: string;
  provider: {
    name: string;
    avatar?: string;
    initials: string;
  };
  jobTitle: string;
  date: string;
  rating: number;
  comment: string;
}

interface PendingReview {
  id: string;
  provider: {
    name: string;
    avatar?: string;
    initials: string;
  };
  jobTitle: string;
  completionDate: string;
}

export function MobileCustomerReviews() {
  // Mock reviews - keep the same data as the desktop version
  const reviews: Review[] = [
    {
      id: "rev1",
      provider: {
        name: "Green Thumb Landscaping",
        avatar: "https://images.unsplash.com/photo-1534528741775-53994a69daeb?q=80&w=1964&auto=format&fit=crop",
        initials: "GT"
      },
      jobTitle: "Lawn Maintenance",
      date: "Apr 10, 2025",
      rating: 5,
      comment: "Excellent service! They were on time, professional, and left my yard looking amazing. I'll definitely hire them again for future landscaping needs."
    },
    {
      id: "rev2",
      provider: {
        name: "Mike's Plumbing",
        initials: "MP"
      },
      jobTitle: "Bathroom Sink Repair",
      date: "Mar 28, 2025",
      rating: 4,
      comment: "Good service overall. Fixed the issue quickly, though arrived a bit later than scheduled. Fair pricing and professional demeanor."
    },
    {
      id: "rev3",
      provider: {
        name: "Elite Electricians",
        initials: "EE"
      },
      jobTitle: "Lighting Installation",
      date: "Mar 15, 2025",
      rating: 3,
      comment: "Average service. The work was completed correctly, but they left a mess that I had to clean up afterward. Communication could be improved."
    }
  ];
  
  // Mock pending reviews - keep the same data as the desktop version
  const pendingReviews: PendingReview[] = [
    {
      id: "pending1",
      provider: {
        name: "Deluxe Renovations",
        initials: "DR"
      },
      jobTitle: "Kitchen Cabinet Installation",
      completionDate: "Apr 18, 2025"
    }
  ];
  
  // Generate stars based on rating
  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }).map((_, index) => (
      <Star 
        key={index} 
        className={`h-3.5 w-3.5 ${index < rating ? 'text-amber-500 fill-amber-500' : 'text-gray-300'}`} 
      />
    ));
  };

  return (
    <div className="p-4 pb-16 space-y-4">
      {/* Pending Reviews Section */}
      {pendingReviews.length > 0 && (
        <div className="mb-6">
          <h2 className="text-base font-bold mb-2">Pending Reviews</h2>
          <div className="space-y-3">
            {pendingReviews.map(review => (
              <Card key={review.id} className="border-amber-200 bg-amber-50 shadow-sm">
                <CardContent className="p-4">
                  <div className="flex items-start gap-3">
                    <Avatar className="h-10 w-10">
                      {review.provider.avatar && (
                        <AvatarImage src={review.provider.avatar} alt={review.provider.name} />
                      )}
                      <AvatarFallback>{review.provider.initials}</AvatarFallback>
                    </Avatar>
                    <div className="flex-1">
                      <h4 className="font-medium text-sm">{review.provider.name}</h4>
                      <p className="text-xs text-muted-foreground">{review.jobTitle}</p>
                      <p className="text-xs text-muted-foreground mb-3">Completed on {review.completionDate}</p>
                      <Button size="sm" className="w-full text-xs">
                        <PlusCircle className="h-3.5 w-3.5 mr-1.5" /> Leave Review
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      )}

      {/* Completed Reviews Section */}
      <div>
        <div className="flex justify-between items-center mb-2">
          <h2 className="text-base font-bold">Your Reviews</h2>
          <Badge variant="outline" className="bg-gray-100">{reviews.length}</Badge>
        </div>
        
        <div className="space-y-3">
          {reviews.length > 0 ? (
            reviews.map(review => (
              <Card key={review.id} className="shadow-sm overflow-hidden">
                <div className={cn(
                  "h-1",
                  review.rating >= 4 ? "bg-green-500" : 
                  review.rating >= 3 ? "bg-amber-500" : "bg-red-500"
                )} />
                <CardHeader className="p-3 pb-0">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Avatar className="h-8 w-8">
                        {review.provider.avatar && (
                          <AvatarImage src={review.provider.avatar} alt={review.provider.name} />
                        )}
                        <AvatarFallback>{review.provider.initials}</AvatarFallback>
                      </Avatar>
                      <div>
                        <h4 className="font-medium text-sm leading-tight">{review.provider.name}</h4>
                        <p className="text-xs text-muted-foreground leading-tight">{review.jobTitle}</p>
                      </div>
                    </div>
                    <div className="text-xs text-muted-foreground">{review.date}</div>
                  </div>
                </CardHeader>
                <CardContent className="p-3 pt-2">
                  <div className="flex mb-1.5">{renderStars(review.rating)}</div>
                  <p className="text-xs leading-normal">
                    {review.comment.length > 150 
                      ? `${review.comment.substring(0, 150)}...` 
                      : review.comment}
                  </p>
                </CardContent>
                <CardFooter className="p-3 pt-0 flex justify-end">
                  <Button variant="ghost" size="sm" className="h-7 text-xs">
                    Edit
                  </Button>
                </CardFooter>
              </Card>
            ))
          ) : (
            <Card className="border-dashed">
              <CardContent className="py-8 text-center">
                <div className="flex justify-center mb-4">
                  <Star className="h-10 w-10 text-muted-foreground" />
                </div>
                <h3 className="text-base font-medium mb-1">No reviews yet</h3>
                <p className="text-xs text-muted-foreground mb-4">
                  You haven't left any reviews for providers yet. Reviews help the community find great service providers.
                </p>
              </CardContent>
            </Card>
          )}
        </div>
      </div>

      {/* Empty state if no reviews and no pending reviews */}
      {reviews.length === 0 && pendingReviews.length === 0 && (
        <div className="flex flex-col items-center justify-center text-center pt-8">
          <div className="bg-gray-100 rounded-full p-4 mb-4">
            <Star className="h-8 w-8 text-gray-400" />
          </div>
          <h3 className="text-lg font-medium mb-2">No reviews yet</h3>
          <p className="text-sm text-muted-foreground mb-6 max-w-xs mx-auto">
            When you complete a service, you'll be able to leave reviews for providers here.
          </p>
          <Button variant="outline">
            Find services
          </Button>
        </div>
      )}
    </div>
  );
}
