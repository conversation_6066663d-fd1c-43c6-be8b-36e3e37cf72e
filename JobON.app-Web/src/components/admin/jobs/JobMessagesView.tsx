
import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Textarea } from "@/components/ui/textarea";
import { Button } from "@/components/ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { MessageSquare, Send } from "lucide-react";
import { useToast } from "@/hooks/use-toast";

interface Message {
  id: string;
  sender: "customer" | "provider" | "admin";
  senderName: string;
  senderAvatar?: string;
  text: string;
  timestamp: string;
  attachments?: {
    id: string;
    name: string;
    url: string;
  }[];
}

interface JobMessagesViewProps {
  jobId: string;
  customerName: string;
  customerAvatar?: string;
  providerName?: string;
  providerAvatar?: string;
}

export const JobMessagesView: React.FC<JobMessagesViewProps> = ({
  jobId,
  customerName,
  customerAvatar,
  providerName,
  providerAvatar,
}) => {
  const { toast } = useToast();
  const [adminMessage, setAdminMessage] = React.useState("");
  const [messages, setMessages] = React.useState<Message[]>([]);
  const [loading, setLoading] = React.useState(true);
  
  // Fetch messages for this job
  React.useEffect(() => {
    // In a real app, this would be an API call
    // For now, we'll use mock data
    setTimeout(() => {
      const mockMessages: Message[] = [
        {
          id: "msg-1",
          sender: "customer",
          senderName: customerName,
          senderAvatar: customerAvatar,
          text: "Hello, I need help with my bathroom plumbing issue. The sink is leaking and there's water damage.",
          timestamp: "2025-04-15T10:30:00Z",
          attachments: [
            {
              id: "att-1",
              name: "leak_photo.jpg",
              url: "https://images.unsplash.com/photo-1565068178556-119dae8c5ab7?q=80&w=2070&auto=format&fit=crop",
            },
          ],
        },
        {
          id: "msg-2",
          sender: "provider",
          senderName: providerName || "Provider",
          senderAvatar: providerAvatar,
          text: "I can help with that. Can you provide more details about when the leak started and if you've tried any fixes?",
          timestamp: "2025-04-15T11:15:00Z",
        },
        {
          id: "msg-3",
          sender: "customer",
          senderName: customerName,
          senderAvatar: customerAvatar,
          text: "It started about 2 days ago. I tried tightening the connections but it didn't help.",
          timestamp: "2025-04-15T11:30:00Z",
        },
        {
          id: "msg-4",
          sender: "admin",
          senderName: "Admin User",
          text: "Hi both, just checking if you've been able to schedule a time for the plumber to visit?",
          timestamp: "2025-04-15T14:45:00Z",
        },
        {
          id: "msg-5",
          sender: "provider",
          senderName: providerName || "Provider",
          senderAvatar: providerAvatar,
          text: "Yes, I'll be there tomorrow at 2pm. I've already confirmed with the customer.",
          timestamp: "2025-04-15T15:00:00Z",
        },
      ];
      setMessages(mockMessages);
      setLoading(false);
    }, 1000);
  }, [customerName, customerAvatar, providerName, providerAvatar]);

  const handleSendMessage = () => {
    if (!adminMessage.trim()) return;

    // In a real app, this would send the message to an API
    const newMessage: Message = {
      id: `msg-${Date.now()}`,
      sender: "admin",
      senderName: "Admin User",
      text: adminMessage,
      timestamp: new Date().toISOString(),
    };

    setMessages([...messages, newMessage]);
    toast({
      title: "Message sent",
      description: "Your message has been sent to both customer and provider.",
    });
    setAdminMessage("");
  };

  // Get initials from name for avatar fallback
  const getInitials = (name: string) => {
    return name
      .split(" ")
      .map((n) => n[0])
      .join("")
      .toUpperCase()
      .substring(0, 2);
  };

  return (
    <Card className="h-full">
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center gap-2 text-lg">
          <MessageSquare className="h-5 w-5" />
          Conversation
        </CardTitle>
      </CardHeader>
      <CardContent>
        {loading ? (
          <div className="flex justify-center py-8">
            <p>Loading messages...</p>
          </div>
        ) : (
          <>
            <ScrollArea className="h-[400px] pr-4 mb-4">
              <div className="space-y-4">
                {messages.map((message) => (
                  <div key={message.id} className="flex flex-col">
                    <div className="flex items-start gap-2 mb-1">
                      <Avatar className="h-8 w-8">
                        <AvatarImage src={message.senderAvatar} />
                        <AvatarFallback>
                          {getInitials(message.senderName)}
                        </AvatarFallback>
                      </Avatar>
                      <div className="flex flex-col">
                        <div className="flex items-center gap-2">
                          <span className="font-medium">{message.senderName}</span>
                          <Badge
                            variant="outline"
                            className={
                              message.sender === "admin"
                                ? "bg-blue-50 text-blue-800 border-blue-200"
                                : message.sender === "provider"
                                ? "bg-green-50 text-green-800 border-green-200"
                                : "bg-gray-50 text-gray-800 border-gray-200"
                            }
                          >
                            {message.sender.charAt(0).toUpperCase() + message.sender.slice(1)}
                          </Badge>
                          <span className="text-xs text-gray-500">
                            {new Date(message.timestamp).toLocaleString()}
                          </span>
                        </div>
                        <p className="text-sm mt-1">{message.text}</p>
                        
                        {message.attachments && message.attachments.length > 0 && (
                          <div className="mt-2 flex flex-wrap gap-2">
                            {message.attachments.map((attachment) => (
                              <div
                                key={attachment.id}
                                className="relative group"
                              >
                                <img
                                  src={attachment.url}
                                  alt={attachment.name}
                                  className="h-20 w-20 object-cover rounded border border-gray-200"
                                />
                                <div className="absolute bottom-0 left-0 right-0 bg-black bg-opacity-50 text-white text-xs p-1 truncate">
                                  {attachment.name}
                                </div>
                              </div>
                            ))}
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </ScrollArea>

            <div className="mt-4">
              <Textarea
                placeholder="Type your message as admin..."
                value={adminMessage}
                onChange={(e) => setAdminMessage(e.target.value)}
                className="min-h-[100px]"
              />
              <div className="flex justify-end mt-2">
                <Button onClick={handleSendMessage} disabled={!adminMessage.trim()}>
                  <Send className="h-4 w-4 mr-2" />
                  Send Message
                </Button>
              </div>
            </div>
          </>
        )}
      </CardContent>
    </Card>
  );
};
