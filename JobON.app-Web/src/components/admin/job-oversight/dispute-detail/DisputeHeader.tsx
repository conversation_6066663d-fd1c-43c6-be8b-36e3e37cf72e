
import React, { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { ArrowLeft } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { JobDispute, DisputeStatus } from "@/types/jobs";
import { DisputeResolutionDialog } from "./resolution/DisputeResolutionDialog";

interface DisputeHeaderProps {
  dispute: JobDispute;
  onBack: () => void;
  onStatusChange?: (disputeId: string, newStatus: DisputeStatus) => void;
}

export const DisputeHeader: React.FC<DisputeHeaderProps> = ({
  dispute,
  onBack,
  onStatusChange
}) => {
  const { toast } = useToast();
  const [showResolveDialog, setShowResolveDialog] = useState(false);

  const handleResolveDispute = (resolutionNote: string, selectedActions: string[]) => {
    // Update dispute status
    if (onStatusChange) {
      onStatusChange(dispute.id, "resolved");
    }
    
    toast({
      title: "Dispute resolved",
      description: "The resolution has been saved and all parties notified"
    });
  };

  return (
    <div className="flex items-center justify-between">
      <Button
        variant="ghost"
        size="sm"
        onClick={onBack}
        className="gap-1"
      >
        <ArrowLeft className="h-4 w-4" />
        Back to disputes
      </Button>
      
      <div className="flex items-center gap-2">
        {dispute.status !== "resolved" && dispute.status !== "closed" && (
          <DisputeResolutionDialog
            open={showResolveDialog}
            onOpenChange={setShowResolveDialog}
            onResolve={handleResolveDispute}
          />
        )}
        
        {dispute.status !== "resolved" && dispute.status !== "closed" && (
          <Button 
            className="bg-green-600 hover:bg-green-700"
            onClick={() => setShowResolveDialog(true)}
          >
            Resolve Dispute
          </Button>
        )}
        
        {dispute.status !== "submitted" && (
          <Button
            variant="outline"
            onClick={() => {
              // In a real app, this would make an API call
              toast({
                description: "Notification sent to both parties"
              });
            }}
          >
            Send Update
          </Button>
        )}
      </div>
    </div>
  );
};
