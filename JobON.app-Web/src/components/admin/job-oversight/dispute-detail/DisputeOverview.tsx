
import React from "react";
import { useToast } from "@/hooks/use-toast";
import { But<PERSON> } from "@/components/ui/button";
import { JobDispute, DisputeStatus } from "@/types/jobs";

interface DisputeOverviewProps {
  dispute: JobDispute;
  onStatusChange?: (disputeId: string, newStatus: DisputeStatus) => void;
  onOpenResolveDialog: () => void;
}

export const DisputeOverview: React.FC<DisputeOverviewProps> = ({ 
  dispute, 
  onStatusChange,
  onOpenResolveDialog
}) => {
  const { toast } = useToast();

  return (
    <div className="p-4 space-y-4">
      <div>
        <h3 className="text-sm font-medium mb-2">Dispute Details</h3>
        <div className="grid grid-cols-2 gap-2 text-sm">
          <div className="flex flex-col p-2 bg-muted rounded-md">
            <span className="text-muted-foreground">Created</span>
            <span>{new Date(dispute.createdAt).toLocaleDateString()}</span>
          </div>
          <div className="flex flex-col p-2 bg-muted rounded-md">
            <span className="text-muted-foreground">Updated</span>
            <span>{new Date(dispute.updatedAt).toLocaleDateString()}</span>
          </div>
          <div className="flex flex-col p-2 bg-muted rounded-md">
            <span className="text-muted-foreground">Status</span>
            <span className="capitalize">{dispute.status.replace("_", " ")}</span>
          </div>
          <div className="flex flex-col p-2 bg-muted rounded-md">
            <span className="text-muted-foreground">Severity</span>
            <span className="capitalize">{dispute.severity}</span>
          </div>
        </div>
      </div>
      
      {dispute.resolution ? (
        <div>
          <h3 className="text-sm font-medium mb-2">Resolution</h3>
          <div className="p-3 border rounded-md">
            <div className="text-sm">{dispute.resolution.description}</div>
            
            <div className="mt-3">
              <h4 className="text-xs font-medium text-muted-foreground mb-1">Actions Taken</h4>
              <div className="space-y-2">
                {dispute.resolution.actionTaken.map((action, index) => (
                  <div key={index} className="flex items-center gap-2 text-sm">
                    <div className="w-2 h-2 rounded-full bg-green-500"></div>
                    <span>{action.description}</span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      ) : (
        <div className="space-y-3">
          <h3 className="text-sm font-medium">Recommended Actions</h3>
          
          <div className="grid gap-2">
            {dispute.status === "submitted" && (
              <Button 
                className="justify-start text-left"
                onClick={() => {
                  if (onStatusChange) {
                    onStatusChange(dispute.id, "under_review");
                  }
                  toast({
                    description: "Dispute status updated to Under Review"
                  });
                }}
              >
                Start Review Process
              </Button>
            )}
            
            {dispute.status === "under_review" && (
              <Button 
                className="justify-start text-left"
                onClick={() => {
                  if (onStatusChange) {
                    onStatusChange(dispute.id, "mediation");
                  }
                  toast({
                    description: "Mediation process has been initiated"
                  });
                }}
              >
                Begin Mediation
              </Button>
            )}
            
            {(dispute.status === "under_review" || dispute.status === "mediation") && (
              <Button 
                variant="outline"
                className="justify-start text-left"
                onClick={() => {
                  toast({
                    description: "Scheduled a meeting with both parties"
                  });
                }}
              >
                Schedule Meeting
              </Button>
            )}
            
            <Button 
              variant={dispute.status === "mediation" ? "default" : "outline"}
              className="justify-start text-left"
              onClick={onOpenResolveDialog}
            >
              Resolve Dispute
            </Button>
          </div>
        </div>
      )}
    </div>
  );
};
