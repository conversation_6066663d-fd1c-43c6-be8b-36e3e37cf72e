
import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { useToast } from "@/hooks/use-toast";
import { JobDispute, DisputeStatus } from "@/types/jobs";
import { ArrowLeft } from "lucide-react";
import { 
  <PERSON><PERSON>, 
  <PERSON><PERSON><PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON><PERSON><PERSON>, 
  She<PERSON><PERSON>rigger 
} from "@/components/ui/sheet";
import { DisputeStatusBadge } from "../DisputeStatusBadge";

interface MobileDisputeHeaderProps {
  dispute: JobDispute;
  onBack: () => void;
  onStatusChange?: (disputeId: string, newStatus: DisputeStatus) => void;
}

export const MobileDisputeHeader: React.FC<MobileDisputeHeaderProps> = ({
  dispute,
  onBack,
  onStatusChange
}) => {
  const { toast } = useToast();

  // Get next action text based on status
  const getNextActionText = () => {
    switch(dispute.status) {
      case "submitted":
        return "Begin Review";
      case "under_review":
        return "Start Mediation";
      case "mediation":
        return "Resolve Dispute";
      default:
        return "Resolve Dispute";
    }
  };
  
  // Get next status based on current status
  const getNextStatus = () => {
    switch(dispute.status) {
      case "submitted":
        return "under_review";
      case "under_review":
        return "mediation";
      case "mediation":
        return "resolved";
      default:
        return "resolved";
    }
  };

  const handlePrimaryAction = () => {
    // If dispute is already resolved or closed, do nothing
    if (dispute.status === "resolved" || dispute.status === "closed") {
      return;
    }
    
    // For mediation status, show resolve dialog instead of changing status directly
    if (dispute.status === "mediation") {
      toast({
        description: "Opening resolution dialog"
      });
      return;
    }
    
    // Otherwise, update status
    if (onStatusChange) {
      const nextStatus = getNextStatus();
      onStatusChange(dispute.id, nextStatus as DisputeStatus);
      toast({
        description: `Dispute moved to ${nextStatus.replace('_', ' ')} stage`
      });
    }
  };

  // Check if dispute is active (not resolved or closed)
  const isDisputeActive = dispute.status !== "resolved" && dispute.status !== "closed";
  
  return (
    <div className="bg-white border-b fixed top-0 left-0 right-0 z-40 shadow-sm">
      <div className="px-3 py-2">
        <div className="flex items-center justify-between">
          <Button
            variant="ghost"
            size="sm"
            onClick={onBack}
            className="p-0 h-8 w-8 min-w-8"
          >
            <ArrowLeft className="h-4 w-4" />
          </Button>
          
          <div className="flex-1 mx-2">
            <div className="flex items-center gap-2">
              <h2 className="text-sm font-medium">Dispute #{dispute.id.substring(0, 8)}</h2>
              <DisputeStatusBadge status={dispute.status} />
            </div>
            <span className="text-xs text-muted-foreground">
              {new Date(dispute.updatedAt).toLocaleDateString()}
            </span>
          </div>
          
          {isDisputeActive && (
            <Sheet>
              <SheetTrigger asChild>
                <Button 
                  variant="outline" 
                  size="sm" 
                  className="h-8 border rounded-md"
                >
                  Actions
                </Button>
              </SheetTrigger>
              <SheetContent side="bottom" className="h-[40%] pt-6">
                <SheetHeader>
                  <SheetTitle>Dispute Actions</SheetTitle>
                </SheetHeader>
                <div className="grid gap-3 py-4">
                  {isDisputeActive && (
                    <Button 
                      className="mb-2 bg-green-600 hover:bg-green-700 text-white"
                      onClick={handlePrimaryAction}
                    >
                      {getNextActionText()}
                    </Button>
                  )}
                  
                  <Button 
                    variant="outline" 
                    className="justify-start h-10"
                    onClick={() => {
                      toast({
                        description: "Notification sent to both parties"
                      });
                    }}
                  >
                    Send update to both parties
                  </Button>
                  
                  <Button 
                    variant="outline" 
                    className="justify-start h-10"
                    onClick={() => {
                      toast({
                        description: "Dispute flagged as priority"
                      });
                    }}
                  >
                    Flag as priority
                  </Button>
                  
                  <Button 
                    variant="outline" 
                    className="justify-start h-10"
                    onClick={() => {
                      toast({
                        description: "Dispute assigned to current admin"
                      });
                    }}
                  >
                    Assign to me
                  </Button>
                </div>
              </SheetContent>
            </Sheet>
          )}
        </div>
      </div>
    </div>
  );
};
