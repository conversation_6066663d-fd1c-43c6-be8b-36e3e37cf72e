
import React from "react";
import { JobDispute, DisputeStatus } from "@/types/jobs";
import { Check, Clock, FileText, MessageSquare, User } from "lucide-react";
import { Badge } from "@/components/ui/badge";

interface DisputeWorkflowProps {
  dispute: JobDispute;
  onStatusChange?: (disputeId: string, newStatus: DisputeStatus) => void;
}

export const DisputeWorkflow: React.FC<DisputeWorkflowProps> = ({
  dispute,
  onStatusChange
}) => {
  // Define workflow steps
  const workflowSteps = [
    { 
      id: "submitted", 
      label: "Submitted", 
      description: "Dispute has been filed",
      icon: <Clock className="h-5 w-5" />,
      completed: true
    },
    { 
      id: "under_review", 
      label: "Under Review", 
      description: "Admin reviewing details",
      icon: <FileText className="h-5 w-5" />,
      completed: ["under_review", "mediation", "resolved", "closed"].includes(dispute.status)
    },
    { 
      id: "gathering_info", 
      label: "Gathering Information", 
      description: "Collecting statements and evidence",
      icon: <MessageSquare className="h-5 w-5" />,
      completed: ["mediation", "resolved", "closed"].includes(dispute.status)
    },
    { 
      id: "admin_decision", 
      label: "Admin Decision", 
      description: "Final resolution determined",
      icon: <User className="h-5 w-5" />,
      completed: ["resolved", "closed"].includes(dispute.status)
    },
    { 
      id: "resolved", 
      label: "Resolved", 
      description: "Dispute closed with resolution",
      icon: <Check className="h-5 w-5" />,
      completed: ["resolved", "closed"].includes(dispute.status)
    }
  ];

  const currentStepIndex = workflowSteps.findIndex(step => 
    (step.id === dispute.status) || 
    (step.id === "gathering_info" && dispute.status === "mediation") ||
    (step.id === "admin_decision" && dispute.status === "mediation" && dispute.resolution)
  );

  return (
    <div className="p-4 border rounded-md bg-muted/30 space-y-4">
      <h3 className="font-medium">Resolution Progress</h3>
      
      <div className="relative">
        {/* Progress line */}
        <div className="absolute left-4 top-0 w-0.5 h-full bg-gray-200"></div>
        
        {/* Steps */}
        <div className="space-y-6 relative">
          {workflowSteps.map((step, index) => (
            <div key={step.id} className="flex items-start ml-2">
              <div className={`z-10 flex items-center justify-center w-8 h-8 rounded-full shrink-0 
                ${step.completed 
                  ? "bg-green-100 text-green-600 border-green-200" 
                  : index === currentStepIndex 
                    ? "bg-blue-100 text-blue-600 border-blue-200"
                    : "bg-gray-100 text-gray-400 border-gray-200"} 
                border-2`}
              >
                {step.completed ? <Check className="h-4 w-4" /> : step.icon}
              </div>
              <div className="ml-3">
                <div className="flex items-center">
                  <h4 className="font-medium">{step.label}</h4>
                  {index === currentStepIndex && !step.completed && (
                    <Badge className="ml-2 bg-blue-100 text-blue-700 border-blue-200">Current</Badge>
                  )}
                </div>
                <p className="text-sm text-muted-foreground">{step.description}</p>
                
                {index === currentStepIndex && !step.completed && (
                  <div className="mt-2">
                    <p className="text-xs text-blue-600">
                      {step.id === "under_review" 
                        ? "Review case details and decide if more information is needed"
                        : step.id === "gathering_info" || step.id === "admin_decision"
                          ? "Communicate with both parties to gather necessary information"
                          : ""}
                    </p>
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};
