
import React from "react";
import { JobDispute, DisputeStatus } from "@/types/jobs";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { useToast } from "@/hooks/use-toast";
import { useUIHelpers } from "@/hooks/use-ui-helpers";
import { WorkflowStageItem } from "./workflow/WorkflowStageItem";
import { getWorkflowStages, getOverallProgress } from "./workflow/getWorkflowStages";

interface DisputeStageWorkflowProps {
  dispute: JobDispute;
  onStatusChange?: (disputeId: string, newStatus: DisputeStatus) => void;
}

export const DisputeStageWorkflow: React.FC<DisputeStageWorkflowProps> = ({
  dispute,
  onStatusChange
}) => {
  const { toast } = useToast();
  const { isMobile } = useUIHelpers();
  
  // Get workflow stages with more detailed steps
  const workflowStages = getWorkflowStages(
    dispute.status, 
    dispute.evidence && dispute.evidence.length > 0
  );

  // Find the current active stage
  const currentStageIndex = workflowStages.findIndex(stage => 
    stage.id === dispute.status || 
    (stage.id === "mediation" && dispute.status === "mediation") ||
    (stage.id === "admin_decision" && dispute.status === "mediation" && dispute.resolution)
  );

  const handleStageClick = (stageId: string) => {
    // Only allow moving to stages that are adjacent to the current stage
    const currentIndex = workflowStages.findIndex(stage => 
      stage.id === dispute.status
    );
    
    const targetIndex = workflowStages.findIndex(stage => 
      stage.id === stageId
    );
    
    // Only allow moving one stage at a time, forward or backward
    if (Math.abs(targetIndex - currentIndex) !== 1) {
      toast({
        description: "You can only move to adjacent stages",
        variant: "destructive"
      });
      return;
    }
    
    if (onStatusChange) {
      onStatusChange(dispute.id, stageId as DisputeStatus);
      toast({
        description: `Dispute moved to ${workflowStages[targetIndex].label} stage`
      });
    }
  };

  const isDisputeResolved = dispute.status === "resolved" || dispute.status === "closed";
  const overallProgress = getOverallProgress(dispute.status);

  return (
    <div className={`p-4 ${isMobile ? 'p-3' : 'p-6'} border rounded-md bg-muted/30 space-y-4 ${isMobile ? 'space-y-3' : 'space-y-6'}`}>
      <div className="space-y-2">
        <div className="flex items-center justify-between">
          <h3 className="font-medium text-sm">Resolution Progress</h3>
          <Badge variant="outline" className={
            isDisputeResolved 
              ? "bg-green-50 text-green-700 border-green-200" 
              : "bg-blue-50 text-blue-700 border-blue-200"
          }>
            {isDisputeResolved 
              ? "Completed" 
              : `${overallProgress}% Complete`}
          </Badge>
        </div>
        <Progress value={overallProgress} className="h-2 bg-gray-100" />
      </div>
      
      <div className={isMobile ? "space-y-3" : "space-y-6"}>
        {workflowStages.map((stage, index) => (
          <WorkflowStageItem
            key={stage.id}
            stage={stage}
            index={index}
            currentStageIndex={currentStageIndex}
            isLastStage={index === workflowStages.length - 1}
            isDisputeResolved={isDisputeResolved}
            onStageClick={handleStageClick}
          />
        ))}
      </div>
    </div>
  );
};
