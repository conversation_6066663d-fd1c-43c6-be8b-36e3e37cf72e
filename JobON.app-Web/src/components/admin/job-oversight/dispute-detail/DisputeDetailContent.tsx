
import React from "react";
import { <PERSON>, CardContent, CardDescription, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { DisputeStatusBadge } from "../DisputeStatusBadge";
import { DisputeParties } from "./DisputeParties";
import { DisputeTimeline } from "./DisputeTimeline";
import { JobDispute } from "@/types/jobs";
import { AlertTriangle, Clock, ShieldAlert } from "lucide-react";
import { useUIHelpers } from "@/hooks/use-ui-helpers";

interface DisputeDetailContentProps {
  dispute: JobDispute;
}

export const DisputeDetailContent: React.FC<DisputeDetailContentProps> = ({ dispute }) => {
  const { isMobile } = useUIHelpers();
  
  const getSeverityIcon = () => {
    switch (dispute.severity) {
      case "urgent":
        return <AlertTriangle className="h-5 w-5 text-red-600" />;
      case "high":
        return <ShieldAlert className="h-5 w-5 text-orange-500" />;
      case "medium":
        return <Clock className="h-5 w-5 text-yellow-500" />;
      case "low":
        return <Clock className="h-5 w-5 text-blue-500" />;
    }
  };

  return (
    <Card className={isMobile ? "shadow-sm bg-white" : ""}>
      <CardHeader className={isMobile ? "pb-0 px-3 pt-2" : "pb-3"}>
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            {getSeverityIcon()}
            <CardTitle className={isMobile ? "text-base" : ""}>
              {dispute.title}
            </CardTitle>
          </div>
          {!isMobile && <DisputeStatusBadge status={dispute.status} />}
        </div>
        <CardDescription className={isMobile ? "mt-0.5 text-sm" : "mt-2"}>
          {dispute.description}
        </CardDescription>
      </CardHeader>
      <CardContent className={isMobile ? "px-3 py-1" : ""}>
        <DisputeParties dispute={dispute} />
        <DisputeTimeline dispute={dispute} />
      </CardContent>
    </Card>
  );
};
