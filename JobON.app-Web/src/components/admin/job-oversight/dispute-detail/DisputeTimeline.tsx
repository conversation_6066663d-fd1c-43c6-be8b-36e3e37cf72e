
import React from "react";
import { JobDispute } from "@/types/jobs";
import { useUIHelpers } from "@/hooks/use-ui-helpers";

interface DisputeTimelineProps {
  dispute: JobDispute;
}

export const DisputeTimeline: React.FC<DisputeTimelineProps> = ({ dispute }) => {
  const { isMobile } = useUIHelpers();
  
  const formatDate = (dateStr: string) => {
    const date = new Date(dateStr);
    return isMobile 
      ? `${date.toLocaleDateString()}, ${date.toLocaleTimeString([], {hour: 'numeric', minute:'2-digit'})}`
      : date.toLocaleString();
  };
  
  return (
    <div className={isMobile ? "mt-1" : "mt-6"}>
      <h4 className="text-sm font-medium mb-1">Dispute Timeline</h4>
      <div className="space-y-1.5">
        <div className="flex gap-2">
          <div className="flex flex-col items-center">
            <div className="w-2 h-2 rounded-full bg-blue-500"></div>
            <div className="w-0.5 h-full bg-gray-200"></div>
          </div>
          <div>
            <div className="text-sm font-medium">Dispute submitted</div>
            <div className="text-xs text-muted-foreground">
              {formatDate(dispute.createdAt)}
            </div>
          </div>
        </div>
        
        {dispute.status !== "submitted" && (
          <div className="flex gap-2">
            <div className="flex flex-col items-center">
              <div className="w-2 h-2 rounded-full bg-yellow-500"></div>
              <div className="w-0.5 h-full bg-gray-200"></div>
            </div>
            <div>
              <div className="text-sm font-medium">Under review</div>
              <div className="text-xs text-muted-foreground">
                {formatDate(dispute.updatedAt)}
              </div>
            </div>
          </div>
        )}
        
        {dispute.status === "mediation" && (
          <div className="flex gap-2">
            <div className="flex flex-col items-center">
              <div className="w-2 h-2 rounded-full bg-purple-500"></div>
              <div className="w-0.5 h-full bg-gray-200"></div>
            </div>
            <div>
              <div className="text-sm font-medium">Mediation started</div>
              <div className="text-xs text-muted-foreground">
                {formatDate(dispute.updatedAt)}
              </div>
            </div>
          </div>
        )}
        
        {dispute.resolution && (
          <div className="flex gap-2">
            <div className="flex flex-col items-center">
              <div className="w-2 h-2 rounded-full bg-green-500"></div>
              <div className="w-0.5 h-0 bg-gray-200"></div>
            </div>
            <div>
              <div className="text-sm font-medium">Resolved</div>
              <div className="text-xs text-muted-foreground">
                {formatDate(dispute.resolution.resolvedAt)}
              </div>
              <div className="mt-1 text-sm">{dispute.resolution.description}</div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};
