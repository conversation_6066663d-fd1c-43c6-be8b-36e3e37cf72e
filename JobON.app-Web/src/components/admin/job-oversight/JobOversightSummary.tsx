
import React from "react";
import { Card, CardContent } from "@/components/ui/card";
import { 
  BarChart3,
  Clock,
  CheckCircle,
  AlertCircle,
  Users
} from "lucide-react";

export const JobOversightSummary = () => {
  // Sample data for summary cards
  const summaryData = [
    {
      title: "Active Jobs",
      value: 24,
      change: "+12%",
      trend: "up",
      color: "bg-blue-500",
      icon: BarChart3
    },
    {
      title: "Pending Bids",
      value: 8,
      change: "-3%",
      trend: "down",
      color: "bg-amber-500",
      icon: Clock
    },
    {
      title: "Completed Jobs",
      value: 156,
      change: "+18%",
      trend: "up",
      color: "bg-green-500",
      icon: CheckCircle
    },
    {
      title: "Requiring Attention",
      value: 3,
      change: "-25%",
      trend: "down",
      color: "bg-red-500",
      icon: AlertCircle
    },
    {
      title: "Active Providers",
      value: 42,
      change: "+5%",
      trend: "up",
      color: "bg-purple-500",
      icon: Users
    }
  ];

  return (
    <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-5 gap-4">
      {summaryData.map((item, index) => (
        <Card key={index} className="border-none shadow-sm hover:shadow-md transition-shadow">
          <CardContent className="p-6 flex flex-row md:flex-col lg:flex-row items-center gap-4">
            <div className={`p-3 rounded-lg ${item.color} bg-opacity-15`}>
              <item.icon className={`h-6 w-6 text-${item.color.split('-')[1]}-500`} />
            </div>
            <div>
              <p className="text-sm font-medium text-muted-foreground">{item.title}</p>
              <div className="flex items-center gap-2">
                <span className="text-2xl font-bold">{item.value}</span>
                <span className={`text-xs font-medium ${item.trend === 'up' ? 'text-green-500' : 'text-red-500'}`}>
                  {item.change}
                </span>
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
};
