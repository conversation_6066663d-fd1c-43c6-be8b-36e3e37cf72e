
import React from 'react';
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { AlertCircle } from "lucide-react";

export const AttentionRequiredCard = () => {
  return (
    <Card>
      <CardHeader className="pb-2">
        <CardTitle className="text-lg">Attention Required</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          <div className="p-3 bg-red-50 rounded-md flex items-start gap-3">
            <AlertCircle className="h-5 w-5 text-red-500 mt-0.5" />
            <div>
              <p className="text-sm font-medium">4 active disputes</p>
              <p className="text-xs text-muted-foreground">Require resolution</p>
            </div>
          </div>
          <div className="p-3 bg-yellow-50 rounded-md flex items-start gap-3">
            <AlertCircle className="h-5 w-5 text-yellow-500 mt-0.5" />
            <div>
              <p className="text-sm font-medium">8 jobs awaiting bids</p>
              <p className="text-xs text-muted-foreground">Over 3 days old</p>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
