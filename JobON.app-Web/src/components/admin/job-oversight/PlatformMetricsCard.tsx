
import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";

interface PlatformMetricsProps {
  activeJobs: number;
  completedJobsThisMonth: number;
  customerSatisfaction: number;
}

export const PlatformMetricsCard = ({ 
  activeJobs, 
  completedJobsThisMonth, 
  customerSatisfaction 
}: PlatformMetricsProps) => {
  return (
    <Card>
      <CardHeader className="pb-2">
        <CardTitle className="text-lg">Overview</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className="flex justify-between items-center">
            <span className="text-sm text-muted-foreground">Active Jobs</span>
            <span className="font-medium">{activeJobs}</span>
          </div>
          <div className="flex justify-between items-center">
            <span className="text-sm text-muted-foreground">Completed (This Month)</span>
            <span className="font-medium">{completedJobsThisMonth}</span>
          </div>
          <div>
            <div className="flex justify-between mb-1">
              <span className="text-sm text-muted-foreground">Customer Satisfaction</span>
              <span className="font-medium">{customerSatisfaction}%</span>
            </div>
            <Progress value={customerSatisfaction} className="h-2" />
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
