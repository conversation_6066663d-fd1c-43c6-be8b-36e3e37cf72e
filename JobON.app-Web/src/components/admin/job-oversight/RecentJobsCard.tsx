
import React from 'react';
import { <PERSON>, Card<PERSON>ontent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { ArrowRight } from "lucide-react";
import { MobileJobsList } from './MobileJobsList';
import { DesktopJobsTable } from './DesktopJobsTable';
import { useUIHelpers } from "@/hooks/use-ui-helpers";

interface Job {
  id: string;
  title: string;
  status: string;
  customer: string;
  customerAvatar: string;
  provider: string | null;
  date: string;
  value: string;
}

interface RecentJobsCardProps {
  jobs: Job[];
}

export const RecentJobsCard = ({ jobs }: RecentJobsCardProps) => {
  const { isMobile } = useUIHelpers();
  
  return (
    <Card>
      <CardHeader>
        <CardTitle>Recent Jobs</CardTitle>
        <CardDescription>Latest activity on the platform</CardDescription>
      </CardHeader>
      <CardContent>
        {isMobile ? (
          <MobileJobsList jobs={jobs} />
        ) : (
          <DesktopJobsTable jobs={jobs} />
        )}
      </CardContent>
      <CardFooter className="flex justify-center md:justify-end border-t pt-4">
        <Button variant="outline" size="sm" className="gap-1">
          View All Jobs
          <ArrowRight className="h-4 w-4 ml-1" />
        </Button>
      </CardFooter>
    </Card>
  );
};
