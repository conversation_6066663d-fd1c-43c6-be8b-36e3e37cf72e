
import React from "react";
import { Badge } from "@/components/ui/badge";
import { DisputeStatus } from "@/types/jobs";

interface DisputeStatusBadgeProps {
  status: DisputeStatus;
}

export const DisputeStatusBadge = ({ status }: DisputeStatusBadgeProps) => {
  const getStatusConfig = (status: DisputeStatus) => {
    switch (status) {
      case "submitted":
        return {
          label: "Submitted",
          variant: "outline" as const,
          className: "border-blue-200 bg-blue-50 text-blue-700"
        };
      case "under_review":
        return {
          label: "Under Review",
          variant: "outline" as const,
          className: "border-yellow-200 bg-yellow-50 text-yellow-700"
        };
      case "mediation":
        return {
          label: "In Mediation",
          variant: "outline" as const,
          className: "border-purple-200 bg-purple-50 text-purple-700"
        };
      case "resolved":
        return {
          label: "Resolved",
          variant: "outline" as const,
          className: "border-green-200 bg-green-50 text-green-700"
        };
      case "appealed":
        return {
          label: "Appealed",
          variant: "outline" as const,
          className: "border-orange-200 bg-orange-50 text-orange-700"
        };
      case "closed":
        return {
          label: "Closed",
          variant: "outline" as const,
          className: "border-gray-200 bg-gray-50 text-gray-700"
        };
    }
  };

  const config = getStatusConfig(status);

  return (
    <Badge variant={config.variant} className={config.className}>
      {config.label}
    </Badge>
  );
};
