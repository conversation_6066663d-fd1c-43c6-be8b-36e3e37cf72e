
import React, { useState } from "react";
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { useMediaQuery } from "@/hooks/use-media-query";
import { Eye, CheckCircle, XCircle } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { JobDetailView } from "@/components/admin/jobs/JobDetailView";

export const JobBidsList = () => {
  const { toast } = useToast();
  const isMobile = useMediaQuery("(max-width: 768px)");
  const [selectedJobId, setSelectedJobId] = useState<string | null>(null);
  const [bids, setBids] = useState([
    {
      id: "bid-001",
      jobId: "JOB-2345",
      jobTitle: "Bathroom Remodel",
      provider: {
        name: "<PERSON>'s Plumbing",
        avatar: "/placeholder.svg",
        rating: 4.8,
      },
      amount: 1200,
      timeframe: "3-5 days",
      submitted: "2025-05-01",
      status: "pending"
    },
    {
      id: "bid-002",
      jobId: "JOB-2346",
      jobTitle: "Kitchen Sink Repair",
      provider: {
        name: "Mike's Home Service",
        avatar: "/placeholder.svg",
        rating: 4.9,
      },
      amount: 350,
      timeframe: "1-2 days",
      submitted: "2025-05-02",
      status: "pending"
    },
    {
      id: "bid-003",
      jobId: "JOB-2347",
      jobTitle: "Water Heater Installation",
      provider: {
        name: "Premier Plumbing",
        avatar: "/placeholder.svg",
        rating: 4.7,
      },
      amount: 850,
      timeframe: "2-3 days",
      submitted: "2025-05-03",
      status: "pending"
    },
    {
      id: "bid-004",
      jobId: "JOB-2348",
      jobTitle: "Electrical Wiring Upgrade",
      provider: {
        name: "Electro Solutions",
        avatar: "/placeholder.svg",
        rating: 4.6,
      },
      amount: 1500,
      timeframe: "4-6 days",
      submitted: "2025-05-02",
      status: "pending"
    },
    {
      id: "bid-005",
      jobId: "JOB-2349",
      jobTitle: "Home Cleaning Service",
      provider: {
        name: "CleanPro Services",
        avatar: "/placeholder.svg",
        rating: 4.9,
      },
      amount: 200,
      timeframe: "1 day",
      submitted: "2025-05-03",
      status: "pending"
    }
  ]);

  const handleApproveBid = (e: React.MouseEvent, bidId: string) => {
    e.stopPropagation(); // Prevent row click event
    setBids(bids.map(bid => 
      bid.id === bidId ? { ...bid, status: "approved" } : bid
    ));
    
    toast({
      title: "Bid Approved",
      description: "Provider has been notified and job status updated.",
    });
  };
  
  const handleDeclineBid = (e: React.MouseEvent, bidId: string) => {
    e.stopPropagation(); // Prevent row click event
    setBids(bids.map(bid => 
      bid.id === bidId ? { ...bid, status: "declined" } : bid
    ));
    
    toast({
      title: "Bid Declined",
      description: "Provider has been notified of the decision.",
    });
  };

  const handleViewDetails = (jobId: string) => {
    setSelectedJobId(jobId);
  };

  const handleCloseDetails = () => {
    setSelectedJobId(null);
  };

  // Find the selected job for the detail view
  const selectedJob = selectedJobId ? 
    {
      id: selectedJobId,
      title: bids.find(bid => bid.jobId === selectedJobId)?.jobTitle || "",
      customerName: "Customer Name", // In a real app, you would fetch this data
      customerAvatar: "/placeholder.svg",
      provider: bids.find(bid => bid.jobId === selectedJobId)?.provider.name,
      providerAvatar: "/placeholder.svg",
      status: "Open",
      value: `$${bids.find(bid => bid.jobId === selectedJobId)?.amount}`,
      description: "Detailed job description would be displayed here. In a real application, this would be fetched from the database.",
      createdDate: new Date().toISOString().split('T')[0],
      hasNotes: false
    } : null;

  const MobileBidCard = ({ bid }: { bid: any }) => (
    <div 
      className="mb-4 bg-white rounded-lg shadow-sm border p-4 transition-all hover:shadow-md cursor-pointer"
      onClick={() => handleViewDetails(bid.jobId)}
    >
      <div className="flex justify-between items-start mb-3">
        <div>
          <span className="text-xs text-gray-500">{bid.jobId}</span>
          <h3 className="font-medium">{bid.jobTitle}</h3>
        </div>
        <Badge 
          variant={bid.status === "approved" ? "success" : bid.status === "declined" ? "destructive" : "outline"}
          className={
            bid.status === "approved" 
              ? "bg-green-100 text-green-800 border-green-200" 
              : bid.status === "declined" 
              ? "bg-red-100 text-red-800 border-red-200"
              : "bg-blue-100 text-blue-800 border-blue-200"
          }
        >
          {bid.status === "approved" ? "Approved" : bid.status === "declined" ? "Declined" : "Pending"}
        </Badge>
      </div>

      <div className="flex items-center gap-3 mb-3 bg-gray-50 p-3 rounded-md">
        <Avatar>
          <AvatarImage src={bid.provider.avatar} />
          <AvatarFallback>{bid.provider.name.charAt(0)}</AvatarFallback>
        </Avatar>
        <div>
          <p className="font-medium">{bid.provider.name}</p>
          <div className="flex items-center">
            <span className="text-xs bg-yellow-100 text-yellow-800 px-1.5 py-0.5 rounded">★ {bid.provider.rating}</span>
          </div>
        </div>
      </div>
      
      <div className="grid grid-cols-2 gap-2 mb-3">
        <div>
          <p className="text-xs text-gray-500">Bid Amount</p>
          <p className="font-semibold">${bid.amount}</p>
        </div>
        <div>
          <p className="text-xs text-gray-500">Timeframe</p>
          <p>{bid.timeframe}</p>
        </div>
      </div>

      {bid.status === "pending" && (
        <div className="flex gap-2 mt-3">
          <Button 
            variant="outline" 
            size="sm" 
            className="flex-1 border-green-200 text-green-700 hover:bg-green-50"
            onClick={(e) => handleApproveBid(e, bid.id)}
          >
            <CheckCircle className="h-4 w-4 mr-1" /> Approve
          </Button>
          <Button 
            variant="outline" 
            size="sm" 
            className="flex-1 border-red-200 text-red-700 hover:bg-red-50"
            onClick={(e) => handleDeclineBid(e, bid.id)}
          >
            <XCircle className="h-4 w-4 mr-1" /> Decline
          </Button>
        </div>
      )}
    </div>
  );

  return (
    <>
      {isMobile ? (
        <div className="space-y-2">
          {bids.map(bid => (
            <MobileBidCard key={bid.id} bid={bid} />
          ))}
        </div>
      ) : (
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Job ID</TableHead>
              <TableHead>Provider</TableHead>
              <TableHead>Amount</TableHead>
              <TableHead>Timeframe</TableHead>
              <TableHead>Submitted</TableHead>
              <TableHead>Status</TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {bids.map(bid => (
              <TableRow 
                key={bid.id} 
                className="cursor-pointer hover:bg-muted/80"
                onClick={() => handleViewDetails(bid.jobId)}
              >
                <TableCell>
                  <div>
                    <div className="font-medium">{bid.jobId}</div>
                    <div className="text-sm text-muted-foreground">{bid.jobTitle}</div>
                  </div>
                </TableCell>
                <TableCell>
                  <div className="flex items-center gap-2">
                    <Avatar className="h-8 w-8">
                      <AvatarImage src={bid.provider.avatar} />
                      <AvatarFallback>{bid.provider.name.charAt(0)}</AvatarFallback>
                    </Avatar>
                    <div>
                      <div>{bid.provider.name}</div>
                      <div className="text-xs text-yellow-600">★ {bid.provider.rating}</div>
                    </div>
                  </div>
                </TableCell>
                <TableCell><span className="font-medium">${bid.amount}</span></TableCell>
                <TableCell>{bid.timeframe}</TableCell>
                <TableCell>{new Date(bid.submitted).toLocaleDateString()}</TableCell>
                <TableCell>
                  <Badge 
                    variant={bid.status === "approved" ? "success" : bid.status === "declined" ? "destructive" : "outline"}
                    className={
                      bid.status === "approved" 
                        ? "bg-green-100 text-green-800 border-green-200" 
                        : bid.status === "declined" 
                        ? "bg-red-100 text-red-800 border-red-200"
                        : "bg-blue-100 text-blue-800 border-blue-200"
                    }
                  >
                    {bid.status === "approved" ? "Approved" : bid.status === "declined" ? "Declined" : "Pending"}
                  </Badge>
                </TableCell>
                <TableCell className="text-right">
                  <div className="flex justify-end gap-2" onClick={(e) => e.stopPropagation()}>
                    <Button variant="ghost" size="icon">
                      <Eye className="h-4 w-4" />
                    </Button>
                    {bid.status === "pending" && (
                      <>
                        <Button 
                          variant="ghost" 
                          size="icon"
                          className="text-green-600 hover:text-green-700 hover:bg-green-50"
                          onClick={(e) => handleApproveBid(e, bid.id)}
                        >
                          <CheckCircle className="h-4 w-4" />
                        </Button>
                        <Button 
                          variant="ghost" 
                          size="icon"
                          className="text-red-600 hover:text-red-700 hover:bg-red-50"
                          onClick={(e) => handleDeclineBid(e, bid.id)}
                        >
                          <XCircle className="h-4 w-4" />
                        </Button>
                      </>
                    )}
                  </div>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      )}

      {/* Job Detail Dialog */}
      {selectedJob && (
        <JobDetailView 
          job={selectedJob} 
          open={!!selectedJobId} 
          onClose={handleCloseDetails} 
        />
      )}
    </>
  );
};
