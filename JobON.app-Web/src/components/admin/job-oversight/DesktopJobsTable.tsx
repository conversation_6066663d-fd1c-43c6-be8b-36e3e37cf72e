
import React from 'react';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { JobStatusBadge } from './JobStatusBadge';

interface Job {
  id: string;
  title: string;
  status: string;
  customer: string;
  customerAvatar: string;
  provider: string | null;
  date: string;
  value: string;
}

interface DesktopJobsTableProps {
  jobs: Job[];
}

export const DesktopJobsTable = ({ jobs }: DesktopJobsTableProps) => {
  return (
    <Table>
      <TableHeader>
        <TableRow>
          <TableHead>Job ID</TableHead>
          <TableHead>Title</TableHead>
          <TableHead>Customer</TableHead>
          <TableHead>Provider</TableHead>
          <TableHead>Date</TableHead>
          <TableHead>Value</TableHead>
          <TableHead>Status</TableHead>
        </TableRow>
      </TableHeader>
      <TableBody>
        {jobs.map(job => (
          <TableRow key={job.id}>
            <TableCell className="font-medium">{job.id}</TableCell>
            <TableCell>{job.title}</TableCell>
            <TableCell>
              <div className="flex items-center">
                <Avatar className="h-7 w-7 mr-2">
                  <AvatarImage src={job.customerAvatar} />
                  <AvatarFallback>{job.customer[0]}</AvatarFallback>
                </Avatar>
                {job.customer}
              </div>
            </TableCell>
            <TableCell>
              {job.provider ? job.provider : <span className="text-sm italic">Not assigned</span>}
            </TableCell>
            <TableCell>{job.date}</TableCell>
            <TableCell>{job.value}</TableCell>
            <TableCell>
              <JobStatusBadge status={job.status} />
            </TableCell>
          </TableRow>
        ))}
      </TableBody>
    </Table>
  );
};
