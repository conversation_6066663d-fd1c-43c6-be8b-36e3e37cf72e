
import React, { useState } from "react";
import { 
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from "@/components/ui/table";
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from "@/components/ui/dropdown-menu";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  AlertTriangle,
  Clock,
  Filter,
  MoreVertical,
  ShieldAlert,
  Flag
} from "lucide-react";
import { JobDispute, DisputeStatus, DisputeSeverity } from "@/types/jobs";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { DisputeStatusBadge } from "./DisputeStatusBadge";

// Sample disputes data
const mockDisputes: JobDispute[] = [
  {
    id: "disp-1",
    jobId: "job-123",
    title: "Incomplete service",
    description: "Provider left before completing all agreed services",
    status: "under_review",
    severity: "medium",
    initiator: "customer",
    initiatorName: "<PERSON>",
    initiatorAvatar: "https://images.unsplash.com/photo-1494790108377-be9c29b29330",
    respondentName: "Alex <PERSON>",
    respondentAvatar: "https://images.unsplash.com/photo-1500648767791-00dcc994a43e",
    createdAt: "2025-05-01T14:32:00Z",
    updatedAt: "2025-05-02T09:15:00Z",
    evidence: [
      {
        id: "ev-1",
        type: "image",
        submittedBy: "customer",
        submitterName: "Jane Cooper",
        description: "Unfinished bathroom tiling",
        url: "https://example.com/evidence1.jpg",
        timestamp: "2025-05-01T14:35:00Z"
      },
      {
        id: "ev-2",
        type: "message",
        submittedBy: "provider",
        submitterName: "Alex Johnson",
        description: "Chat messages showing agreement to continue work next day",
        timestamp: "2025-05-01T15:00:00Z"
      }
    ]
  },
  {
    id: "disp-2",
    jobId: "job-456",
    title: "Payment dispute",
    description: "Provider claiming additional charges not agreed upon",
    status: "mediation",
    severity: "high",
    initiator: "provider",
    initiatorName: "Michael Wilson",
    initiatorAvatar: "https://images.unsplash.com/photo-1519085360753-af0119f7cbe7",
    respondentName: "Sarah Miller",
    respondentAvatar: "https://images.unsplash.com/photo-1438761681033-6461ffad8d80",
    createdAt: "2025-04-28T10:22:00Z",
    updatedAt: "2025-05-03T11:30:00Z",
    evidence: [
      {
        id: "ev-3",
        type: "contract",
        submittedBy: "provider",
        submitterName: "Michael Wilson",
        description: "Original service contract",
        url: "https://example.com/contract.pdf",
        timestamp: "2025-04-28T10:25:00Z"
      },
      {
        id: "ev-4",
        type: "document",
        submittedBy: "customer",
        submitterName: "Sarah Miller",
        description: "Email confirming fixed price",
        url: "https://example.com/email.pdf",
        timestamp: "2025-04-28T16:40:00Z"
      }
    ],
    adminNotes: ["Customer showed evidence of fixed price agreement", "Provider claims additional work was performed beyond scope"]
  },
  {
    id: "disp-3",
    jobId: "job-789",
    title: "Service quality issue",
    description: "Customer unhappy with quality of painting work",
    status: "resolved",
    severity: "low",
    initiator: "customer",
    initiatorName: "Robert Brown",
    initiatorAvatar: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e",
    respondentName: "Emily Davis",
    respondentAvatar: "https://images.unsplash.com/photo-**********-94ddf0286df2",
    createdAt: "2025-04-25T09:10:00Z",
    updatedAt: "2025-05-02T14:20:00Z",
    evidence: [
      {
        id: "ev-5",
        type: "image",
        submittedBy: "customer",
        submitterName: "Robert Brown",
        description: "Paint peeling after 2 days",
        url: "https://example.com/paint.jpg",
        timestamp: "2025-04-25T09:15:00Z"
      }
    ],
    resolution: {
      type: "admin_decision",
      description: "Provider to redo the affected areas at no additional cost",
      actionTaken: [
        {
          type: "service_modification",
          description: "Provider agreed to repaint affected areas",
          appliedTo: "provider"
        }
      ],
      adminId: "admin-1",
      adminName: "Admin User",
      resolvedAt: "2025-05-02T14:20:00Z"
    }
  },
  {
    id: "disp-4",
    jobId: "job-101",
    title: "No-show provider",
    description: "Provider did not appear for scheduled appointment",
    status: "submitted",
    severity: "urgent",
    initiator: "customer",
    initiatorName: "Lisa Wang",
    initiatorAvatar: "https://images.unsplash.com/photo-1487412720507-e7ab37603c6f",
    respondentName: "David Smith",
    respondentAvatar: "https://images.unsplash.com/photo-1463453091185-61582044d556",
    createdAt: "2025-05-03T15:00:00Z",
    updatedAt: "2025-05-03T15:05:00Z",
    evidence: [
      {
        id: "ev-6",
        type: "document",
        submittedBy: "customer",
        submitterName: "Lisa Wang",
        description: "Appointment confirmation",
        url: "https://example.com/appointment.pdf",
        timestamp: "2025-05-03T15:02:00Z"
      }
    ]
  }
];

interface DisputesListProps {
  onViewDispute: (dispute: JobDispute) => void;
}

export const DisputesList = ({ onViewDispute }: DisputesListProps) => {
  const [disputes] = useState<JobDispute[]>(mockDisputes);
  const [statusFilter, setStatusFilter] = useState<DisputeStatus | "all">("all");
  const [severityFilter, setSeverityFilter] = useState<DisputeSeverity | "all">("all");

  const filteredDisputes = disputes.filter(dispute => {
    const matchesStatus = statusFilter === "all" || dispute.status === statusFilter;
    const matchesSeverity = severityFilter === "all" || dispute.severity === severityFilter;
    return matchesStatus && matchesSeverity;
  });

  const getSeverityIcon = (severity: DisputeSeverity) => {
    switch (severity) {
      case "urgent":
        return <AlertTriangle className="h-4 w-4 text-red-600" />;
      case "high":
        return <ShieldAlert className="h-4 w-4 text-orange-500" />;
      case "medium":
        return <Flag className="h-4 w-4 text-yellow-500" />;
      case "low":
        return <Clock className="h-4 w-4 text-blue-500" />;
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-xl font-semibold flex items-center space-x-2">
          <ShieldAlert className="h-5 w-5 mr-2 text-red-500" />
          <span>Active Disputes</span>
        </CardTitle>
        <CardDescription>
          Manage customer and provider disputes requiring admin mediation
        </CardDescription>
        
        <div className="flex flex-wrap items-center justify-between gap-4 mt-4">
          <div className="flex flex-wrap gap-2">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" size="sm" className="flex items-center gap-1">
                  <Filter className="h-3.5 w-3.5 mr-1" />
                  Status: {statusFilter === "all" ? "All" : statusFilter.replace("_", " ")}
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="start">
                <DropdownMenuItem onClick={() => setStatusFilter("all")}>All Statuses</DropdownMenuItem>
                <DropdownMenuItem onClick={() => setStatusFilter("submitted")}>Submitted</DropdownMenuItem>
                <DropdownMenuItem onClick={() => setStatusFilter("under_review")}>Under Review</DropdownMenuItem>
                <DropdownMenuItem onClick={() => setStatusFilter("mediation")}>Mediation</DropdownMenuItem>
                <DropdownMenuItem onClick={() => setStatusFilter("resolved")}>Resolved</DropdownMenuItem>
                <DropdownMenuItem onClick={() => setStatusFilter("appealed")}>Appealed</DropdownMenuItem>
                <DropdownMenuItem onClick={() => setStatusFilter("closed")}>Closed</DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>

            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" size="sm" className="flex items-center gap-1">
                  <Filter className="h-3.5 w-3.5 mr-1" />
                  Severity: {severityFilter === "all" ? "All" : severityFilter}
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="start">
                <DropdownMenuItem onClick={() => setSeverityFilter("all")}>All Severities</DropdownMenuItem>
                <DropdownMenuItem onClick={() => setSeverityFilter("urgent")}>Urgent</DropdownMenuItem>
                <DropdownMenuItem onClick={() => setSeverityFilter("high")}>High</DropdownMenuItem>
                <DropdownMenuItem onClick={() => setSeverityFilter("medium")}>Medium</DropdownMenuItem>
                <DropdownMenuItem onClick={() => setSeverityFilter("low")}>Low</DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
          
          <div className="flex items-center gap-2">
            <Badge variant="outline" className="font-normal">
              {filteredDisputes.filter(d => d.status !== "resolved" && d.status !== "closed").length} Active
            </Badge>
            <Badge variant="outline" className="font-normal">
              {filteredDisputes.filter(d => d.status === "resolved" || d.status === "closed").length} Resolved
            </Badge>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Status</TableHead>
              <TableHead>Dispute</TableHead>
              <TableHead>Initiated By</TableHead>
              <TableHead>Severity</TableHead>
              <TableHead>Date</TableHead>
              <TableHead className="w-[80px]"></TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredDisputes.length > 0 ? (
              filteredDisputes.map((dispute) => (
                <TableRow 
                  key={dispute.id}
                  className="cursor-pointer hover:bg-muted/50"
                  onClick={() => onViewDispute(dispute)}
                >
                  <TableCell>
                    <DisputeStatusBadge status={dispute.status} />
                  </TableCell>
                  <TableCell>
                    <div className="font-medium">{dispute.title}</div>
                    <div className="text-sm text-muted-foreground truncate max-w-[200px]">
                      {dispute.description}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <div className="w-8 h-8 rounded-full bg-muted overflow-hidden">
                        {dispute.initiatorAvatar ? (
                          <img 
                            src={dispute.initiatorAvatar} 
                            alt={dispute.initiatorName}
                            className="h-full w-full object-cover"
                          />
                        ) : (
                          <div className="h-full w-full flex items-center justify-center bg-primary/10 text-primary font-medium">
                            {dispute.initiatorName.charAt(0)}
                          </div>
                        )}
                      </div>
                      <div>
                        <div className="font-medium">{dispute.initiatorName}</div>
                        <div className="text-xs text-muted-foreground capitalize">
                          {dispute.initiator}
                        </div>
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-1.5">
                      {getSeverityIcon(dispute.severity)}
                      <span className="capitalize">{dispute.severity}</span>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="font-medium">
                      {new Date(dispute.createdAt).toLocaleDateString()}
                    </div>
                    <div className="text-xs text-muted-foreground">
                      {new Date(dispute.createdAt).toLocaleTimeString()}
                    </div>
                  </TableCell>
                  <TableCell>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild onClick={e => e.stopPropagation()}>
                        <Button variant="ghost" size="icon" className="h-8 w-8">
                          <MoreVertical className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem onClick={(e) => {
                          e.stopPropagation();
                          onViewDispute(dispute);
                        }}>
                          View details
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={(e) => e.stopPropagation()}>
                          Assign to me
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={(e) => e.stopPropagation()}>
                          Mark as priority
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={6} className="text-center py-6 text-muted-foreground">
                  No disputes found matching the current filters
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </CardContent>
    </Card>
  );
};
