import React, { useState, useEffect, useMemo } from 'react';
import { useAuth } from '@/features/auth/hooks/useAuth';
import { useToast } from '@/hooks/use-toast';
import { 
  emailTrackingService, 
  EmailTrackingStatistics, 
  DailyMetricsResponse,
  EmailDistributionResponse,
  DailyMetric,
  EmailDistribution
} from '@/services/emailTrackingService';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Pagination } from '@/components/ui/pagination';
import { DatePicker } from '@/components/ui/date-picker';
import { Calendar as CalendarComponent } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Loader2, Mail, CheckCircle, MousePointer, AlertTriangle, Ban, XCircle, RefreshCw, Download, BarChart3, PieChart, Calendar, Filter, Search } from 'lucide-react';
import { format, subDays } from 'date-fns';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import type { DateRange } from 'react-day-picker';
// Import the chart components
import { EmailPerformanceChart, EmailDistributionChart } from './EmailTrackingCharts';

// Status badge variants
const statusVariants: Record<string, { color: string; icon: React.ReactNode; bgColor: string }> = {
  delivered: { color: 'text-blue-600', icon: <Mail className="h-3 w-3 mr-1" />, bgColor: 'bg-blue-50' },
  opened: { color: 'text-green-600', icon: <CheckCircle className="h-3 w-3 mr-1" />, bgColor: 'bg-green-50' },
  clicked: { color: 'text-purple-600', icon: <MousePointer className="h-3 w-3 mr-1" />, bgColor: 'bg-purple-50' },
  bounced: { color: 'text-amber-600', icon: <AlertTriangle className="h-3 w-3 mr-1" />, bgColor: 'bg-amber-50' },
  spam: { color: 'text-red-600', icon: <Ban className="h-3 w-3 mr-1" />, bgColor: 'bg-red-50' },
  unsubscribed: { color: 'text-gray-600', icon: <XCircle className="h-3 w-3 mr-1" />, bgColor: 'bg-gray-50' },
};

// Stats card component with animation
const StatsCard = ({ 
  title, 
  value, 
  icon, 
  description, 
  change, 
  bgColor = 'bg-white'
}: { 
  title: string; 
  value: number; 
  icon: React.ReactNode; 
  description?: string;
  change?: number;
  bgColor?: string;
}) => (
  <Card className={`overflow-hidden transition-all duration-200 hover:shadow-md ${bgColor}`}>
    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
      <CardTitle className="text-sm font-medium">{title}</CardTitle>
      <div className="rounded-full p-1.5 bg-gray-100">
        {icon}
      </div>
    </CardHeader>
    <CardContent>
      <div className="text-2xl font-bold">{value}</div>
      {description && <p className="text-xs text-muted-foreground mt-1">{description}</p>}
      {change !== undefined && (
        <div className={`flex items-center mt-2 text-xs ${change >= 0 ? 'text-green-600' : 'text-red-600'}`}>
          {change >= 0 ? '↑' : '↓'} {Math.abs(change)}% from previous period
        </div>
      )}
    </CardContent>
  </Card>
);

// Time period options
const timePeriods = [
  { label: 'Last 7 days', value: '7days', startDate: subDays(new Date(), 7) },
  { label: 'Last 30 days', value: '30days', startDate: subDays(new Date(), 30) },
  { label: 'Last 90 days', value: '90days', startDate: subDays(new Date(), 90) },
  { label: 'Custom range', value: 'custom', startDate: null },
] as const;

export const EmailTrackingDashboard = () => {
  const { token } = useAuth();
  const { toast } = useToast();

  // State for tracking statistics
  const [statistics, setStatistics] = useState<EmailTrackingStatistics | null>(null);
  const [dailyMetrics, setDailyMetrics] = useState<DailyMetric[]>([]);
  const [emailDistribution, setEmailDistribution] = useState<{
    total: number;
    distribution: EmailDistribution[];
  } | null>(null);
  const [loading, setLoading] = useState(true);
  const [metricsLoading, setMetricsLoading] = useState(true);
  const [distributionLoading, setDistributionLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('overview');
  const [timePeriod, setTimePeriod] = useState('7days');

  // Custom date range state for overview/analytics tabs
  const [customDateRange, setCustomDateRange] = useState<DateRange>({
    from: undefined,
    to: undefined,
  });

  // Filter states for data tab
  const [filters, setFilters] = useState<{
    type: string;
    startDate: Date | null;
    endDate: Date | null;
    notificationType: string;
    email: string;
    page: number;
    perPage: number;
  }>({
    type: 'all',
    startDate: subDays(new Date(), 7),
    endDate: new Date(),
    notificationType: 'all',
    email: '',
    page: 1,
    perPage: 10,
  });

  // Fetch email tracking statistics
  const fetchStatistics = async () => {
    if (!token) {
      toast({
        title: 'Authentication Error',
        description: 'No authentication token available',
        variant: 'destructive',
      });
      return;
    }

    setLoading(true);
    try {
      const response = await emailTrackingService.getEmailTrackingStatistics(
        token,
        {
          type: filters.type === 'all' ? undefined : filters.type,
          start_date: filters.startDate ? format(filters.startDate, 'yyyy-MM-dd') : undefined,
          end_date: filters.endDate ? format(filters.endDate, 'yyyy-MM-dd') : undefined,
          notification_type: filters.notificationType === 'all' ? undefined : filters.notificationType,
          email: filters.email || undefined,
          page: filters.page,
          per_page: filters.perPage,
        }
      );

      if (response.data && response.data.data) {
        setStatistics(response.data.data);
      }
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to fetch email tracking statistics',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  // Fetch daily metrics
  const fetchDailyMetrics = async () => {
    if (!token) return;

    setMetricsLoading(true);
    try {
      const response = await emailTrackingService.getDailyMetrics(
        token,
        {
          start_date: filters.startDate ? format(filters.startDate, 'yyyy-MM-dd') : undefined,
          end_date: filters.endDate ? format(filters.endDate, 'yyyy-MM-dd') : undefined,
        }
      );

      if (response.data && response.data.data) {
        setDailyMetrics(response.data.data);
      }
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to fetch daily metrics',
        variant: 'destructive',
      });
    } finally {
      setMetricsLoading(false);
    }
  };

  // Fetch email distribution
  const fetchEmailDistribution = async () => {
    if (!token) return;

    setDistributionLoading(true);
    try {
      const response = await emailTrackingService.getEmailDistribution(
        token,
        {
          start_date: filters.startDate ? format(filters.startDate, 'yyyy-MM-dd') : undefined,
          end_date: filters.endDate ? format(filters.endDate, 'yyyy-MM-dd') : undefined,
        }
      );

      if (response.data && response.data.data) {
        setEmailDistribution(response.data.data);
      }
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to fetch email distribution',
        variant: 'destructive',
      });
    } finally {
      setDistributionLoading(false);
    }
  };

  // Initial fetch
  useEffect(() => {
    fetchStatistics();
  }, [filters.page, filters.perPage]);

  // Fetch analytics data when tab changes or filters change
  useEffect(() => {
    if (activeTab === 'overview' || activeTab === 'analytics') {
      fetchDailyMetrics();
      fetchEmailDistribution();
    }
  }, [activeTab, filters.startDate, filters.endDate]);

  // Handle time period change for overview/analytics tabs
  const handleTimePeriodChange = (value: string) => {
    setTimePeriod(value);

    if (value !== 'custom') {
      const period = timePeriods.find(p => p.value === value);
      if (period) {
        // For overview/analytics, we update the filters to fetch new data
        setFilters(prev => ({
          ...prev,
          startDate: period.startDate,
          endDate: new Date(),
        }));
        // Reset custom date range
        setCustomDateRange({ from: undefined, to: undefined });
      }
    }
    // For custom range, we don't update filters until user selects dates
  };

  // Handle custom date range change
  const handleCustomDateRangeChange = (range: DateRange | undefined) => {
    if (range) {
      setCustomDateRange({
        from: range.from,
        to: range.to,
      });

      // If both dates are selected, update filters
      if (range.from && range.to) {
        setFilters(prev => ({
          ...prev,
          startDate: range.from || null,
          endDate: range.to || null,
        }));
      }
    }
  };

  // Handle filter changes
  const handleFilterChange = (key: keyof typeof filters, value: any) => {
    setFilters(prev => ({ ...prev, [key]: value }));
  };

  // Apply filters
  const applyFilters = () => {
    setFilters(prev => ({ ...prev, page: 1 })); // Reset to first page
    fetchStatistics();
  };

  // Reset filters
  const resetFilters = () => {
    const defaultStartDate = timePeriods.find(p => p.value === timePeriod)?.startDate ?? subDays(new Date(), 7);

    setFilters({
      type: 'all',
      startDate: defaultStartDate,
      endDate: new Date(),
      notificationType: 'all',
      email: '',
      page: 1,
      perPage: 10,
    });
    fetchStatistics();
  };

  // Format date for display
  const formatDate = (dateString?: string) => {
    if (!dateString) return 'N/A';
    return format(new Date(dateString), 'MMM dd, yyyy HH:mm');
  };

  // Calculate engagement rate
  const engagementRate = useMemo(() => {
    if (!emailDistribution) return 0;
    
    const openedDistribution = emailDistribution.distribution.find(d => d.status === 'Opened');
    const clickedDistribution = emailDistribution.distribution.find(d => d.status === 'Clicked');
    
    const openedCount = openedDistribution?.count || 0;
    const clickedCount = clickedDistribution?.count || 0;
    const totalOpened = openedCount + clickedCount;
    
    return emailDistribution.total > 0 ? Math.round((totalOpened / emailDistribution.total) * 100) : 0;
  }, [emailDistribution]);

  // Calculate click-through rate
  const clickThroughRate = useMemo(() => {
    if (!emailDistribution) return 0;
    
    const openedDistribution = emailDistribution.distribution.find(d => d.status === 'Opened');
    const clickedDistribution = emailDistribution.distribution.find(d => d.status === 'Clicked');
    
    const openedCount = openedDistribution?.count || 0;
    const clickedCount = clickedDistribution?.count || 0;
    const totalOpened = openedCount + clickedCount;
    
    return totalOpened > 0 ? Math.round((clickedCount / totalOpened) * 100) : 0;
  }, [emailDistribution]);

  // Calculate bounce rate
  const bounceRate = useMemo(() => {
    if (!emailDistribution) return 0;
    
    const bouncedDistribution = emailDistribution.distribution.find(d => d.status === 'Bounced');
    const bouncedCount = bouncedDistribution?.count || 0;
    
    return emailDistribution.total > 0 ? Math.round((bouncedCount / emailDistribution.total) * 100) : 0;
  }, [emailDistribution]);

  // Calculate metrics for display
  const dashboardMetrics = useMemo(() => {
    if (!emailDistribution) {
      return {
        totalEmails: 0,
        delivered: 0,
        opened: 0,
        clicked: 0,
        bounced: 0
      };
    }

    const unopenedDist = emailDistribution.distribution.find(d => d.status === 'Unopened');
    const openedDist = emailDistribution.distribution.find(d => d.status === 'Opened');
    const clickedDist = emailDistribution.distribution.find(d => d.status === 'Clicked');
    const bouncedDist = emailDistribution.distribution.find(d => d.status === 'Bounced');

    return {
      totalEmails: emailDistribution.total,
      delivered: emailDistribution.total - (bouncedDist?.count || 0),
      opened: (openedDist?.count || 0) + (clickedDist?.count || 0),
      clicked: clickedDist?.count || 0,
      bounced: bouncedDist?.count || 0
    };
  }, [emailDistribution]);

  // Calculate percentage changes (mock data for now)
  const getPercentageChange = (metric: string): number => {
    // In a real implementation, this would compare current period to previous period
    const mockChanges: Record<string, number> = {
      totalEmails: 2.5,
      delivered: 1.8,
      opened: 3.2,
      clicked: -1.5,
      bounced: -0.5
    };
    
    return mockChanges[metric] || 0;
  };

  return (
    <div className="p-4 md:p-6 lg:p-8 space-y-6 max-w-7xl mx-auto">
      <div className="flex flex-col space-y-2 md:flex-row md:items-center md:justify-between md:space-y-0">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">Email Tracking Dashboard</h2>
          <p className="text-muted-foreground">Monitor and analyze your email campaign performance</p>
        </div>
        <div className="flex items-center space-x-2">
          {/* Only show time period selector for overview and analytics tabs */}
          {(activeTab === 'overview' || activeTab === 'analytics') && (
            <>
              <Select value={timePeriod} onValueChange={handleTimePeriodChange}>
                <SelectTrigger className="w-[180px]">
                  <Calendar className="mr-2 h-4 w-4" />
                  <SelectValue placeholder="Select period" />
                </SelectTrigger>
                <SelectContent>
                  {timePeriods.map((period) => (
                    <SelectItem key={period.value} value={period.value}>
                      {period.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>

              {/* Custom date range picker */}
              {timePeriod === 'custom' && (
                <Popover>
                  <PopoverTrigger asChild>
                    <Button variant="outline" className="w-[240px] justify-start text-left font-normal">
                      <Calendar className="mr-2 h-4 w-4" />
                      {customDateRange.from ? (
                        customDateRange.to ? (
                          <>
                            {format(customDateRange.from, "MMM d, yyyy")} - {format(customDateRange.to, "MMM d, yyyy")}
                          </>
                        ) : (
                          format(customDateRange.from, "MMM d, yyyy")
                        )
                      ) : (
                        "Select date range"
                      )}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0" align="start">
                    <CalendarComponent
                      mode="range"
                      selected={{ from: customDateRange.from || undefined, to: customDateRange.to || undefined }}
                      onSelect={handleCustomDateRangeChange}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
              )}
            </>
          )}

          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button variant="outline" size="icon" onClick={fetchStatistics}>
                  <RefreshCw className="h-4 w-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>Refresh data</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button variant="outline" size="icon">
                  <Download className="h-4 w-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>Export data</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-2 md:w-auto md:grid-cols-3">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="data">Email Data</TabsTrigger>
          <TabsTrigger value="analytics" className="hidden md:block">Analytics</TabsTrigger>
        </TabsList>
        
        <TabsContent value="overview" className="space-y-4 mt-4">
          {metricsLoading || distributionLoading ? (
            <div className="flex justify-center items-center py-12">
              <Loader2 className="h-8 w-8 animate-spin text-primary" />
            </div>
          ) : (
            <>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
                <StatsCard 
                  title="Total Emails" 
                  value={dashboardMetrics.totalEmails} 
                  icon={<Mail className="h-4 w-4 text-gray-600" />} 
                  change={getPercentageChange('totalEmails')}
                />
                <StatsCard 
                  title="Delivered" 
                  value={dashboardMetrics.delivered} 
                  icon={<Mail className="h-4 w-4 text-blue-600" />} 
                  bgColor="bg-blue-50"
                  change={getPercentageChange('delivered')}
                />
                <StatsCard 
                  title="Opened" 
                  value={dashboardMetrics.opened} 
                  icon={<CheckCircle className="h-4 w-4 text-green-600" />} 
                  bgColor="bg-green-50"
                  change={getPercentageChange('opened')}
                />
                <StatsCard 
                  title="Clicked" 
                  value={dashboardMetrics.clicked} 
                  icon={<MousePointer className="h-4 w-4 text-purple-600" />} 
                  bgColor="bg-purple-50"
                  change={getPercentageChange('clicked')}
                />
                <StatsCard 
                  title="Bounced" 
                  value={dashboardMetrics.bounced} 
                  icon={<AlertTriangle className="h-4 w-4 text-amber-600" />} 
                  bgColor="bg-amber-50"
                  change={getPercentageChange('bounced')}
                />
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <Card>
                  <CardHeader>
                    <CardTitle className="text-sm font-medium">Engagement Rate</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{engagementRate}%</div>
                    <div className="mt-4 h-2 w-full bg-gray-100 rounded-full overflow-hidden">
                      <div 
                        className="h-full bg-green-500 rounded-full" 
                        style={{ width: `${engagementRate}%` }}
                      />
                    </div>
                    <p className="text-xs text-muted-foreground mt-2">Percentage of delivered emails that were opened</p>
                  </CardContent>
                </Card>
                
                <Card>
                  <CardHeader>
                    <CardTitle className="text-sm font-medium">Click-Through Rate</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{clickThroughRate}%</div>
                    <div className="mt-4 h-2 w-full bg-gray-100 rounded-full overflow-hidden">
                      <div 
                        className="h-full bg-purple-500 rounded-full" 
                        style={{ width: `${clickThroughRate}%` }}
                      />
                    </div>
                    <p className="text-xs text-muted-foreground mt-2">Percentage of opened emails that were clicked</p>
                  </CardContent>
                </Card>
                
                <Card>
                  <CardHeader>
                    <CardTitle className="text-sm font-medium">Bounce Rate</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{bounceRate}%</div>
                    <div className="mt-4 h-2 w-full bg-gray-100 rounded-full overflow-hidden">
                      <div 
                        className="h-full bg-amber-500 rounded-full" 
                        style={{ width: `${bounceRate}%` }}
                      />
                    </div>
                    <p className="text-xs text-muted-foreground mt-2">Percentage of emails that bounced</p>
                  </CardContent>
                </Card>
              </div>
            </>
          )}
        </TabsContent>
        
        <TabsContent value="data" className="space-y-4 mt-4">
          {/* Filters */}
          <Card className="border-0 shadow-sm">
            <CardHeader className="pb-4">
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="text-lg">Filters</CardTitle>
                  <CardDescription className="text-sm">Refine your search criteria</CardDescription>
                </div>
                <Button variant="ghost" size="sm" onClick={resetFilters}>
                  <Filter className="h-4 w-4 mr-2" />
                  Clear filters
                </Button>
              </div>
            </CardHeader>
            <CardContent className="pt-0">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
                <div className="space-y-2">
                  <label className="text-xs font-medium text-muted-foreground uppercase tracking-wide">Status</label>
                  <Select
                    value={filters.type}
                    onValueChange={(value) => handleFilterChange('type', value)}
                  >
                    <SelectTrigger className="h-9 text-sm">
                      <SelectValue placeholder="All Statuses" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Statuses</SelectItem>
                      <SelectItem value="delivered">Delivered</SelectItem>
                      <SelectItem value="opened">Opened</SelectItem>
                      <SelectItem value="clicked">Clicked</SelectItem>
                      <SelectItem value="bounced">Bounced</SelectItem>
                      <SelectItem value="spam">Spam</SelectItem>
                      <SelectItem value="unsubscribed">Unsubscribed</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <label className="text-xs font-medium text-muted-foreground uppercase tracking-wide">Start Date</label>
                  <DatePicker
                    selected={filters.startDate}
                    onSelect={(date: Date | null) => handleFilterChange('startDate', date)}
                    placeholderText="Select start date"
                    className="h-9 text-sm"
                  />
                </div>

                <div className="space-y-2">
                  <label className="text-xs font-medium text-muted-foreground uppercase tracking-wide">End Date</label>
                  <DatePicker
                    selected={filters.endDate}
                    onSelect={(date: Date | null) => handleFilterChange('endDate', date)}
                    placeholderText="Select end date"
                    className="h-9 text-sm"
                  />
                </div>

                <div className="space-y-2">
                  <label className="text-xs font-medium text-muted-foreground uppercase tracking-wide">Type</label>
                  <Select
                    value={filters.notificationType}
                    onValueChange={(value) => handleFilterChange('notificationType', value)}
                  >
                    <SelectTrigger className="h-9 text-sm">
                      <SelectValue placeholder="All Types" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Types</SelectItem>
                      <SelectItem value="booking_confirmation">Booking Confirmation</SelectItem>
                      <SelectItem value="booking_reminder">Booking Reminder</SelectItem>
                      <SelectItem value="payment_receipt">Payment Receipt</SelectItem>
                      <SelectItem value="account_verification">Account Verification</SelectItem>
                      <SelectItem value="password_reset">Password Reset</SelectItem>
                      <SelectItem value="marketing">Marketing</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <label className="text-xs font-medium text-muted-foreground uppercase tracking-wide">Email</label>
                  <div className="relative">
                    <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                    <Input
                      placeholder="Search by email"
                      value={filters.email}
                      onChange={(e) => handleFilterChange('email', e.target.value)}
                      className="pl-8 h-9 text-sm"
                    />
                  </div>
                </div>
              </div>

              <div className="flex justify-end mt-4 pt-4 border-t">
                <Button onClick={applyFilters} size="sm" className="px-6">
                  Apply Filters
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Email tracking data table */}
          <Card className="border-0 shadow-sm">
            <CardHeader className="pb-4">
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="text-lg">Email Tracking Data</CardTitle>
                  <CardDescription className="text-sm">
                    Detailed information about email delivery and engagement
                  </CardDescription>
                </div>
                <div className="flex items-center space-x-2">
                  <span className="text-xs text-muted-foreground">Show</span>
                  <Select
                    value={filters.perPage.toString()}
                    onValueChange={(value) => handleFilterChange('perPage', parseInt(value))}
                  >
                    <SelectTrigger className="w-[70px] h-8">
                      <SelectValue placeholder="10" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="10">10</SelectItem>
                      <SelectItem value="25">25</SelectItem>
                      <SelectItem value="50">50</SelectItem>
                      <SelectItem value="100">100</SelectItem>
                    </SelectContent>
                  </Select>
                  <span className="text-xs text-muted-foreground">entries</span>
                </div>
              </div>
            </CardHeader>
            <CardContent className="pt-0">
              {loading ? (
                <div className="flex justify-center items-center py-12">
                  <Loader2 className="h-8 w-8 animate-spin text-primary" />
                </div>
              ) : (
                <>
                  <div className="rounded-lg border border-border/50 overflow-hidden">
                    <Table>
                      <TableHeader>
                        <TableRow className="bg-muted/30">
                          <TableHead className="font-semibold">Email</TableHead>
                          <TableHead className="font-semibold">Subject</TableHead>
                          <TableHead className="font-semibold">Sent At</TableHead>
                          <TableHead className="font-semibold">Status</TableHead>
                          <TableHead className="font-semibold">Type</TableHead>
                          <TableHead className="font-semibold">Last Updated</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {statistics?.data && statistics.data.length > 0 ? (
                          statistics.data.map((item) => (
                            <TableRow key={item.notification_id} className="hover:bg-muted/50">
                              <TableCell className="font-medium">
                                <div className="flex flex-col">
                                  <span>{item.email}</span>
                                  <Badge variant="outline" className="text-xs bg-gray-100 text-gray-700 border-0 mt-1 w-fit">
                                    {item.role_name}
                                  </Badge>
                                </div>
                              </TableCell>
                              <TableCell>{item.type.split('\\').pop()}</TableCell>
                              <TableCell>{formatDate(item.sent_at)}</TableCell>
                              <TableCell>
                                <Badge 
                                  variant="outline" 
                                  className={`${item.opened_at ? statusVariants['opened'].color : statusVariants['delivered'].color} ${item.opened_at ? statusVariants['opened'].bgColor : statusVariants['delivered'].bgColor} border-0`}
                                >
                                  <span className="flex items-center">
                                    {item.opened_at ? statusVariants['opened'].icon : statusVariants['delivered'].icon}
                                    {item.opened_at ? 'Opened' : 'Delivered'}
                                  </span>
                                </Badge>
                              </TableCell>
                              <TableCell>
                                <Badge variant="secondary" className="font-normal">
                                  {item.type.split('\\').pop()?.replace('Notification', '')}
                                </Badge>
                              </TableCell>
                              <TableCell>
                                {item.opened_at ? formatDate(item.opened_at) : formatDate(item.sent_at)}
                              </TableCell>
                            </TableRow>
                          ))
                        ) : (
                          <TableRow>
                            <TableCell colSpan={6} className="text-center py-6 text-muted-foreground">
                              No email tracking data found
                            </TableCell>
                          </TableRow>
                        )}
                      </TableBody>
                    </Table>
                  </div>

                  {/* Pagination */}
                  {statistics && statistics.total > 0 && (
                    <div className="flex items-center justify-between mt-6 pt-4 border-t border-border/50">
                      <div className="text-xs text-muted-foreground">
                        Showing {statistics.from} to {statistics.to} of {statistics.total} results
                      </div>
                      <Pagination
                        currentPage={statistics.current_page}
                        totalItems={statistics.total}
                        itemsPerPage={statistics.per_page}
                        onPageChange={(page) => handleFilterChange('page', page)}
                      />
                    </div>
                  )}
                </>
              )}
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="analytics" className="space-y-4 mt-4">
          {metricsLoading || distributionLoading ? (
            <div className="flex justify-center items-center py-12">
              <Loader2 className="h-8 w-8 animate-spin text-primary" />
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Card>
                <CardHeader>
                  <CardTitle className="text-xl">Email Performance</CardTitle>
                  <CardDescription>Visualization of email metrics over time</CardDescription>
                </CardHeader>
                <CardContent>
                  <EmailPerformanceChart 
                    dailyMetrics={dailyMetrics} 
                    loading={metricsLoading} 
                  />
                </CardContent>
              </Card>
              
              <Card>
                <CardHeader>
                  <CardTitle className="text-xl">Email Distribution</CardTitle>
                  <CardDescription>Breakdown of email statuses</CardDescription>
                </CardHeader>
                <CardContent>
                  {distributionLoading ? (
                    <div className="flex items-center justify-center h-[300px]">
                      <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
                    </div>
                  ) : !emailDistribution || emailDistribution.distribution.length === 0 ? (
                    <div className="flex flex-col items-center justify-center h-[300px] text-center">
                      <PieChart className="h-12 w-12 text-muted-foreground mb-4" />
                      <p className="text-muted-foreground">No distribution data available</p>
                    </div>
                  ) : (
                    <div className="space-y-4">
                      <div className="grid grid-cols-1 gap-3">
                        {emailDistribution.distribution.map((item, index) => {
                          const colors = ['#3b82f6', '#22c55e', '#a855f7', '#f59e0b', '#ef4444', '#6b7280'];
                          const color = colors[index % colors.length];
                          const statusName = item.status.charAt(0).toUpperCase() + item.status.slice(1);
                          return (
                            <div key={item.status} className="flex items-center justify-between p-2 rounded-md border">
                              <div className="flex items-center">
                                <div className="w-3 h-3 rounded-full mr-3" style={{ backgroundColor: color }}></div>
                                <span className="font-medium">{statusName}</span>
                              </div>
                              <div className="flex items-center space-x-4">
                                <span className="text-sm font-semibold">{item.count}</span>
                                <span className="text-sm text-muted-foreground">{item.percentage.toFixed(2)}%</span>
                              </div>
                            </div>
                          );
                        })}
                      </div>
                      <div className="pt-2 text-xs text-muted-foreground text-center">
                        Total emails: {emailDistribution.total}
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
};