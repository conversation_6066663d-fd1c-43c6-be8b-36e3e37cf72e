
import React, { useState, useEffect } from "react";
import { useIsMobile } from "@/hooks/use-mobile";
import { MobileCustomerDetailView } from "./MobileCustomerDetailView";
import { useToast } from "@/hooks/use-toast";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Mail, MapPin, User, Calendar, UserRoundX, Flag, MessageSquare } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Tabs, TabsList, TabsTrigger, TabsContent } from "@/components/ui/tabs";
import { JobBookingsList } from "../job-bookings/JobBookingsList";
import { JobBooking } from "@/services/jobBookingService";
import { apiService } from "@/services/api";
import { useAuth } from "@/features/auth/hooks/useAuth";
import { Customer } from "@/services/customerService";

interface CustomerProfileDetailProps {
  customer: Customer;
}

export const CustomerProfileDetail: React.FC<CustomerProfileDetailProps> = ({ customer }) => {
  const isMobile = useIsMobile();
  const { toast } = useToast();
  const { token } = useAuth();
  const [activeTab, setActiveTab] = useState("overview");
  const [bookings, setBookings] = useState<JobBooking[]>([]);
  const [isLoadingBookings, setIsLoadingBookings] = useState(false);

  // Define the expected structure for the API response
  interface BookingsApiResponse {
    data: JobBooking[];
    // Add other pagination fields like meta, links if they exist
  }

  const handleDeactivate = () => {
    toast({
      title: "Customer Deactivated",
      description: "The customer account has been deactivated.",
      variant: "destructive",
    });
  };

  const handleFlag = () => {
    toast({
      title: "Customer Flagged",
      description: "The customer has been flagged for review.",
    });
  };

  const handleMessage = () => {
    toast({
      title: "Message Composer",
      description: "Opening message composer...",
    });
  };

  // Fetch job bookings when the tab is selected
  useEffect(() => {
    if (activeTab === "bookings" && customer?.id && token) {
      fetchCustomerBookings();
    }
  }, [activeTab, customer?.id, token]);

  const fetchCustomerBookings = async () => {
    if (!token) {
      toast({
        title: "Authentication Error",
        description: "Not authorized to fetch bookings.",
        variant: "destructive",
      });
      setIsLoadingBookings(false);
      return;
    }
    setIsLoadingBookings(true);
    try {
      const endpoint = `/api/job-bookings?user_id=${customer.id}&page=1&per_page=10`;
      const response = await apiService<BookingsApiResponse>(endpoint, {
        method: 'GET',
        headers: {
          'Authorization': token,
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        },
        requiresAuth: true,
        includeCredentials: true
      });

      if (response.isSuccess && response.data) {
        setBookings(response.data.data);
      } else {
        toast({
          title: "Error",
          description: response.error || "Failed to fetch job bookings",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error("Error fetching job bookings:", error);
      toast({
        title: "Error",
        description: "An unexpected error occurred",
        variant: "destructive",
      });
    } finally {
      setIsLoadingBookings(false);
    }
  };

  if (isMobile) {
    return (
      <div className="bg-white rounded-lg shadow overflow-hidden">
        {/* Customer Header */}
        <div className="p-4 flex flex-col items-center border-b">
          <div className="relative w-24 h-24 bg-slate-100 rounded-full flex items-center justify-center text-2xl font-semibold mb-3">
            {customer.name.split(" ").map(n => n[0]).join("")}
          </div>

          <h2 className="text-2xl font-bold text-center">{customer.name}</h2>

          <div className="flex gap-2 mt-2 mb-3">
            {customer.status && (
              <Badge
                variant={customer.status.toLowerCase() === "active" ? "success" : "secondary"}
              >
                {customer.status}
              </Badge>
            )}
            {customer.reward_tier && (
              <Badge
                className={customer.reward_tier.toLowerCase() === "gold" ? "bg-amber-500 text-white" : ""}
              >
                {customer.reward_tier}
              </Badge>
            )}
            {customer.flagged && (
              <Badge variant="destructive">
                Flagged
              </Badge>
            )}
          </div>

          <div className="text-gray-600 flex items-center gap-2 mb-2">
            <Mail className="w-4 h-4" /> {customer.email}
          </div>

          <div className="text-gray-600 flex items-center gap-2 mb-2">
            <MapPin className="w-4 h-4" /> {customer.location}
          </div>

          <div className="text-gray-600 flex items-center gap-2 mb-4">
            <Calendar className="w-4 h-4" /> Joined {customer.created_at}
          </div>

          {/* Action Buttons */}
          <div className="grid grid-cols-3 gap-2 w-full mt-2">
            <Button
              variant="destructive"
              onClick={handleDeactivate}
              className="flex items-center justify-center"
              size="sm"
            >
              <UserRoundX className="w-4 h-4 mr-1" />
              Deactivate
            </Button>
            <Button
              variant="outline"
              onClick={handleFlag}
              className="flex items-center justify-center"
              size="sm"
            >
              <Flag className="w-4 h-4 mr-1" />
              Flag
            </Button>
            <Button
              onClick={handleMessage}
              className="flex items-center justify-center"
              size="sm"
            >
              <MessageSquare className="w-4 h-4 mr-1" />
              Message
            </Button>
          </div>
        </div>

        {/* Tabbed Content */}
        <Tabs defaultValue="overview" className="w-full">
          <div className="sticky top-0 z-10 bg-white border-b">
            <TabsList className="w-full grid grid-cols-4 rounded-none h-12">
              <TabsTrigger value="overview" className="data-[state=active]:bg-slate-50 data-[state=active]:font-semibold">
                Overview
              </TabsTrigger>
              <TabsTrigger value="bookings" className="data-[state=active]:bg-slate-50 data-[state=active]:font-semibold">
                Bookings
              </TabsTrigger>
              <TabsTrigger value="messages" className="data-[state=active]:bg-slate-50 data-[state=active]:font-semibold">
                Messages
              </TabsTrigger>
              <TabsTrigger value="admin" className="data-[state=active]:bg-slate-50 data-[state=active]:font-semibold">
                Admin
              </TabsTrigger>
            </TabsList>
          </div>

          <TabsContent value="overview" className="p-4 mt-0">
            <Card>
              <CardContent className="p-4 space-y-3">
                <div className="flex items-center gap-2">
                  <User className="w-4 h-4 text-muted-foreground" />
                  <span className="text-sm text-muted-foreground">Customer ID:</span>
                  <span className="text-sm font-medium">{customer.id}</span>
                </div>
                <div className="space-y-1">
                  <p className="text-sm text-muted-foreground">Recent Activity:</p>
                  <p className="text-sm">Last login: 3 days ago</p>
                  <p className="text-sm">Last booking: 1 week ago</p>
                </div>
                {/* Additional overview details would go here */}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="bookings" className="p-4 mt-0">
            <JobBookingsList bookings={bookings} isLoading={isLoadingBookings} />
          </TabsContent>

          <TabsContent value="messages" className="p-4 mt-0">
            <div className="space-y-3">
              {[1, 2].map((message) => (
                <Card key={message} className="cursor-pointer hover:bg-slate-50">
                  <CardContent className="p-4">
                    <div className="flex justify-between items-start">
                      <div>
                        <h4 className="font-medium">Provider Name</h4>
                        <p className="text-sm text-muted-foreground">Re: Kitchen Renovation</p>
                        <p className="text-xs text-gray-400">Last message: 2 days ago</p>
                      </div>
                      <Badge variant="outline">3 unread</Badge>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="admin" className="p-4 mt-0">
            <div className="space-y-3">
              {[1, 2, 3].map((note) => (
                <Card key={note}>
                  <CardContent className="p-4">
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <p className="font-medium">Admin User</p>
                        <p className="text-xs text-gray-400">April {note * 2}, 2025</p>
                      </div>
                      <p className="text-sm">
                        {note === 1
                          ? "Customer requested refund for booking #1234"
                          : note === 2
                          ? "Followed up about the missing appointment"
                          : "First contact with customer, seems satisfied with service"}
                      </p>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>
        </Tabs>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
      {/* Customer Details */}
      <div>
        <h2 className="text-lg font-semibold mb-4">Customer Details</h2>
        <Card>
          <CardContent className="space-y-3">
            <div className="flex items-center gap-2">
              <User className="w-4 h-4 text-muted-foreground" />
              <span className="text-sm text-muted-foreground">Customer ID:</span>
              <span className="text-sm font-medium">{customer.id}</span>
            </div>
            <div className="space-y-1">
              <p className="text-sm text-muted-foreground">Name:</p>
              <p className="font-medium">{customer.name}</p>
            </div>
            <div className="space-y-1">
              <p className="text-sm text-muted-foreground">Email:</p>
              <div className="flex items-center gap-2">
                <Mail className="w-4 h-4 text-muted-foreground" />
                <a href={`mailto:${customer.email}`} className="font-medium hover:underline">
                  {customer.email}
                </a>
              </div>
            </div>
            <div className="space-y-1">
              <p className="text-sm text-muted-foreground">Location:</p>
              <div className="flex items-center gap-2">
                <MapPin className="w-4 h-4 text-muted-foreground" />
                <span className="font-medium">{customer.location}</span>
              </div>
            </div>
            <div className="space-y-1">
              <p className="text-sm text-muted-foreground">Join Date:</p>
              <p className="font-medium">{customer.created_at}</p>
            </div>
            <div className="flex gap-2">
              {customer.status && (
                <Badge variant={customer.status.toLowerCase() === "active" ? "success" : "secondary"}>
                  {customer.status}
                </Badge>
              )}
              {customer.reward_tier && (
                <Badge className={customer.reward_tier.toLowerCase() === "gold" ? "bg-amber-500 text-white" : ""}>
                  {customer.reward_tier}
                </Badge>
              )}
              {customer.flagged && (
                <Badge variant="destructive">
                  Flagged
                </Badge>
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Actions */}
      <div>
        <h2 className="text-lg font-semibold mb-4">Actions</h2>
        <Card>
          <CardContent className="space-y-4">
            <Button variant="destructive" onClick={handleDeactivate} className="w-full">
              Deactivate Customer
            </Button>
            <Button variant="outline" onClick={handleFlag} className="w-full">
              Flag Customer
            </Button>
            <Button onClick={handleMessage} className="w-full">
              Message Customer
            </Button>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};
