
import React, { useState } from "react";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Tabs, TabsList, TabsTrigger, TabsContent } from "@/components/ui/tabs";
import { Customer } from "@/services/customerService";
import {
  Mail,
  MapPin,
  User,
  Flag,
  MessageSquare,
  X,
  Calendar,
  Clock,
  Briefcase,
  MessageCircle,
  Settings,
  AlertCircle
} from "lucide-react";

interface MobileCustomerDetailViewProps {
  customer: Customer;
  onDeactivate: () => void;
  onFlag: () => void;
  onMessage: () => void;
}

export const MobileCustomerDetailView: React.FC<MobileCustomerDetailViewProps> = ({
  customer,
  onDeactivate,
  onFlag,
  onMessage,
}) => {
  const [activeTab, setActiveTab] = useState("overview");

  // Get appropriate badge style based on status
  const getStatusBadge = (status: string) => {
    switch(status.toLowerCase()) {
      case "active":
        return "bg-gradient-to-r from-green-500 to-emerald-500";
      case "pending":
        return "bg-gradient-to-r from-amber-400 to-orange-500";
      case "flagged":
        return "bg-gradient-to-r from-red-500 to-rose-600";
      case "inactive":
        return "bg-gradient-to-r from-slate-400 to-gray-500";
      default:
        return "bg-gradient-to-r from-blue-500 to-indigo-600";
    }
  };

  // Get tier badge style
  const getTierBadge = (tier: string) => {
    switch(tier?.toLowerCase()) {
      case "gold":
        return "bg-gradient-to-r from-yellow-400 to-amber-500";
      case "platinum":
        return "bg-gradient-to-r from-slate-300 to-zinc-400";
      case "silver":
        return "bg-gradient-to-r from-slate-400 to-gray-500";
      case "bronze":
        return "bg-gradient-to-r from-amber-700 to-yellow-800";
      default:
        return "bg-gradient-to-r from-blue-500 to-indigo-600";
    }
  };

  return (
    <div className="flex flex-col h-full bg-gradient-to-b from-background to-background/80">
      {/* Sticky Header */}
      <div className="sticky top-0 z-10 bg-background/95 backdrop-blur-sm border-b pb-4 px-4 pt-3 shadow-sm">
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h2 className="text-xl font-semibold bg-gradient-to-r from-primary to-indigo-600 bg-clip-text text-transparent">
              {customer.name}
            </h2>
            <div className="flex gap-2">
              {customer.status && (
                <Badge
                  className={`${getStatusBadge(customer.status)} text-white shadow-sm animate-fade-in`}
                >
                  {customer.status}
                </Badge>
              )}
              {customer.reward_tier && (
                <Badge
                  className={`${getTierBadge(customer.reward_tier)} text-white shadow-sm animate-fade-in`}
                >
                  {customer.reward_tier}
                </Badge>
              )}
              {customer.flagged && (
                <Badge
                  className="bg-gradient-to-r from-red-500 to-rose-600 text-white shadow-sm animate-fade-in"
                >
                  Flagged
                </Badge>
              )}
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex gap-2">
            <Button
              variant="destructive"
              size="sm"
              onClick={onDeactivate}
              className="flex-1 bg-gradient-to-r from-red-500 to-rose-600 hover:from-red-600 hover:to-rose-700 shadow-sm transition-all duration-300 hover:shadow"
            >
              <X className="h-4 w-4 mr-1" />
              Deactivate
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={onFlag}
              className="flex-1 border-amber-500/50 text-amber-700 dark:text-amber-400 hover:bg-amber-50 dark:hover:bg-amber-950/40 transition-all duration-300"
            >
              <Flag className="h-4 w-4 mr-1 text-amber-500" />
              Flag
            </Button>
            <Button
              variant="default"
              size="sm"
              onClick={onMessage}
              className="flex-1 bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700 shadow-sm transition-all duration-300 hover:shadow"
            >
              <MessageSquare className="h-4 w-4 mr-1" />
              Message
            </Button>
          </div>
        </div>

        {/* Tabs Navigation */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="mt-4">
          <TabsList className="w-full grid grid-cols-4 bg-slate-100/70 dark:bg-slate-800/70 rounded-lg p-1">
            <TabsTrigger
              value="overview"
              className="rounded-md data-[state=active]:bg-white dark:data-[state=active]:bg-slate-700 data-[state=active]:text-primary dark:data-[state=active]:text-primary-foreground data-[state=active]:shadow-sm transition-all duration-300"
            >
              <User className="h-4 w-4 sm:mr-1" />
              <span className="hidden sm:inline">Overview</span>
            </TabsTrigger>
            <TabsTrigger
              value="bookings"
              className="rounded-md data-[state=active]:bg-white dark:data-[state=active]:bg-slate-700 data-[state=active]:text-primary dark:data-[state=active]:text-primary-foreground data-[state=active]:shadow-sm transition-all duration-300"
            >
              <Briefcase className="h-4 w-4 sm:mr-1" />
              <span className="hidden sm:inline">Bookings</span>
            </TabsTrigger>
            <TabsTrigger
              value="messages"
              className="rounded-md data-[state=active]:bg-white dark:data-[state=active]:bg-slate-700 data-[state=active]:text-primary dark:data-[state=active]:text-primary-foreground data-[state=active]:shadow-sm transition-all duration-300"
            >
              <MessageCircle className="h-4 w-4 sm:mr-1" />
              <span className="hidden sm:inline">Messages</span>
            </TabsTrigger>
            <TabsTrigger
              value="admin"
              className="rounded-md data-[state=active]:bg-white dark:data-[state=active]:bg-slate-700 data-[state=active]:text-primary dark:data-[state=active]:text-primary-foreground data-[state=active]:shadow-sm transition-all duration-300"
            >
              <Settings className="h-4 w-4 sm:mr-1" />
              <span className="hidden sm:inline">Admin</span>
            </TabsTrigger>
          </TabsList>
        </Tabs>
      </div>

      {/* Tab Content */}
      <div className="flex-1 overflow-auto p-4">
        <TabsContent value="overview" className="m-0 animate-in fade-in-50 duration-300">
          <Card className="overflow-hidden border border-slate-200 dark:border-slate-800 shadow-sm hover:shadow transition-all duration-300">
            <CardContent className="space-y-4 pt-5">
              <div className="flex items-center gap-2">
                <User className="w-4 h-4 text-indigo-500" />
                <span className="text-sm text-muted-foreground">ID:</span>
                <span className="text-sm font-medium">{customer.id}</span>
              </div>

              <div className="space-y-1">
                <div className="flex items-center gap-2">
                  <Mail className="w-4 h-4 text-cyan-500" />
                  <a href={`mailto:${customer.email}`} className="font-medium text-primary hover:text-primary/80 hover:underline transition-colors">
                    {customer.email}
                  </a>
                </div>
              </div>

              <div className="space-y-1">
                <div className="flex items-center gap-2">
                  <MapPin className="w-4 h-4 text-red-500" />
                  <span className="font-medium">{customer.location}</span>
                </div>
              </div>

              <div className="space-y-1">
                <div className="flex items-center gap-2">
                  <Calendar className="w-4 h-4 text-emerald-500" />
                  <div>
                    <p className="text-sm text-muted-foreground">Join Date:</p>
                    <p className="font-medium">{customer.created_at}</p>
                  </div>
                </div>
              </div>

              {customer.reward_tier && (
                <div className="space-y-1">
                  <div className="flex items-center gap-2">
                    <Badge className={`${getTierBadge(customer.reward_tier)} text-white`}>
                      {customer.reward_tier} Member
                    </Badge>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="bookings" className="m-0 animate-in fade-in-50 duration-300">
          <Card className="border border-slate-200 dark:border-slate-800 shadow-sm">
            <CardContent className="p-6 text-center">
              <div className="flex flex-col items-center justify-center py-4">
                <Briefcase className="h-10 w-10 text-muted-foreground/60 mb-2" />
                <p className="text-muted-foreground">No bookings found for this customer.</p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="messages" className="m-0 animate-in fade-in-50 duration-300">
          <Card className="border border-slate-200 dark:border-slate-800 shadow-sm">
            <CardContent className="p-6 text-center">
              <div className="flex flex-col items-center justify-center py-4">
                <MessageCircle className="h-10 w-10 text-muted-foreground/60 mb-2" />
                <p className="text-muted-foreground">No messages found for this customer.</p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="admin" className="m-0 animate-in fade-in-50 duration-300">
          <Card className="border border-slate-200 dark:border-slate-800 shadow-sm">
            <CardContent className="p-6 text-center">
              <div className="flex flex-col items-center justify-center py-4">
                <AlertCircle className="h-10 w-10 text-muted-foreground/60 mb-2" />
                <p className="text-muted-foreground">No admin messages for this customer.</p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </div>
    </div>
  );
};
