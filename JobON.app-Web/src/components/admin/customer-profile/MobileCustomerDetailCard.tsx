
import { useState, useEffect } from "react";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Mail,
  Phone,
  MapPin,
  Star,
  Calendar,
  Briefcase,
  MessageSquare,
  UserX,
  UserCheck,
  Clock,
} from "lucide-react";
import { JobBookingsList } from "../job-bookings/JobBookingsList";
import { JobBooking } from "@/services/jobBookingService";
import { Customer } from "@/services/customerService";
import { apiService } from "@/services/api";
import { useAuth } from "@/features/auth/hooks/useAuth";
import { useToast } from "@/hooks/use-toast";

interface MobileCustomerDetailCardProps {
  customer: Customer;
  onClose: () => void;
  onSuspend: (customer: Customer) => void;
  onOpenMessageComposer?: (customer: Customer) => void;
}

export const MobileCustomerDetailCard = ({
  customer,
  onClose,
  onSuspend,
  onOpenMessageComposer,
}: MobileCustomerDetailCardProps) => {
  const [activeTab, setActiveTab] = useState("info");
  const { token } = useAuth();
  const { toast } = useToast();
  const [bookings, setBookings] = useState<JobBooking[]>([]);
  const [isLoadingBookings, setIsLoadingBookings] = useState(false);

  // Define the expected structure for the API response
  interface BookingsApiResponse {
    data: JobBooking[];
    // Add other pagination fields like meta, links if they exist
  }

  // Fetch job bookings when the tab is selected
  useEffect(() => {
    if (activeTab === "bookings" && customer?.id && token) {
      fetchCustomerBookings();
    }
  }, [activeTab, customer?.id, token]);

  const fetchCustomerBookings = async () => {
    if (!token) {
      toast({
        title: "Authentication Error",
        description: "Not authorized to fetch bookings.",
        variant: "destructive",
      });
      setIsLoadingBookings(false);
      return;
    }
    setIsLoadingBookings(true);
    try {
      const endpoint = `/api/job-bookings?user_id=${customer.id}&page=1&per_page=10`;
      const response = await apiService<BookingsApiResponse>(endpoint, {
        method: 'GET',
        headers: {
          'Authorization': token,
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        },
        requiresAuth: true,
        includeCredentials: true
      });

      if (response.isSuccess && response.data) {
        setBookings(response.data.data);
      } else {
        toast({
          title: "Error",
          description: response.error || "Failed to fetch job bookings",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error("Error fetching job bookings:", error);
      toast({
        title: "Error",
        description: "An unexpected error occurred",
        variant: "destructive",
      });
    } finally {
      setIsLoadingBookings(false);
    }
  };

  const getStatusBadge = (status: string | number, flagged: boolean) => {
    if (flagged) {
      return (
        <Badge variant="destructive" className="flex items-center gap-1">
          Flagged
        </Badge>
      );
    }

    // Handle numeric status values
    if (typeof status === 'number') {
      if (status === 1) {
        return <Badge variant="success">Active</Badge>;
      } else {
        return (
          <Badge variant="outline" className="bg-gray-100 text-gray-500">
            Inactive
          </Badge>
        );
      }
    }

    // Handle string status values (existing logic)
    switch (status) {
      case "Active":
        return <Badge variant="success">{status}</Badge>;
      case "Inactive":
        return (
          <Badge variant="outline" className="bg-gray-100 text-gray-500">
            {status}
          </Badge>
        );
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const getRewardBadge = (tier: string) => {
    switch (tier) {
      case "Platinum":
        return <Badge className="bg-purple-500">{tier}</Badge>;
      case "Gold":
        return <Badge variant="business">{tier}</Badge>;
      case "Silver":
        return <Badge variant="secondary">{tier}</Badge>;
      default:
        return <Badge variant="outline">{tier}</Badge>;
    }
  };

  const handleOpenMessageComposer = () => {
    if (onOpenMessageComposer) {
      onOpenMessageComposer(customer);
    }
  };

  return (
    <div className="flex flex-col h-full">
      {/* Customer Header */}
      <div className="mb-4 p-4 bg-card rounded-lg border shadow-sm">
        <div className="flex items-center gap-3">
          <Avatar className="h-16 w-16">
            <AvatarImage src={customer.avatar} alt={customer.name} />
            <AvatarFallback className="text-xl">
              {customer.name.charAt(0)}
              {customer.name.split(" ")[1]?.charAt(0)}
            </AvatarFallback>
          </Avatar>

          <div className="flex-1 min-w-0">
            <h2 className="text-xl font-bold truncate">{customer.name}</h2>
            <div className="flex items-center gap-1 mt-1">
              {customer.status && getStatusBadge(customer.status, customer.flagged ?? false)}
              {customer.reward_tier && (
                <>
                  <span className="mx-1">•</span>
                  {getRewardBadge(customer.reward_tier)}
                </>
              )}
            </div>
            <div className="flex items-center text-sm text-muted-foreground mt-1">
              <Mail className="h-3.5 w-3.5 mr-1" />
              <span className="truncate">{customer.email}</span>
            </div>
          </div>
        </div>

        {/* Quick actions */}
        <div className="grid grid-cols-2 gap-2 mt-4">
          {customer.status === "Inactive" ? (
            <Button
              variant="success"
              size="sm"
              onClick={() => onSuspend(customer)}
              className="w-full"
            >
              <UserCheck className="mr-2 h-4 w-4" /> Activate
            </Button>
          ) : (
            <Button
              variant="destructive"
              size="sm"
              onClick={() => onSuspend(customer)}
              className="w-full"
            >
              <UserX className="mr-2 h-4 w-4" /> Deactivate
            </Button>
          )}
          <Button
            variant="outline"
            size="sm"
            onClick={handleOpenMessageComposer}
            className="w-full"
          >
            <MessageSquare className="mr-2 h-4 w-4" /> Message
          </Button>
        </div>
      </div>

      {/* Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="flex-1">
        <TabsList className="grid w-full grid-cols-3 mb-4">
          <TabsTrigger value="info">Info</TabsTrigger>
          <TabsTrigger value="bookings">Bookings</TabsTrigger>
          <TabsTrigger value="messages">Messages</TabsTrigger>
        </TabsList>

        <TabsContent value="info" className="space-y-4">
          <Card>
            <CardContent className="p-4">
              <h3 className="font-medium mb-3">Customer Details</h3>
              <div className="space-y-3 text-sm">
                <div className="flex items-center">
                  <Mail className="h-4 w-4 mr-3 text-muted-foreground" />
                  <div>
                    <div className="text-muted-foreground">Email</div>
                    <div>{customer.email}</div>
                  </div>
                </div>

                <div className="flex items-center">
                  <MapPin className="h-4 w-4 mr-3 text-muted-foreground" />
                  <div>
                    <div className="text-muted-foreground">Location</div>
                    <div>{customer.location}</div>
                  </div>
                </div>

                {customer.reward_tier && (
                  <div className="flex items-center">
                    <Star className="h-4 w-4 mr-3 text-muted-foreground" />
                    <div>
                      <div className="text-muted-foreground">Reward Tier</div>
                      <div className="flex items-center">
                        {getRewardBadge(customer.reward_tier)}
                      </div>
                    </div>
                  </div>
                )}

                <div className="flex items-center">
                  <Calendar className="h-4 w-4 mr-3 text-muted-foreground" />
                  <div>
                    <div className="text-muted-foreground">Member Since</div>
                    <div>{customer.created_at}</div>
                  </div>
                </div>

                <div className="flex items-center">
                  <Clock className="h-4 w-4 mr-3 text-muted-foreground" />
                  <div>
                    <div className="text-muted-foreground">Last Active</div>
                    <div>{customer.last_active}</div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <h3 className="font-medium mb-3">Booking Summary</h3>
              <div className="grid grid-cols-2 gap-y-4">
                <div>
                  <div className="text-xs text-muted-foreground">Total Bookings</div>
                  <div className="text-xl font-semibold mt-1">{customer.total_bookings ?? 0}</div>
                </div>
                <div>
                  <div className="text-xs text-muted-foreground">Active Bookings</div>
                  <div className="text-xl font-semibold mt-1">0</div>
                </div>
                <div>
                  <div className="text-xs text-muted-foreground">Last Booking</div>
                  <div className="text-xl font-semibold mt-1">
                    {(customer.total_bookings ?? 0) > 0 ? "1 month ago" : "Never"}
                  </div>
                </div>
                <div>
                  <div className="text-xs text-muted-foreground">Cancellations</div>
                  <div className="text-xl font-semibold mt-1">0</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="bookings" className="space-y-4">
          <JobBookingsList bookings={bookings} isLoading={isLoadingBookings} />
        </TabsContent>

        <TabsContent value="messages" className="space-y-4">
          <Card>
            <CardContent className="p-4">
              <div className="mb-3 flex justify-between items-center">
                <h3 className="font-medium">Recent Messages</h3>
              </div>
              {(customer.total_bookings ?? 0) > 0 ? (
                <div className="space-y-4">
                  {[1, 2].map((i) => (
                    <div key={i} className="pb-3 border-b last:border-0 last:pb-0">
                      <div className="flex items-start gap-3">
                        <Avatar className="h-8 w-8">
                          <AvatarFallback>
                            {i === 1 ? "MP" : "SJ"}
                          </AvatarFallback>
                        </Avatar>
                        <div>
                          <div className="flex justify-between">
                            <div className="font-medium">
                              {i === 1 ? "Mike Smith" : "Sarah Johnson"}
                            </div>
                            <div className="text-xs text-muted-foreground">
                              {new Date(new Date().setDate(new Date().getDate() - i * 3)).toLocaleDateString()}
                            </div>
                          </div>
                          <div className="text-sm text-muted-foreground">Provider</div>
                          <div className="mt-1">
                            {i === 1
                              ? "I'll be there tomorrow at 2pm as scheduled."
                              : "Thanks for your business! Let me know if you need anything else."}
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-6 text-muted-foreground">
                  No messages found for this customer.
                </div>
              )}
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="mb-3">
                <h3 className="font-medium">Admin Messages</h3>
              </div>
              <div className="space-y-3">
                <div className="pb-3 border-b">
                  <div className="flex justify-between">
                    <div className="font-medium">Admin Team</div>
                    <div className="text-xs text-muted-foreground">
                      {new Date(new Date().setDate(new Date().getDate() - 5)).toLocaleDateString()}
                    </div>
                  </div>
                  <div className="mt-1">Welcome to our platform! Let us know if you need any help.</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Bottom action */}
      <div className="mt-4">
        <Button variant="outline" className="w-full" onClick={onClose}>
          Close
        </Button>
      </div>
    </div>
  );
};
