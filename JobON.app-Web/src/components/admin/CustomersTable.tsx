import React, { useState, useEffect, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import useAuthHeader from 'react-auth-kit/hooks/useAuthHeader';
import { nullToUndefined } from '@/utils/typeHelpers';
import { customerService, Customer, CustomerStats } from '@/services/customerService';
import { chatService } from '@/services/chatService';
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Loader2, Mail, MessageSquare, Phone, Trash2, User, Search, X } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";

import { ScrollArea } from "@/components/ui/scroll-area";
import { Pagination } from "@/components/ui/pagination";
import { useDebounceValue } from "@/hooks/use-debounce";
import { useUIHelpers } from "@/hooks/use-ui-helpers";
import { toast } from 'sonner';

// Define conversation message type locally
interface ConversationMessage {
  id: string;
  message: string;
  sender_type: 'admin' | 'customer' | 'provider' | 'user';
  created_at: string;
}

// Define message type for display
interface Message {
  id: string;
  content: { type: string; data: string }[];
  sender: 'user' | 'bot';
  timestamp: Date;
}

interface CustomersTableProps {
  onOpenMessageComposer?: (customerId: string) => void;
  isMobile: boolean;
}

const CustomersTableComponent: React.FC<CustomersTableProps> = ({ onOpenMessageComposer, isMobile }) => {
  const [customers, setCustomers] = useState<Customer[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isDeleting, setIsDeleting] = useState(false);
  const [isDetailModalOpen, setIsDetailModalOpen] = useState(false);
  const [customerToDelete, setCustomerToDelete] = useState<Customer | null>(null);
  const [selectedCustomer, setSelectedCustomer] = useState<Customer | null>(null);
  const [customerStats, setCustomerStats] = useState<CustomerStats | null>(null);
  const [loadingStats, setLoadingStats] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [isConversationOpen, setIsConversationOpen] = useState(false);
  const [conversationMessages, setConversationMessages] = useState<Message[]>([]);
  const [isCreatingChat, setIsCreatingChat] = useState<string | null>(null);

  // Pagination and search states
  const [currentPage, setCurrentPage] = useState(1);
  const [perPage, setPerPage] = useState(10);
  const [totalItems, setTotalItems] = useState(0);
  const [searchQuery, setSearchQuery] = useState('');

  // Create debounced version of the search query
  const debouncedSearchQuery = useDebounceValue(searchQuery, 800);

  const authHeader = useAuthHeader();
  const navigate = useNavigate();
  const { toast: showToast } = useToast();
  const { isMobile: isMobileHelper } = useUIHelpers();

  // Reset to first page when search query changes
  useEffect(() => {
    setCurrentPage(1);
  }, [debouncedSearchQuery]);

  // Fetch customers function with pagination and search
  const fetchCustomers = useCallback(async () => {
    setIsLoading(true);
    try {
      const response = await customerService.getCustomers(
        currentPage,
        perPage,
        debouncedSearchQuery || undefined,
        nullToUndefined(authHeader) || ''
      );

      if (response.isSuccess && response.data) {
        setCustomers(response.data.data);
        setTotalItems(response.data.meta.total);
      } else {
        toast.error(response.error || 'Failed to fetch customers');
      }
    } catch (error) {
      console.error('Error fetching customers:', error);
      toast.error('Failed to fetch customers');
    } finally {
      setIsLoading(false);
    }
  }, [currentPage, debouncedSearchQuery, perPage, authHeader]);

  // Fetch customers on component mount and when dependencies change
  useEffect(() => {
    fetchCustomers();
  }, [currentPage, debouncedSearchQuery, perPage]);

  // Handle page change
  const handlePageChange = useCallback((page: number) => {
    setCurrentPage(page);
  }, []);

  // Handle clear search
  const handleClearSearch = useCallback(() => {
    setSearchQuery('');
    setCurrentPage(1);
  }, []);

  // Fetch customer stats
  const fetchCustomerStats = async (customerId: string) => {
    try {
      const response = await customerService.getCustomerStats(customerId, nullToUndefined(authHeader) || '');
      
      if (response.isSuccess && response.data) {
        return response.data;
      }
      return null;
    } catch (error) {
      console.error('Error fetching customer stats:', error);
      return null;
    }
  };

  const formatDate = (dateInput: string | Date): string => {
    const date = typeof dateInput === 'string' ? new Date(dateInput) : dateInput;
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  const formatCurrency = (amount: number): string => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  const handleViewDetails = async (customer: Customer) => {
    setSelectedCustomer(customer);
    setLoadingStats(true);

    const stats = await fetchCustomerStats(customer.id);
    if (stats) {
      setCustomerStats(stats);
    }

    setLoadingStats(false);
    setIsDetailModalOpen(true);
  };

  // Handle chat creation with customer
  const handleCreateChat = async (customer: Customer) => {
    setIsCreatingChat(customer.id);

    try {
      const response = await chatService.createChat(
        customer.id,
        'direct',
        undefined,
        nullToUndefined(authHeader) || ''
      );

      if (response.isSuccess && response.data) {
        showToast({
          title: "Success",
          description: `Chat room created with ${customer.name}`,
        });
        navigate(`/admin/messages?userId=${customer.id}`);
      } else {
        if (response.error?.includes('already exists') || response.error?.includes('duplicate')) {
          showToast({
            title: "Success",
            description: `Opening existing chat with ${customer.name}`,
          });
          navigate(`/admin/messages?userId=${customer.id}`);
        } else {
          showToast({
            title: "Error",
            description: response.error || 'Failed to create chat room',
            variant: "destructive"
          });
        }
      }
    } catch (error) {
      console.error('Error creating chat:', error);
      showToast({
        title: "Error",
        description: 'Failed to create chat room',
        variant: "destructive"
      });
    } finally {
      setIsCreatingChat(null);
    }
  };

  // Handle customer deletion
  const handleDeleteCustomer = async () => {
    if (!customerToDelete) return;

    setIsDeleting(true);
    try {
      const response = await customerService.deleteCustomer(
        customerToDelete.id,
        nullToUndefined(authHeader) || ''
      );

      if (response.isSuccess) {
        toast.success('Customer deleted successfully');
        // Refresh the current page to get updated data
        fetchCustomers();
        setIsDeleteModalOpen(false);
        setCustomerToDelete(null);
      } else {
        toast.error(response.error || 'Failed to delete customer');
      }
    } catch (error) {
      console.error('Error deleting customer:', error);
      toast.error('Failed to delete customer');
    } finally {
      setIsDeleting(false);
    }
  };

  const getUserInitials = (name: string): string => {
    return name.split(' ').map(name => name.charAt(0).toUpperCase()).join('');
  };

  const MobileCustomerCard = ({ customer }: { customer: Customer }) => {
    
    return (
      <Card className="mb-4">
        <CardContent className="p-4">
          <div className="flex items-center space-x-4">
            <Avatar>
              <AvatarImage src="/placeholder.svg" alt={customer.name} />
              <AvatarFallback>{getUserInitials(customer.name)}</AvatarFallback>
            </Avatar>
            <div>
              <CardTitle className="text-lg font-semibold">{customer.name}</CardTitle>
              <CardDescription className="text-gray-500">
                {customer.email}
              </CardDescription>
            </div>
          </div>
          <div className="mt-3 flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => handleViewDetails(customer)}
              className="flex-1"
            >
              View Details
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => handleCreateChat(customer)}
              disabled={isCreatingChat === customer.id}
              className="flex-1"
            >
              {isCreatingChat === customer.id ? (
                <>
                  <Loader2 className="h-4 w-4 mr-1 animate-spin" />
                  Creating...
                </>
              ) : (
                <>
                  <MessageSquare className="h-4 w-4 mr-1" />
                  Chat
                </>
              )}
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                setCustomerToDelete(customer);
                setIsDeleteModalOpen(true);
              }}
              className="px-2"
            >
              <Trash2 className="h-4 w-4" />
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  };

  const tableRowContent = customers.map((customer) => (
    <TableRow key={customer.id}>
      <TableCell className="font-medium">{customer.id}</TableCell>
      <TableCell>
        <div className="flex items-center space-x-2">
          <Avatar>
            <AvatarImage src="/placeholder.svg" alt={customer.name} />
            <AvatarFallback>{getUserInitials(customer.name)}</AvatarFallback>
          </Avatar>
          <span>{customer.name}</span>
        </div>
      </TableCell>
      <TableCell>{customer.email}</TableCell>
      <TableCell>{customer.phone}</TableCell>
      <TableCell>
        {customer.last_login ? formatDate(customer.last_login) : 'N/A'}
      </TableCell>
      <TableCell>
        <div className="flex gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleViewDetails(customer)}
          >
            View
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleCreateChat(customer)}
            disabled={isCreatingChat === customer.id}
          >
            {isCreatingChat === customer.id ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <MessageSquare className="h-4 w-4" />
            )}
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => {
              setCustomerToDelete(customer);
              setIsDeleteModalOpen(true);
            }}
          >
            <Trash2 className="h-4 w-4" />
          </Button>
        </div>
      </TableCell>
    </TableRow>
  ));

  // Fix the conversation display
  const handleViewConversation = (customer: Customer) => {
    if (customer.conversation_history) {
      const messages: Message[] = customer.conversation_history.map((msg: ConversationMessage) => ({
        id: msg.id,
        content: [{ type: 'text', data: msg.message }],
        sender: msg.sender_type === 'customer' || msg.sender_type === 'user' ? 'user' : 'bot',
        timestamp: new Date(msg.created_at),
      }));
      
      setConversationMessages(messages);
      setIsConversationOpen(true);
    }
  };

  return (
    <>
      <Card>
        <div className='block sm:flex justify-between items-center p-6'>
          <div className="">
            <CardTitle>Customers</CardTitle>
            <CardDescription>
              Manage registered customers and their details.
            </CardDescription>
          </div>
          <div className="">
            <div className="sm:flex gap-2 justify-end items-center">
            <div className="w-full sm:w-[400px] mt-4 sm:mt-0">
              {!isMobileHelper ? (
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <Input
                    type="text"
                    placeholder="Search customers by name, email, or phone..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-9 pr-8 py-2 bg-gray-50 border-gray-200"
                  />
                  {searchQuery && (
                    <div
                      className="absolute right-1 top-1/2 transform -translate-y-1/2 cursor-pointer mr-3"
                      onClick={handleClearSearch}
                    >
                      <X className="h-4 w-4" />
                    </div>
                  )}
                </div>
              ) : (
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <Input
                    className="pl-9 pr-8 py-2 rounded-full bg-gray-50 border-gray-200"
                    placeholder="Search customers..."
                    value={searchQuery}
                    onChange={(e) => {
                      setCurrentPage(1)
                      setSearchQuery(e.target.value)
                    }}
                  />
                  {searchQuery && (
                    <Button
                      variant="ghost"
                      size="icon"
                      className="absolute right-1 top-1/2 transform -translate-y-1/2 h-7 w-7"
                      onClick={handleClearSearch}
                    >
                      <X className="h-3 w-3" />
                    </Button>
                  )}
                </div>
              )}
            </div>
          </div>
      </div>
        </div>
        <CardContent className="relative">
          {/* Loading Overlay */}
          {isLoading && customers.length > 0 && (
            <div className="absolute inset-0 bg-white/50 dark:bg-gray-900/50 z-10 flex items-center justify-center">
              <div className="bg-white dark:bg-gray-800 rounded-lg p-4 shadow-lg">
                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary mx-auto mb-2"></div>
                <p className="text-sm text-gray-600 dark:text-gray-400">Loading...</p>
              </div>
            </div>
          )}

          {isLoading && customers.length === 0 ? (
            <div className="flex justify-center items-center p-8">
              <div className="text-center">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
                <p className="text-gray-600 dark:text-gray-400">Loading customers...</p>
              </div>
            </div>
          ) : (
            <>
              {isMobile ? (
                customers.length === 0 && !isLoading ? (
                  <div className="text-center py-12">
                    <div className="text-gray-500 dark:text-gray-400">
                      {debouncedSearchQuery ? (
                        <>
                          <p className="text-lg font-medium mb-2">No customers found</p>
                          <p>No customers match your search for "{debouncedSearchQuery}"</p>
                          <Button
                            variant="outline"
                            onClick={handleClearSearch}
                            className="mt-4"
                          >
                            Clear search
                          </Button>
                        </>
                      ) : (
                        <>
                          <p className="text-lg font-medium mb-2">No customers yet</p>
                          <p>Customers will appear here once they register</p>
                        </>
                      )}
                    </div>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {customers.map((customer) => (
                      <MobileCustomerCard key={customer.id} customer={customer} />
                    ))}

                    {/* Mobile Pagination */}
                    {totalItems > 0 && !isLoading && (
                      <div className="mt-6">
                        <Pagination
                          totalItems={totalItems}
                          itemsPerPage={perPage}
                          currentPage={currentPage}
                          onPageChange={handlePageChange}
                        />
                      </div>
                    )}
                  </div>
                )
              ) : (
                <div className="rounded-md border">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead className="w-[80px]">ID</TableHead>
                        <TableHead>Name</TableHead>
                        <TableHead>Email</TableHead>
                        <TableHead>Phone</TableHead>
                        <TableHead>Last Login</TableHead>
                        <TableHead className="text-right">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {customers.length === 0 && !isLoading ? (
                        <TableRow>
                          <TableCell colSpan={6} className="text-center py-12">
                            <div className="text-gray-500 dark:text-gray-400">
                              {debouncedSearchQuery ? (
                                <>
                                  <p className="text-lg font-medium mb-2">No customers found</p>
                                  <p>No customers match your search for "{debouncedSearchQuery}"</p>
                                  <Button
                                    variant="outline"
                                    onClick={handleClearSearch}
                                    className="mt-4"
                                  >
                                    Clear search
                                  </Button>
                                </>
                              ) : (
                                <>
                                  <p className="text-lg font-medium mb-2">No customers yet</p>
                                  <p>Customers will appear here once they register</p>
                                </>
                              )}
                            </div>
                          </TableCell>
                        </TableRow>
                      ) : (
                        tableRowContent
                      )}
                    </TableBody>
                  </Table>
                </div>
              )}

              {/* Desktop Pagination */}
              {!isMobile && totalItems > 0 && !isLoading && (
                <div className="mt-6">
                  <Pagination
                    totalItems={totalItems}
                    itemsPerPage={perPage}
                    currentPage={currentPage}
                    onPageChange={handlePageChange}
                  />
                </div>
              )}
            </>
          )}
        </CardContent>
      </Card>

      {/* Customer Details Modal */}
      <Dialog open={isDetailModalOpen} onOpenChange={setIsDetailModalOpen}>
        <DialogContent className="sm:max-w-[525px]">
          <DialogHeader>
            <DialogTitle>Customer Details</DialogTitle>
            <DialogDescription>
              View detailed information about the selected customer.
            </DialogDescription>
          </DialogHeader>
          {selectedCustomer && (
            <ScrollArea className="h-[400px] w-full space-y-4">
              <div className="py-4">
                <div className="flex items-center space-x-4">
                  <Avatar>
                    <AvatarImage src="/placeholder.svg" alt={selectedCustomer.name} />
                    <AvatarFallback>{getUserInitials(selectedCustomer.name)}</AvatarFallback>
                  </Avatar>
                  <div>
                    <div className="text-lg font-semibold">{selectedCustomer.name}</div>
                    <div className="text-sm text-gray-500">{selectedCustomer.email}</div>
                  </div>
                </div>
                <div className="mt-4 space-y-2">
                  <div className="flex items-center space-x-2">
                    <User className="h-4 w-4 text-gray-500" />
                    <span>ID: {selectedCustomer.id}</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Phone className="h-4 w-4 text-gray-500" />
                    <span>Phone: {selectedCustomer.phone || 'N/A'}</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Mail className="h-4 w-4 text-gray-500" />
                    <span>Email: {selectedCustomer.email}</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Mail className="h-4 w-4 text-gray-500" />
                    <span>Role ID: {selectedCustomer.role_id}</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Mail className="h-4 w-4 text-gray-500" />
                    <span>Created At: {selectedCustomer.created_at}</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Mail className="h-4 w-4 text-gray-500" />
                    <span>Updated At: {selectedCustomer.updated_at}</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Mail className="h-4 w-4 text-gray-500" />
                    <span>Last Login: {selectedCustomer.last_login}</span>
                  </div>
                </div>
              </div>

              {loadingStats ? (
                <div className="flex justify-center items-center p-8">
                  <Loader2 className="h-6 w-6 animate-spin text-primary" />
                </div>
              ) : customerStats ? (
                <div className="py-4 border-t">
                  <div className="text-lg font-semibold mb-2">Statistics</div>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <Card>
                      <CardHeader>
                        <CardTitle>Total Orders</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="text-3xl font-bold">{customerStats.totalOrders}</div>
                      </CardContent>
                    </Card>
                    <Card>
                      <CardHeader>
                        <CardTitle>Total Spend</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="text-3xl font-bold">
                          {formatCurrency(customerStats.totalSpend)}
                        </div>
                      </CardContent>
                    </Card>
                  </div>
                </div>
              ) : (
                <div className="py-4 border-t text-center text-gray-500">
                  Could not load customer statistics.
                </div>
              )}
            </ScrollArea>
          )}
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Modal */}
      <Dialog open={isDeleteModalOpen} onOpenChange={setIsDeleteModalOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Delete Customer</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete this customer? This action cannot be
              undone.
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            {customerToDelete && (
              <div className="border rounded-md p-4">
                <div className="text-sm font-medium">
                  {customerToDelete.name}
                </div>
                <div className="text-sm text-gray-500">{customerToDelete.email}</div>
              </div>
            )}
          </div>
          <div className="flex justify-end space-x-2">
            <Button variant="outline" onClick={() => setIsDeleteModalOpen(false)}>
              Cancel
            </Button>
            <Button
              variant="destructive"
              disabled={isDeleting}
              onClick={handleDeleteCustomer}
            >
              {isDeleting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Deleting...
                </>
              ) : (
                "Delete Customer"
              )}
            </Button>
          </div>
        </DialogContent>
      </Dialog>

      {/* Conversation Modal */}
      <Dialog open={isConversationOpen} onOpenChange={setIsConversationOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>Conversation History</DialogTitle>
            <DialogDescription>
              View the conversation history with the selected customer.
            </DialogDescription>
          </DialogHeader>
          <ScrollArea className="h-[400px] w-full space-y-4">
            {/* Conversation display will go here */}
          </ScrollArea>
        </DialogContent>
      </Dialog>
    </>
  );
};

// Optimize with React.memo for better performance
export const CustomersTable = React.memo(CustomersTableComponent);

// Add displayName for debugging
CustomersTable.displayName = 'CustomersTable';
