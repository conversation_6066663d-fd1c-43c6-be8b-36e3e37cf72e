
import { useState } from "react";
import { 
  Table,
  TableBody,
  TableCaption,
  TableCell,
  TableHead,
  TableHeader,
  TableRow 
} from "@/components/ui/table";
import { Input } from "@/components/ui/input";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Search, MoreVertical, Eye, Plus, Download, Filter, Trash, X, ChevronRight } from "lucide-react";
import { 
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog";
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Sheet,
  She<PERSON><PERSON>ontent,
  She<PERSON><PERSON>eader,
  She<PERSON><PERSON><PERSON><PERSON>,
  SheetTrigger,
} from "@/components/ui/sheet";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { useToast } from "@/hooks/use-toast";
import { useUIHelpers } from "@/hooks/use-ui-helpers";

export const RewardsManagement = () => {
  const { toast } = useToast();
  const { isMobile } = useUIHelpers();
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedReward, setSelectedReward] = useState<any | null>(null);
  const [showRewardDetails, setShowRewardDetails] = useState(false);
  const [showNewRewardDialog, setShowNewRewardDialog] = useState(false);
  const [typeFilter, setTypeFilter] = useState<string>("all");
  const [selectedRewards, setSelectedRewards] = useState<string[]>([]);
  const [filterOpen, setFilterOpen] = useState(false);
  const [showMobileDetails, setShowMobileDetails] = useState(false);
  
  // Form states for new reward
  const [newRewardCustomer, setNewRewardCustomer] = useState("");
  const [newRewardType, setNewRewardType] = useState("Referral");
  const [newRewardAmount, setNewRewardAmount] = useState("50");

  // Mock data - in a real app, this would come from an API
  const rewards = [
    {
      id: "RWD-1001",
      customerName: "Sarah Johnson",
      customerId: "CUS-001",
      type: "Referral",
      amount: 50.00,
      date: "2025-04-10",
      notes: "First successful referral reward"
    },
    {
      id: "RWD-1002",
      customerName: "David Wilson",
      customerId: "CUS-003",
      type: "First Job",
      amount: 25.00,
      date: "2025-04-12",
      notes: "Welcome bonus for first job"
    },
    {
      id: "RWD-1003",
      customerName: "Emily Davis",
      customerId: "CUS-002",
      type: "3 Jobs",
      amount: 35.00,
      date: "2025-04-08",
      notes: "Milestone reward for 3 completed jobs"
    },
    {
      id: "RWD-1004",
      customerName: "James Miller",
      customerId: "CUS-004",
      type: "Referral",
      amount: 50.00,
      date: "2025-04-14",
      notes: "Second successful referral reward"
    },
    {
      id: "RWD-1005",
      customerName: "Lisa Parker",
      customerId: "CUS-005",
      type: "Referral",
      amount: 50.00,
      date: "2025-04-15",
      notes: "First successful referral reward"
    }
  ];

  // Mock customers for dropdown
  const customers = [
    { id: "CUS-001", name: "Sarah Johnson" },
    { id: "CUS-002", name: "Emily Davis" },
    { id: "CUS-003", name: "David Wilson" },
    { id: "CUS-004", name: "James Miller" },
    { id: "CUS-005", name: "Lisa Parker" },
  ];

  // Filter rewards based on search query and type filter
  const filteredRewards = rewards.filter(reward => {
    const matchesSearch = 
      reward.customerName.toLowerCase().includes(searchQuery.toLowerCase()) ||
      reward.id.toLowerCase().includes(searchQuery.toLowerCase()) ||
      reward.type.toLowerCase().includes(searchQuery.toLowerCase());
    
    const matchesTypeFilter = typeFilter === "all" || reward.type === typeFilter;
    
    return matchesSearch && matchesTypeFilter;
  });

  const handleViewDetails = (reward: any) => {
    setSelectedReward(reward);
    if (isMobile) {
      setShowMobileDetails(true);
    } else {
      setShowRewardDetails(true);
    }
  };

  const handleCheckboxChange = (id: string) => {
    setSelectedRewards(prev => 
      prev.includes(id) ? prev.filter(item => item !== id) : [...prev, id]
    );
  };

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedRewards(filteredRewards.map(reward => reward.id));
    } else {
      setSelectedRewards([]);
    }
  };

  const handleCreateReward = () => {
    // In a real app, this would be an API call
    toast({
      title: "Reward created",
      description: `A ${newRewardAmount} credit has been issued to the customer.`,
    });
    setShowNewRewardDialog(false);
    // Reset form
    setNewRewardCustomer("");
    setNewRewardType("Referral");
    setNewRewardAmount("50");
  };

  const handleDeleteSelectedRewards = () => {
    // In a real app, this would be an API call
    toast({
      title: "Rewards deleted",
      description: `${selectedRewards.length} rewards have been deleted.`,
    });
    setSelectedRewards([]);
  };

  const handleExportCSV = () => {
    toast({
      title: "Export initiated",
      description: "Rewards data is being exported to CSV.",
    });
  };

  // Helper function for reward type badge styling
  const getTypeBadge = (type: string) => {
    switch(type) {
      case "Referral":
        return <Badge variant="outline" className="bg-purple-50 text-purple-600 border-purple-200">Referral</Badge>;
      case "First Job":
        return <Badge variant="outline" className="bg-blue-50 text-blue-600 border-blue-200">First Job</Badge>;
      case "3 Jobs":
        return <Badge variant="outline" className="bg-green-50 text-green-600 border-green-200">3 Jobs</Badge>;
      default:
        return <Badge variant="outline">{type}</Badge>;
    }
  };

  // Mobile reward card component
  const MobileRewardCard = ({ reward }: { reward: any }) => (
    <Card 
      className="mb-3" 
      onClick={() => handleViewDetails(reward)}
    >
      <CardContent className="p-4">
        <div className="flex items-start justify-between">
          <div>
            <h3 className="font-medium">{reward.id}</h3>
            <p className="text-sm text-muted-foreground">{new Date(reward.date).toLocaleDateString()}</p>
          </div>
          <div className="flex items-center gap-2">
            {getTypeBadge(reward.type)}
            <ChevronRight className="h-4 w-4 text-muted-foreground" />
          </div>
        </div>
        
        <div className="mt-2">
          <div className="flex justify-between items-center">
            <span className="text-sm font-medium text-muted-foreground">Customer:</span>
            <span className="text-sm">{reward.customerName}</span>
          </div>
          <div className="flex justify-between items-center mt-1">
            <span className="text-sm font-medium text-muted-foreground">Amount:</span>
            <span className="text-sm font-semibold">${reward.amount.toFixed(2)}</span>
          </div>
        </div>
      </CardContent>
    </Card>
  );

  // Mobile reward detail component
  const MobileRewardDetailView = ({ reward, onClose }: { reward: any, onClose: () => void }) => (
    <div className="flex flex-col h-full bg-background">
      <div className="sticky top-0 z-10 bg-background border-b p-4 flex items-center justify-between">
        <h2 className="font-semibold text-lg">Reward Details</h2>
        <Button variant="ghost" size="icon" onClick={onClose}>
          <X className="h-5 w-5" />
        </Button>
      </div>
      
      <div className="flex-1 overflow-auto p-4">
        <div className="space-y-6">
          <div className="space-y-2">
            <h3 className="text-sm font-medium text-muted-foreground">Reward ID</h3>
            <p className="font-medium">{reward.id}</p>
          </div>
          
          <div className="space-y-2">
            <h3 className="text-sm font-medium text-muted-foreground">Date</h3>
            <p className="font-medium">{new Date(reward.date).toLocaleDateString()}</p>
          </div>
          
          <div className="space-y-2">
            <h3 className="text-sm font-medium text-muted-foreground">Customer</h3>
            <p className="font-medium">{reward.customerName}</p>
            <p className="text-xs text-muted-foreground">{reward.customerId}</p>
          </div>
          
          <div className="space-y-2">
            <h3 className="text-sm font-medium text-muted-foreground">Reward Type</h3>
            <div>{getTypeBadge(reward.type)}</div>
          </div>
          
          <div className="space-y-2">
            <h3 className="text-sm font-medium text-muted-foreground">Amount</h3>
            <p className="font-medium">${reward.amount.toFixed(2)}</p>
          </div>
          
          <div className="space-y-2">
            <h3 className="text-sm font-medium text-muted-foreground">Notes</h3>
            <p className="mt-1">{reward.notes}</p>
          </div>
        </div>
      </div>
      
      <div className="sticky bottom-0 z-10 bg-background border-t p-4">
        <Button 
          variant="destructive" 
          className="w-full"
          onClick={() => {
            onClose();
            setSelectedRewards([reward.id]);
            handleDeleteSelectedRewards();
          }}
        >
          <Trash className="h-4 w-4 mr-2" />
          Delete Reward
        </Button>
      </div>
    </div>
  );

  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row md:items-center justify-between gap-4">
        <h1 className="text-2xl font-bold">Manage Rewards</h1>
        
        {isMobile ? (
          <div className="flex items-center gap-2">
            <Sheet open={filterOpen} onOpenChange={setFilterOpen}>
              <SheetTrigger asChild>
                <Button variant="outline" size="icon">
                  <Filter className="h-4 w-4" />
                </Button>
              </SheetTrigger>
              <SheetContent side="bottom" className="h-[50vh]">
                <SheetHeader className="mb-4">
                  <SheetTitle>Filter Rewards</SheetTitle>
                </SheetHeader>
                <div className="space-y-4">
                  <div>
                    <label className="text-sm font-medium mb-1 block">Search</label>
                    <div className="relative">
                      <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                      <Input
                        placeholder="Search rewards..."
                        value={searchQuery}
                        onChange={(e) => setSearchQuery(e.target.value)}
                        className="pl-9"
                      />
                      {searchQuery && (
                        <Button
                          variant="ghost"
                          size="icon"
                          className="absolute right-1 top-1/2 -translate-y-1/2 h-7 w-7"
                          onClick={() => setSearchQuery("")}
                        >
                          <X className="h-4 w-4" />
                        </Button>
                      )}
                    </div>
                  </div>
                  
                  <div>
                    <label className="text-sm font-medium mb-1 block">Reward Type</label>
                    <Select value={typeFilter} onValueChange={setTypeFilter}>
                      <SelectTrigger>
                        <SelectValue placeholder="All Types" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">All Types</SelectItem>
                        <SelectItem value="Referral">Referral</SelectItem>
                        <SelectItem value="First Job">First Job</SelectItem>
                        <SelectItem value="3 Jobs">3 Jobs</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  
                  <div className="pt-4">
                    <Button 
                      className="w-full" 
                      onClick={() => setFilterOpen(false)}
                    >
                      Apply Filters
                    </Button>
                  </div>
                </div>
              </SheetContent>
            </Sheet>
            
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search rewards..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-9 w-full"
              />
              {searchQuery && (
                <Button
                  variant="ghost"
                  size="icon"
                  className="absolute right-1 top-1/2 -translate-y-1/2 h-7 w-7"
                  onClick={() => setSearchQuery("")}
                >
                  <X className="h-4 w-4" />
                </Button>
              )}
            </div>
            
            <Button
              variant="default"
              size="icon"
              onClick={() => setShowNewRewardDialog(true)}
            >
              <Plus className="h-4 w-4" />
            </Button>
          </div>
        ) : (
          <div className="flex flex-wrap items-center gap-2">
            <div className="relative">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                type="search"
                placeholder="Search rewards..."
                className="pl-8 w-[250px] md:w-[300px]"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
            
            <Select value={typeFilter} onValueChange={setTypeFilter}>
              <SelectTrigger className="w-[180px]">
                <div className="flex items-center gap-2">
                  <Filter className="h-4 w-4" />
                  <SelectValue placeholder="Filter by type" />
                </div>
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Types</SelectItem>
                <SelectItem value="Referral">Referral</SelectItem>
                <SelectItem value="First Job">First Job</SelectItem>
                <SelectItem value="3 Jobs">3 Jobs</SelectItem>
              </SelectContent>
            </Select>
            
            <Button 
              variant="outline" 
              className="flex items-center gap-2"
              onClick={handleExportCSV}
            >
              <Download className="h-4 w-4" />
              Export CSV
            </Button>
            
            <Button 
              variant="default" 
              className="flex items-center gap-2"
              onClick={() => setShowNewRewardDialog(true)}
            >
              <Plus className="h-4 w-4" />
              Issue Reward
            </Button>
          </div>
        )}
      </div>

      {selectedRewards.length > 0 && (
        <div className="bg-muted/50 p-2 rounded-md flex items-center justify-between">
          <p className="text-sm">{selectedRewards.length} rewards selected</p>
          <div className="flex gap-2">
            <Button 
              variant="outline" 
              size="sm"
              onClick={() => setSelectedRewards([])}
            >
              Deselect All
            </Button>
            <Button 
              variant="destructive" 
              size="sm"
              onClick={handleDeleteSelectedRewards}
            >
              <Trash className="h-4 w-4 mr-1" />
              Delete Selected
            </Button>
          </div>
        </div>
      )}

      {isMobile ? (
        <div className="space-y-2">
          {filteredRewards.length === 0 ? (
            <div className="text-center py-8">
              <p className="text-muted-foreground">No rewards found matching your filters.</p>
            </div>
          ) : (
            filteredRewards.map(reward => (
              <MobileRewardCard key={reward.id} reward={reward} />
            ))
          )}
        </div>
      ) : (
        <Table>
          <TableCaption>A list of all reward grants on the platform.</TableCaption>
          <TableHeader>
            <TableRow>
              <TableHead className="w-[50px]">
                <Checkbox 
                  checked={selectedRewards.length > 0 && selectedRewards.length === filteredRewards.length}
                  onCheckedChange={handleSelectAll}
                  aria-label="Select all"
                />
              </TableHead>
              <TableHead>Reward ID</TableHead>
              <TableHead>Customer</TableHead>
              <TableHead>Reward Type</TableHead>
              <TableHead>Amount</TableHead>
              <TableHead>Date</TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredRewards.map((reward) => (
              <TableRow key={reward.id} className={selectedRewards.includes(reward.id) ? "bg-muted/50" : ""}>
                <TableCell>
                  <Checkbox 
                    checked={selectedRewards.includes(reward.id)}
                    onCheckedChange={() => handleCheckboxChange(reward.id)}
                    aria-label={`Select reward ${reward.id}`}
                  />
                </TableCell>
                <TableCell className="font-medium">{reward.id}</TableCell>
                <TableCell>{reward.customerName}</TableCell>
                <TableCell>{getTypeBadge(reward.type)}</TableCell>
                <TableCell>${reward.amount.toFixed(2)}</TableCell>
                <TableCell>{new Date(reward.date).toLocaleDateString()}</TableCell>
                <TableCell className="text-right">
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" className="h-8 w-8 p-0">
                        <MoreVertical className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem onClick={() => handleViewDetails(reward)}>
                        <Eye className="mr-2 h-4 w-4" />
                        <span>View Details</span>
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => {
                        handleCheckboxChange(reward.id);
                        handleDeleteSelectedRewards();
                      }}>
                        <Trash className="mr-2 h-4 w-4" />
                        <span>Delete Reward</span>
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      )}

      {/* Reward Details Dialog (Desktop) */}
      <Dialog open={showRewardDetails} onOpenChange={setShowRewardDetails}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>Reward Details</DialogTitle>
            <DialogDescription>
              View detailed information about this reward.
            </DialogDescription>
          </DialogHeader>
          {selectedReward && (
            <div className="py-4 space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground">Reward ID</h3>
                  <p className="font-medium">{selectedReward.id}</p>
                </div>
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground">Date</h3>
                  <p className="font-medium">{new Date(selectedReward.date).toLocaleDateString()}</p>
                </div>
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground">Customer</h3>
                  <p className="font-medium">{selectedReward.customerName}</p>
                  <p className="text-xs text-muted-foreground">{selectedReward.customerId}</p>
                </div>
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground">Amount</h3>
                  <p className="font-medium">${selectedReward.amount.toFixed(2)}</p>
                </div>
              </div>

              <div>
                <h3 className="text-sm font-medium text-muted-foreground">Reward Type</h3>
                <p className="mt-1">{getTypeBadge(selectedReward.type)}</p>
              </div>

              <div>
                <h3 className="text-sm font-medium text-muted-foreground">Notes</h3>
                <p className="mt-1">{selectedReward.notes}</p>
              </div>
            </div>
          )}
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowRewardDetails(false)}>Close</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Mobile Reward Detail View */}
      {isMobile && selectedReward && (
        <div className={`fixed inset-0 bg-background z-50 ${showMobileDetails ? 'block' : 'hidden'}`}>
          <MobileRewardDetailView
            reward={selectedReward}
            onClose={() => setShowMobileDetails(false)}
          />
        </div>
      )}

      {/* New Reward Dialog */}
      <Dialog open={showNewRewardDialog} onOpenChange={setShowNewRewardDialog}>
        <DialogContent className={isMobile ? "w-[95%] max-w-[500px] p-4" : "sm:max-w-[500px]"}>
          <DialogHeader>
            <DialogTitle>Issue New Reward</DialogTitle>
            <DialogDescription>
              Manually issue a wallet credit reward to a customer.
            </DialogDescription>
          </DialogHeader>
          <div className="py-4 space-y-4">
            <div className="space-y-2">
              <Label htmlFor="customer">Customer</Label>
              <Select value={newRewardCustomer} onValueChange={setNewRewardCustomer}>
                <SelectTrigger id="customer">
                  <SelectValue placeholder="Select customer" />
                </SelectTrigger>
                <SelectContent>
                  {customers.map((customer) => (
                    <SelectItem key={customer.id} value={customer.id}>
                      {customer.name} ({customer.id})
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="type">Reward Type</Label>
              <Select value={newRewardType} onValueChange={setNewRewardType}>
                <SelectTrigger id="type">
                  <SelectValue placeholder="Select reward type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Referral">Referral</SelectItem>
                  <SelectItem value="First Job">First Job</SelectItem>
                  <SelectItem value="3 Jobs">3 Jobs</SelectItem>
                  <SelectItem value="Custom">Custom</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="amount">Reward Amount ($)</Label>
              <Input
                id="amount"
                type="number"
                value={newRewardAmount}
                onChange={(e) => setNewRewardAmount(e.target.value)}
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="notes">Notes</Label>
              <Input
                id="notes"
                placeholder="Add notes about this reward (optional)"
              />
            </div>
          </div>
          <DialogFooter className={isMobile ? "flex-col space-y-2" : undefined}>
            <Button 
              variant="outline" 
              className={isMobile ? "w-full" : undefined} 
              onClick={() => setShowNewRewardDialog(false)}
            >
              Cancel
            </Button>
            <Button 
              className={isMobile ? "w-full" : undefined}
              onClick={handleCreateReward}
              disabled={!newRewardCustomer || !newRewardType || !newRewardAmount}
            >
              Issue Reward
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};
