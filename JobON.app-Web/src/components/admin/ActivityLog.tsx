
import { useState } from "react";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Card } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { ScrollArea } from "@/components/ui/scroll-area";
import { 
  Search, 
  UserCheck,
  UserX,
  Wallet,
  Gift,
  AlertCircle,
  RotateCcw,
  CalendarX,
  ChevronDown,
  Filter
} from "lucide-react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Popover,
  PopoverContent,
  PopoverTrigger
} from "@/components/ui/popover";
import {
  She<PERSON>,
  <PERSON><PERSON><PERSON>onte<PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>rigger
} from "@/components/ui/sheet";
import { Calendar } from "@/components/ui/calendar";
import { format } from "date-fns";
import { useMediaQuery } from "@/hooks/use-media-query";

export const ActivityLog = () => {
  const [searchQuery, setSearchQuery] = useState("");
  const [actionFilter, setActionFilter] = useState("all");
  const [adminFilter, setAdminFilter] = useState("all");
  const [dateRange, setDateRange] = useState<{
    from: Date | undefined;
    to: Date | undefined;
  }>({
    from: undefined,
    to: undefined,
  });
  const [isLoading, setIsLoading] = useState(false);
  const [selectedActivity, setSelectedActivity] = useState<string | null>(null);
  
  // Media query for responsive design
  const isMobile = useMediaQuery("(max-width: 640px)");

  // Mock activity data
  const activities = [
    {
      id: "act-001",
      action: "SuspendProvider",
      actionLabel: "Provider Suspended",
      admin: {
        name: "Admin A",
        avatar: "",
      },
      target: "Mike Smith (PRV-001)",
      timestamp: "2025-04-22T09:30:00Z",
      details: "Provider suspended for policy violation - late job completion."
    },
    {
      id: "act-002",
      action: "ReactivateCustomer",
      actionLabel: "Customer Reactivated",
      admin: {
        name: "Admin B",
        avatar: "",
      },
      target: "James Thompson (CUS-003)",
      timestamp: "2025-04-21T14:45:00Z",
      details: "Customer account reactivated after account verification."
    },
    {
      id: "act-003",
      action: "AdjustWallet",
      actionLabel: "Wallet Adjustment",
      admin: {
        name: "Admin A",
        avatar: "",
      },
      target: "Emma Wilson (CUS-002)",
      timestamp: "2025-04-21T11:20:00Z",
      details: "Added $50 credits as compensation for service delay."
    },
    {
      id: "act-004",
      action: "RewardIssued",
      actionLabel: "Reward Issued",
      admin: {
        name: "Admin C",
        avatar: "",
      },
      target: "John Doe (CUS-001)",
      timestamp: "2025-04-20T16:15:00Z",
      details: "Manual reward of $100 issued for referring 5 new customers."
    },
    {
      id: "act-005",
      action: "CancelJob",
      actionLabel: "Job Cancelled",
      admin: {
        name: "Admin B",
        avatar: "",
      },
      target: "JOB-1003 (Emily Davis)",
      timestamp: "2025-04-19T13:10:00Z",
      details: "Job cancelled due to provider unavailability."
    },
    {
      id: "act-006",
      action: "RefundIssued",
      actionLabel: "Refund Issued",
      admin: {
        name: "Admin A",
        avatar: "",
      },
      target: "David Wilson (CUS-003)",
      timestamp: "2025-04-18T10:05:00Z",
      details: "Refund of $180 issued for incomplete service."
    }
  ];

  // Available admin users for filter (derived from activities)
  const adminUsers = [...new Set(activities.map(activity => activity.admin.name))];
  
  // Available action types for filter (derived from activities)
  const actionTypes = [...new Set(activities.map(activity => activity.action))];

  // Filter activities based on search and filters
  const filteredActivities = activities.filter(activity => {
    // Match search query
    const matchesSearch = 
      activity.target.toLowerCase().includes(searchQuery.toLowerCase()) ||
      activity.admin.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      activity.details.toLowerCase().includes(searchQuery.toLowerCase()) ||
      activity.actionLabel.toLowerCase().includes(searchQuery.toLowerCase());
    
    // Match action filter
    const matchesAction = actionFilter === "all" || activity.action === actionFilter;
    
    // Match admin filter
    const matchesAdmin = adminFilter === "all" || activity.admin.name === adminFilter;
    
    // Match date range filter
    let matchesDateRange = true;
    if (dateRange.from) {
      const activityDate = new Date(activity.timestamp);
      if (dateRange.from && activityDate < dateRange.from) {
        matchesDateRange = false;
      }
      if (dateRange.to && activityDate > dateRange.to) {
        matchesDateRange = false;
      }
    }
    
    return matchesSearch && matchesAction && matchesAdmin && matchesDateRange;
  });

  // Helper function for action badge styling
  const getActionBadge = (action: string) => {
    switch(action) {
      case "SuspendProvider":
      case "SuspendCustomer":
        return <Badge variant="destructive">{action.replace(/([A-Z])/g, ' $1').trim()}</Badge>;
      case "ReactivateProvider":
      case "ReactivateCustomer":
        return <Badge variant="success">{action.replace(/([A-Z])/g, ' $1').trim()}</Badge>;
      case "AdjustWallet":
        return <Badge variant="business">{action.replace(/([A-Z])/g, ' $1').trim()}</Badge>;
      case "RewardIssued":
        return <Badge variant="success">{action.replace(/([A-Z])/g, ' $1').trim()}</Badge>;
      case "CancelJob":
        return <Badge variant="destructive">{action.replace(/([A-Z])/g, ' $1').trim()}</Badge>;
      case "RefundIssued":
        return <Badge variant="warning">{action.replace(/([A-Z])/g, ' $1').trim()}</Badge>;
      default:
        return <Badge variant="outline">{action.replace(/([A-Z])/g, ' $1').trim()}</Badge>;
    }
  };

  // Helper function for action icon
  const getActionIcon = (action: string) => {
    switch(action) {
      case "SuspendProvider":
      case "SuspendCustomer":
        return <UserX className="h-4 w-4 text-red-500" />;
      case "ReactivateProvider":
      case "ReactivateCustomer":
        return <UserCheck className="h-4 w-4 text-green-500" />;
      case "AdjustWallet":
        return <Wallet className="h-4 w-4 text-blue-500" />;
      case "RewardIssued":
        return <Gift className="h-4 w-4 text-green-500" />;
      case "CancelJob":
        return <CalendarX className="h-4 w-4 text-red-500" />;
      case "RefundIssued":
        return <RotateCcw className="h-4 w-4 text-yellow-500" />;
      default:
        return <AlertCircle className="h-4 w-4 text-gray-500" />;
    }
  };

  // Format timestamp
  const formatTimestamp = (timestamp: string) => {
    const date = new Date(timestamp);
    return format(date, isMobile ? "MMM d, yyyy" : "MMM d, yyyy 'at' h:mm a");
  };

  // Simulate loading state for better UX
  const handleReload = () => {
    setIsLoading(true);
    setTimeout(() => {
      setIsLoading(false);
    }, 1000);
  };

  // Handle activity click for mobile view
  const handleActivityClick = (id: string) => {
    if (isMobile) {
      setSelectedActivity(id === selectedActivity ? null : id);
    }
  };

  return (
    <div className="space-y-4">
      <div>
        <h1 className="text-xl md:text-2xl font-bold">Activity Log</h1>
        <p className="text-muted-foreground text-sm">Track all admin actions on the platform.</p>
      </div>
      
      {/* Mobile Filters */}
      {isMobile && (
        <div className="flex items-center justify-between gap-2 mb-2">
          <div className="relative flex-1">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              type="search"
              placeholder="Search activities..."
              className="pl-8 h-10"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
          
          <Sheet>
            <SheetTrigger asChild>
              <Button variant="outline" size="sm" className="px-2 h-10">
                <Filter className="h-4 w-4 mr-1" />
                Filters
              </Button>
            </SheetTrigger>
            <SheetContent side="bottom" className="h-[80vh]">
              <SheetHeader>
                <SheetTitle>Filter Activities</SheetTitle>
              </SheetHeader>
              <div className="mt-4 space-y-5">
                <div className="space-y-2">
                  <label className="text-sm font-medium">Action Type</label>
                  <Select value={actionFilter} onValueChange={setActionFilter}>
                    <SelectTrigger className="w-full h-10">
                      <SelectValue placeholder="Action Type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Actions</SelectItem>
                      {actionTypes.map(type => (
                        <SelectItem key={type} value={type}>
                          {type.replace(/([A-Z])/g, ' $1').trim()}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                
                <div className="space-y-2">
                  <label className="text-sm font-medium">Admin</label>
                  <Select value={adminFilter} onValueChange={setAdminFilter}>
                    <SelectTrigger className="w-full h-10">
                      <SelectValue placeholder="Admin" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Admins</SelectItem>
                      {adminUsers.map(admin => (
                        <SelectItem key={admin} value={admin}>{admin}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                
                <div className="space-y-2">
                  <label className="text-sm font-medium">Date Range</label>
                  <div className="border rounded-md p-3">
                    <Calendar
                      mode="range"
                      selected={{
                        from: dateRange.from,
                        to: dateRange.to,
                      }}
                      onSelect={(range: any) => setDateRange(range)}
                      className="mx-auto"
                    />
                  </div>
                </div>
                
                <div className="flex items-center justify-between pt-4">
                  <Button 
                    variant="outline" 
                    onClick={() => {
                      setActionFilter("all");
                      setAdminFilter("all");
                      setDateRange({from: undefined, to: undefined});
                    }}
                  >
                    Clear Filters
                  </Button>
                  <Button onClick={handleReload}>Apply Filters</Button>
                </div>
              </div>
            </SheetContent>
          </Sheet>
        </div>
      )}
      
      {/* Desktop Filters */}
      {!isMobile && (
        <div className="flex flex-wrap gap-4 items-center justify-between">
          <div className="flex flex-wrap w-full lg:w-auto gap-2 items-center">
            <div className="relative flex-1 lg:flex-none">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                type="search"
                placeholder="Search activities..."
                className="pl-8 min-w-[200px]"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
            
            <Select value={actionFilter} onValueChange={setActionFilter}>
              <SelectTrigger className="w-[160px]">
                <SelectValue placeholder="Action Type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Actions</SelectItem>
                {actionTypes.map(type => (
                  <SelectItem key={type} value={type}>
                    {type.replace(/([A-Z])/g, ' $1').trim()}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            
            <Select value={adminFilter} onValueChange={setAdminFilter}>
              <SelectTrigger className="w-[140px]">
                <SelectValue placeholder="Admin" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Admins</SelectItem>
                {adminUsers.map(admin => (
                  <SelectItem key={admin} value={admin}>{admin}</SelectItem>
                ))}
              </SelectContent>
            </Select>
            
            <Popover>
              <PopoverTrigger asChild>
                <Button variant="outline" className="min-w-[160px] justify-start">
                  {dateRange.from ? (
                    dateRange.to ? (
                      <>
                        {format(dateRange.from, "MMM d, yyyy")} - {format(dateRange.to, "MMM d, yyyy")}
                      </>
                    ) : (
                      format(dateRange.from, "MMM d, yyyy")
                    )
                  ) : (
                    "Date Range"
                  )}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0" align="start">
                <Calendar
                  mode="range"
                  selected={{ from: dateRange.from, to: dateRange.to }}
                  onSelect={(range: any) => setDateRange(range)}
                  initialFocus
                />
              </PopoverContent>
            </Popover>
          </div>
          
          <div className="flex items-center gap-2 w-full lg:w-auto justify-end">
            <Button variant="outline" onClick={handleReload}>
              Refresh
            </Button>
          </div>
        </div>
      )}
      
      {/* Mobile Activity List */}
      {isMobile ? (
        <div className="space-y-3">
          {isLoading ? (
            // Loading skeleton cards
            Array(4).fill(0).map((_, i) => (
              <Card key={`skeleton-${i}`} className="p-4 space-y-3">
                <div className="flex items-center justify-between">
                  <Skeleton className="h-4 w-24" />
                  <Skeleton className="h-4 w-32" />
                </div>
                <div className="flex items-center gap-2">
                  <Skeleton className="h-8 w-8 rounded-full" />
                  <Skeleton className="h-4 w-20" />
                </div>
                <Skeleton className="h-4 w-full" />
              </Card>
            ))
          ) : filteredActivities.length > 0 ? (
            filteredActivities.map((activity) => (
              <Card 
                key={activity.id} 
                className="p-3"
                onClick={() => handleActivityClick(activity.id)}
              >
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      {getActionIcon(activity.action)}
                      {getActionBadge(activity.action)}
                    </div>
                    <span className="text-xs text-muted-foreground">
                      {formatTimestamp(activity.timestamp)}
                    </span>
                  </div>
                  
                  <div className="flex items-center gap-2">
                    <Avatar className="h-8 w-8">
                      <AvatarImage src={activity.admin.avatar} />
                      <AvatarFallback>{activity.admin.name.charAt(0)}</AvatarFallback>
                    </Avatar>
                    <div>
                      <p className="text-sm font-medium">{activity.admin.name}</p>
                      <p className="text-xs text-muted-foreground truncate max-w-[200px]">
                        {activity.target}
                      </p>
                    </div>
                  </div>
                  
                  {selectedActivity === activity.id && (
                    <div className="text-sm mt-2 border-t pt-2">
                      <p className="text-muted-foreground">{activity.details}</p>
                    </div>
                  )}
                  
                  {selectedActivity !== activity.id && (
                    <div className="flex justify-end">
                      <Button 
                        variant="ghost" 
                        size="sm"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleActivityClick(activity.id);
                        }}
                        className="text-xs py-1 h-7"
                      >
                        View Details <ChevronDown className="h-3 w-3 ml-1" />
                      </Button>
                    </div>
                  )}
                </div>
              </Card>
            ))
          ) : (
            <Card className="p-8 text-center">
              <p className="text-muted-foreground">No activities found matching your filters.</p>
            </Card>
          )}
        </div>
      ) : (
        // Desktop Activity Table
        <Card>
          <ScrollArea className="max-h-[calc(100vh-280px)]">
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Action</TableHead>
                    <TableHead>Admin</TableHead>
                    <TableHead>Target</TableHead>
                    <TableHead>Details</TableHead>
                    <TableHead>Timestamp</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {isLoading ? (
                    // Loading skeleton rows
                    Array(6).fill(0).map((_, i) => (
                      <TableRow key={`skeleton-${i}`}>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            <Skeleton className="h-4 w-4 rounded-full" />
                            <Skeleton className="h-5 w-24" />
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            <Skeleton className="h-8 w-8 rounded-full" />
                            <Skeleton className="h-4 w-16" />
                          </div>
                        </TableCell>
                        <TableCell><Skeleton className="h-4 w-32" /></TableCell>
                        <TableCell><Skeleton className="h-4 w-full max-w-[300px]" /></TableCell>
                        <TableCell><Skeleton className="h-4 w-32" /></TableCell>
                      </TableRow>
                    ))
                  ) : filteredActivities.length > 0 ? (
                    filteredActivities.map((activity) => (
                      <TableRow key={activity.id}>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            {getActionIcon(activity.action)}
                            {getActionBadge(activity.action)}
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            <Avatar className="h-8 w-8">
                              <AvatarImage src={activity.admin.avatar} />
                              <AvatarFallback>{activity.admin.name.charAt(0)}</AvatarFallback>
                            </Avatar>
                            <span>{activity.admin.name}</span>
                          </div>
                        </TableCell>
                        <TableCell>{activity.target}</TableCell>
                        <TableCell>
                          <div className="max-w-[300px] truncate" title={activity.details}>
                            {activity.details}
                          </div>
                        </TableCell>
                        <TableCell>{formatTimestamp(activity.timestamp)}</TableCell>
                      </TableRow>
                    ))
                  ) : (
                    <TableRow>
                      <TableCell colSpan={5} className="text-center py-8">
                        No activities found matching your filters.
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </div>
          </ScrollArea>
        </Card>
      )}
    </div>
  );
};
