
import React, { useState } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { cn } from '@/lib/utils';
import { useIsMobile } from '@/hooks/use-mobile';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/features/auth/hooks/useAuth';
import { motion } from 'framer-motion';
import {
  LayoutDashboard,
  Users,
  Briefcase,
  Settings,
  MessagesSquare,
  UserCircle,
  CreditCard,
  Star,
  Gift,
  Share2,
  Mail,
  LogOut,
  Activity,
  Calendar,
  Building,
  ShieldAlert,
  ShieldCheck,
  Crown,
  FileText
} from "lucide-react";
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';

// Animation variants
const sidebarVariants = {
  open: {
    width: "15rem",
  },
  closed: {
    width: "3.5rem",
  },
};

const contentVariants = {
  open: { 
    opacity: 1,
    x: 0,
    transition: { 
      opacity: { duration: 0.2 },
      x: { duration: 0.3 }
    }
  },
  closed: { 
    opacity: 0,
    x: -10,
    transition: { 
      opacity: { duration: 0.2 },
      x: { duration: 0.3 }
    }
  },
};

const transitionProps = {
  type: "tween",
  ease: "easeInOut",
  duration: 0.3,
};

// Define the navigation items to display
const navigationItems = [
  {
    name: "Dashboard",
    path: "/admin",
    icon: LayoutDashboard
  },
  {
    name: "Job Oversight",
    path: "/admin/job-oversight",
    icon: ShieldAlert,
    badge: "New"
  },
  {
    name: "Job Booking",
    path: "/admin/bookings",
    icon: Calendar
  },
  {
    name: "Manage Certificates",
    path: "/admin/certificates",
    icon: ShieldCheck
  },
  {
    name: "Business",
    path: "/admin/business",
    icon: Building
  },
  {
    name: "Providers",
    path: "/admin/providers",
    icon: Users
  },
  {
    name: "Provider Invitations",
    path: "/admin/provider-invitations",
    icon: Mail
  },
  {
    name: "Provider Tier",
    path: "/admin/provider-tier",
    icon: Crown
  },
  {
    name: "Customers",
    path: "/admin/customers",
    icon: UserCircle
  },
  {
    name: "Jobs",
    path: "/admin/jobs",
    icon: Briefcase
  },
  {
    name: "Bids Dashboard",
    path: "/admin/dashboard/bids",
    icon: FileText
  },
  {
    name: "Payments",
    path: "/admin/payments",
    icon: CreditCard
  },
  {
    name: "Reviews",
    path: "/admin/reviews",
    icon: Star
  },
  {
    name: "Rewards",
    path: "/admin/rewards",
    icon: Gift
  },
  {
    name: "Referrals",
    path: "/admin/referrals",
    icon: Share2
  },
  {
    name: "Email Tracking",
    path: "/admin/email-tracking",
    icon: Activity,
    badge: "New"
  },
  {
    name: "Messages",
    path: "/admin/messages",
    icon: MessagesSquare
  },
  {
    name: "Settings",
    path: "/admin/settings",
    icon: Settings
  },
];

export const AdminSidebar = () => {
  const location = useLocation();
  const path = location.pathname;
  const isMobile = useIsMobile();
  const { toast } = useToast();
  const { logout } = useAuth();
  const [isCollapsed, setIsCollapsed] = useState(true);

  const handleLogout = () => {
    logout(); // Handle logout
    toast({
      title: "Logged out successfully",
      description: "You have been logged out of your account."
    });
  };

  // On mobile, use a vertical stacked menu
  if (isMobile) {
    return (
      <div className="flex flex-col w-full space-y-1">
        {navigationItems.map(item => (
          <Link
            key={item.path}
            to={item.path}
            className={cn(
              "flex items-center px-4 py-3 rounded-md transition-colors",
              path === item.path 
                ? "bg-primary/10 text-primary" 
                : "text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-800"
            )}
          >
            <item.icon className="h-5 w-5 mr-3" />
            <span>{item.name}</span>
            {item.badge && (
              <Badge 
                variant="outline" 
                className="ml-auto bg-purple-100 text-purple-800 border-purple-200"
              >
                {item.badge}
              </Badge>
            )}
          </Link>
        ))}

        <Separator className="my-2" />

        <Button 
          variant="ghost" 
          className="flex items-center justify-start text-left text-red-500 hover:bg-red-50 hover:text-red-600 dark:hover:bg-red-900/10 px-4 py-3"
          onClick={handleLogout}
        >
          <LogOut className="h-5 w-5 mr-3" />
          <span>Log Out</span>
        </Button>
      </div>
    );
  }

  // On desktop, use the sidebar with icons and hover effect
  return (
    <motion.div
      className="bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700 h-screen fixed left-0 top-0 z-30 flex flex-col"
      initial="closed"
      animate={isCollapsed ? "closed" : "open"}
      variants={sidebarVariants}
      transition={transitionProps}
      onMouseEnter={() => setIsCollapsed(false)}
      onMouseLeave={() => setIsCollapsed(true)}
    >
      <div className="flex flex-col items-center pt-6 pb-4">
        <Avatar className="h-8 w-8">
          <AvatarImage src="/images/avatar-default.svg" alt="Admin" />
          <AvatarFallback>AD</AvatarFallback>
        </Avatar>
      </div>

      <Separator />

      <nav className="flex-1 overflow-y-auto py-4">
        <div className="px-2 space-y-1">
          {navigationItems.map((item, index) => (
            <Link
              key={item.path}
              to={item.path}
              className={cn(
                "flex items-center p-2 rounded-md transition-colors relative",
                isCollapsed ? "justify-center" : "justify-start",
                path === item.path 
                  ? "bg-primary/10 text-primary" 
                  : "text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-800"
              )}
            >
              <div className="flex-shrink-0 w-5 flex justify-center">
                <item.icon className="h-5 w-5" />
              </div>

              {!isCollapsed && (
                <motion.span
                  variants={contentVariants}
                  transition={{
                    delay: index * 0.03, // Sequential delay based on item index
                    ...transitionProps
                  }}
                  className="ml-3 text-sm font-medium overflow-hidden whitespace-nowrap"
                >
                  {item.name}
                </motion.span>
              )}

              {item.badge && (
                <>
                  {isCollapsed ? (
                    <span className="absolute top-0 right-0 w-2 h-2 bg-purple-500 rounded-full"></span>
                  ) : (
                    <motion.div 
                      variants={contentVariants}
                      transition={{
                        delay: index * 0.03, // Match the delay of the text
                        ...transitionProps
                      }}
                      className="ml-auto"
                    >
                      <Badge 
                        variant="outline" 
                        className="bg-purple-100 text-purple-800 border-purple-200"
                      >
                        {item.badge}
                      </Badge>
                    </motion.div>
                  )}
                </>
              )}
            </Link>
          ))}
        </div>
        <div className="p-2 mt-auto">
          <Button
              variant="ghost"
              className={cn(
                  "flex text-red-500 hover:bg-red-50 hover:text-red-600 dark:hover:bg-red-900/10",
                  isCollapsed ? "w-full justify-center aspect-square" : "w-full justify-start px-2 py-2"
              )}
              onClick={handleLogout}
          >
            <div className="flex-shrink-0 w-5 flex justify-center">
              <LogOut className="h-5 w-5" />
            </div>
            {!isCollapsed && (
                <motion.span
                    variants={contentVariants}
                    transition={{
                      delay: navigationItems.length * 0.03, // Delay after all navigation items
                      ...transitionProps
                    }}
                    className="ml-3 text-sm font-medium overflow-hidden"
                >
                  Log Out
                </motion.span>
            )}
          </Button>
        </div>
      </nav>
    </motion.div>
  );
};
