import { useState, useEffect } from "react";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { AdminNotes } from "@/components/admin/shared/AdminNotes";
import { AdminMessageThread } from "../shared/AdminMessageThread";
import { AdminMessageComposer } from "../shared/AdminMessageComposer";
import {
  MessageSquare,
  Mail,
  Briefcase,
  MapPin,
  Star,
  Calendar,
  Check,
  UserX,
  CreditCard,
  FileText,
  X
} from "lucide-react";
import { JobBookingsList } from "../job-bookings/JobBookingsList";
import { JobBooking, jobBookingService } from "@/services/jobBookingService";
import { useAuth } from "@/features/auth/hooks/useAuth";
import { useToast } from "@/hooks/use-toast";
import { useIsMobile } from "@/hooks/use-mobile";
import { MobileProviderDetailCard } from "./MobileProviderDetailCard";

interface Provider {
  id: string | number;
  avatar: string;
  name: string;
  status: "Active" | "Suspended" | "Pending" | string;
  plan: "Elite" | "Pro" | "Starter" | string;
  email: string;
  specialty: string;
  location: string;
  rating: number | string;
}

interface ProviderProfileDetailProps {
  provider: Provider;
  onClose: () => void;
  onSuspend: (provider: Provider) => void;
  onOpenMessageComposer?: (provider: Provider) => void;
}

export const ProviderProfileDetail = ({
  provider,
  onClose,
  onSuspend,
  onOpenMessageComposer
}: ProviderProfileDetailProps) => {
  const { toast } = useToast();
  const { token } = useAuth(); // Changed from auth to token
  const [activeTab, setActiveTab] = useState("overview");
  const isMobile = useIsMobile();
  const [isMessageComposerOpen, setIsMessageComposerOpen] = useState(false);
  const [bookings, setBookings] = useState<JobBooking[]>([]);
  const [isLoadingBookings, setIsLoadingBookings] = useState(false);

  const mockData = {
    providerDetails: {
      id: provider.id,
      name: provider.name,
      email: provider.email,
      avatar: provider.avatar,
      dateJoined: "2024-12-15",
      specialty: provider.specialty,
      location: provider.location,
      status: provider.status,
      plan: provider.plan,
      rating: provider.rating,
    },
    adminNotes: [
      {
        text: "Verified business license and insurance documentation.",
        timestamp: "2025-04-15T14:30:00Z",
        admin: "Admin A",
      },
      {
        text: "Provider requested upgrade to Pro plan next month.",
        timestamp: "2025-04-10T11:15:00Z",
        admin: "Admin B",
      },
      {
        text: "Reached out about improving response rate to job requests.",
        timestamp: "2025-04-05T09:45:00Z",
        admin: "Admin C",
      },
    ],
    messages: [
      {
        customerId: "CUS-001",
        customerName: "John Doe",
        jobTitle: "Bathroom Sink Repair",
        lastMessage: "I'll be there at 3pm tomorrow as agreed.",
        timestamp: "2025-04-20T15:23:00Z",
        unread: false,
      },
      {
        customerId: "CUS-003",
        customerName: "Sarah Johnson",
        jobTitle: "Kitchen Faucet Installation",
        lastMessage: "Can you recommend a good brand for the new faucet?",
        timestamp: "2025-04-19T10:05:00Z",
        unread: true,
      },
      {
        customerId: "CUS-007",
        customerName: "Michael Brown",
        jobTitle: "Water Heater Replacement",
        lastMessage: "Thanks for the quick service! Very professional.",
        timestamp: "2025-04-15T16:30:00Z",
        unread: false,
      },
    ],
    payouts: [
      {
        id: "PAY-001",
        amount: 250.00,
        status: "Paid",
        date: "2025-04-10",
        jobId: "JOB-0023",
      },
      {
        id: "PAY-002",
        amount: 175.50,
        status: "Paid",
        date: "2025-03-27",
        jobId: "JOB-0019",
      },
      {
        id: "PAY-003",
        amount: 320.00,
        status: "Pending",
        date: "2025-04-21",
        jobId: "JOB-0028",
      },
    ],
    subscriptionHistory: [
      {
        plan: provider.plan,
        startDate: "2024-12-15",
        renewalDate: "2025-05-15",
        status: "Active",
      },
      {
        plan: "Starter",
        startDate: "2024-10-05",
        endDate: "2024-12-14",
        status: "Upgraded",
        note: "Upgraded to Pro plan",
      },
    ],
    performance: {
      jobsBidOn: 23,
      jobsAwarded: 15,
      jobsCompleted: 12,
      completionRate: "80%",
      averageResponseTime: "2.5 hours",
      customerSatisfaction: "92%",
    },
    reviews: [
      {
        customerName: "Emma Wilson",
        rating: 5,
        text: "Mike was punctual, professional, and fixed our sink issue quickly. Highly recommend!",
        date: "2025-04-02",
      },
      {
        customerName: "David Thompson",
        rating: 4,
        text: "Good service overall. Fixed the problem effectively but left some minor mess.",
        date: "2025-03-18",
      },
      {
        customerName: "Jessica Martinez",
        rating: 5,
        text: "Excellent job replacing our water heater. Fair price and clean work.",
        date: "2025-03-05",
      },
    ],
  };

  const handleAddNote = (note: string) => {
    toast({
      title: "Admin note added",
      description: "The note has been added successfully.",
    });
  };

  const getStatusBadge = (status: string) => {
    switch(status) {
      case "Active":
        return <Badge variant="success">{status}</Badge>;
      case "Suspended":
        return <Badge variant="destructive">{status}</Badge>;
      case "Pending":
        return <Badge variant="warning">{status}</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const getPlanBadge = (plan: string) => {
    switch(plan) {
      case "Elite":
        return <Badge className="bg-purple-500">{plan}</Badge>;
      case "Pro":
        return <Badge variant="business">{plan}</Badge>;
      case "Starter":
        return <Badge variant="secondary">{plan}</Badge>;
      default:
        return <Badge variant="outline">{plan}</Badge>;
    }
  };

  const getPayoutStatusBadge = (status: string) => {
    switch(status) {
      case "Paid":
        return <Badge variant="success">{status}</Badge>;
      case "Pending":
        return <Badge variant="warning">{status}</Badge>;
      case "Failed":
        return <Badge variant="destructive">{status}</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const mockAdminMessages = [
    {
      id: "msg-admin-p1",
      adminName: "Admin Team",
      adminAvatar: "",
      text: "Hello, we'd like to verify your business license and insurance documents. Can you please upload them in your profile?",
      timestamp: "2025-03-10T09:30:00Z",
      isFromAdmin: true,
    },
    {
      id: "msg-provider-p1",
      adminName: "",
      adminAvatar: "",
      text: "I've just uploaded my business license and insurance documents. Let me know if you need anything else!",
      timestamp: "2025-03-10T11:20:00Z",
      isFromAdmin: false,
    },
    {
      id: "msg-admin-p2",
      adminName: "Verification Team",
      adminAvatar: "",
      text: "Thank you! We've reviewed your documents and everything looks good. Your account has been verified.",
      timestamp: "2025-03-11T14:15:00Z",
      isFromAdmin: true,
    }
  ];

  const handleMessageComposerOpen = () => {
    setIsMessageComposerOpen(true);
  };

  // Fetch job bookings when the tab is selected
  useEffect(() => {
    if (activeTab === "bookings" && provider?.id) {
      fetchProviderBookings();
    }
  }, [activeTab, provider?.id]);

  const fetchProviderBookings = async () => {
    setIsLoadingBookings(true);
    if (!token) {
      toast({
        title: "Authentication Error",
        description: "Not authenticated. Please log in.",
        variant: "destructive",
      });
      setIsLoadingBookings(false);
      return;
    }
    try {
      // First, get all job bookings associated with this provider
      const response = await jobBookingService.getJobBookingsByUserId(
        String(provider.id),
        1,
        10,
        token // token is now narrowed to string
      );

      if (response.isSuccess && response.data) {
        setBookings(response.data.data);
      } else {
        toast({
          title: "Error",
          description: response.error || "Failed to fetch job bookings",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error("Error fetching job bookings:", error);
      toast({
        title: "Error",
        description: "An unexpected error occurred",
        variant: "destructive",
      });
    } finally {
      setIsLoadingBookings(false);
    }
  };

  if (isMobile) {
    return (
      <MobileProviderDetailCard
        provider={provider}
        onClose={onClose}
        onSuspend={onSuspend}
        onOpenMessageComposer={handleMessageComposerOpen}
      />
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row gap-6 items-start">
        <Avatar className="h-24 w-24">
          <AvatarImage src={provider.avatar} alt={provider.name} />
          <AvatarFallback className="text-2xl">{provider.name.charAt(0)}{provider.name.split(' ')[1]?.charAt(0)}</AvatarFallback>
        </Avatar>

        <div className="space-y-2 flex-1">
          <div className="flex flex-wrap items-center gap-2">
            <h2 className="text-2xl font-bold">{provider.name}</h2>
            {getStatusBadge(provider.status)}
            {getPlanBadge(provider.plan)}
          </div>

          <div className="flex items-center gap-2 text-muted-foreground">
            <Mail className="h-4 w-4" />
            <span>{provider.email}</span>
          </div>

          <div className="flex flex-wrap gap-6 mt-2">
            <div className="flex items-center gap-2">
              <Briefcase className="h-4 w-4 text-muted-foreground" />
              <span>{provider.specialty}</span>
            </div>
            <div className="flex items-center gap-2">
              <MapPin className="h-4 w-4 text-muted-foreground" />
              <span>{provider.location}</span>
            </div>
            <div className="flex items-center gap-2">
              <Star className="h-4 w-4 text-amber-500 fill-amber-500" />
              <span>{provider.rating}</span>
            </div>
            <div className="flex items-center gap-2">
              <Calendar className="h-4 w-4 text-muted-foreground" />
              <span>Joined {mockData.providerDetails.dateJoined}</span>
            </div>
          </div>

          <div className="pt-3 flex flex-wrap gap-2">
            {provider.status === "Suspended" ? (
              <Button
                variant="success"
                onClick={() => onSuspend(provider)}
                className="mt-2"
              >
                <Check className="mr-2 h-4 w-4" /> Reactivate Provider
              </Button>
            ) : (
              <Button
                variant="destructive"
                onClick={() => onSuspend(provider)}
                className="mt-2"
              >
                <UserX className="mr-2 h-4 w-4" /> Suspend Provider
              </Button>
            )}
            <Button variant="secondary" className="mt-2">
              <CreditCard className="mr-2 h-4 w-4" /> Update Plan
            </Button>
            <Button
              variant="secondary"
              onClick={handleMessageComposerOpen}
              className="mt-2"
            >
              <MessageSquare className="mr-2 h-4 w-4" /> Message
            </Button>
          </div>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="mt-6">
        <TabsList className="grid w-full grid-cols-2 md:grid-cols-7 lg:w-auto">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="bookings">Bookings</TabsTrigger>
          <TabsTrigger value="messages">Messages</TabsTrigger>
          <TabsTrigger value="payouts">Payouts</TabsTrigger>
          <TabsTrigger value="reviews">Reviews</TabsTrigger>
          <TabsTrigger value="notes">Admin Notes</TabsTrigger>
          <TabsTrigger value="adminMessages">Admin Messages</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-lg">
                  <CreditCard className="h-5 w-5" /> Subscription Plan
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <div>
                      <p className="font-medium">Current Plan</p>
                      <p className="flex items-center gap-2">
                        {getPlanBadge(provider.plan)}
                        <span className="text-muted-foreground text-sm">
                          Renews {mockData.subscriptionHistory[0].renewalDate}
                        </span>
                      </p>
                    </div>
                  </div>
                  <div>
                    <p className="font-medium mb-1">Plan History</p>
                    <div className="space-y-2">
                      {mockData.subscriptionHistory.map((sub, idx) => (
                        <div key={idx} className="text-sm border-l-2 border-gray-200 pl-3 py-1">
                          <div className="flex items-center gap-2">
                            {getPlanBadge(sub.plan)}
                            <span className="text-muted-foreground">{sub.startDate} to {sub.endDate || "Present"}</span>
                          </div>
                          {sub.note && <p className="text-xs text-muted-foreground mt-1">{sub.note}</p>}
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-lg">
                  <Briefcase className="h-5 w-5" /> Job Performance
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <p className="text-muted-foreground text-sm">Jobs Bid On</p>
                    <p className="text-xl font-semibold">{mockData.performance.jobsBidOn}</p>
                  </div>
                  <div>
                    <p className="text-muted-foreground text-sm">Jobs Awarded</p>
                    <p className="text-xl font-semibold">{mockData.performance.jobsAwarded}</p>
                  </div>
                  <div>
                    <p className="text-muted-foreground text-sm">Jobs Completed</p>
                    <p className="text-xl font-semibold">{mockData.performance.jobsCompleted}</p>
                  </div>
                  <div>
                    <p className="text-muted-foreground text-sm">Completion Rate</p>
                    <p className="text-xl font-semibold">{mockData.performance.completionRate}</p>
                  </div>
                  <div>
                    <p className="text-muted-foreground text-sm">Avg Response Time</p>
                    <p className="text-xl font-semibold">{mockData.performance.averageResponseTime}</p>
                  </div>
                  <div>
                    <p className="text-muted-foreground text-sm">Customer Satisfaction</p>
                    <p className="text-xl font-semibold">{mockData.performance.customerSatisfaction}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between">
                <CardTitle className="flex items-center gap-2 text-lg">
                  <Star className="h-5 w-5" /> Recent Reviews
                </CardTitle>
                <Button variant="link" onClick={() => setActiveTab("reviews")}>View All</Button>
              </CardHeader>
              <CardContent>
                {mockData.reviews.slice(0, 2).map((review, idx) => (
                  <div key={idx} className={`${idx > 0 ? "border-t pt-3 mt-3" : ""}`}>
                    <div className="flex justify-between">
                      <p className="font-medium">{review.customerName}</p>
                      <div className="flex items-center">
                        {Array.from({ length: 5 }).map((_, i) => (
                          <Star
                            key={i}
                            className={`h-4 w-4 ${i < review.rating ? "text-amber-500 fill-amber-500" : "text-gray-300"}`}
                          />
                        ))}
                      </div>
                    </div>
                    <p className="text-sm mt-1 text-gray-700 dark:text-gray-300">{review.text}</p>
                    <p className="text-xs text-muted-foreground mt-1">{review.date}</p>
                  </div>
                ))}
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between">
                <CardTitle className="flex items-center gap-2 text-lg">
                  <MessageSquare className="h-5 w-5" /> Recent Messages
                </CardTitle>
                <Button variant="link" onClick={() => setActiveTab("messages")}>View All</Button>
              </CardHeader>
              <CardContent>
                {mockData.messages.slice(0, 3).map((message, idx) => (
                  <div key={idx} className={`${idx > 0 ? "border-t pt-3 mt-3" : ""}`}>
                    <div className="flex justify-between">
                      <p className="font-medium">{message.customerName}</p>
                      <p className="text-xs text-muted-foreground">
                        {new Date(message.timestamp).toLocaleDateString()}
                      </p>
                    </div>
                    <p className="text-xs text-muted-foreground">{message.jobTitle}</p>
                    <p className="text-sm mt-1 truncate">{message.lastMessage}</p>
                  </div>
                ))}
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="bookings">
          <Card>
            <CardHeader>
              <CardTitle>Job Bookings</CardTitle>
            </CardHeader>
            <CardContent>
              <JobBookingsList bookings={bookings} isLoading={isLoadingBookings} />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="messages" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Customer Conversations</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="divide-y">
                {mockData.messages.map((message, idx) => (
                  <div key={idx} className="py-4 first:pt-0 last:pb-0">
                    <div className="flex justify-between items-start">
                      <div>
                        <div className="flex items-center gap-2">
                          <span className="font-medium">{message.customerName}</span>
                          {message.unread && <Badge variant="warning" className="text-xs">New</Badge>}
                        </div>
                        <p className="text-sm text-muted-foreground">{message.jobTitle}</p>
                      </div>
                      <p className="text-xs text-muted-foreground">
                        {new Date(message.timestamp).toLocaleString()}
                      </p>
                    </div>
                    <p className="mt-2 text-sm">{message.lastMessage}</p>
                    <div className="mt-3">
                      <Button variant="outline" size="sm">
                        <MessageSquare className="h-4 w-4 mr-2" /> View Conversation
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="payouts" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Payout Transactions</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead>
                    <tr className="border-b">
                      <th className="text-left pb-3 font-medium">Payout ID</th>
                      <th className="text-left pb-3 font-medium">Date</th>
                      <th className="text-left pb-3 font-medium">Amount</th>
                      <th className="text-left pb-3 font-medium">Job ID</th>
                      <th className="text-left pb-3 font-medium">Status</th>
                      <th className="text-right pb-3 font-medium">Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {mockData.payouts.map((payout, idx) => (
                      <tr key={idx} className="border-b last:border-0">
                        <td className="py-3">{payout.id}</td>
                        <td className="py-3">{payout.date}</td>
                        <td className="py-3">${payout.amount.toFixed(2)}</td>
                        <td className="py-3">{payout.jobId}</td>
                        <td className="py-3">{getPayoutStatusBadge(payout.status)}</td>
                        <td className="py-3 text-right">
                          <Button variant="outline" size="sm">
                            <FileText className="h-4 w-4 mr-2" /> Details
                          </Button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="reviews" className="space-y-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between">
              <CardTitle>Customer Reviews</CardTitle>
              <div className="flex items-center gap-1">
                <Star className="h-5 w-5 fill-amber-500 text-amber-500" />
                <span className="font-bold">{provider.rating}</span>
                <span className="text-muted-foreground text-sm">({mockData.reviews.length} reviews)</span>
              </div>
            </CardHeader>
            <CardContent>
              <div className="divide-y">
                {mockData.reviews.map((review, idx) => (
                  <div key={idx} className="py-4 first:pt-0 last:pb-0">
                    <div className="flex justify-between items-start">
                      <span className="font-medium">{review.customerName}</span>
                      <div className="flex items-center">
                        {Array.from({ length: 5 }).map((_, i) => (
                          <Star
                            key={i}
                            className={`h-4 w-4 ${i < review.rating ? "text-amber-500 fill-amber-500" : "text-gray-300"}`}
                          />
                        ))}
                      </div>
                    </div>
                    <p className="text-xs text-muted-foreground mb-2">{review.date}</p>
                    <p className="text-sm">{review.text}</p>
                    <div className="mt-3">
                      <Button variant="outline" size="sm" className="text-red-500 hover:text-red-700 hover:bg-red-50">
                        <X className="h-4 w-4 mr-2" /> Remove Review
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="notes">
          <Card>
            <CardHeader>
              <CardTitle>Admin Notes</CardTitle>
            </CardHeader>
            <CardContent>
              <AdminNotes
                notes={mockData.adminNotes}
                onAddNote={handleAddNote}
                entityType="Provider"
                entityId={String(provider.id)}
              />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="adminMessages">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between">
              <CardTitle>Admin Communication</CardTitle>
              <Button size="sm" onClick={handleMessageComposerOpen}>
                <MessageSquare className="mr-2 h-4 w-4" /> New Message
              </Button>
            </CardHeader>
            <CardContent>
              <AdminMessageThread
                messages={mockAdminMessages}
                entityName={provider.name}
              />
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Message Composer */}
      <AdminMessageComposer
        open={isMessageComposerOpen}
        onClose={() => setIsMessageComposerOpen(false)}
        recipientType="provider"
        recipient={{
          id: String(provider.id),
          name: provider.name,
          email: provider.email
        }}
      />
    </div>
  );
};
