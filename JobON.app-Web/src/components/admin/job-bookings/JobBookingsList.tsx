import React from 'react';
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { JobBooking } from '@/services/jobBookingService';
import { JobBookingStatusBadge } from './JobBookingStatusBadge';
import { Calendar, MapPin } from 'lucide-react';
import { Skeleton } from '@/components/ui/skeleton';
import { formatDate } from '@/lib/utils';

interface JobBookingsListProps {
  bookings: JobBooking[];
  isLoading: boolean;
}

export const JobBookingsList: React.FC<JobBookingsListProps> = ({
  bookings,
  isLoading,
}) => {
  // Format date for display
  const formatScheduleDate = (date: string) => {
    try {
      return formatDate(date);
    } catch (error) {
      return date;
    }
  };

  if (isLoading) {
    return (
      <div className="space-y-4">
        {Array(3).fill(0).map((_, i) => (
          <Card key={`skeleton-${i}`}>
            <CardContent className="p-4">
              <div className="flex justify-between items-start">
                <div className="space-y-2">
                  <Skeleton className="h-5 w-32" />
                  <Skeleton className="h-4 w-24" />
                  <Skeleton className="h-4 w-40" />
                </div>
                <Skeleton className="h-6 w-20" />
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  if (bookings.length === 0) {
    return (
      <Card>
        <CardContent className="p-4 text-center py-8">
          No job bookings found
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      {bookings.map((booking) => (
        <Card key={booking.jobId} className="overflow-hidden border border-slate-200 dark:border-slate-800 shadow-sm hover:shadow-md transition-all duration-300">
          <CardContent className="p-4">
            <div className="flex justify-between items-start">
              <div>
                <h3 className="font-medium capitalize">{booking.service.category}</h3>
                <p className="text-sm text-muted-foreground flex items-center gap-1 mt-1">
                  <Calendar className="h-3.5 w-3.5 text-emerald-500" />
                  {formatScheduleDate(booking.schedule.date)}
                  <span className="text-xs ml-1 capitalize">({booking.schedule.timePreference})</span>
                </p>
                <p className="text-sm text-muted-foreground flex items-center gap-1 mt-1">
                  <MapPin className="h-3.5 w-3.5 text-blue-500" />
                  {booking.location.city}, {booking.location.state}
                </p>
                <p className="text-sm text-muted-foreground mt-1">ID: {booking.projectCode}</p>
                {booking.service.tasks.length > 0 && (
                  <div className="mt-2 flex flex-wrap gap-1">
                    {booking.service.tasks.map((task, index) => (
                      <Badge key={index} variant="outline" className="bg-slate-50 text-slate-700">
                        {task}
                      </Badge>
                    ))}
                  </div>
                )}
              </div>
              <JobBookingStatusBadge status={booking.status} />
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
};
