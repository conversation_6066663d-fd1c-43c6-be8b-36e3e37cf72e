import React, { useState } from 'react';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Pagination } from "@/components/ui/pagination";
import { JobBookingStatusBadge } from './JobBookingStatusBadge';
import { JobBooking, JobBookingResponse } from '@/services/jobBookingService';
import { Skeleton } from '@/components/ui/skeleton';
import { Search, Calendar, MapPin } from 'lucide-react';
import { formatDate } from '@/lib/utils';

interface JobBookingsTableProps {
  bookings: JobBooking[];
  isLoading: boolean;
  pagination: {
    current_page: number;
    per_page: number;
    total: number;
    last_page: number;
  };
  onPageChange: (page: number) => void;
  onSearch: (query: string) => void;
}

export const JobBookingsTable: React.FC<JobBookingsTableProps> = ({
  bookings,
  isLoading,
  pagination,
  onPageChange,
  onSearch,
}) => {
  const [searchQuery, setSearchQuery] = useState('');

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    onSearch(searchQuery);
  };

  // Helper function to get initials from name
  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(part => part[0])
      .join('')
      .toUpperCase()
      .substring(0, 2);
  };

  // Format date for display
  const formatScheduleDate = (date: string) => {
    try {
      return formatDate(date);
    } catch (error) {
      return date;
    }
  };

  return (
    <>
      <Card>
        <CardHeader className="flex flex-col sm:flex-row items-start sm:items-center justify-between space-y-2 sm:space-y-0 pb-4">
          <CardTitle>Job Bookings</CardTitle>
          <form onSubmit={handleSearch} className="flex w-full sm:w-auto">
            <Input
              placeholder="Search bookings..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="mr-2"
            />
            <Button type="submit" size="sm">
              <Search className="h-4 w-4 mr-2" />
              Search
            </Button>
          </form>
        </CardHeader>
        <CardContent>
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Project Code</TableHead>
                  <TableHead>Service</TableHead>
                  <TableHead>Contact</TableHead>
                  <TableHead>Location</TableHead>
                  <TableHead>Schedule</TableHead>
                  <TableHead>Status</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {isLoading ? (
                  // Loading skeletons
                  Array(5).fill(0).map((_, i) => (
                    <TableRow key={`skeleton-${i}`}>
                      <TableCell><Skeleton className="h-4 w-24" /></TableCell>
                      <TableCell><Skeleton className="h-4 w-32" /></TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <Skeleton className="h-8 w-8 rounded-full" />
                          <div className="space-y-1">
                            <Skeleton className="h-4 w-24" />
                            <Skeleton className="h-3 w-32" />
                          </div>
                        </div>
                      </TableCell>
                      <TableCell><Skeleton className="h-4 w-24" /></TableCell>
                      <TableCell><Skeleton className="h-4 w-24" /></TableCell>
                      <TableCell><Skeleton className="h-6 w-20" /></TableCell>
                    </TableRow>
                  ))
                ) : bookings.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={6} className="text-center py-8">
                      No job bookings found
                    </TableCell>
                  </TableRow>
                ) : (
                  bookings.map((booking) => (
                    <TableRow key={booking.jobId}>
                      <TableCell className="font-medium">{booking.projectCode}</TableCell>
                      <TableCell>
                        <div>
                          <div className="font-medium capitalize">{booking.service.category}</div>
                          <div className="text-sm text-muted-foreground">
                            {booking.service.tasks.join(', ')}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <Avatar className="h-8 w-8">
                            <AvatarFallback>{getInitials(booking.contact.fullName)}</AvatarFallback>
                          </Avatar>
                          <div>
                            <div className="font-medium">{booking.contact.fullName}</div>
                            <div className="text-sm text-muted-foreground">{booking.contact.email}</div>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-1">
                          <MapPin className="h-4 w-4 text-muted-foreground" />
                          <span>{booking.location.city}, {booking.location.state}</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-1">
                          <Calendar className="h-4 w-4 text-muted-foreground" />
                          <span>{formatScheduleDate(booking.schedule.date)}</span>
                          <span className="text-xs text-muted-foreground ml-1 capitalize">
                            ({booking.schedule.timePreference})
                          </span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <JobBookingStatusBadge status={booking.status} />
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>
      
      {pagination.total > 0 && !isLoading && (
        <div className="mt-4">
          <Pagination
            totalItems={pagination.total}
            itemsPerPage={pagination.per_page}
            currentPage={pagination.current_page}
            onPageChange={onPageChange}
          />
        </div>
      )}
    </>
  );
};
