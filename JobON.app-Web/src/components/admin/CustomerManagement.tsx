
import { useState } from "react";
import { CustomersTable } from "./CustomersTable";
import { AdminMessageComposer } from "./shared/AdminMessageComposer";
import { useUIHelpers } from "@/hooks/use-ui-helpers";
import { Customer } from "@/services/customerService";

export const CustomerManagement = () => {
  const [showMessageComposer, setShowMessageComposer] = useState(false);
  const [selectedCustomer, setSelectedCustomer] = useState<Customer | null>(null);
  const { isMobile } = useUIHelpers();

  // This function will be passed to CustomersTable to handle opening the message composer
  const handleOpenMessageComposer = (customer: Customer) => {
    setSelectedCustomer(customer);
    setShowMessageComposer(true);
  };

  return (
    <>
      <CustomersTable
        onOpenMessageComposer={handleOpenMessageComposer}
        isMobile={isMobile}
      />

      {selectedCustomer && (
        <AdminMessageComposer
          open={showMessageComposer}
          onClose={() => setShowMessageComposer(false)}
          recipientType="customer"
          recipient={{
            id: selectedCustomer.id,
            name: selectedCustomer.name,
            email: selectedCustomer.email,
          }}
        />
      )}
    </>
  );
};
