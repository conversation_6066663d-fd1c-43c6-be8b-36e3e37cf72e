import React, { useState, useEffect } from 'react';
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useToast } from "@/hooks/use-toast";
import { useAuth } from "@/features/auth/hooks/useAuth";
import { providerService, Provider } from "@/services/providerService";
import { planService } from "@/services/planService";
import { ProviderTierManagementFormValues, providerTierManagementSchema, ProviderPlan } from "./schemas";
import { nullToUndefined } from "@/utils/typeHelpers";

import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Loader2 } from "lucide-react";



interface ProviderTierManagementFormProps {
  onSuccess?: () => void;
  selectedProvider?: Provider;
}

export const ProviderTierManagementForm = ({ onSuccess, selectedProvider }: ProviderTierManagementFormProps) => {
  const { toast } = useToast();
  const { token } = useAuth();
  const [plans, setPlans] = useState<ProviderPlan[]>([]);
  const [isLoadingPlans, setIsLoadingPlans] = useState(false);
  const form = useForm<ProviderTierManagementFormValues>({
    resolver: zodResolver(providerTierManagementSchema),
    defaultValues: {
      providerId: selectedProvider ? String(selectedProvider.id) : "",
      planId: "",
      duration: "monthly",
      notes: "",
    },
  });

  const { formState: { isSubmitting } } = form;



  // Fetch plans when component mounts
  useEffect(() => {
    const fetchPlans = async () => {
      setIsLoadingPlans(true);
      
      try {
        const plansResponse = await planService.getPlans(1, 100, nullToUndefined(token));
        if (plansResponse.success && plansResponse.data) {
          setPlans(plansResponse.data.data);
        } else {
          toast({
            title: "Error fetching plans",
            description: plansResponse.message || "Failed to load subscription plans",
            variant: "destructive",
          });
        }
      } catch (error) {
        toast({
          title: "Error",
          description: "Failed to load plans. Please try again.",
          variant: "destructive",
        });
      } finally {
        setIsLoadingPlans(false);
      }
    };

    fetchPlans();
  }, [token, toast]);

  // Set provider ID when selectedProvider prop changes
  useEffect(() => {
    if (selectedProvider && selectedProvider.current_subscription?.plan?.id) {
      form.setValue("providerId", String(selectedProvider.id));
      form.setValue("planId", String(selectedProvider.current_subscription.plan.id));
    }
  }, [selectedProvider, form, plans]);

  const onSubmit = async (data: ProviderTierManagementFormValues) => {
    try {
      const response = await planService.assignPlanToProvider(
        data.providerId,
        data.planId,
        data.notes || undefined,
        nullToUndefined(token),
        data.duration
      );
      
      if (response.success) {
        // Get plan details for the success message
        const plan = plans.find(p => p.id === data.planId);
        
        toast({
          title: "Tier updated",
          description: `${selectedProvider?.name} has been assigned to the ${plan?.name} plan.`,
        });
        
        // Reset the form
        form.reset();
        
        // Call onSuccess callback to close popup
        onSuccess?.();
      } else {
        toast({
          title: "Update failed",
          description: response.message || "There was an error updating the provider tier.",
          variant: "destructive",
        });
      }
    } catch (error) {
      toast({
        title: "Update failed",
        description: "There was an error updating the provider tier. Please try again.",
        variant: "destructive",
      });
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Provider Tier Management</CardTitle>
        <CardDescription>
          Assign subscription plans to {selectedProvider?.name} and manage their tier status.
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            {/* Show provider details directly without dropdown */}
            {selectedProvider && (
              <div className="bg-muted p-4 rounded-md">
                <h3 className="font-medium mb-2">Provider Details</h3>
                <div className="grid grid-cols-2 gap-2 text-sm">
                  <div>
                    <span className="text-muted-foreground">Name:</span> {selectedProvider.name}
                  </div>
                  <div>
                    <span className="text-muted-foreground">Email:</span> {selectedProvider.email}
                  </div>
                  <div>
                    <span className="text-muted-foreground">Phone:</span> {selectedProvider.phone || 'N/A'}
                  </div>
                  <div>
                    <span className="text-muted-foreground">Business:</span> {selectedProvider.business_name || 'N/A'}
                  </div>
                  <div>
                    <span className="text-muted-foreground">Current Plan:</span> {selectedProvider.plan || 'None'}
                  </div>
                </div>
              </div>
            )}

            <FormField
              control={form.control}
              name="planId"
              render={({ field }) => {
                return (
                  <FormItem>
                    <FormLabel>Subscription Plan</FormLabel>
                    <Select
                      onValueChange={(value) => {
                        field.onChange(value);
                      }}
                      value={field.value}
                      disabled={isLoadingPlans}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select a plan" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {isLoadingPlans ? (
                          <div className="flex items-center justify-center p-4">
                            <Loader2 className="h-4 w-4 animate-spin mr-2" />
                            <span>Loading plans...</span>
                          </div>
                        ) : (
                          plans.map((plan) => (
                              <SelectItem key={plan.id} value={String(plan.id)}>
                                {plan.name} (${plan.price}{plan.price === 0 ? '' : '/month'}) - {plan.commission} commission
                              </SelectItem>
                          ))
                        )}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                );
              }}
            />

            <FormField
              control={form.control}
              name="duration"
              render={({ field }) => {
                return (
                  <FormItem>
                    <FormLabel>Duration</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select duration" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="monthly">Monthly</SelectItem>
                        <SelectItem value="yearly">Yearly</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                );
              }}
            />



            <FormField
              control={form.control}
              name="notes"
              render={({ field }) => {
                return (
                  <FormItem>
                    <FormLabel>Notes (Optional - Max 500 characters)</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Add any notes about this tier assignment"
                        className="resize-none"
                        maxLength={500}
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                );
              }}
            />

            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Saving...
                </>
              ) : (
                "Assign Plan"
              )}
            </Button>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
};