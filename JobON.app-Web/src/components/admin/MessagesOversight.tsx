
import { useState, useRef, useEffect } from "react";
import { Card } from "@/components/ui/card";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Input } from "@/components/ui/input";
import { But<PERSON> } from "@/components/ui/button";
import { Search, Flag, AlertCircle, X, Filter, ArrowLeft } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useToast } from "@/hooks/use-toast";
import { useUIHelpers } from "@/hooks/use-ui-helpers";
import { Sheet, SheetContent, SheetHeader, SheetTitle, SheetTrigger } from "@/components/ui/sheet";
import { ScrollArea } from "@/components/ui/scroll-area";

export const MessagesOversight = () => {
  const { toast } = useToast();
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedFilter, setSelectedFilter] = useState("all");
  const [isLoading, setIsLoading] = useState(false);
  const [selectedConversation, setSelectedConversation] = useState<any | null>(null);
  const [showMobileConversation, setShowMobileConversation] = useState(false);
  const { isMobile } = useUIHelpers();
  
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Mock conversation data
  const mockConversations = [
    {
      id: 'conv1',
      customer: { 
        name: 'Michael Johnson', 
        avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d', 
        initials: 'MJ' 
      },
      provider: { 
        name: 'John Smith', 
        avatar: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e', 
        initials: 'JS'
      },
      lastMessage: "What time will you arrive tomorrow?",
      timestamp: "10:45 AM",
      unread: true,
      jobTitle: "Main Line Drain Cleaning",
      jobId: "JOB-1234",
      status: "In Progress",
      messages: [
        {
          id: 'msg1',
          sender: 'customer',
          content: "Hi John, I'm having an issue with my main drain line. There's water backing up in the basement sink when we use the washing machine.",
          timestamp: "Yesterday, 4:30 PM"
        },
        {
          id: 'msg2',
          sender: 'provider',
          content: "Hello Michael, thanks for reaching out. That sounds like a potential clog in your main line. I have availability to come check it out tomorrow or Thursday.",
          timestamp: "Yesterday, 4:45 PM"
        },
        {
          id: 'msg3',
          sender: 'customer',
          content: "Tomorrow would be great if possible. What time can you come?",
          timestamp: "Yesterday, 5:10 PM"
        },
        {
          id: 'msg4',
          sender: 'provider',
          content: "I can arrive between 10:00 AM and 12:00 PM tomorrow. Does that work for you?",
          timestamp: "Yesterday, 5:25 PM"
        },
        {
          id: 'msg5',
          sender: 'customer',
          content: "What time will you arrive tomorrow?",
          timestamp: "Today, 10:45 AM"
        }
      ],
      flagged: false
    },
    {
      id: 'conv2',
      customer: { 
        name: 'Emma Wilson', 
        avatar: 'https://images.unsplash.com/photo-1494790108377-be9c29b29330', 
        initials: 'EW' 
      },
      provider: { 
        name: 'Sarah Miller', 
        avatar: 'https://images.unsplash.com/photo-1534528741775-53994a69daeb', 
        initials: 'SM' 
      },
      lastMessage: "I'll send over the invoice shortly.",
      timestamp: "Yesterday",
      unread: false,
      jobTitle: "Shower Valve Replacement",
      jobId: "JOB-1235",
      status: "Completed",
      messages: [
        {
          id: 'msg1',
          sender: 'customer',
          content: "Hi Sarah, just following up on the shower valve replacement. When will I receive my invoice?",
          timestamp: "Yesterday, 11:30 AM"
        },
        {
          id: 'msg2',
          sender: 'provider',
          content: "Hello Emma, I'm finalizing the details now. I'll send over the invoice shortly.",
          timestamp: "Yesterday, 2:15 PM"
        }
      ],
      flagged: false
    },
    {
      id: 'conv3',
      customer: { 
        name: 'James Thompson', 
        avatar: '', 
        initials: 'JT' 
      },
      provider: { 
        name: 'Robert Chen', 
        avatar: '', 
        initials: 'RC' 
      },
      lastMessage: "This is the third time I've had to call about this issue!",
      timestamp: "2 days ago",
      unread: true,
      jobTitle: "Air Conditioner Repair",
      jobId: "JOB-1236",
      status: "In Progress",
      messages: [
        {
          id: 'msg1',
          sender: 'customer',
          content: "This is the third time I've had to call about this issue! My AC is still not cooling properly after your visit last week.",
          timestamp: "2 days ago, 9:15 AM"
        }
      ],
      flagged: true
    }
  ];

  // Filter conversations based on search query and filters
  const filteredConversations = mockConversations.filter(conversation => {
    // Match search query
    const matchesSearch = 
      conversation.customer.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      conversation.provider.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      conversation.jobTitle.toLowerCase().includes(searchQuery.toLowerCase());
    
    // Match filter
    const matchesFilter = 
      selectedFilter === "all" ||
      (selectedFilter === "unread" && conversation.unread) ||
      (selectedFilter === "flagged" && conversation.flagged);
    
    return matchesSearch && matchesFilter;
  });

  const getStatusBadge = (status: string) => {
    switch(status) {
      case "Open":
        return <Badge variant="warning">Open</Badge>;
      case "In Progress":
        return <Badge variant="business">In Progress</Badge>;
      case "Completed":
        return <Badge variant="success">Completed</Badge>;
      case "Cancelled":
        return <Badge variant="destructive">Cancelled</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  // Function to handle flagging a conversation
  const handleFlagConversation = (conversation: any, event: React.MouseEvent) => {
    event.stopPropagation();
    
    // In a real app, this would be an API call
    toast({
      title: conversation.flagged ? "Conversation unflagged" : "Conversation flagged",
      description: `The conversation between ${conversation.customer.name} and ${conversation.provider.name} has been ${conversation.flagged ? "unflagged" : "flagged"}.`,
    });
  };

  // Simulate loading state for better UX
  const handleReload = () => {
    setIsLoading(true);
    setTimeout(() => {
      setIsLoading(false);
    }, 1000);
  };

  // Scroll to bottom of messages when conversation is selected
  useEffect(() => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [selectedConversation]);

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-3xl font-bold tracking-tight mb-2">Messages Oversight</h2>
        <p className="text-muted-foreground">Monitor customer and provider communications.</p>
      </div>
      
      <Tabs defaultValue="all">
        <div className="flex flex-col sm:flex-row justify-between">
          <TabsList className="mb-4 w-full sm:w-auto">
            <TabsTrigger value="all">All Messages</TabsTrigger>
            <TabsTrigger value="flagged">Flagged</TabsTrigger>
            <TabsTrigger value="escalated">Escalated</TabsTrigger>
          </TabsList>
          
          <div className="flex items-center gap-2 mb-4">
            <Button variant="outline" onClick={handleReload}>
              Refresh
            </Button>
          </div>
        </div>
        
        <TabsContent value="all" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 h-[calc(100vh-240px)] min-h-[500px]">
            {/* Conversation List */}
            <Card className={`p-4 overflow-hidden flex flex-col h-full ${isMobile && showMobileConversation ? 'hidden' : ''}`}>
              <div className="mb-4 space-y-2">
                <div className="relative">
                  <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input 
                    placeholder="Search conversations..." 
                    className="pl-8" 
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                  />
                </div>
                <div className="flex items-center gap-2">
                  {isMobile ? (
                    <Sheet>
                      <SheetTrigger asChild>
                        <Button variant="outline" size="sm" className="w-full flex items-center justify-between">
                          <span>Filter: {selectedFilter.charAt(0).toUpperCase() + selectedFilter.slice(1)}</span>
                          <Filter className="h-4 w-4 ml-2" />
                        </Button>
                      </SheetTrigger>
                      <SheetContent side="bottom" className="h-[30%]">
                        <SheetHeader>
                          <SheetTitle>Filter Messages</SheetTitle>
                        </SheetHeader>
                        <div className="grid gap-4 py-4">
                          <div 
                            className={`p-3 rounded-md border cursor-pointer ${selectedFilter === "all" ? "border-primary bg-primary/5" : ""}`}
                            onClick={() => {
                              setSelectedFilter("all");
                              document.body.click(); // Close sheet
                            }}
                          >
                            All Messages
                          </div>
                          <div 
                            className={`p-3 rounded-md border cursor-pointer ${selectedFilter === "unread" ? "border-primary bg-primary/5" : ""}`}
                            onClick={() => {
                              setSelectedFilter("unread");
                              document.body.click(); // Close sheet
                            }}
                          >
                            Unread
                          </div>
                          <div 
                            className={`p-3 rounded-md border cursor-pointer ${selectedFilter === "flagged" ? "border-primary bg-primary/5" : ""}`}
                            onClick={() => {
                              setSelectedFilter("flagged");
                              document.body.click(); // Close sheet
                            }}
                          >
                            Flagged
                          </div>
                        </div>
                      </SheetContent>
                    </Sheet>
                  ) : (
                    <Select value={selectedFilter} onValueChange={setSelectedFilter}>
                      <SelectTrigger>
                        <SelectValue placeholder="Filter" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">All Messages</SelectItem>
                        <SelectItem value="unread">Unread</SelectItem>
                        <SelectItem value="flagged">Flagged</SelectItem>
                      </SelectContent>
                    </Select>
                  )}
                </div>
              </div>
              
              <ScrollArea className="flex-grow">
                {isLoading ? (
                  // Loading skeleton
                  Array(3).fill(0).map((_, i) => (
                    <div key={`skeleton-${i}`} className="flex gap-3 p-3 border-b">
                      <Skeleton className="h-10 w-10 rounded-full" />
                      <div className="flex-grow space-y-2">
                        <div className="flex justify-between">
                          <Skeleton className="h-4 w-24" />
                          <Skeleton className="h-4 w-12" />
                        </div>
                        <Skeleton className="h-4 w-full" />
                        <Skeleton className="h-3 w-32" />
                      </div>
                    </div>
                  ))
                ) : filteredConversations.length > 0 ? (
                  filteredConversations.map(conversation => (
                    <div 
                      key={conversation.id}
                      onClick={() => {
                        setSelectedConversation(conversation);
                        if (isMobile) setShowMobileConversation(true);
                      }}
                      className={`flex items-start gap-3 p-3 cursor-pointer border-b hover:bg-muted ${selectedConversation?.id === conversation.id ? 'bg-primary/5 border border-primary/10' : ''}`}
                    >
                      <div className="flex gap-1">
                        <Avatar className="h-10 w-10">
                          <AvatarImage src={conversation.customer.avatar} alt={conversation.customer.name} />
                          <AvatarFallback>{conversation.customer.initials}</AvatarFallback>
                        </Avatar>
                      </div>
                      <div className="flex-grow min-w-0">
                        <div className="flex justify-between items-center">
                          <h4 className="font-medium truncate">
                            {conversation.customer.name} 
                            <span className="mx-1 text-gray-400">/</span>
                            {conversation.provider.name}
                          </h4>
                          <div className="flex items-center">
                            {conversation.flagged && (
                              <Flag size={14} className="text-red-500 mr-1" />
                            )}
                            <span className="text-xs text-muted-foreground whitespace-nowrap">{conversation.timestamp}</span>
                          </div>
                        </div>
                        <p className="text-sm truncate text-muted-foreground">{conversation.lastMessage}</p>
                        <div className="mt-1 flex items-center justify-between">
                          <span className="text-xs text-muted-foreground">{conversation.jobTitle}</span>
                          {getStatusBadge(conversation.status)}
                        </div>
                      </div>
                      {conversation.unread && (
                        <Badge className="h-2.5 w-2.5 rounded-full p-0 ml-1" />
                      )}
                    </div>
                  ))
                ) : (
                  <div className="p-6 text-center">
                    <p className="text-muted-foreground">No conversations found.</p>
                  </div>
                )}
              </ScrollArea>

              {isMobile && (
                <div className="pt-3 mt-2 border-t grid grid-cols-3 gap-2">
                  <Button variant="outline" size="sm" className="flex flex-col items-center py-3 h-auto">
                    <Search className="h-4 w-4 mb-1" />
                    <span className="text-xs">Search</span>
                  </Button>
                  <Button variant="outline" size="sm" className="flex flex-col items-center py-3 h-auto">
                    <Flag className="h-4 w-4 mb-1" />
                    <span className="text-xs">Flagged</span>
                  </Button>
                  <Button variant="outline" size="sm" className="flex flex-col items-center py-3 h-auto">
                    <AlertCircle className="h-4 w-4 mb-1" />
                    <span className="text-xs">Escalate</span>
                  </Button>
                </div>
              )}
            </Card>
            
            {/* Message Thread */}
            <Card className={`p-4 md:col-span-2 flex flex-col h-full ${isMobile && !showMobileConversation ? 'hidden' : ''}`}>
              {selectedConversation ? (
                <>
                  {/* Header */}
                  <div className="flex items-center justify-between pb-4 border-b">
                    <div className="flex items-center gap-3">
                      {isMobile && (
                        <Button 
                          variant="ghost" 
                          size="icon" 
                          className="mr-1" 
                          onClick={() => setShowMobileConversation(false)}
                        >
                          <ArrowLeft className="h-4 w-4" />
                        </Button>
                      )}
                      <div className="flex gap-1">
                        <Avatar>
                          <AvatarImage src={selectedConversation.customer.avatar} alt={selectedConversation.customer.name} />
                          <AvatarFallback>{selectedConversation.customer.initials}</AvatarFallback>
                        </Avatar>
                        <Avatar>
                          <AvatarImage src={selectedConversation.provider.avatar} alt={selectedConversation.provider.name} />
                          <AvatarFallback>{selectedConversation.provider.initials}</AvatarFallback>
                        </Avatar>
                      </div>
                      <div>
                        <div className="flex items-center">
                          <h4 className="font-medium text-sm">
                            {selectedConversation.customer.name} / {selectedConversation.provider.name}
                          </h4>
                          {selectedConversation.flagged && (
                            <Flag size={14} className="text-red-500 ml-2" />
                          )}
                        </div>
                        <div className="flex flex-wrap gap-2 items-center">
                          <p className="text-xs text-muted-foreground">{selectedConversation.jobTitle}</p>
                          <span className="text-xs text-muted-foreground">{selectedConversation.jobId}</span>
                          {getStatusBadge(selectedConversation.status)}
                        </div>
                      </div>
                    </div>
                    <div className="flex gap-2">
                      {isMobile ? (
                        <Sheet>
                          <SheetTrigger asChild>
                            <Button variant="outline" size="sm">Actions</Button>
                          </SheetTrigger>
                          <SheetContent side="bottom" className="h-[30%]">
                            <SheetHeader>
                              <SheetTitle>Conversation Actions</SheetTitle>
                            </SheetHeader>
                            <div className="grid gap-4 py-4">
                              <Button 
                                variant={selectedConversation.flagged ? "destructive" : "outline"}
                                size="sm"
                                className="flex justify-start items-center h-12"
                                onClick={() => handleFlagConversation(selectedConversation, {} as React.MouseEvent)}
                              >
                                <Flag className="h-4 w-4 mr-2" />
                                {selectedConversation.flagged ? 'Unflag Conversation' : 'Flag Conversation'}
                              </Button>
                              <Button 
                                variant="outline"
                                size="sm"
                                className="flex justify-start items-center h-12"
                              >
                                <AlertCircle className="h-4 w-4 mr-2" />
                                Escalate Conversation
                              </Button>
                            </div>
                          </SheetContent>
                        </Sheet>
                      ) : (
                        <>
                          <Button 
                            variant={selectedConversation.flagged ? "destructive" : "outline"}
                            size="sm"
                            onClick={() => handleFlagConversation(selectedConversation, {} as React.MouseEvent)}
                          >
                            <Flag className="h-4 w-4 mr-1" />
                            {selectedConversation.flagged ? 'Unflag' : 'Flag'}
                          </Button>
                          <Button variant="outline" size="sm">
                            <AlertCircle className="h-4 w-4 mr-1" />
                            Escalate
                          </Button>
                        </>
                      )}
                    </div>
                  </div>
                  
                  {/* Messages */}
                  <ScrollArea className="flex-grow p-4">
                    <div className="space-y-4">
                      {selectedConversation.messages.map((message: any) => (
                        <div 
                          key={message.id} 
                          className={`flex ${message.sender === 'provider' ? 'justify-end' : 'justify-start'}`}
                        >
                          {message.sender === 'customer' && (
                            <Avatar className="h-8 w-8 mr-2 mt-1">
                              <AvatarImage src={selectedConversation.customer.avatar} alt={selectedConversation.customer.name} />
                              <AvatarFallback>{selectedConversation.customer.initials}</AvatarFallback>
                            </Avatar>
                          )}
                          <div 
                            className={`max-w-[80%] rounded-lg p-3 ${
                              message.sender === 'provider' 
                                ? 'bg-blue-100 text-blue-900 dark:bg-blue-900 dark:text-blue-100' 
                                : 'bg-muted'
                            }`}
                          >
                            <p className="text-sm">{message.content}</p>
                            <span className="text-xs opacity-70 block text-right mt-1">
                              {message.timestamp}
                            </span>
                          </div>
                          {message.sender === 'provider' && (
                            <Avatar className="h-8 w-8 ml-2 mt-1">
                              <AvatarImage src={selectedConversation.provider.avatar} alt={selectedConversation.provider.name} />
                              <AvatarFallback>{selectedConversation.provider.initials}</AvatarFallback>
                            </Avatar>
                          )}
                        </div>
                      ))}
                      <div ref={messagesEndRef} />
                    </div>
                  </ScrollArea>
                  
                  {/* Admin Notes */}
                  <div className="pt-4 border-t mt-auto">
                    <h4 className="text-sm font-medium mb-2">Admin Notes</h4>
                    <div className="bg-muted p-3 rounded-md">
                      <p className="text-sm">
                        <span className="font-medium">System:</span> This conversation is being monitored for quality assurance.
                      </p>
                    </div>
                  </div>
                </>
              ) : (
                <div className="flex items-center justify-center h-full">
                  <div className="text-center">
                    <h3 className="font-medium">Select a conversation</h3>
                    <p className="text-sm text-muted-foreground">Choose a conversation from the list to view messages</p>
                  </div>
                </div>
              )}
            </Card>
          </div>
        </TabsContent>
        
        <TabsContent value="flagged">
          <div className="p-6 text-center">
            <h3 className="text-lg font-medium">Flagged Conversations</h3>
            <p className="text-muted-foreground">View and manage flagged conversations that require attention.</p>
          </div>
        </TabsContent>
        
        <TabsContent value="escalated">
          <div className="p-6 text-center">
            <h3 className="text-lg font-medium">Escalated Conversations</h3>
            <p className="text-muted-foreground">View conversations that have been escalated for admin resolution.</p>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};
