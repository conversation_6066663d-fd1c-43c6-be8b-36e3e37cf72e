
import { Layout } from "@/components/Layout";
import { AdminSidebar } from "@/components/admin/AdminSidebar";
import { ProjectsTable } from "@/components/admin/ProjectsTable";
import { ProvidersTable } from "@/components/admin/ProvidersTable";
import { CustomersTable } from "@/components/admin/CustomersTable";
import { DashboardStats } from "@/components/admin/DashboardStats";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Bell, Search, LogOut, Settings as SettingsIcon, User } from "lucide-react";
import { useNavigate, useLocation } from "react-router-dom";
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuTrigger 
} from "@/components/ui/dropdown-menu";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { useEffect, useState } from "react";
import { MessagesOversight } from "@/components/admin/MessagesOversight";
import { CustomerManagement } from "@/components/admin/CustomerManagement";
import { AdminSettings } from "@/components/admin/settings";

const AdminDashboard = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const path = location.pathname;
  const [activeTab, setActiveTab] = useState("projects");

  // Determine active tab based on URL path
  useEffect(() => {
    if (path === "/admin") {
      setActiveTab("dashboard");
    } else if (path === "/admin/projects") {
      setActiveTab("projects");
    } else if (path === "/admin/providers") {
      setActiveTab("providers");
    } else if (path === "/admin/messages") {
      setActiveTab("messages");
    } else if (path === "/admin/customers") {
      setActiveTab("customers");
    } else if (path === "/admin/settings") {
      setActiveTab("settings");
    }
  }, [path]);

  // Update navigation when tab changes
  const handleTabChange = (value: string) => {
    setActiveTab(value);
    navigate(`/admin${value === "dashboard" ? "" : `/${value}`}`);
  };

  return (
    <Layout>
      <div className="flex min-h-screen bg-gray-100">
        <AdminSidebar />
        <main className="flex-1">
          <div className="flex justify-between items-center bg-white py-4 px-8 border-b">
            <h1 className="text-2xl font-bold text-gray-900">Admin Dashboard</h1>
            <div className="flex items-center space-x-6">
              <div className="relative hidden md:flex items-center">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
                <Input 
                  placeholder="Search..." 
                  className="pl-10 w-64 bg-gray-50 border-none" 
                />
              </div>
              <Button variant="ghost" size="icon" className="relative">
                <Bell className="h-5 w-5" />
                <span className="absolute top-1 right-1 w-2 h-2 bg-red-500 rounded-full"></span>
              </Button>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" className="relative flex items-center gap-2">
                    <Avatar className="h-8 w-8">
                      <AvatarImage src="https://github.com/shadcn.png" alt="Admin" />
                      <AvatarFallback>AD</AvatarFallback>
                    </Avatar>
                    <span className="hidden md:inline-block">Admin User</span>
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="w-56">
                  <DropdownMenuItem>
                    <User className="mr-2 h-4 w-4" />
                    <span>Profile</span>
                  </DropdownMenuItem>
                  <DropdownMenuItem>
                    <SettingsIcon className="mr-2 h-4 w-4" />
                    <span>Settings</span>
                  </DropdownMenuItem>
                  <DropdownMenuItem>
                    <LogOut className="mr-2 h-4 w-4" />
                    <span>Log out</span>
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>

          <div className="p-8">
            <DashboardStats />

            <Tabs value={activeTab} onValueChange={handleTabChange} className="w-full">
              <TabsList className="mb-8">
                <TabsTrigger value="dashboard">Dashboard</TabsTrigger>
                <TabsTrigger value="projects">Projects</TabsTrigger>
                <TabsTrigger value="providers">Providers</TabsTrigger>
                <TabsTrigger value="messages">Messages</TabsTrigger>
                <TabsTrigger value="customers">Customers</TabsTrigger>
                <TabsTrigger value="settings">Settings</TabsTrigger>
              </TabsList>

              <TabsContent value="dashboard">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <div className="bg-white p-6 rounded-xl shadow-sm">
                    <h2 className="text-lg font-semibold mb-4">Recent Activity</h2>
                    <div className="space-y-4">
                      {/* Dashboard content goes here */}
                      <p className="text-gray-500">Welcome to the JobOn Admin Dashboard</p>
                    </div>
                  </div>
                </div>
              </TabsContent>

              <TabsContent value="projects">
                <ProjectsTable />
              </TabsContent>

              <TabsContent value="providers">
                <ProvidersTable />
              </TabsContent>

              <TabsContent value="messages">
                <MessagesOversight />
              </TabsContent>

              <TabsContent value="customers">
                <CustomerManagement />
              </TabsContent>

              <TabsContent value="settings">
                <AdminSettings />
              </TabsContent>
            </Tabs>
          </div>
        </main>
      </div>
    </Layout>
  );
};

export default AdminDashboard;
