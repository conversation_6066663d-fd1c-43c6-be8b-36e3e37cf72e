
import { useEffect } from "react";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { ScrollArea } from "@/components/ui/scroll-area";
import { useChat } from "@/hooks/useChat";
import { useAuth } from "@/features/auth/hooks/useAuth";

interface MessageThreadProps {
  chatId: string;
  messageEndRef: React.RefObject<HTMLDivElement>;
}

export const MessageThread = ({
  chatId,
  messageEndRef
}: MessageThreadProps) => {
  const { messages, loadMessages, currentChat } = useChat();
  const { user } = useAuth();
  const currentUserId = user?.id;

  useEffect(() => {
    if (chatId) {
      loadMessages(chatId);
    }
  }, [chatId, loadMessages]);

  // Format timestamp for display
  const formatMessageTime = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const yesterday = new Date(now);
    yesterday.setDate(now.getDate() - 1);

    const isToday = date.toDateString() === now.toDateString();
    const isYesterday = date.toDateString() === yesterday.toDateString();

    if (isToday) {
      return `Today at ${date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}`;
    } else if (isYesterday) {
      return `Yesterday at ${date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}`;
    } else {
      return date.toLocaleDateString() + ' ' + date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    }
  };

  // Get initials for avatar fallback
  const getInitials = (name: string) => {
    return name
      .split(" ")
      .map((n) => n[0])
      .join("")
      .toUpperCase();
  };

  return (
    <ScrollArea className="h-[calc(100%-80px)]">
      {messages.length > 0 ? (
        <div className="flex flex-col space-y-2 p-4 pb-6">
          {messages.map((message, index) => {
            const isFromCurrentUser = message.user_id === currentUserId;
            const showAvatar = !isFromCurrentUser && (
              index === messages.length - 1 || 
              messages[index + 1]?.user_id !== message.user_id
            );

            return (
              <div
                key={message.id}
                className={`flex items-end gap-2 ${
                  isFromCurrentUser ? "justify-start" : "justify-end"
                }`}
              >
                {isFromCurrentUser && (
                  <div className="w-8 h-8 flex-shrink-0">
                    <Avatar className="h-8 w-8">
                      <AvatarImage src={user?.avatar} />
                      <AvatarFallback className="text-xs">
                        {getInitials(user?.name || 'Me')}
                      </AvatarFallback>
                    </Avatar>
                  </div>
                )}

                <div
                  className={`max-w-[70%] rounded-2xl px-4 py-2 ${
                    isFromCurrentUser
                      ? "bg-gray-200 dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                      : "bg-blue-500 text-white"
                  }`}
                >
                  <p className="text-sm whitespace-pre-wrap break-words leading-relaxed">
                    {message.message}
                  </p>
                  <div className={`text-xs mt-1 opacity-70 ${
                    isFromCurrentUser ? "text-left" : "text-right"
                  }`}>
                    {formatMessageTime(message.created_at)}
                    {isFromCurrentUser && message.read_at && (
                      <span className="ml-2 text-gray-500 dark:text-gray-400">✓✓</span>
                    )}
                    {isFromCurrentUser && !message.read_at && (
                      <span className="ml-2 text-gray-500 dark:text-gray-400">✓</span>
                    )}
                  </div>
                </div>

                {!isFromCurrentUser && (
                  <div className="w-8 h-8 flex-shrink-0">
                    {showAvatar ? (
                      <Avatar className="h-8 w-8">
                        <AvatarImage src={message?.user?.avatar} />
                        <AvatarFallback className="text-xs">
                          {getInitials(message?.user?.name || 'U')}
                        </AvatarFallback>
                      </Avatar>
                    ) : null}
                  </div>
                )}
              </div>
            );
          })}
          <div ref={messageEndRef} />
        </div>
      ) : (
        <div className="flex items-center justify-center h-full">
          <p className="text-muted-foreground">No messages yet</p>
        </div>
      )}
    </ScrollArea>
  );
};
