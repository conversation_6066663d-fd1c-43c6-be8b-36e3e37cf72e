
import React from 'react';
import { Input } from "@/components/ui/input";
import { Search, Filter, X } from "lucide-react";

interface MobileFilterBarProps {
  searchQuery: string;
  onSearchQueryChange: (value: string) => void;
  filter: string;
  onFilterChange: (value: string) => void;
  totalConversations: number;
  unreadCount: number;
  flaggedCount: number;
}

export const MobileFilterBar: React.FC<MobileFilterBarProps> = ({
  searchQuery,
  onSearchQueryChange,
  totalConversations,
  unreadCount,
  flaggedCount
}) => {
  return (
    <div className="space-y-3">
      <div className="relative">
        <div className="absolute inset-y-0 start-0 flex items-center ps-3 pointer-events-none">
          <Search className="w-4 h-4 text-gray-500 dark:text-gray-400" />
        </div>
        <Input
          type="search"
          value={searchQuery}
          onChange={(e) => onSearchQueryChange(e.target.value)}
          className="ps-10 pe-10 bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700 rounded-lg"
          placeholder="Search messages..."
        />
        {searchQuery && (
          <button 
            onClick={() => onSearchQueryChange('')}
            className="absolute inset-y-0 end-0 flex items-center pe-3"
          >
            <X className="w-4 h-4 text-gray-500 dark:text-gray-400" />
          </button>
        )}
      </div>
      
      <div className="flex justify-between items-center text-sm text-gray-500 dark:text-gray-400">
        <div className="flex items-center gap-1">
          <Filter className="w-3 h-3" />
          <span>{totalConversations} conversations</span>
        </div>
        
        <div className="flex gap-3">
          {unreadCount > 0 && (
            <div className="flex items-center gap-1">
              <span className="w-2 h-2 bg-blue-500 rounded-full"></span>
              <span>{unreadCount} unread</span>
            </div>
          )}
          
          {flaggedCount > 0 && (
            <div className="flex items-center gap-1">
              <span className="w-2 h-2 bg-rose-500 rounded-full"></span>
              <span>{flaggedCount} flagged</span>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};
