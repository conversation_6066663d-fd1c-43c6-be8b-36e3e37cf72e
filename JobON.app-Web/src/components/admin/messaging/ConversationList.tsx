
import { useState, useEffect } from "react";
import { Search, Filter, Flag, Pin } from "lucide-react";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Sheet,
  SheetContent,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
} from "@/components/ui/sheet";
import { useUIHelpers } from "@/hooks/use-ui-helpers";
import { 
  AdminConversation, 
  getAdminConversations, 
  toggleConversationFlag,
  toggleConversationPin 
} from "@/utils/messagingUtils";

interface ConversationListProps {
  onSelectConversation: (conversation: AdminConversation) => void;
  activeConversationId?: string;
  filter: string;
  onFilterChange: (filter: string) => void;
  searchQuery: string;
  onSearchQueryChange: (query: string) => void;
}

export const ConversationList = ({
  onSelectConversation,
  activeConversationId,
  filter,
  onFilterChange,
  searchQuery,
  onSearchQueryChange,
}: ConversationListProps) => {
  const [conversations, setConversations] = useState<AdminConversation[]>([]);
  const { isMobile } = useUIHelpers();

  // Load conversations
  useEffect(() => {
    const allConversations = getAdminConversations();
    setConversations(allConversations);
  }, []);

  // Filter conversations
  const filteredConversations = conversations.filter((conversation) => {
    // Search filter
    const matchesSearch = conversation.recipientName
      .toLowerCase()
      .includes(searchQuery.toLowerCase());

    // Type filter
    let matchesFilter = true;
    if (filter === "providers") {
      matchesFilter = conversation.recipientType === "provider";
    } else if (filter === "customers") {
      matchesFilter = conversation.recipientType === "customer";
    } else if (filter === "unread") {
      matchesFilter = conversation.unread;
    } else if (filter === "flagged") {
      matchesFilter = conversation.flagged;
    } else if (filter === "pinned") {
      matchesFilter = conversation.pinned || false;
    }

    return matchesSearch && matchesFilter;
  });

  // Sort conversations: pinned first, then by timestamp (newest first)
  const sortedConversations = [...filteredConversations].sort((a, b) => {
    // Pinned conversations first
    if ((a.pinned && !b.pinned) || (a.pinned && !b.pinned)) return -1;
    if ((!a.pinned && b.pinned) || (!a.pinned && b.pinned)) return 1;

    // Then by timestamp (newest first)
    return new Date(b.timestamp).getTime() - 
           new Date(a.timestamp).getTime();
  });

  const handleFlag = (e: React.MouseEvent, conversationId: string) => {
    e.stopPropagation();
    toggleConversationFlag(conversationId);
    
    // Update local state
    const updatedConversations = conversations.map(conv => 
      conv.id === conversationId 
        ? { ...conv, flagged: !conv.flagged } 
        : conv
    );
    setConversations(updatedConversations);
  };

  const handlePin = (e: React.MouseEvent, conversationId: string) => {
    e.stopPropagation();
    toggleConversationPin(conversationId);
    
    // Update local state
    const updatedConversations = conversations.map(conv => 
      conv.id === conversationId 
        ? { ...conv, pinned: !conv.pinned } 
        : conv
    );
    setConversations(updatedConversations);
  };

  return (
    <div className="flex flex-col h-full">
      <div className="mb-4 space-y-2">
        <div className="relative">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search conversations..."
            className="pl-8"
            value={searchQuery}
            onChange={(e) => onSearchQueryChange(e.target.value)}
          />
        </div>
        <div>
          {isMobile ? (
            <Sheet>
              <SheetTrigger asChild>
                <Button
                  variant="outline"
                  className="w-full flex items-center justify-between"
                >
                  <span>
                    Filter: {filter.charAt(0).toUpperCase() + filter.slice(1)}
                  </span>
                  <Filter className="h-4 w-4 ml-2" />
                </Button>
              </SheetTrigger>
              <SheetContent side="bottom" className="h-[40%]">
                <SheetHeader>
                  <SheetTitle>Filter Messages</SheetTitle>
                </SheetHeader>
                <div className="grid gap-4 py-4">
                  <div
                    className={`p-3 rounded-md border cursor-pointer ${
                      filter === "all" ? "border-primary bg-primary/5" : ""
                    }`}
                    onClick={() => onFilterChange("all")}
                  >
                    All Messages
                  </div>
                  <div
                    className={`p-3 rounded-md border cursor-pointer ${
                      filter === "providers" ? "border-primary bg-primary/5" : ""
                    }`}
                    onClick={() => onFilterChange("providers")}
                  >
                    Provider Messages
                  </div>
                  <div
                    className={`p-3 rounded-md border cursor-pointer ${
                      filter === "customers" ? "border-primary bg-primary/5" : ""
                    }`}
                    onClick={() => onFilterChange("customers")}
                  >
                    Customer Messages
                  </div>
                  <div
                    className={`p-3 rounded-md border cursor-pointer ${
                      filter === "unread" ? "border-primary bg-primary/5" : ""
                    }`}
                    onClick={() => onFilterChange("unread")}
                  >
                    Unread
                  </div>
                  <div
                    className={`p-3 rounded-md border cursor-pointer ${
                      filter === "flagged" ? "border-primary bg-primary/5" : ""
                    }`}
                    onClick={() => onFilterChange("flagged")}
                  >
                    Flagged
                  </div>
                  <div
                    className={`p-3 rounded-md border cursor-pointer ${
                      filter === "pinned" ? "border-primary bg-primary/5" : ""
                    }`}
                    onClick={() => onFilterChange("pinned")}
                  >
                    Pinned
                  </div>
                </div>
              </SheetContent>
            </Sheet>
          ) : (
            <div className="w-full overflow-x-auto pb-2">
              <div className="flex space-x-1 min-w-max">
                <Button 
                  variant={filter === "all" ? "default" : "outline"}
                  size="sm"
                  onClick={() => onFilterChange("all")}
                  className="px-3"
                >
                  All
                </Button>
                <Button 
                  variant={filter === "providers" ? "default" : "outline"}
                  size="sm"
                  onClick={() => onFilterChange("providers")}
                  className="px-3"
                >
                  Providers
                </Button>
                <Button 
                  variant={filter === "customers" ? "default" : "outline"}
                  size="sm"
                  onClick={() => onFilterChange("customers")}
                  className="px-3"
                >
                  Customers
                </Button>
                <Button 
                  variant={filter === "unread" ? "default" : "outline"}
                  size="sm"
                  onClick={() => onFilterChange("unread")}
                  className="px-3"
                >
                  Unread
                </Button>
                <Button 
                  variant={filter === "flagged" ? "default" : "outline"}
                  size="sm"
                  onClick={() => onFilterChange("flagged")}
                  className="px-3"
                >
                  Flagged
                </Button>
                <Button 
                  variant={filter === "pinned" ? "default" : "outline"}
                  size="sm"
                  onClick={() => onFilterChange("pinned")}
                  className="px-3"
                >
                  Pinned
                </Button>
              </div>
            </div>
          )}
        </div>
      </div>

      <ScrollArea className="flex-grow">
        {sortedConversations.length > 0 ? (
          <div className="space-y-1">
            {sortedConversations.map((conversation) => (
              <div
                key={conversation.id}
                onClick={() => onSelectConversation(conversation)}
                className={`flex items-start p-3 rounded-md cursor-pointer hover:bg-muted relative ${
                  activeConversationId === conversation.id
                    ? "bg-primary/5 border border-primary/10"
                    : "border border-transparent"
                }`}
              >
                <Avatar className="h-10 w-10 mr-3">
                  <AvatarImage src={conversation.recipientAvatar} />
                  <AvatarFallback>
                    {conversation.recipientName
                      .split(" ")
                      .map((n) => n[0])
                      .join("")}
                  </AvatarFallback>
                </Avatar>
                <div className="flex-grow min-w-0">
                  <div className="flex justify-between items-start">
                    <h4 className="font-medium truncate flex items-center gap-2">
                      {conversation.recipientName}
                      {conversation.pinned && (
                        <Pin className="h-3 w-3 text-primary" />
                      )}
                      {conversation.flagged && (
                        <Flag className="h-3 w-3 text-red-500" />
                      )}
                    </h4>
                    <span className="text-xs text-muted-foreground whitespace-nowrap">
                      {new Date(
                        conversation.timestamp
                      ).toLocaleDateString()}
                    </span>
                  </div>
                  <p
                    className={`text-sm truncate ${
                      conversation.unread ? "font-medium" : "text-muted-foreground"
                    }`}
                  >
                    {conversation.lastMessage}
                  </p>
                  <div className="mt-1 flex items-center">
                    <Badge
                      variant={
                        conversation.recipientType === "provider"
                          ? "secondary"
                          : "outline"
                      }
                      className="text-xs"
                    >
                      {conversation.recipientType}
                    </Badge>
                    {conversation.unread && (
                      <Badge className="ml-2 bg-primary" variant="default">
                        New
                      </Badge>
                    )}
                  </div>
                </div>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild onClick={(e) => e.stopPropagation()}>
                    <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                      <span className="sr-only">Open menu</span>
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        className="h-4 w-4"
                      >
                        <circle cx="12" cy="12" r="1" />
                        <circle cx="12" cy="5" r="1" />
                        <circle cx="12" cy="19" r="1" />
                      </svg>
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem
                      onClick={(e) => handleFlag(e, conversation.id)}
                    >
                      <Flag className="mr-2 h-4 w-4" />
                      {conversation.flagged ? "Unflag" : "Flag"}
                    </DropdownMenuItem>
                    <DropdownMenuItem
                      onClick={(e) => handlePin(e, conversation.id)}
                    >
                      <Pin className="mr-2 h-4 w-4" />
                      {conversation.pinned ? "Unpin" : "Pin"}
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            ))}
          </div>
        ) : (
          <div className="flex items-center justify-center h-40">
            <p className="text-muted-foreground">No conversations found</p>
          </div>
        )}
      </ScrollArea>
    </div>
  );
};
