
import { useState, useCallback, useRef, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Paperclip, Send, Loader2 } from "lucide-react";
import { useChat } from "@/hooks/useChat";
import { useToast } from "@/hooks/use-toast";

interface MessageComposerProps {
  chatId: string;
  recipientName: string;
}

export const MessageComposer = ({ chatId, recipientName }: MessageComposerProps) => {
  const [message, setMessage] = useState("");
  const [isSending, setIsSending] = useState(false);
  const [isTyping, setIsTyping] = useState(false);
  const { sendMessage } = useChat(); // Remove isLoading dependency
  const { toast } = useToast();

  // Debounce mechanism
  const debounceTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const lastClickTimeRef = useRef<number>(0);
  const typingTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Memoized send message handler with debounce
  const handleSendMessage = useCallback(async () => {
    const messageText = message.trim();
    if (!messageText) return;

    // Debounce: prevent rapid clicks (300ms minimum between clicks)
    const now = Date.now();
    if (now - lastClickTimeRef.current < 300) {
      console.log('Debouncing rapid click...');
      return;
    }
    lastClickTimeRef.current = now;

    // Prevent sending if already in progress
    if (isSending) {
      console.log('Message already being sent, skipping...');
      return;
    }

    // Clear any existing timeout
    if (debounceTimeoutRef.current) {
      clearTimeout(debounceTimeoutRef.current);
    }

    setIsSending(true);

    try {
      // Add timeout to prevent infinite loading
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('Message sending timed out after 30 seconds')), 30000);
      });

      const sendPromise = sendMessage(chatId, {
        type: 'text',
        message: messageText
      });

      // Race between the actual send and the timeout
      await Promise.race([sendPromise, timeoutPromise]);

      // Clear input only on success
      setMessage("");

      toast({
        title: "Message sent",
        description: `Your message has been sent to ${recipientName}.`,
      });
    } catch (error) {
      console.error("Failed to send message:", error);

      // More detailed error message
      const errorMessage = error instanceof Error 
        ? error.message 
        : "Please try again later.";

      toast({
        title: "Failed to send message",
        description: errorMessage,
        variant: "destructive"
      });
    } finally {
      setIsSending(false);

      // Add a small delay before allowing another send
      debounceTimeoutRef.current = setTimeout(() => {
        debounceTimeoutRef.current = null;
      }, 300);
    }
  }, [message, chatId, recipientName, sendMessage, isSending, toast]);

  // Handle typing state with debounce
  const handleTypingStart = useCallback(() => {
    setIsTyping(true);

    // Clear existing timeout
    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current);
    }

    // Set new timeout to stop typing after 1.5 seconds of inactivity
    typingTimeoutRef.current = setTimeout(() => {
      setIsTyping(false);
    }, 1500);
  }, []);

  const handleMessageChange = useCallback((e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setMessage(e.target.value);
    handleTypingStart();
  }, [handleTypingStart]);

  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      // Clear any pending debounce timeout
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current);
        debounceTimeoutRef.current = null;
      }
      // Clear typing timeout
      if (typingTimeoutRef.current) {
        clearTimeout(typingTimeoutRef.current);
        typingTimeoutRef.current = null;
      }
    };
  }, []);

  return (
    <div className="border rounded-md p-3 bg-background">
      <Textarea
        placeholder={`Message ${recipientName}...`}
        className="min-h-[80px] resize-none border-0 p-2 focus-visible:ring-0 focus-visible:ring-offset-0"
        value={message}
        onChange={handleMessageChange}
        onKeyDown={handleKeyDown}
        disabled={isSending}
      />
      <div className="flex justify-between items-center mt-2">
        <Button variant="ghost" size="icon" disabled={isSending}>
          <Paperclip className="h-4 w-4" />
        </Button>
        <Button
          onClick={handleSendMessage}
          disabled={!message.trim() || isSending || isTyping}
          className="px-3"
        >
          {isSending ? (
            <>
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              Sending...
            </>
          ) : (
            <>
              <Send className="h-4 w-4 mr-2" />
              Send
            </>
          )}
        </Button>
      </div>
    </div>
  );
};
