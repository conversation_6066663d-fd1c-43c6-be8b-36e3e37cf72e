
import { <PERSON><PERSON><PERSON>he<PERSON>, Flag, MoreVertical, Pin, UserRound } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Badge } from "@/components/ui/badge";
import { AdminConversation, toggleConversationFlag, toggleConversationPin } from "@/utils/messagingUtils";

interface ConversationHeaderProps {
  conversation: AdminConversation;
}

export const ConversationHeader = ({ conversation }: ConversationHeaderProps) => {
  // Get initials for avatar fallback
  const getInitials = (name: string) => {
    return name
      .split(" ")
      .map((n) => n[0])
      .join("")
      .toUpperCase();
  };

  const handleToggleFlag = () => {
    toggleConversationFlag(conversation.id);
    // In a real app, we would update the UI state here
  };

  const handleTogglePin = () => {
    toggleConversationPin(conversation.id);
    // In a real app, we would update the UI state here
  };

  return (
    <div className="flex items-center justify-between pb-3 border-b">
      <div className="flex items-center gap-3">
        <Avatar>
          <AvatarImage src={conversation.recipientAvatar} />
          <AvatarFallback>{getInitials(conversation.recipientName)}</AvatarFallback>
        </Avatar>
        <div>
          <div className="flex items-center gap-2 flex-wrap">
            <h3 className="font-medium leading-none mb-1">{conversation.recipientName}</h3>
            {conversation.flagged && (
              <Badge variant="destructive" className="ml-1">Flagged</Badge>
            )}
            {conversation.pinned && (
              <Badge variant="secondary" className="ml-1">Pinned</Badge>
            )}
          </div>
          <p className="text-sm text-muted-foreground flex items-center">
            {conversation.recipientType === "provider" ? (
              <BadgeCheck className="h-3 w-3 mr-1 text-blue-500" />
            ) : (
              <UserRound className="h-3 w-3 mr-1" />
            )}
            {conversation.recipientType === "provider" ? "Provider" : "Customer"}
          </p>
        </div>
      </div>
      <div>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" size="sm">
              <MoreVertical className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem onClick={handleToggleFlag}>
              <Flag className="h-4 w-4 mr-2" />
              {conversation.flagged ? "Unflag Conversation" : "Flag Conversation"}
            </DropdownMenuItem>
            <DropdownMenuItem onClick={handleTogglePin}>
              <Pin className="h-4 w-4 mr-2" />
              {conversation.pinned ? "Unpin Conversation" : "Pin Conversation"}
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </div>
  );
};
