
import React from 'react';
import { Button } from "@/components/ui/button";
import { ArrowLeft } from 'lucide-react';
import { useNavigate, useLocation } from 'react-router-dom';
import { getPreviousPage, navigateToPreviousPage } from '@/utils/navigationUtils';

interface MobileBackButtonProps {
  onClick?: () => void;
  text?: string;
  returnToPrevious?: boolean;
}

export const MobileBackButton: React.FC<MobileBackButtonProps> = ({ 
  onClick, 
  text = "Back to messages",
  returnToPrevious = false 
}) => {
  const navigate = useNavigate();
  const location = useLocation();
  
  const handleClick = () => {
    if (onClick) {
      onClick();
    } else if (returnToPrevious) {
      navigateToPreviousPage(navigate, location.state);
    } else {
      navigate(-1);
    }
  };
  
  return (
    <Button 
      variant="ghost" 
      size="sm" 
      onClick={handleClick}
      className="mb-2 flex items-center text-indigo-600 hover:text-indigo-800 hover:bg-indigo-50"
    >
      <ArrowLeft className="w-4 h-4 mr-1" />
      <span>{text}</span>
    </Button>
  );
};
