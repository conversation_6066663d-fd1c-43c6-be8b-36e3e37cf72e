
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useEffect, useState } from "react";
import { useAuth } from "@/features/auth/hooks/useAuth";
import { useToast } from "@/hooks/use-toast";
import { AdminProfileFormValues, adminProfileSchema } from "./schemas";
import { useMediaQuery } from "@/hooks/use-media-query";
import { apiService } from "@/services/api";

import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import useAuthHeader from "react-auth-kit/hooks/useAuthHeader";

export const AdminProfileForm = () => {
  const { toast } = useToast();
  const { user, roles, refreshRoles, updateUser } = useAuth();
  const [isLoadingRoles, setIsLoadingRoles] = useState(false);
  const isMobile = useMediaQuery("(max-width: 640px)");
  const authHeader = useAuthHeader()

  const form = useForm<AdminProfileFormValues>({
    resolver: zodResolver(adminProfileSchema),
    defaultValues: {
      name: "", // Initial empty default
      email: "", // Initial empty default
      roleId: undefined, // Initial empty default
    },
  });

  const { formState: { isSubmitting }, reset } = form; // Destructure reset

  // Populate form with user data once available
  useEffect(() => {
    if (user) {
      reset({
        name: user.name || "",
        email: user.email || "",
        roleId: user.role?.id || undefined,
      });
    }
  }, [user, reset]); // Add reset to dependency array

  // Refresh roles if they're empty
  useEffect(() => {
    const loadRoles = async () => {
      if (roles.length === 0 ) {
        setIsLoadingRoles(true);
        try {
          await refreshRoles();
        } catch (error) {
          console.error("Error refreshing roles:", error);
        } finally {
          setIsLoadingRoles(false);
        }
      }
    };

    loadRoles();
  }, [roles, refreshRoles]);

  const onSubmit = async (data: AdminProfileFormValues) => {
    try {
      // Extract only name and email fields from form data
      const { name, email } = data;

      // Create payload for the API request
      const payload = {
        name,
        email,
        phone: "12313123" // Note: phone is hardcoded
      };

      if (!authHeader) {
        toast({
          title: "Authentication Error",
          description: "You are not authenticated. Please log in again.",
          variant: "destructive",
        });
        return;
      }

      // authHeader is now a string here due to the check above
      const apiHeaders: Record<string, string> = {
        'Authorization': authHeader,
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      };

      // Make PUT request to the profile endpoint using apiService
      const response = await apiService('/api/profile', {
        method: 'PUT',
        headers: apiHeaders,
        body: JSON.stringify(payload) // Stringify the payload
      });

      if (response.isSuccess) {
        // Update the user information in the auth state
        updateUser({ // Assuming updateUser only needs name and email
          name,
          email
        });

        toast({
          title: "Profile updated",
          description: "Your admin profile has been updated successfully.",
          className: "bg-green-400 text-white",
        });
      } else {
        throw new Error(response.error || 'Failed to update profile');
      }
    } catch (error) {
      console.error("Error updating profile:", error);
      toast({
        title: "Update failed",
        description: "There was an error updating your profile. Please try again.",
        variant: "destructive",
      });
    }
  };

  return (
    <Card className="shadow-sm">
      <CardHeader className={isMobile ? "px-4 py-4" : ""}>
        <CardTitle className="text-lg md:text-xl">Admin Profile</CardTitle>
        <CardDescription>
          Update your admin profile information.
        </CardDescription>
      </CardHeader>
      <CardContent className={isMobile ? "px-4 pb-4" : ""}>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <div className={`grid ${isMobile ? "grid-cols-1 gap-4" : "grid-cols-2 gap-4"}`}>
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Name</FormLabel>
                    <FormControl>
                      <Input {...field} className="h-11" />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="email"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Email</FormLabel>
                    <FormControl>
                      <Input type="email" {...field} className="h-11" />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="roleId"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Role</FormLabel>
                  <Select
                    onValueChange={(value) => field.onChange(parseInt(value))}
                    value={field.value ? field.value.toString() : undefined}
                  >
                    <FormControl>
                      <SelectTrigger className="h-11">
                        <SelectValue placeholder="Select a role" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {isLoadingRoles ? (
                        <div className="p-2 text-center text-sm text-muted-foreground">
                          Loading roles...
                        </div>
                      ) : roles.length === 0 ? (
                        <div className="p-2 text-center text-sm text-muted-foreground">
                          No roles available
                        </div>
                      ) : (
                        roles.map((role) => (
                            <SelectItem key={role.id} value={role.id.toString()}>
                              {role.name === "user" ? "Customer" : role.name}
                            </SelectItem>
                        ))
                      )}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            <Button 
              type="submit" 
              disabled={isSubmitting}
              className={`${isMobile ? "w-full h-11" : ""}`}
            >
              {isSubmitting ? "Saving..." : "Save Profile"}
            </Button>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
};
