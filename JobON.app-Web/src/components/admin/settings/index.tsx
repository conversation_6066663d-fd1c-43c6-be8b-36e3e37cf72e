
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { ActivityLog } from "@/components/admin/ActivityLog";
import { AdminProfileForm } from "./AdminProfileForm";
import { PlatformSettingsForm } from "./PlatformSettingsForm";
import { NotificationPreferencesForm } from "./NotificationPreferencesForm";
import { TeamSection } from "./TeamSection";
import { useMediaQuery } from "@/hooks/use-media-query";
import { useEffect, useState } from "react";
import { ArrowLeft } from "lucide-react";
import { Button } from "@/components/ui/button";

export const AdminSettings = () => {
  const isMobile = useMediaQuery("(max-width: 640px)");
  const [activeTab, setActiveTab] = useState("general");
  const [showTabContent, setShowTabContent] = useState(true);
  
  // For mobile view, when tab changes, scroll to top
  useEffect(() => {
    if (isMobile) {
      window.scrollTo({ top: 0, behavior: "smooth" });
    }
  }, [activeTab, isMobile]);

  // Handle tab change
  const handleTabChange = (value: string) => {
    setActiveTab(value);
    setShowTabContent(true);
  };

  // Back button handler for mobile view
  const handleBack = () => {
    setShowTabContent(false);
  };

  return (
    <div className="space-y-4 pb-20 md:pb-0">
      <div>
        <h1 className="text-xl md:text-2xl font-bold">Admin Settings</h1>
        <p className="text-muted-foreground text-sm md:text-base">Manage admin account and system settings.</p>
      </div>
      
      {isMobile && !showTabContent ? (
        <div className="space-y-4">
          <div className="grid grid-cols-1 gap-3">
            <Button 
              variant="outline" 
              className="flex justify-between items-center w-full p-4 h-auto"
              onClick={() => handleTabChange("general")}
            >
              <span className="text-base font-medium">General Settings</span>
              <ArrowLeft className="rotate-180 h-5 w-5" />
            </Button>
            <Button 
              variant="outline" 
              className="flex justify-between items-center w-full p-4 h-auto"
              onClick={() => handleTabChange("notifications")}
            >
              <span className="text-base font-medium">Notification Preferences</span>
              <ArrowLeft className="rotate-180 h-5 w-5" />
            </Button>
            <Button 
              variant="outline" 
              className="flex justify-between items-center w-full p-4 h-auto"
              onClick={() => handleTabChange("activity")}
            >
              <span className="text-base font-medium">Activity Log</span>
              <ArrowLeft className="rotate-180 h-5 w-5" />
            </Button>
            <Button 
              variant="outline" 
              className="flex justify-between items-center w-full p-4 h-auto"
              onClick={() => handleTabChange("team")}
            >
              <span className="text-base font-medium">Team Management</span>
              <ArrowLeft className="rotate-180 h-5 w-5" />
            </Button>
          </div>
        </div>
      ) : (
        <div className="space-y-6">
          {isMobile && showTabContent && (
            <Button 
              variant="ghost" 
              className="flex items-center mb-2 pl-0"
              onClick={handleBack}
            >
              <ArrowLeft className="h-5 w-5 mr-2" />
              Back to Settings
            </Button>
          )}

          <Tabs 
            value={activeTab} 
            onValueChange={handleTabChange} 
            className={isMobile ? "hidden" : "block"}
          >
            <TabsList className="grid w-full max-w-lg grid-cols-4">
              <TabsTrigger value="general">General</TabsTrigger>
              <TabsTrigger value="notifications">Notifications</TabsTrigger>
              <TabsTrigger value="activity">Activity Log</TabsTrigger>
              <TabsTrigger value="team">Team</TabsTrigger>
            </TabsList>
            
            <TabsContent value="general" className="mt-6 space-y-4">
              <AdminProfileForm />
              <PlatformSettingsForm />
            </TabsContent>
            
            <TabsContent value="notifications" className="mt-6 space-y-4">
              <NotificationPreferencesForm />
            </TabsContent>
            
            <TabsContent value="activity" className="mt-6">
              <ActivityLog />
            </TabsContent>
            
            <TabsContent value="team" className="mt-6">
              <TeamSection />
            </TabsContent>
          </Tabs>

          {isMobile && showTabContent && (
            <div className="space-y-4">
              {activeTab === "general" && (
                <>
                  <AdminProfileForm />
                  <PlatformSettingsForm />
                </>
              )}
              {activeTab === "notifications" && <NotificationPreferencesForm />}
              {activeTab === "activity" && <ActivityLog />}
              {activeTab === "team" && <TeamSection />}
            </div>
          )}
        </div>
      )}
    </div>
  );
};

// Export all components for direct imports if needed
export * from "./AdminProfileForm";
export * from "./PlatformSettingsForm";
export * from "./NotificationPreferencesForm";
export * from "./TeamSection";
