import React from 'react';
import { format } from 'date-fns';
import { <PERSON>ader2, <PERSON><PERSON><PERSON>3, <PERSON><PERSON><PERSON> } from 'lucide-react';
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Pie, Cell, ResponsiveContainer, XAxis, YAxis, CartesianGrid, Tooltip as RechartsT<PERSON>tip, Legend, Sector } from 'recharts';
import { DailyMetric, EmailDistribution } from '@/services/emailTrackingService';

// Custom tooltip for the line chart
export const CustomLineTooltip = ({ active, payload, label }: any) => {
  if (active && payload && payload.length) {
    return (
      <div className="bg-white p-3 border rounded-md shadow-md">
        <p className="font-medium">{label}</p>
        {payload.map((entry: any, index: number) => (
          <p key={`item-${index}`} style={{ color: entry.color }}>
            {entry.name}: {entry.value}
          </p>
        ))}
      </div>
    );
  }
  return null;
};

// Custom active shape for pie chart
export const renderActiveShape = (props: any) => {
  const { cx, cy, midAngle, innerRadius, outerRadius, startAngle, endAngle, fill, payload, percent, value } = props;
  const sin = Math.sin(-midAngle * Math.PI / 180);
  const cos = Math.cos(-midAngle * Math.PI / 180);
  const sx = cx + (outerRadius + 10) * cos;
  const sy = cy + (outerRadius + 10) * sin;
  const mx = cx + (outerRadius + 30) * cos;
  const my = cy + (outerRadius + 30) * sin;
  const ex = mx + (cos >= 0 ? 1 : -1) * 22;
  const ey = my;
  const textAnchor = cos >= 0 ? 'start' : 'end';

  return (
    <g>
      <text x={cx} y={cy} dy={8} textAnchor="middle" fill={fill}>
        {payload.name}
      </text>
      <Sector
        cx={cx}
        cy={cy}
        innerRadius={innerRadius}
        outerRadius={outerRadius}
        startAngle={startAngle}
        endAngle={endAngle}
        fill={fill}
      />
      <Sector
        cx={cx}
        cy={cy}
        startAngle={startAngle}
        endAngle={endAngle}
        innerRadius={outerRadius + 6}
        outerRadius={outerRadius + 10}
        fill={fill}
      />
      <path d={`M${sx},${sy}L${mx},${my}L${ex},${ey}`} stroke={fill} fill="none" />
      <circle cx={ex} cy={ey} r={2} fill={fill} stroke="none" />
      <text x={ex + (cos >= 0 ? 1 : -1) * 12} y={ey} textAnchor={textAnchor} fill="#333">{`${value}`}</text>
      <text x={ex + (cos >= 0 ? 1 : -1) * 12} y={ey} dy={18} textAnchor={textAnchor} fill="#999">
        {`(${payload.percentage ? payload.percentage.toFixed(2) : (percent * 100).toFixed(2)}%)`}
      </text>
    </g>
  );
};

interface EmailPerformanceChartProps {
  dailyMetrics: DailyMetric[];
  loading: boolean;
}

export const EmailPerformanceChart: React.FC<EmailPerformanceChartProps> = ({ dailyMetrics, loading }) => {
  return (
    <div className="h-[300px]">
      {loading ? (
        <div className="flex items-center justify-center h-full">
          <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
        </div>
      ) : dailyMetrics.length === 0 ? (
        <div className="flex flex-col items-center justify-center h-full text-center">
          <BarChart3 className="h-12 w-12 text-muted-foreground mb-4" />
          <p className="text-muted-foreground">No data available for the selected period</p>
        </div>
      ) : (
        <ResponsiveContainer width="100%" height="100%">
          <LineChart
            data={dailyMetrics.map(metric => ({
              date: format(new Date(metric.date), 'MMM dd'),
              sent: metric.sent,
              opened: metric.opened,
              clicked: metric.clicked
            }))}
            margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
          >
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="date" />
            <YAxis />
            <RechartsTooltip content={<CustomLineTooltip />} />
            <Legend />
            <Line type="monotone" dataKey="sent" stroke="#3b82f6" activeDot={{ r: 8 }} />
            <Line type="monotone" dataKey="opened" stroke="#22c55e" />
            <Line type="monotone" dataKey="clicked" stroke="#a855f7" />
          </LineChart>
        </ResponsiveContainer>
      )}
    </div>
  );
};

interface EmailDistributionChartProps {
  emailDistribution: {
    total: number;
    distribution: EmailDistribution[];
  } | null;
  loading: boolean;
}

export const EmailDistributionChart: React.FC<EmailDistributionChartProps> = ({ emailDistribution, loading }) => {
  return (
    <div className="h-[300px]">
      {loading ? (
        <div className="flex items-center justify-center h-full">
          <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
        </div>
      ) : !emailDistribution || emailDistribution.distribution.length === 0 ? (
        <div className="flex flex-col items-center justify-center h-full text-center">
          <PieChart className="h-12 w-12 text-muted-foreground mb-4" />
          <p className="text-muted-foreground">No distribution data available</p>
        </div>
      ) : (
        <ResponsiveContainer width="100%" height="100%">
          <RechartsPieChart>
            <Pie
              activeIndex={0}
              activeShape={renderActiveShape}
              data={emailDistribution.distribution.map(item => ({
                name: item.status.charAt(0).toUpperCase() + item.status.slice(1),
                value: item.count,
                percentage: item.percentage
              }))}
              cx="50%"
              cy="50%"
              innerRadius={60}
              outerRadius={80}
              fill="#8884d8"
              dataKey="value"
            >
              {emailDistribution.distribution.map((entry, index) => {
                const colors = ['#3b82f6', '#22c55e', '#a855f7', '#f59e0b', '#ef4444', '#6b7280'];
                return <Cell key={`cell-${index}`} fill={colors[index % colors.length]} />;
              })}
            </Pie>
          </RechartsPieChart>
        </ResponsiveContainer>
      )}
    </div>
  );
};