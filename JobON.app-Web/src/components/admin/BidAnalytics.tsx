import React, { useState, useEffect, use<PERSON><PERSON><PERSON>, use<PERSON>emo } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';

import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { useToast } from '@/hooks/use-toast';
import { getBidStatistics } from '@/services/bidService';
import { BidStatistics } from '@/types/bid';
import { useAuth } from '@/features/auth/hooks/useAuth';
import {
  TrendingUp,
  TrendingDown,
  DollarSign,
  Users,
  Clock,
  CheckCircle,
  XCircle,
  AlertCircle,
  BarChart3,
  PieC<PERSON>,

  Download,
  Loader2,
  RefreshCw
} from 'lucide-react';
import {
  LineChart,
  Line,
  AreaChart,
  Area,
  BarChart,
  Bar,
  <PERSON><PERSON><PERSON> as Recha<PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>ianG<PERSON>,
  <PERSON><PERSON><PERSON>,
  Legend,
  ResponsiveContainer
} from 'recharts';

interface BidAnalyticsProps {
  className?: string;
}

interface ChartData {
  name: string;
  value: number;
  percentage?: number;
  color?: string;
}

interface TrendData {
  period: string;
  totalBids: number;
  acceptedBids: number;
  rejectedBids: number;
  pendingBids: number;
  averageAmount: number;
  acceptanceRate: number;
}

const COLORS = {
  primary: '#3b82f6',
  success: '#10b981',
  warning: '#f59e0b',
  danger: '#ef4444',
  secondary: '#6b7280',
  accent: '#8b5cf6'
};

const STATUS_COLORS = {
  pending: COLORS.warning,
  accepted: COLORS.success,
  rejected: COLORS.danger,
  withdrawn: COLORS.secondary
};

export const BidAnalytics: React.FC<BidAnalyticsProps> = ({ className }) => {
  const [statistics, setStatistics] = useState<BidStatistics | null>(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [timeRange, setTimeRange] = useState<'7d' | '30d' | '90d' | '1y'>('30d');
  const [activeTab, setActiveTab] = useState('overview');
  const { toast } = useToast();
  const { token } = useAuth();

  // Mock trend data for charts
  const [trendData, setTrendData] = useState<TrendData[]>([]);

  const getDateRange = useCallback((range: '7d' | '30d' | '90d' | '1y') => {
    const now = new Date();
    const endDate = now.toISOString();
    let startDate: string;

    switch (range) {
      case '7d':
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000).toISOString();
        break;
      case '30d':
        startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000).toISOString();
        break;
      case '90d':
        startDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000).toISOString();
        break;
      case '1y':
        startDate = new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000).toISOString();
        break;
      default:
        startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000).toISOString();
    }

    return { startDate, endDate };
  }, []);

  const generateTrendData = useCallback((stats: BidStatistics) => {
    // Generate mock trend data based on current statistics
    const periods = [];
    const now = new Date();

    for (let i = 29; i >= 0; i--) {
      const date = new Date(now);
      date.setDate(date.getDate() - i);

      // Generate realistic trend data
      const baseTotal = Math.floor(stats.totalBids / 30);
      const variation = Math.floor(Math.random() * baseTotal * 0.5);
      const totalBids = Math.max(0, baseTotal + variation - baseTotal * 0.25);

      const acceptanceRate = 0.3 + Math.random() * 0.4; // 30-70%
      const acceptedBids = Math.floor(totalBids * acceptanceRate);
      const rejectedBids = Math.floor(totalBids * 0.3);
      const pendingBids = totalBids - acceptedBids - rejectedBids;

      periods.push({
        period: date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' }),
        totalBids,
        acceptedBids,
        rejectedBids,
        pendingBids,
        averageAmount: stats.averageBidAmount * (0.8 + Math.random() * 0.4),
        acceptanceRate: acceptanceRate * 100
      });
    }

    setTrendData(periods);
  }, []);

  const fetchStatistics = useCallback(async (showRefreshLoader = false) => {
    // Prevent multiple simultaneous calls
    if (loading || refreshing) return;

    if (showRefreshLoader) {
      setRefreshing(true);
    } else {
      setLoading(true);
    }

    try {
      const { startDate, endDate } = getDateRange(timeRange);
      const response = await getBidStatistics({
        dateFrom: startDate,
        dateTo: endDate,
      },
      token || ''
    );

      if (response.isSuccess && response.data) {
        setStatistics(response.data);
        generateTrendData(response.data);
      } else {
        toast({
          title: 'Error',
          description: 'Failed to fetch bid analytics',
          variant: 'destructive'
        });
      }
    } catch (error) {
      console.error('Failed to fetch statistics:', error);
      toast({
        title: 'Error',
        description: 'Failed to fetch bid analytics',
        variant: 'destructive'
      });
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  }, [timeRange, token, getDateRange, generateTrendData, toast, loading, refreshing]);

  useEffect(() => {
    fetchStatistics();
  }, [fetchStatistics]);

  const statusDistribution = useMemo((): ChartData[] => {
    if (!statistics) return [];

    return [
      {
        name: 'Pending',
        value: statistics.pendingBids,
        percentage: (statistics.pendingBids / statistics.totalBids) * 100,
        color: STATUS_COLORS.pending
      },
      {
        name: 'Accepted',
        value: statistics.acceptedBids,
        percentage: (statistics.acceptedBids / statistics.totalBids) * 100,
        color: STATUS_COLORS.accepted
      },
      {
        name: 'Rejected',
        value: statistics.rejectedBids,
        percentage: (statistics.rejectedBids / statistics.totalBids) * 100,
        color: STATUS_COLORS.rejected
      },
      {
        name: 'Withdrawn',
        value: statistics.withdrawnBids || 0,
        percentage: ((statistics.withdrawnBids || 0) / statistics.totalBids) * 100,
        color: STATUS_COLORS.withdrawn
      }
    ].filter(item => item.value > 0);
  }, [statistics]);

  const formatCurrency = useCallback((amount: number): string => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  }, []);

  const formatPercentage = useCallback((value: number): string => {
    return `${value.toFixed(1)}%`;
  }, []);

  const exportData = useCallback(() => {
    if (!statistics) return;

    const data = {
      statistics,
      trendData,
      exportedAt: new Date().toISOString(),
      timeRange
    };

    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `bid-analytics-${timeRange}-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    toast({
      title: 'Success',
      description: 'Analytics data exported successfully'
    });
  }, [statistics, trendData, timeRange, toast]);

  if (loading) {
    return (
      <Card className={className}>
        <CardContent className="flex justify-center items-center h-96">
          <Loader2 className="h-8 w-8 animate-spin" />
        </CardContent>
      </Card>
    );
  }

  if (!statistics) {
    return (
      <Card className={className}>
        <CardContent className="flex justify-center items-center h-96">
          <div className="text-center">
            <AlertCircle className="h-8 w-8 text-gray-400 mx-auto mb-2" />
            <p className="text-gray-500">Failed to load analytics data</p>
            <Button onClick={() => fetchStatistics()} className="mt-4">
              Try Again
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Bid Analytics</h2>
          <p className="text-gray-600">Comprehensive insights into bidding activity</p>
        </div>
        <div className="flex items-center gap-3">
          <Select value={timeRange} onValueChange={(value: any) => setTimeRange(value)}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="7d">Last 7 days</SelectItem>
              <SelectItem value="30d">Last 30 days</SelectItem>
              <SelectItem value="90d">Last 90 days</SelectItem>
              <SelectItem value="1y">Last year</SelectItem>
            </SelectContent>
          </Select>
          <Button
            variant="outline"
            size="sm"
            onClick={() => fetchStatistics(true)}
            disabled={refreshing}
          >
            {refreshing ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <RefreshCw className="h-4 w-4" />
            )}
          </Button>
          <Button variant="outline" size="sm" onClick={exportData}>
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Bids</p>
                <p className="text-2xl font-bold text-gray-900">{statistics.totalBids.toLocaleString()}</p>
              </div>
              <div className="p-3 bg-blue-100 rounded-full">
                <BarChart3 className="h-6 w-6 text-blue-600" />
              </div>
            </div>
            <div className="mt-4 flex items-center">
              <TrendingUp className="h-4 w-4 text-green-500 mr-1" />
              <span className="text-sm text-green-600">+12.5% from last period</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Acceptance Rate</p>
                <p className="text-2xl font-bold text-gray-900">
                  {formatPercentage((statistics.acceptedBids / statistics.totalBids) * 100)}
                </p>
              </div>
              <div className="p-3 bg-green-100 rounded-full">
                <CheckCircle className="h-6 w-6 text-green-600" />
              </div>
            </div>
            <div className="mt-4 flex items-center">
              <TrendingUp className="h-4 w-4 text-green-500 mr-1" />
              <span className="text-sm text-green-600">+3.2% from last period</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Average Bid</p>
                <p className="text-2xl font-bold text-gray-900">
                  {formatCurrency(statistics.averageBidAmount)}
                </p>
              </div>
              <div className="p-3 bg-yellow-100 rounded-full">
                <DollarSign className="h-6 w-6 text-yellow-600" />
              </div>
            </div>
            <div className="mt-4 flex items-center">
              <TrendingDown className="h-4 w-4 text-red-500 mr-1" />
              <span className="text-sm text-red-600">-2.1% from last period</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Bid Value</p>
                <p className="text-2xl font-bold text-gray-900">{formatCurrency(statistics.totalBidValue)}</p>
              </div>
              <div className="p-3 bg-purple-100 rounded-full">
                <Users className="h-6 w-6 text-purple-600" />
              </div>
            </div>
            <div className="mt-4 flex items-center">
              <TrendingUp className="h-4 w-4 text-green-500 mr-1" />
              <span className="text-sm text-green-600">+8.7% from last period</span>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Charts */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="trends">Trends</TabsTrigger>
          <TabsTrigger value="distribution">Distribution</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Bid Status Distribution */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <PieChart className="h-5 w-5" />
                  Bid Status Distribution
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <RechartsPieChart>
                    <Pie
                      data={statusDistribution}
                      cx="50%"
                      cy="50%"
                      innerRadius={60}
                      outerRadius={100}
                      paddingAngle={5}
                      dataKey="value"
                    >
                      {statusDistribution.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.color} />
                      ))}
                    </Pie>
                    <Tooltip formatter={(value: number) => [value, 'Bids']} />
                    <Legend />
                  </RechartsPieChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            {/* Recent Activity */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Clock className="h-5 w-5" />
                  Recent Activity
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
                    <div className="flex items-center gap-3">
                      <CheckCircle className="h-5 w-5 text-green-600" />
                      <div>
                        <p className="font-medium text-green-900">Bid Accepted</p>
                        <p className="text-sm text-green-700">Kitchen renovation project</p>
                      </div>
                    </div>
                    <span className="text-sm text-green-600">2 min ago</span>
                  </div>
                  
                  <div className="flex items-center justify-between p-3 bg-blue-50 rounded-lg">
                    <div className="flex items-center gap-3">
                      <DollarSign className="h-5 w-5 text-blue-600" />
                      <div>
                        <p className="font-medium text-blue-900">New Bid Submitted</p>
                        <p className="text-sm text-blue-700">Bathroom plumbing repair</p>
                      </div>
                    </div>
                    <span className="text-sm text-blue-600">5 min ago</span>
                  </div>
                  
                  <div className="flex items-center justify-between p-3 bg-red-50 rounded-lg">
                    <div className="flex items-center gap-3">
                      <XCircle className="h-5 w-5 text-red-600" />
                      <div>
                        <p className="font-medium text-red-900">Bid Rejected</p>
                        <p className="text-sm text-red-700">Garden landscaping</p>
                      </div>
                    </div>
                    <span className="text-sm text-red-600">12 min ago</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="trends" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Bid Volume Trends</CardTitle>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={400}>
                <AreaChart data={trendData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="period" />
                  <YAxis />
                  <Tooltip />
                  <Legend />
                  <Area
                    type="monotone"
                    dataKey="totalBids"
                    stackId="1"
                    stroke={COLORS.primary}
                    fill={COLORS.primary}
                    fillOpacity={0.6}
                    name="Total Bids"
                  />
                  <Area
                    type="monotone"
                    dataKey="acceptedBids"
                    stackId="2"
                    stroke={COLORS.success}
                    fill={COLORS.success}
                    fillOpacity={0.6}
                    name="Accepted"
                  />
                  <Area
                    type="monotone"
                    dataKey="rejectedBids"
                    stackId="3"
                    stroke={COLORS.danger}
                    fill={COLORS.danger}
                    fillOpacity={0.6}
                    name="Rejected"
                  />
                </AreaChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Acceptance Rate Trend</CardTitle>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <LineChart data={trendData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="period" />
                  <YAxis domain={[0, 100]} />
                  <Tooltip formatter={(value: number) => [`${value.toFixed(1)}%`, 'Acceptance Rate']} />
                  <Line
                    type="monotone"
                    dataKey="acceptanceRate"
                    stroke={COLORS.success}
                    strokeWidth={3}
                    dot={{ fill: COLORS.success, strokeWidth: 2, r: 4 }}
                  />
                </LineChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="distribution" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Bid Amount Distribution</CardTitle>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={400}>
                <BarChart data={trendData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="period" />
                  <YAxis />
                  <Tooltip formatter={(value: number) => [formatCurrency(value), 'Average Amount']} />
                  <Bar dataKey="averageAmount" fill={COLORS.accent} />
                </BarChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};