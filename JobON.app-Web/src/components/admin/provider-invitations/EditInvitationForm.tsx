
import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/sheet";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectGroup, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useToast } from "@/hooks/use-toast";

interface ProviderInvitation {
  id: string;
  businessName: string;
  email: string;
  phone?: string;
  serviceType: string;
  location?: string;
  invitationCode: string;
  status: "draft" | "sent" | "opened" | "signed_up" | "logged_in";
  sentDate?: string;
  openedDate?: string;
  signupDate?: string;
  loginDate?: string;
}

interface EditInvitationFormProps {
  invitation: ProviderInvitation | null;
  isOpen: boolean;
  onClose: () => void;
  onSave: (updatedInvitation: ProviderInvitation) => void;
}

const SERVICE_TYPES = [
  "Plumbing",
  "HVAC",
  "Electrical",
  "Roofing",
  "Solar",
  "Landscaping",
  "Cleaning",
  "Handyman",
  "Pest Control",
  "Appliance Repair"
];

export const EditInvitationForm = ({ invitation, isOpen, onClose, onSave }: EditInvitationFormProps) => {
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);
  const [formData, setFormData] = useState<Partial<ProviderInvitation>>(invitation || {});
  const [showCodeWarning, setShowCodeWarning] = useState(false);

  const handleChange = (field: keyof ProviderInvitation, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (field === 'invitationCode' && value !== invitation?.invitationCode) {
      setShowCodeWarning(true);
    } else if (field === 'invitationCode') {
      setShowCodeWarning(false);
    }
  };

  const getAvailableStatuses = (currentStatus: string) => {
    const statusFlow = {
      draft: ["draft", "sent"],
      sent: ["sent", "opened"],
      opened: ["opened", "signed_up"],
      signed_up: ["signed_up", "logged_in"],
      logged_in: ["logged_in"]
    };
    return statusFlow[currentStatus as keyof typeof statusFlow] || ["draft"];
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!formData.businessName || !formData.email || !formData.serviceType) {
      toast({
        title: "Validation Error",
        description: "Please fill in all required fields",
        variant: "destructive"
      });
      return;
    }

    setIsLoading(true);
    try {
      // In a real implementation, this would be an API call
      await new Promise(resolve => setTimeout(resolve, 1000)); // Simulating API call
      if (invitation?.id) {
        onSave({ ...invitation, ...formData } as ProviderInvitation);
      }
      toast({
        title: "Success",
        description: "Provider invitation updated successfully",
      });
      onClose();
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to update provider invitation",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Sheet open={isOpen} onOpenChange={onClose}>
      <SheetContent className="sm:max-w-[450px]">
        <SheetHeader>
          <SheetTitle>Edit Provider Invitation</SheetTitle>
        </SheetHeader>
        <form onSubmit={handleSubmit} className="space-y-6 mt-6">
          <div className="space-y-4">
            <div className="space-y-2">
              <label className="text-sm font-medium">Business Name*</label>
              <Input
                value={formData.businessName || ''}
                onChange={(e) => handleChange('businessName', e.target.value)}
                placeholder="Enter business name"
                required
              />
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">Email Address*</label>
              <Input
                type="email"
                value={formData.email || ''}
                onChange={(e) => handleChange('email', e.target.value)}
                placeholder="Enter email address"
                required
              />
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">Service Type*</label>
              <Select
                value={formData.serviceType}
                onValueChange={(value) => handleChange('serviceType', value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select service type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectGroup>
                    {SERVICE_TYPES.map((type) => (
                      <SelectItem key={type} value={type}>
                        {type}
                      </SelectItem>
                    ))}
                  </SelectGroup>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">Invitation Code</label>
              <Input
                value={formData.invitationCode || ''}
                onChange={(e) => handleChange('invitationCode', e.target.value)}
                placeholder="Enter invitation code"
              />
              {showCodeWarning && (
                <p className="text-yellow-600 text-sm">
                  ⚠️ Changing the invitation code may affect tracking. Proceed carefully.
                </p>
              )}
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">Status</label>
              <Select
                value={formData.status}
                onValueChange={(value) => handleChange('status', value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectGroup>
                    {invitation && getAvailableStatuses(invitation.status).map((status) => (
                      <SelectItem key={status} value={status}>
                        {status.replace('_', ' ').charAt(0).toUpperCase() + status.slice(1)}
                      </SelectItem>
                    ))}
                  </SelectGroup>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2">
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
              disabled={isLoading}
            >
              Cancel
            </Button>
            <Button type="submit" disabled={isLoading}>
              {isLoading ? "Saving..." : "Save Changes"}
            </Button>
          </div>
        </form>
      </SheetContent>
    </Sheet>
  );
};
