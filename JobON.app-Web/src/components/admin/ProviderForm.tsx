import React, { useState, useEffect } from 'react';
import useAuth<PERSON>eader from 'react-auth-kit/hooks/useAuthHeader';
import { nullToUndefined } from '@/utils/typeHelpers';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import * as z from 'zod';
import { toast } from 'sonner';
import { Loader2 } from 'lucide-react';

import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { providerService, Provider, CreateProviderRequest, UpdateProviderRequest } from '@/services/providerService';

// Form schema for validation
const createProviderSchema = z.object({
  name: z.string().min(2, { message: 'Name must be at least 2 characters' }),
  email: z.string().email({ message: 'Please enter a valid email address' }),
  phone: z.string().optional(),
  password: z.string().min(8, { message: 'Password must be at least 8 characters' }),
  password_confirmation: z.string().min(8, { message: 'Password confirmation must be at least 8 characters' }),
  role_id: z.coerce.number(),
}).refine(data => data.password === data.password_confirmation, {
  message: "Passwords don't match",
  path: ['password_confirmation'],
});

const updateProviderSchema = z.object({
  name: z.string().min(2, { message: 'Name must be at least 2 characters' }),
  email: z.string().email({ message: 'Please enter a valid email address' }),
  phone: z.string().optional(),
  password: z.string().min(8, { message: 'Password must be at least 8 characters' }).optional(),
  password_confirmation: z.string().min(8, { message: 'Password confirmation must be at least 8 characters' }).optional(),
  role_id: z.coerce.number(),
}).refine(data => !data.password || !data.password_confirmation || data.password === data.password_confirmation, {
  message: "Passwords don't match",
  path: ['password_confirmation'],
});

type CreateProviderFormValues = z.infer<typeof createProviderSchema>;
type UpdateProviderFormValues = z.infer<typeof updateProviderSchema>;

interface ProviderFormProps {
  provider?: Provider;
  onSuccess: () => void;
  onCancel: () => void;
}

export const ProviderForm: React.FC<ProviderFormProps> = ({
  provider,
  onSuccess,
  onCancel,
}) => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [roles, setRoles] = useState<{ id: number; name: string }[]>([]);
  const [specializations, setSpecializations] = useState<{ id: number; name: string }[]>([]);
  const [businessTypes, setBusinessTypes] = useState<{ id: number; name: string }[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const isEditMode = !!provider;
  const authHeader = useAuthHeader();

  // Initialize form with either create or update schema
  const form = useForm<CreateProviderFormValues | UpdateProviderFormValues>({
    resolver: zodResolver(isEditMode ? updateProviderSchema : createProviderSchema),
    defaultValues: {
      name: provider?.name || '',
      email: provider?.email || '',
      phone: provider?.phone || '',
      role_id: provider?.role_id || 3, // Default to provider role (3)
    },
  });

  // Fetch roles on component mount
  useEffect(() => {
    const fetchRoles = async () => {
      setIsLoading(true);
      try {
        const response = await providerService.getPublicRoles(nullToUndefined(authHeader) || '');
        if (response.isSuccess && response.data) {
          setRoles(response.data);
        } else {
          toast.error('Failed to load roles');
        }
      } catch (error) {
        console.error('Error fetching roles:', error);
        toast.error('Failed to load roles');
      } finally {
        setIsLoading(false);
      }
    };

    fetchRoles();
  }, [authHeader]);

  // Fetch specializations
  useEffect(() => {
    const fetchSpecializations = async () => {
      try {
        const response = await providerService.getSpecializations(nullToUndefined(authHeader) || '');
        const businessResponse = await providerService.getBusinessTypes(nullToUndefined(authHeader) || '');
        
        if (response.isSuccess && response.data) {
          setSpecializations(response.data.data);
        }
        
        if (businessResponse.isSuccess && businessResponse.data) {
          setBusinessTypes(businessResponse.data.data);
        }
      } catch (error) {
        console.error('Error fetching data:', error);
      }
    };

    fetchSpecializations();
  }, [authHeader]);

  const onSubmit = async (data: CreateProviderFormValues | UpdateProviderFormValues) => {
    setIsSubmitting(true);
    try {
      let response;

      if (isEditMode && provider) {
        // For update, only include fields that are provided
        const updateData: UpdateProviderRequest = {
          name: data.name,
          email: data.email,
          phone: data.phone,
          role_id: data.role_id,
        };

        // Only include password fields if they are provided
        if ('password' in data && data.password) {
          updateData.password = data.password;
          updateData.password_confirmation = data.password_confirmation;
        }

        response = await providerService.updateProvider(provider.id, updateData, nullToUndefined(authHeader) || '');
      } else {
        // For create, include all required fields
        response = await providerService.createProvider(data as CreateProviderRequest, nullToUndefined(authHeader) || '');
      }

      if (response.isSuccess) {
        toast.success(isEditMode ? 'Provider updated successfully' : 'Provider created successfully');
        onSuccess();
      } else {
        toast.error(response.error || 'An error occurred');
      }
    } catch (error) {
      console.error('Error submitting form:', error);
      toast.error('An unexpected error occurred');
    } finally {
      setIsSubmitting(false);
    }
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center p-8">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <FormField
          control={form.control}
          name="name"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Name</FormLabel>
              <FormControl>
                <Input placeholder="Provider Name" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="email"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Email</FormLabel>
              <FormControl>
                <Input type="email" placeholder="<EMAIL>" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="phone"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Phone (Optional)</FormLabel>
              <FormControl>
                <Input placeholder="Phone number" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="role_id"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Role</FormLabel>
              <Select
                onValueChange={(value) => field.onChange(parseInt(value))}
                defaultValue={field.value?.toString()}
              >
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder="Select a role" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  {roles.map((role) => (
                    <SelectItem key={role.id} value={role.id.toString()}>
                      {role.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />

        {(!isEditMode || form.watch('password')) && (
          <>
            <FormField
              control={form.control}
              name="password"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{isEditMode ? 'New Password (Optional)' : 'Password'}</FormLabel>
                  <FormControl>
                    <Input type="password" placeholder="••••••••" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="password_confirmation"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Confirm Password</FormLabel>
                  <FormControl>
                    <Input type="password" placeholder="••••••••" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </>
        )}

        <div className="flex justify-end space-x-2 pt-4">
          <Button variant="outline" type="button" onClick={onCancel}>
            Cancel
          </Button>
          <Button type="submit" disabled={isSubmitting}>
            {isSubmitting ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                {isEditMode ? 'Updating...' : 'Creating...'}
              </>
            ) : (
              <>{isEditMode ? 'Update Provider' : 'Create Provider'}</>
            )}
          </Button>
        </div>
      </form>
    </Form>
  );
};
