import React, { useEffect, useState } from 'react';
import { Link } from 'react-router-dom';
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle, DialogClose } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { CheckCircle, X, ArrowRight, BadgeCheck } from 'lucide-react';
interface FindJobsSignupDialogProps {
  delayInMs?: number;
}
const FindJobsSignupDialog: React.FC<FindJobsSignupDialogProps> = ({
  delayInMs = 7000
}) => {
  const [open, setOpen] = useState(false);
  useEffect(() => {
    // Show dialog after specified delay on jobs search page visit
    const timer = setTimeout(() => {
      setOpen(true);
    }, delayInMs);
    return () => clearTimeout(timer);
  }, [delayInMs]);
  const handleClose = () => setOpen(false);
  return <Dialog open={open} onOpenChange={setOpen}>
      <DialogContent className="sm:max-w-[600px] md:max-w-[750px] lg:max-w-[900px] p-0 overflow-hidden rounded-xl border-none shadow-2xl">
        <DialogClose className="absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground z-50">
          
          <span className="sr-only">Close</span>
        </DialogClose>
        
        <div className="grid grid-cols-1 md:grid-cols-2">
          {/* Content section - 50% */}
          <div className="p-6 md:p-8">
            <DialogHeader>
              <DialogTitle className="text-2xl md:text-3xl font-bold bg-gradient-to-r from-primary to-blue-600 bg-clip-text text-transparent">
                Get Matched with Better Jobs
              </DialogTitle>
            </DialogHeader>
            
            <div className="mt-4">
              <p className="text-muted-foreground text-base">
                Create your professional profile today and get matched with high-quality clients looking for your specific skills.
              </p>
              
              <div className="space-y-4 mt-6">
                <div className="flex items-start gap-3">
                  <CheckCircle className="h-5 w-5 text-green-500 shrink-0 mt-0.5" />
                  <span className="text-base">Get notified of new job opportunities</span>
                </div>
                <div className="flex items-start gap-3">
                  <CheckCircle className="h-5 w-5 text-green-500 shrink-0 mt-0.5" />
                  <span className="text-base">Showcase your skills and experience</span>
                </div>
                <div className="flex items-start gap-3">
                  <CheckCircle className="h-5 w-5 text-green-500 shrink-0 mt-0.5" />
                  <span className="text-base">Set your own rates and availability</span>
                </div>
              </div>
              
              <Card className="bg-primary/5 mt-6 p-3 border-none rounded-lg">
                <p className="text-center font-medium">
                  <span className="font-bold">Free professional profile</span> - No commitment
                </p>
              </Card>
              
              <div className="mt-6 flex flex-col space-y-3">
                <Button size="lg" className="w-full py-5 text-base font-medium rounded-lg shadow-lg shadow-primary/25 hover:shadow-primary/40 transition-all duration-300 relative group" asChild>
                  <Link to="/auth" className="flex items-center justify-center gap-2">
                    Create Your Profile
                    <ArrowRight className="h-4 w-4 transition-transform group-hover:translate-x-1" />
                  </Link>
                </Button>
                
                <Button variant="outline" className="w-full border-2 py-4 text-base hover:bg-muted/30" asChild>
                  <Link to="/pro-landing" onClick={handleClose}>Learn More</Link>
                </Button>
              </div>
              
              <div className="mt-4 text-center text-xs text-muted-foreground">
                By signing up, you agree to our{' '}
                <Link to="/terms" className="underline underline-offset-2 hover:text-primary transition-colors">Terms</Link> and{' '}
                <Link to="/privacy" className="underline underline-offset-2 hover:text-primary transition-colors">Privacy Policy</Link>
              </div>
            </div>
          </div>
          
          {/* Image section - 50% */}
          <div className="relative hidden md:block">
            <img src="/lovable-uploads/05d16b12-7db3-4f35-a7e3-ab600fe02ab7.png" alt="Professional using JobON" className="h-full w-full object-cover object-contain" />
            <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent"></div>
            <div className="absolute bottom-3 left-3 right-3 p-3 rounded-lg bg-white/90 backdrop-blur-sm">
              <p className="text-sm font-semibold">Join our network of professionals</p>
              <div className="flex items-center mt-1">
                <BadgeCheck className="h-4 w-4 text-primary mr-1" />
                <span className="text-xs text-muted-foreground">Verified clients seeking quality services</span>
              </div>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>;
};
export default FindJobsSignupDialog;