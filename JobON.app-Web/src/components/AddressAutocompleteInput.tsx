
import React, { useState } from 'react';
import { Input } from '@/components/ui/input';
import { Card } from '@/components/ui/card';
import { MapPin, Loader2, AlertCircle } from 'lucide-react';
import { useAddressAutocomplete } from '@/hooks/use-address-autocomplete';
import { MapboxTokenInput } from '@/components/MapboxTokenInput';

interface AddressResult {
  address: string;
  city: string;
  state: string;
  zipCode: string;
}

interface AddressAutocompleteInputProps {
  value: string;
  onAddressSelect: (address: AddressResult) => void;
  placeholder?: string;
  className?: string;
}

export const AddressAutocompleteInput: React.FC<AddressAutocompleteInputProps> = ({
  value,
  onAddressSelect,
  placeholder = "Start typing your address...",
  className = ""
}) => {
  const [showSuggestions, setShowSuggestions] = useState(false);
  
  const {
    query,
    setQuery,
    suggestions,
    isLoading,
    error,
    handleSelectAddress,
    isMapboxClientMissing
  } = useAddressAutocomplete({ debounceMs: 300 });

  // If Mapbox client is missing, render the MapboxTokenInput component
  if (isMapboxClientMissing) {
    return <MapboxTokenInput />;
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    setQuery(newValue);
    setShowSuggestions(true);
  };

  const handleSuggestionClick = (suggestion: AddressResult) => {
    handleSelectAddress(suggestion);
    onAddressSelect(suggestion);
    setShowSuggestions(false);
  };

  const handleInputFocus = () => {
    if (suggestions.length > 0) {
      setShowSuggestions(true);
    }
  };

  const handleInputBlur = () => {
    // Delay hiding suggestions to allow for clicks
    setTimeout(() => setShowSuggestions(false), 200);
  };

  return (
    <div className="relative">
      <div className="relative">
        <MapPin className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
        <Input
          type="text"
          value={query || value}
          onChange={handleInputChange}
          onFocus={handleInputFocus}
          onBlur={handleInputBlur}
          placeholder={placeholder}
          className={`pl-10 touch-target ${className}`}
        />
        {isLoading && (
          <Loader2 className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400 animate-spin" />
        )}
      </div>

      {showSuggestions && suggestions.length > 0 && (
        <Card className="absolute z-50 w-full mt-1 max-h-60 overflow-auto shadow-lg bg-white border">
          <div className="p-2">
            {suggestions.map((suggestion, index) => (
              <div
                key={index}
                className="flex items-center space-x-3 p-3 hover:bg-gray-50 cursor-pointer rounded-md transition-colors touch-target"
                onClick={() => handleSuggestionClick(suggestion)}
              >
                <MapPin className="h-4 w-4 text-gray-400 flex-shrink-0" />
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium text-gray-900 truncate">
                    {suggestion.address}
                  </p>
                  {suggestion.city && suggestion.state && (
                    <p className="text-xs text-gray-500">
                      {suggestion.city}, {suggestion.state} {suggestion.zipCode}
                    </p>
                  )}
                </div>
              </div>
            ))}
          </div>
        </Card>
      )}

      {error && (
        <div className="mt-2 flex items-center text-sm text-red-600">
          <AlertCircle className="h-4 w-4 mr-1" />
          {error}
        </div>
      )}
    </div>
  );
};
