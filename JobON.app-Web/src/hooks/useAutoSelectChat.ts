import { useEffect, useCallback } from 'react';
import { useSearchParams } from 'react-router-dom';
import { useChat } from './useChat';
import { Chat } from '@/types/chat';

/**
 * Custom hook for automatically selecting a chat based on URL parameters
 * 
 * This hook:
 * - Extracts userId from URL query parameters
 * - Finds the chat containing that user as a participant
 * - Automatically selects the chat when chats are loaded
 * - Handles edge cases like user not found or multiple chats
 * 
 * Usage:
 * - Navigate to /admin/messages?userId=123
 * - Navigate to /customer/messages?userId=456
 * - Navigate to /provider/messages?userId=789
 */
export const useAutoSelectChat = () => {
  const [searchParams, setSearchParams] = useSearchParams();
  const { chats, currentChat, dispatch, isLoading } = useChat();
  
  // Get userId from URL parameters
  const userIdParam = searchParams.get('userId');
  
  /**
   * Find chat by participant user ID
   * Returns the first chat that contains the specified user as a participant
   */
  const findChatByUserId = useCallback((userId: string, chatList: Chat[]): Chat | null => {
    if (!userId || !chatList || chatList.length === 0) {
      return null;
    }
    
    // Find chat where any participant has the matching ID
    const foundChat = chatList.find(chat => 
      chat.participants?.some(participant => 
        String(participant.id) === String(userId)
      )
    );
    
    return foundChat || null;
  }, []);
  
  /**
   * Auto-select chat based on userId parameter
   */
  const autoSelectChat = useCallback(() => {
    // Only proceed if we have a userId parameter and chats are loaded
    if (!userIdParam || isLoading || !chats || chats.length === 0) {
      return;
    }
    
    // Don't auto-select if a chat is already selected (unless it's different)
    const targetChat = findChatByUserId(userIdParam, chats);
    
    if (targetChat && targetChat.id !== currentChat?.id) {
      // Select the found chat
      dispatch({ type: 'SELECT_CHAT', payload: targetChat.id });
      
      // Optional: Remove userId from URL after selection to clean up URL
      // This prevents the auto-selection from happening again on refresh
      const newSearchParams = new URLSearchParams(searchParams);
      newSearchParams.delete('userId');
      setSearchParams(newSearchParams, { replace: true });
      
      console.log(`Auto-selected chat with user ${userIdParam}:`, targetChat);
    } else if (!targetChat && userIdParam) {
      // User ID provided but no chat found
      console.warn(`No chat found with user ID: ${userIdParam}`);
      
      // Optional: You could dispatch an error or show a notification here
      // dispatch({ type: 'SET_ERROR', payload: `No conversation found with user ${userIdParam}` });
    }
  }, [userIdParam, isLoading, chats, currentChat?.id, findChatByUserId, dispatch, searchParams, setSearchParams]);
  
  /**
   * Effect to trigger auto-selection when conditions are met
   */
  useEffect(() => {
    autoSelectChat();
  }, [autoSelectChat]);
  
  /**
   * Manual function to select chat by user ID
   * Can be called programmatically if needed
   */
  const selectChatByUserId = useCallback((userId: string) => {
    if (!chats || chats.length === 0) {
      console.warn('No chats available to search');
      return false;
    }
    
    const targetChat = findChatByUserId(userId, chats);
    
    if (targetChat) {
      dispatch({ type: 'SELECT_CHAT', payload: targetChat.id });
      console.log(`Manually selected chat with user ${userId}:`, targetChat);
      return true;
    } else {
      console.warn(`No chat found with user ID: ${userId}`);
      return false;
    }
  }, [chats, findChatByUserId, dispatch]);
  
  return {
    userIdParam,
    isAutoSelecting: !!userIdParam && isLoading,
    selectChatByUserId,
    findChatByUserId
  };
};

export default useAutoSelectChat;
