import { useState, useCallback } from 'react';
import { updateBidStatus } from '@/services/bidService';
import { Bid, UpdateBidStatusRequest } from '@/types/bid';
import { useAuth } from '@/features/auth/hooks/useAuth';
import { useToast } from '@/hooks/use-toast';

interface UseBidStatusUpdateOptions {
  onSuccess?: (updatedBid: Bid) => void;
  onError?: (error: string) => void;
}

interface UseBidStatusUpdateReturn {
  updateStatus: (bidId: string, status: 'accepted' | 'rejected') => Promise<boolean>;
  isLoading: boolean;
  error: string | null;
  clearError: () => void;
}

/**
 * Custom hook for updating bid status (accept/reject)
 * Implements Task 22 requirements with proper authentication and error handling
 * 
 * @param options - Configuration options for success/error callbacks
 * @returns Object with updateStatus function and state management
 */
export const useBidStatusUpdate = (
  options: UseBidStatusUpdateOptions = {}
): UseBidStatusUpdateReturn => {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  const { token, isCustomer, isAuthenticated } = useAuth();
  const { toast } = useToast();
  const { onSuccess, onError } = options;

  const clearError = useCallback(() => {
    setError(null);
  }, []);

  const updateStatus = useCallback(async (
    bidId: string,
    status: 'accepted' | 'rejected'
  ): Promise<boolean> => {
    // Clear any previous errors
    setError(null);

    // Validate authentication and role
    if (!isAuthenticated) {
      const errorMsg = 'You must be logged in to update bid status';
      setError(errorMsg);
      toast({
        title: 'Authentication Required',
        description: errorMsg,
        variant: 'destructive'
      });
      onError?.(errorMsg);
      return false;
    }

    if (!isCustomer) {
      const errorMsg = 'Only customers can accept or reject bids';
      setError(errorMsg);
      toast({
        title: 'Access Denied',
        description: errorMsg,
        variant: 'destructive'
      });
      onError?.(errorMsg);
      return false;
    }

    // Validate inputs
    if (!bidId || typeof bidId !== 'string') {
      const errorMsg = 'Valid bid ID is required';
      setError(errorMsg);
      toast({
        title: 'Invalid Input',
        description: errorMsg,
        variant: 'destructive'
      });
      onError?.(errorMsg);
      return false;
    }

    if (!['accepted', 'rejected'].includes(status)) {
      const errorMsg = 'Status must be either "accepted" or "rejected"';
      setError(errorMsg);
      toast({
        title: 'Invalid Status',
        description: errorMsg,
        variant: 'destructive'
      });
      onError?.(errorMsg);
      return false;
    }

    setIsLoading(true);

    try {
      const statusData: UpdateBidStatusRequest = { status };
      const response = await updateBidStatus(bidId, statusData, token || undefined);

      if (response.isSuccess && response.data) {
        // Success handling
        const successMessage = status === 'accepted' 
          ? 'Bid accepted successfully' 
          : 'Bid rejected successfully';
        
        toast({
          title: 'Success',
          description: successMessage,
        });

        onSuccess?.(response.data);
        return true;
      } else {
        // API error handling
        const errorMsg = response.error || `Failed to ${status} bid`;
        setError(errorMsg);
        toast({
          title: 'Update Failed',
          description: errorMsg,
          variant: 'destructive'
        });
        onError?.(errorMsg);
        return false;
      }
    } catch (error) {
      // Unexpected error handling
      const errorMsg = error instanceof Error 
        ? error.message 
        : 'An unexpected error occurred';
      
      setError(errorMsg);
      toast({
        title: 'Error',
        description: errorMsg,
        variant: 'destructive'
      });
      onError?.(errorMsg);
      return false;
    } finally {
      setIsLoading(false);
    }
  }, [isAuthenticated, isCustomer, token, toast, onSuccess, onError]);

  return {
    updateStatus,
    isLoading,
    error,
    clearError
  };
};
