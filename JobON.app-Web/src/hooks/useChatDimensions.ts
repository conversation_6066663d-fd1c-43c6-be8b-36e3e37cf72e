import { useIsMobile } from '@/hooks/use-mobile';
import { useLocation } from 'react-router-dom';

/**
 * Hook to calculate chat layout dimensions based on current layout context
 * Provides responsive dimensions for fixed chat positioning
 */
export const useChatDimensions = () => {
  const isMobile = useIsMobile();
  const location = useLocation();

  // Determine layout context based on current route
  const getLayoutContext = () => {
    const pathname = location.pathname;
    
    if (pathname.startsWith('/admin')) {
      return 'admin';
    } else if (pathname.startsWith('/provider')) {
      return 'provider';
    } else if (pathname.startsWith('/customer')) {
      return 'customer';
    }
    return 'general';
  };

  const layoutContext = getLayoutContext();

  // Calculate header height based on layout context
  const getHeaderHeight = () => {
    switch (layoutContext) {
      case 'admin':
        return 56; // AdminLayout header is h-14 (56px)
      case 'provider':
        return isMobile ? 64 : 64; // ProviderLayout varies
      case 'customer':
        return isMobile ? 72 : 64; // MobileCustomerHeader is taller
      default:
        return 64; // Standard Navbar height
    }
  };

  // Calculate footer height based on device and layout
  const getFooterHeight = () => {
    if (isMobile) {
      // Mobile layouts use MobileFooter (h-20 = 80px) or MobileNavigation
      return 80;
    }
    return 64; // Standard Footer height (h-16 = 64px)
  };

  // Chat component dimensions
  const chatHeaderHeight = 64; // Standard chat header height
  const chatInputHeight = 80; // Approximate chat input area height

  const headerHeight = getHeaderHeight();
  const footerHeight = getFooterHeight();

  return {
    // Layout dimensions
    headerHeight,
    footerHeight,
    chatHeaderHeight,
    chatInputHeight,
    
    // Calculated positions for fixed elements
    chatHeaderTop: headerHeight,
    chatInputBottom: footerHeight,
    
    // Content area margins
    contentMarginTop: headerHeight + chatHeaderHeight,
    contentMarginBottom: footerHeight + chatInputHeight,
    
    // Utility values
    isMobile,
    layoutContext,
    
    // Z-index values for proper layering
    zIndex: {
      chatHeader: 40,
      chatInput: 40,
      chatContent: 10,
    }
  };
};

export default useChatDimensions;
