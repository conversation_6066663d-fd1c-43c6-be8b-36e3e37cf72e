
import axios from 'axios';
import useAuthHeader from 'react-auth-kit/hooks/useAuthHeader';
import { BASE_API_URL } from '@/lib/constants';

/**
 * Creates an API client with authentication from react-auth-kit
 * This client automatically includes auth token from cookies in requests
 */
export const createApiClient = () => {
  const authHeader = useAuthHeader();

  const api = axios.create({
    baseURL: BASE_API_URL,
    headers: {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    },
    withCredentials: false, // Enable sending cookies with requests
  });

  // Add auth header to requests
  api.interceptors.request.use(config => {
    // Get the token directly from the authHeader hook
    const token = authHeader;
    if (token) {
      config.headers.Authorization = token;
    }
    return config;
  });

  // Handle response errors
  api.interceptors.response.use(
    response => response,
    error => {
      // Handle authentication errors
      if (error.response?.status === 401) {
        console.error('Authentication error:', error);
        // You could redirect to login page here if needed
      }
      return Promise.reject(error);
    }
  );

  return api;
};
