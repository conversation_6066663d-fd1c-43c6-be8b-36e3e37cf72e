
// API URLs
// In development, use a relative URL to work with the Vite proxy
// In production, use the environment variable or fallback to the production URL
export const BASE_API_URL = import.meta.env.DEV
  ? '' // Empty string means use relative URLs that will be handled by the proxy
  : (import.meta.env.VITE_API_URL || 'https://dash.jobon.app');

// Responsive breakpoints (in pixels)
export const BREAKPOINTS = {
  xs: 320,
  sm: 640,
  md: 768,
  lg: 1024,
  xl: 1280,
  '2xl': 1536
};

// Admin section page titles
export const ADMIN_PAGE_TITLES = {
  dashboard: "Dashboard",
  providers: "Manage Providers",
  "provider-invitations": "Provider Invitations",
  customers: "Manage Customers",
  jobs: "Manage Jobs",
  "job-oversight": "Job Oversight Dashboard",
  payments: "Manage Payments",
  reviews: "Manage Reviews",
  rewards: "Manage Rewards",
  referrals: "Manage Referrals",
  messages: "Messages Oversight",
  settings: "Settings",
  business: "Manage Business",
  bookings: "Manage Job Booking",
  "email-tracking": "Email Tracking Dashboard"
};
