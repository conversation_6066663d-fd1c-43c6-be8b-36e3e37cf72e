
import React, { useEffect } from 'react';
import { Layout } from '@/components/Layout';
import { ProWorkflow } from '@/components/ProWorkflow';
import { useNavigate } from 'react-router-dom';

const ProWorkflowPage = () => {
  const navigate = useNavigate();

  // Check if user is authenticated as a professional
  useEffect(() => {
    const isAuthenticated = localStorage.getItem('isAuthenticated') === 'true';
    const userType = localStorage.getItem('userType');
    
    if (!isAuthenticated || userType !== 'professional') {
      navigate('/auth');
    }
  }, [navigate]);

  const handleWorkflowComplete = () => {
    navigate('/jobs');
  };

  return (
    <Layout>
      <div className="pt-16">
        <ProWorkflow onComplete={handleWorkflowComplete} />
      </div>
    </Layout>
  );
};

export default ProWorkflowPage;
