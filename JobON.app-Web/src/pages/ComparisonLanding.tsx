
import React from 'react';
import { Layout } from '@/components/Layout';
import { SEO } from '@/components/SEO';
import { TestimonialsCarousel } from '@/components/TestimonialsCarousel';
import { Button } from '@/components/ui/button';
import { ArrowRight, Gavel, CreditCard, Award, Brain } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import ComparisonFeature from '@/components/ComparisonFeature';
import DifferentiatorCard from '@/components/DifferentiatorCard';
import TestimonialCard from '@/components/TestimonialCard';

const ComparisonLanding = () => {
  const navigate = useNavigate();

  const comparisonFeatures = [
    { feature: "Live Bidding", jobon: true, angi: false, thumbtack: false, taskrabbit: false },
    { feature: "Pay Only When You Win", jobon: true, angi: false, thumbtack: false, taskrabbit: false },
    { feature: "Book Now or Bid Later", jobon: true, angi: true, thumbtack: false, taskrabbit: false },
    { feature: "Buy Now Pay Later Available", jobon: true, angi: false, thumbtack: false, taskrabbit: false },
    { feature: "AI Smart Matching", jobon: true, angi: false, thumbtack: true, taskrabbit: false },
    { feature: "Local Trust Map", jobon: true, angi: true, thumbtack: false, taskrabbit: true },
    { feature: "Visual Portfolios", jobon: true, angi: true, thumbtack: true, taskrabbit: false },
  ];

  const differentiators = [
    {
      title: "Live Bidding = Best Price",
      description: "Our unique bidding system creates healthy competition among providers, ensuring you get the best service at the best price.",
      icon: <Gavel className="h-8 w-8 text-primary" />
    },
    {
      title: "BNPL Makes Big Jobs Easier",
      description: "Spread the cost of large projects with our flexible Buy Now Pay Later options, making home improvements more accessible.",
      icon: <CreditCard className="h-8 w-8 text-primary" />
    },
    {
      title: "Only Pay When You Win",
      description: "Service providers only pay a small fee when they win a job, not for leads or quotes that go nowhere.",
      icon: <Award className="h-8 w-8 text-primary" />
    },
    {
      title: "Smart Matching Powered by AI",
      description: "Our advanced AI connects you with the perfect service provider for your specific needs based on skills, availability, and ratings.",
      icon: <Brain className="h-8 w-8 text-primary" />
    }
  ];

  const testimonials = [
    {
      type: "Customer",
      quote: "JobON's bidding system saved me over $800 on my bathroom renovation. The ability to compare providers side by side made choosing the right one easy.",
      name: "Sarah K.",
      location: "Denver, CO",
      rating: 5
    },
    {
      type: "Provider",
      quote: "As a plumber, I've tried all the platforms. JobON has the lowest fees by far, and I only pay when I actually get the job. It's been a game changer for my business.",
      name: "Michael T.",
      location: "Austin, TX",
      rating: 5
    }
  ];

  return (
    <Layout fullWidth={true}>
      <SEO 
        title="JobON vs Angi, Thumbtack, TaskRabbit – Feature Comparison"
        description="Compare JobON with Angi, Thumbtack, and TaskRabbit to see how JobON delivers better pricing, smarter matching, and more flexibility for local services."
      />

      {/* Hero Section */}
      <section className="bg-gradient-to-r from-blue-50 to-blue-100 dark:from-gray-900 dark:to-gray-800">
        <div className="container mx-auto px-4 py-16 md:py-24">
          <div className="max-w-3xl mx-auto text-center">
            <h1 className="text-3xl md:text-5xl font-bold text-gray-900 dark:text-white mb-4">
              Compare JobON vs Angi, Thumbtack, and TaskRabbit
            </h1>
            <p className="text-lg md:text-xl text-gray-700 dark:text-gray-300 mb-8">
              Discover the smarter way to hire or offer local services
            </p>
            <div className="flex flex-col sm:flex-row justify-center gap-4">
              <Button size="xl" className="w-full sm:w-auto">
                Try JobON Free
                <ArrowRight className="ml-2 h-5 w-5" />
              </Button>
              <Button 
                size="xl" 
                variant="outline" 
                className="w-full sm:w-auto dark:border-gray-600 dark:hover:bg-gray-700"
                onClick={() => navigate('/jobs')}
              >
                View Live Demo
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Comparison Table */}
      <section className="py-16 md:py-24 bg-white dark:bg-gray-900">
        <div className="container mx-auto px-4">
          <h2 className="text-2xl md:text-4xl font-bold text-center text-gray-900 dark:text-white mb-12">
            How JobON Compares to Other Services
          </h2>
          
          <div className="overflow-x-auto">
            <table className="w-full border-collapse">
              <thead>
                <tr>
                  <th className="p-4 text-left border-b-2 dark:border-gray-700">Features</th>
                  <th className="p-4 text-center border-b-2 dark:border-gray-700 bg-blue-50 dark:bg-blue-900/20">
                    <span className="block text-primary font-bold text-xl">JobON</span>
                  </th>
                  <th className="p-4 text-center border-b-2 dark:border-gray-700">
                    <span className="block font-medium text-gray-600 dark:text-gray-300">Angi</span>
                  </th>
                  <th className="p-4 text-center border-b-2 dark:border-gray-700">
                    <span className="block font-medium text-gray-600 dark:text-gray-300">Thumbtack</span>
                  </th>
                  <th className="p-4 text-center border-b-2 dark:border-gray-700">
                    <span className="block font-medium text-gray-600 dark:text-gray-300">TaskRabbit</span>
                  </th>
                </tr>
              </thead>
              <tbody>
                {comparisonFeatures.map((item, index) => (
                  <ComparisonFeature 
                    key={index}
                    feature={item.feature}
                    jobon={item.jobon}
                    angi={item.angi}
                    thumbtack={item.thumbtack}
                    taskrabbit={item.taskrabbit}
                    index={index}
                  />
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </section>

      {/* What Makes JobON Different */}
      <section className="py-16 md:py-24 bg-gray-50 dark:bg-gray-800">
        <div className="container mx-auto px-4">
          <h2 className="text-2xl md:text-4xl font-bold text-center text-gray-900 dark:text-white mb-12">
            What Makes JobON Different
          </h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {differentiators.map((item, index) => (
              <DifferentiatorCard
                key={index}
                title={item.title}
                description={item.description}
                icon={item.icon}
              />
            ))}
          </div>
        </div>
      </section>

      {/* Testimonials */}
      <section className="py-16 md:py-24 bg-white dark:bg-gray-900">
        <div className="container mx-auto px-4">
          <h2 className="text-2xl md:text-4xl font-bold text-center text-gray-900 dark:text-white mb-12">
            Hear From Our Users
          </h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-4xl mx-auto">
            {testimonials.map((testimonial, index) => (
              <TestimonialCard
                key={index}
                type={testimonial.type}
                quote={testimonial.quote}
                name={testimonial.name}
                location={testimonial.location}
                rating={testimonial.rating}
              />
            ))}
          </div>
          
          {/* Full Testimonials Section */}
          <div className="mt-16">
            <h3 className="text-lg md:text-xl font-medium text-center text-gray-900 dark:text-white mb-6">
              More Success Stories From Our Community
            </h3>
            <TestimonialsCarousel />
          </div>
        </div>
      </section>

      {/* Call-to-Action */}
      <section className="py-16 md:py-24 bg-primary/10 dark:bg-primary/5">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-2xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
            Ready to Book or Earn? Start with JobON Today
          </h2>
          <p className="text-lg md:text-xl text-gray-700 dark:text-gray-300 mb-8 max-w-2xl mx-auto">
            Join thousands of satisfied customers and providers who are saving time and money with JobON.
          </p>
          <div className="flex flex-col sm:flex-row justify-center gap-4">
            <Button 
              size="xl" 
              className="w-full sm:w-auto"
              onClick={() => navigate('/create-job')}
            >
              Post a Job Now
            </Button>
            <Button 
              size="xl" 
              variant="outline" 
              className="w-full sm:w-auto dark:border-gray-600 dark:hover:bg-gray-700"
              onClick={() => navigate('/for-providers')}
            >
              Become a Provider
            </Button>
          </div>
        </div>
      </section>
    </Layout>
  );
};

export default ComparisonLanding;
