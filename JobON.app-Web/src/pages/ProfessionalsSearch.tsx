
// src/pages/PlumbingProfessionals.tsx
import React, { useEffect } from 'react';
import ProfessionalsPage from "@/components/ProfessionalsPage/ProfessionalsPage.tsx";
import { useGeolocation } from '@/hooks/use-geolocation';
import { useSearchParams, useNavigate } from 'react-router-dom';

const ProfessionalsSearch = () => {
  const { zipCode: detectedZipCode, loading } = useGeolocation();
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  
  // If we have a detected zipCode and there's no zip in the URL, add it
  useEffect(() => {
    if (detectedZipCode && !loading && !searchParams.has('zip')) {
      const newSearchParams = new URLSearchParams(searchParams);
      newSearchParams.set('zip', detectedZipCode);
      navigate(`?${newSearchParams.toString()}`, { replace: true });
    }
  }, [detectedZipCode, loading, searchParams, navigate]);

  return (
    <ProfessionalsPage
      serviceId=""
      serviceName="professionals"
      pageTitle="Find Trusted Service Professionals Near You | Compare Free Quotes"
      pageDescription="Easily find licensed and vetted professionals for plumbing, cleaning, HVAC, electrical, landscaping, and more. Compare bids, read real reviews, and connect with pros near you on JobON."
      gradientBackground="linear-gradient(to bottom, #eff6ff 0%, #ffffff 100%)"
    />
  );
};

export default ProfessionalsSearch;
