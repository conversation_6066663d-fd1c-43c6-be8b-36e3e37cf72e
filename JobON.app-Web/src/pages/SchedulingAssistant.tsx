import React, { useState } from 'react';
import { Layout } from '@/components/Layout';
import { Card, CardContent } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Tabs, Ta<PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { motion } from "framer-motion";
import { SEO } from '@/components/SEO';
import { Calendar, Clock, Users, CheckCircle, ArrowRight, CalendarDays, UserPlus, MessageSquare, Zap } from 'lucide-react';
import { Link } from 'react-router-dom';
import { Hero } from '@/components/Hero';
import { AuroraBackground } from '@/components/ui/aurora-background';

const SchedulingAssistant: React.FC = () => {
  const [activeTab, setActiveTab] = useState("features");

  // Animation variants
  const container = {
    hidden: { opacity: 0 },
    show: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };
  
  const item = {
    hidden: { opacity: 0, y: 20 },
    show: { opacity: 1, y: 0 }
  };

  const features = [
    {
      title: "Smart Scheduling",
      description: "Automatically find the best times for appointments based on your availability and preferences.",
      icon: <Calendar className="h-6 w-6" />
    },
    {
      title: "Client Self-booking",
      description: "Let clients book their own appointments through a customizable booking page.",
      icon: <UserPlus className="h-6 w-6" />
    },
    {
      title: "Automated Reminders",
      description: "Reduce no-shows with automated email and SMS reminders for upcoming appointments.",
      icon: <MessageSquare className="h-6 w-6" />
    },
    {
      title: "Calendar Sync",
      description: "Sync with Google Calendar, Outlook, and other calendar services to avoid double-booking.",
      icon: <CalendarDays className="h-6 w-6" />
    },
    {
      title: "Team Management",
      description: "Manage multiple team members' schedules with team-specific availability settings.",
      icon: <Users className="h-6 w-6" />
    },
    {
      title: "Real-time Availability",
      description: "Display real-time availability to clients with automatic updates when bookings occur.",
      icon: <Clock className="h-6 w-6" />
    }
  ];

  return (
    <Layout>
      <SEO 
        title="Free Scheduling Assistant Tool | JobON" 
        description="Streamline your service business scheduling with our free scheduling assistant tool. Manage appointments, send reminders, and let clients book online."
      />
      
      {/* Modern Hero Section */}
      <AuroraBackground className="pt-16 pb-20 relative overflow-hidden">
        <div className="container mx-auto px-4 md:px-8 relative z-10">
          <div className="flex flex-col lg:flex-row items-center gap-12 md:gap-16">
            <div className="w-full lg:w-1/2 text-center lg:text-left space-y-6">
              <motion.div 
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6 }}
              >
                <Badge variant="outline" className="mb-4 px-3 py-1 text-sm bg-primary/10 border-primary/30 text-primary dark:text-blue-300">
                  Free Online Tool
                </Badge>
                <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold leading-tight tracking-tight mb-4">
                  Scheduling <span className="text-primary dark:text-blue-300">Assistant</span>
                </h1>
                <p className="text-lg md:text-xl text-muted-foreground mb-8 max-w-xl mx-auto lg:mx-0">
                  Streamline your service business scheduling. Reduce no-shows, save time, and let clients book online 24/7.
                </p>
                
                <div className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start">
                  <Button size="lg" className="text-base px-8 py-6 h-auto rounded-xl shadow-lg hover:shadow-xl hover:translate-y-[-2px] transition-all">
                    Try It Free
                  </Button>
                  <Button variant="outline" size="lg" className="text-base px-8 py-6 h-auto rounded-xl border-2 hover:bg-primary/10 shadow-lg hover:shadow-xl hover:translate-y-[-2px] transition-all">
                    View Demo
                  </Button>
                </div>
                
                <div className="flex items-center justify-center lg:justify-start mt-8 space-x-4 text-sm text-muted-foreground">
                  <span className="flex items-center">
                    <CheckCircle className="h-4 w-4 mr-1.5 text-green-500" />
                    No credit card
                  </span>
                  <span className="flex items-center">
                    <CheckCircle className="h-4 w-4 mr-1.5 text-green-500" />
                    Free forever plan
                  </span>
                </div>
              </motion.div>
            </div>
            
            <motion.div
              className="w-full lg:w-1/2"
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.6, delay: 0.2 }}
            >
              <div className="relative">
                <div className="absolute inset-0 bg-primary/10 dark:bg-primary/20 rounded-full filter blur-xl opacity-70"></div>
                <div className="relative bg-white dark:bg-gray-800 rounded-2xl p-5 shadow-xl">
                  <img 
                    src="/lovable-uploads/7e358001-5e4d-4674-80aa-19dd13048dc1.png" 
                    alt="Scheduling Assistant Dashboard" 
                    className="w-full h-auto rounded-xl shadow-lg"
                  />
                  
                  <div className="absolute -bottom-6 -right-6 bg-white dark:bg-gray-800 rounded-2xl p-4 shadow-lg border border-gray-100 dark:border-gray-700">
                    <div className="flex items-center gap-3">
                      <div className="w-12 h-12 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center">
                        <CheckCircle className="h-6 w-6 text-green-600 dark:text-green-400" />
                      </div>
                      <div>
                        <p className="font-medium">Appointment Confirmed</p>
                        <p className="text-sm text-muted-foreground">Thursday, 3:30 PM</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </motion.div>
          </div>
          
          {/* Trust indicators */}
          <div className="mt-16 pt-8 border-t border-gray-200 dark:border-gray-800">
            <div className="flex flex-wrap justify-center gap-8 md:gap-16">
              <div className="flex flex-col items-center">
                <p className="text-3xl md:text-4xl font-bold text-primary dark:text-blue-300">10,000+</p>
                <p className="text-sm text-muted-foreground">Active Users</p>
              </div>
              <div className="flex flex-col items-center">
                <p className="text-3xl md:text-4xl font-bold text-primary dark:text-blue-300">1M+</p>
                <p className="text-sm text-muted-foreground">Appointments Booked</p>
              </div>
              <div className="flex flex-col items-center">
                <div className="flex text-amber-400">
                  {[1, 2, 3, 4, 5].map((_, i) => (
                    <svg key={i} xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                      <polygon points="12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2" />
                    </svg>
                  ))}
                </div>
                <p className="text-sm text-muted-foreground">4.9/5 Rating</p>
              </div>
            </div>
          </div>
        </div>
      </AuroraBackground>

      {/* Features Section with Tabs */}
      <section className="py-20 bg-white dark:bg-gray-800">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-5xl font-bold mb-4">
              Everything You Need to Manage Appointments
            </h2>
            <p className="text-lg text-muted-foreground max-w-3xl mx-auto">
              Our scheduling assistant is packed with features to help service professionals manage their time more efficiently.
            </p>
          </div>

          <Tabs 
            defaultValue="features" 
            className="w-full max-w-5xl mx-auto"
            onValueChange={setActiveTab}
          >
            <div className="flex justify-center mb-12">
              <TabsList className="grid w-full max-w-md grid-cols-3">
                <TabsTrigger value="features" className="text-base py-3">Features</TabsTrigger>
                <TabsTrigger value="business" className="text-base py-3">For Business</TabsTrigger>
                <TabsTrigger value="client" className="text-base py-3">For Clients</TabsTrigger>
              </TabsList>
            </div>

            <TabsContent value="features">
              <motion.div
                variants={container}
                initial="hidden"
                animate={activeTab === "features" ? "show" : "hidden"}
                className="grid grid-cols-1 md:grid-cols-3 gap-8"
              >
                {features.map((feature, index) => (
                  <motion.div variants={item} key={index} whileHover={{ y: -5 }} transition={{ type: "spring", stiffness: 300 }}>
                    <Card className="h-full border-2 hover:border-primary/20 transition-all duration-300 hover:shadow-lg overflow-hidden">
                      <CardContent className="p-8">
                        <div className="w-14 h-14 rounded-xl bg-primary/10 flex items-center justify-center mb-6">
                          {React.cloneElement(feature.icon as React.ReactElement, { 
                            className: "h-7 w-7 text-primary dark:text-blue-300" 
                          })}
                        </div>
                        <h3 className="text-xl font-bold mb-3">{feature.title}</h3>
                        <p className="text-muted-foreground">{feature.description}</p>
                      </CardContent>
                    </Card>
                  </motion.div>
                ))}
              </motion.div>
            </TabsContent>

            <TabsContent value="business">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
                <motion.div
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.5 }}
                >
                  <h3 className="text-2xl md:text-3xl font-bold mb-6">Designed for Service Professionals</h3>
                  <p className="text-lg text-muted-foreground mb-8">
                    Take control of your time and streamline your scheduling workflow with tools built specifically for service businesses.
                  </p>
                  
                  <div className="space-y-6">
                    {[
                      {
                        title: "Custom Service Types",
                        description: "Define different service types with specific durations and pricing."
                      },
                      {
                        title: "Buffer Time",
                        description: "Add buffer time between appointments to prepare for your next client."
                      },
                      {
                        title: "Client Management",
                        description: "Build a client database with appointment history and notes."
                      }
                    ].map((item, index) => (
                      <div key={index} className="flex items-start gap-4">
                        <div className="mt-1 w-8 h-8 rounded-full bg-green-100 dark:bg-green-900/30 flex items-center justify-center flex-shrink-0">
                          <svg className="w-5 h-5 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="3" d="M5 13l4 4L19 7"></path>
                          </svg>
                        </div>
                        <div>
                          <h4 className="text-lg font-semibold">{item.title}</h4>
                          <p className="text-muted-foreground">{item.description}</p>
                        </div>
                      </div>
                    ))}
                  </div>
                  
                  <Button className="mt-10" size="lg">
                    Learn More About Business Features
                  </Button>
                </motion.div>
                
                <motion.div 
                  className="bg-gray-50 dark:bg-gray-700 p-8 rounded-2xl shadow-lg"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: 0.2 }}
                >
                  <img 
                    src="/lovable-uploads/81ca20dd-aa4c-417f-b474-3c7e9f872d9c.png" 
                    alt="Business scheduling dashboard" 
                    className="w-full rounded-xl shadow-lg"
                  />
                </motion.div>
              </div>
            </TabsContent>
            
            <TabsContent value="client">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
                <motion.div 
                  className="order-2 lg:order-1 bg-gray-50 dark:bg-gray-700 p-8 rounded-2xl shadow-lg"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: 0.2 }}
                >
                  <img 
                    src="/lovable-uploads/7e358001-5e4d-4674-80aa-19dd13048dc1.png" 
                    alt="Client booking interface" 
                    className="w-full rounded-xl shadow-lg"
                  />
                </motion.div>
                
                <motion.div
                  className="order-1 lg:order-2"
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.5 }}
                >
                  <h3 className="text-2xl md:text-3xl font-bold mb-6">Delightful Client Experience</h3>
                  <p className="text-lg text-muted-foreground mb-8">
                    Give your clients a seamless booking experience that saves them time and leaves a professional impression.
                  </p>
                  
                  <div className="space-y-6">
                    {[
                      {
                        title: "24/7 Self-Service Booking",
                        description: "Clients can book appointments anytime, even outside business hours."
                      },
                      {
                        title: "Easy Rescheduling",
                        description: "Simple self-service rescheduling without phone calls or emails."
                      },
                      {
                        title: "Helpful Reminders",
                        description: "Automatic reminders via email or SMS to reduce no-shows."
                      }
                    ].map((item, index) => (
                      <div key={index} className="flex items-start gap-4">
                        <div className="mt-1 w-8 h-8 rounded-full bg-green-100 dark:bg-green-900/30 flex items-center justify-center flex-shrink-0">
                          <svg className="w-5 h-5 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="3" d="M5 13l4 4L19 7"></path>
                          </svg>
                        </div>
                        <div>
                          <h4 className="text-lg font-semibold">{item.title}</h4>
                          <p className="text-muted-foreground">{item.description}</p>
                        </div>
                      </div>
                    ))}
                  </div>
                  
                  <Button className="mt-10" size="lg">
                    See Client Experience
                  </Button>
                </motion.div>
              </div>
            </TabsContent>
          </Tabs>
        </div>
      </section>

      {/* How It Works */}
      <section className="py-20 bg-gray-50 dark:bg-gray-900">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-5xl font-bold mb-4">
              How It Works
            </h2>
            <p className="text-lg text-muted-foreground max-w-3xl mx-auto">
              Get started with our scheduling assistant in just a few simple steps
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-10 max-w-5xl mx-auto">
            {[
              {
                step: 1,
                title: "Set Your Availability",
                description: "Define when you're available for appointments and how long each service type takes."
              },
              {
                step: 2,
                title: "Share Your Booking Link",
                description: "Add your booking link to your website, social media, or share it directly with clients."
              },
              {
                step: 3,
                title: "Manage Your Calendar",
                description: "View upcoming appointments, send reminders, and keep your schedule organized."
              }
            ].map((item) => (
              <motion.div 
                key={item.step}
                className="text-center"
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: item.step * 0.1 }}
                viewport={{ once: true }}
              >
                <div className="w-20 h-20 rounded-2xl bg-primary text-white flex items-center justify-center text-2xl font-bold mx-auto mb-6">
                  {item.step}
                </div>
                <h3 className="text-2xl font-bold mb-4">{item.title}</h3>
                <p className="text-muted-foreground">
                  {item.description}
                </p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Pricing */}
      <section className="py-20 bg-white dark:bg-gray-800">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-5xl font-bold mb-4">
              Simple, Transparent Pricing
            </h2>
            <p className="text-lg text-muted-foreground max-w-3xl mx-auto">
              Start with our free plan and upgrade as your business grows
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-5xl mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
              viewport={{ once: true }}
            >
              <Card className="h-full border-2 hover:border-primary/20 transition-all hover:shadow-lg">
                <CardContent className="p-8">
                  <Badge variant="outline" className="mb-2">Free</Badge>
                  <h3 className="text-2xl font-bold">Basic</h3>
                  <div className="mt-4 mb-6">
                    <span className="text-5xl font-bold">$0</span>
                    <span className="text-muted-foreground">/month</span>
                  </div>
                  
                  <ul className="space-y-4 mb-8">
                    <li className="flex items-center">
                      <CheckCircle className="h-5 w-5 text-green-500 mr-3" />
                      <span>Up to 30 appointments/month</span>
                    </li>
                    <li className="flex items-center">
                      <CheckCircle className="h-5 w-5 text-green-500 mr-3" />
                      <span>Email reminders</span>
                    </li>
                    <li className="flex items-center">
                      <CheckCircle className="h-5 w-5 text-green-500 mr-3" />
                      <span>Basic booking page</span>
                    </li>
                    <li className="flex items-center">
                      <CheckCircle className="h-5 w-5 text-green-500 mr-3" />
                      <span>Google Calendar sync</span>
                    </li>
                  </ul>
                  
                  <Button className="w-full" size="lg">
                    Get Started Free
                  </Button>
                </CardContent>
              </Card>
            </motion.div>
            
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.1 }}
              viewport={{ once: true }}
              className="md:-mt-6"
            >
              <Card className="relative h-full border-2 border-primary shadow-xl">
                <div className="absolute -top-5 left-0 right-0 mx-auto w-fit px-4 py-1 bg-primary text-white rounded-full text-sm font-medium">
                  Most Popular
                </div>
                <CardContent className="p-8">
                  <Badge variant="outline" className="mb-2 border-primary text-primary">Pro</Badge>
                  <h3 className="text-2xl font-bold">Professional</h3>
                  <div className="mt-4 mb-6">
                    <span className="text-5xl font-bold">$19</span>
                    <span className="text-muted-foreground">/month</span>
                  </div>
                  
                  <ul className="space-y-4 mb-8">
                    <li className="flex items-center">
                      <CheckCircle className="h-5 w-5 text-green-500 mr-3" />
                      <span>Unlimited appointments</span>
                    </li>
                    <li className="flex items-center">
                      <CheckCircle className="h-5 w-5 text-green-500 mr-3" />
                      <span>Email & SMS reminders</span>
                    </li>
                    <li className="flex items-center">
                      <CheckCircle className="h-5 w-5 text-green-500 mr-3" />
                      <span>Custom booking page</span>
                    </li>
                    <li className="flex items-center">
                      <CheckCircle className="h-5 w-5 text-green-500 mr-3" />
                      <span>All calendar integrations</span>
                    </li>
                    <li className="flex items-center">
                      <CheckCircle className="h-5 w-5 text-green-500 mr-3" />
                      <span>Online payments</span>
                    </li>
                  </ul>
                  
                  <Button className="w-full" variant="default" size="lg">
                    Start 14-Day Free Trial
                  </Button>
                </CardContent>
              </Card>
            </motion.div>
            
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.2 }}
              viewport={{ once: true }}
            >
              <Card className="h-full border-2 hover:border-primary/20 transition-all hover:shadow-lg">
                <CardContent className="p-8">
                  <Badge variant="outline" className="mb-2">Business</Badge>
                  <h3 className="text-2xl font-bold">Team</h3>
                  <div className="mt-4 mb-6">
                    <span className="text-5xl font-bold">$49</span>
                    <span className="text-muted-foreground">/month</span>
                  </div>
                  
                  <ul className="space-y-4 mb-8">
                    <li className="flex items-center">
                      <CheckCircle className="h-5 w-5 text-green-500 mr-3" />
                      <span>Everything in Pro</span>
                    </li>
                    <li className="flex items-center">
                      <CheckCircle className="h-5 w-5 text-green-500 mr-3" />
                      <span>Up to 10 team members</span>
                    </li>
                    <li className="flex items-center">
                      <CheckCircle className="h-5 w-5 text-green-500 mr-3" />
                      <span>Advanced reporting</span>
                    </li>
                    <li className="flex items-center">
                      <CheckCircle className="h-5 w-5 text-green-500 mr-3" />
                      <span>Priority support</span>
                    </li>
                    <li className="flex items-center">
                      <CheckCircle className="h-5 w-5 text-green-500 mr-3" />
                      <span>API access</span>
                    </li>
                  </ul>
                  
                  <Button className="w-full" size="lg">
                    Contact Sales
                  </Button>
                </CardContent>
              </Card>
            </motion.div>
          </div>
        </div>
      </section>

      {/* CTA */}
      <section className="py-20 relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-r from-primary to-blue-600"></div>
        <div className="absolute inset-0 opacity-10 bg-[radial-gradient(#fff_1px,transparent_1px)] [background-size:16px_16px]"></div>
        
        <motion.div
          className="container mx-auto px-4 text-center relative z-10"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
        >
          <h2 className="text-3xl md:text-5xl font-bold mb-6 text-white">
            Ready to Save Time and Grow Your Business?
          </h2>
          <p className="text-xl text-white/90 mb-12 max-w-3xl mx-auto">
            Join thousands of service professionals who use our scheduling assistant to streamline their booking process.
          </p>
          
          <div className="flex flex-col sm:flex-row justify-center gap-4 mb-12">
            <Button size="xl" className="text-xl py-7 font-medium rounded-xl bg-white text-primary hover:bg-white/90 shadow-lg hover:shadow-xl hover:translate-y-[-2px] transition-all">
              Try It Free - No Credit Card Required
            </Button>
            <Button variant="outline" size="xl" className="text-xl py-7 font-medium rounded-xl border-white text-white hover:bg-white/10 shadow-lg hover:shadow-xl hover:translate-y-[-2px] transition-all">
              Schedule a Demo
            </Button>
          </div>
          
          <div className="flex flex-wrap items-center justify-center gap-8">
            <div className="flex items-center">
              <Users className="h-6 w-6 mr-2 text-white/90" />
              <span className="text-white/90">10,000+ Users</span>
            </div>
            <div className="flex items-center">
              <Calendar className="h-6 w-6 mr-2 text-white/90" />
              <span className="text-white/90">1M+ Appointments Booked</span>
            </div>
            <div className="flex items-center">
              <CheckCircle className="h-6 w-6 mr-2 text-white/90" />
              <span className="text-white/90">99.9% Uptime</span>
            </div>
          </div>
        </motion.div>
      </section>
    </Layout>
  );
};

export default SchedulingAssistant;
