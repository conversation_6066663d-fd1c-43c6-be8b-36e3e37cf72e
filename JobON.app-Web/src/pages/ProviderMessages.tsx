
import React from 'react';
import { ProviderDashboardLayout } from '@/components/provider/ProviderDashboardLayout';
import { UniversalChat } from '@/components/chat';
import { useAuth } from '@/features/auth/hooks/useAuth';
import { ChatProvider } from '@/contexts/ChatContext';
import { useAutoSelectChat } from '@/hooks/useAutoSelectChat';

const ProviderMessages = () => {
  const { user } = useAuth();

  return (
    <ChatProvider>
      <ProviderDashboardLayout pageTitle="Messages">
        <ProviderMessagesContent userId={user?.id || 'provider-user'} />
      </ProviderDashboardLayout>
    </ChatProvider>
  );
};

// Separate component to use hooks inside ChatProvider
const ProviderMessagesContent: React.FC<{ userId: string }> = ({ userId }) => {
  // Auto-select chat based on URL parameters
  const { userIdParam, isAutoSelecting } = useAutoSelectChat();

  return (
    <div className="h-[calc(100vh-200px)] bg-white rounded-lg shadow-sm border">
      <UniversalChat
        userRole="provider"
        userId={userId}
        variant="full"
        theme="provider"
        onChatSelect={(chatId) => {
          // console.log('Provider selected chat:', chatId);
        }}
        onMessageSent={(message) => {
          // console.log('Provider sent message:', message);
        }}
        onError={(error) => {
          // console.error('Provider chat error:', error);
        }}
      />
    </div>
  );
};

export default ProviderMessages;
