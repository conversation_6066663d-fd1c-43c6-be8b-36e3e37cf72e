
import React, { useState, useRef, useCallback } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Slider } from '@/components/ui/slider';
import { Switch } from '@/components/ui/switch';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Upload, Download, RotateCw, Contrast, Sun, Palette, Crop, Filter, Loader2 } from 'lucide-react';
import { Layout } from '@/components/Layout';
import { SEO } from '@/components/SEO';
import { useLocation } from 'react-router-dom';
import { useToast } from '@/hooks/use-toast';
import html2canvas from 'html2canvas';

const ImageEditor: React.FC = () => {
  const location = useLocation();
  const { toast } = useToast();
  const [images, setImages] = useState<string[]>([]);
  const [activeTemplate, setActiveTemplate] = useState<'standard' | 'slider' | 'gallery'>('standard');
  const [isDownloading, setIsDownloading] = useState(false);
  
  // Filters state
  const [brightness, setBrightness] = useState(100);
  const [contrast, setContrast] = useState(100);
  const [saturation, setSaturation] = useState(100);
  const [rotation, setRotation] = useState(0);
  const [flipHorizontal, setFlipHorizontal] = useState(false);
  const [flipVertical, setFlipVertical] = useState(false);
  
  // Refs for capturing content
  const standardRef = useRef<HTMLDivElement>(null);
  const sliderRef = useRef<HTMLDivElement>(null);
  const galleryRef = useRef<HTMLDivElement>(null);

  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (files) {
      const newImages: string[] = [];
      Array.from(files).forEach(file => {
        const reader = new FileReader();
        reader.onload = (e) => {
          if (e.target?.result) {
            newImages.push(e.target.result as string);
            if (newImages.length === files.length) {
              setImages(prev => [...prev, ...newImages]);
            }
          }
        };
        reader.readAsDataURL(file);
      });
    }
  };

  const removeImage = (index: number) => {
    setImages(prev => prev.filter((_, i) => i !== index));
  };

  const getFilterStyle = () => ({
    filter: `brightness(${brightness}%) contrast(${contrast}%) saturate(${saturation}%)`,
    transform: `rotate(${rotation}deg) scaleX(${flipHorizontal ? -1 : 1}) scaleY(${flipVertical ? -1 : 1})`,
  });

  const resetFilters = () => {
    setBrightness(100);
    setContrast(100);
    setSaturation(100);
    setRotation(0);
    setFlipHorizontal(false);
    setFlipVertical(false);
  };

  const getCurrentTemplateRef = () => {
    switch (activeTemplate) {
      case 'standard':
        return standardRef;
      case 'slider':
        return sliderRef;
      case 'gallery':
        return galleryRef;
      default:
        return standardRef;
    }
  };

  const handleDownload = useCallback(async () => {
    if (images.length === 0) {
      toast({
        title: "No Images",
        description: "Please upload at least one image before downloading.",
        variant: "destructive"
      });
      return;
    }

    setIsDownloading(true);
    
    try {
      const currentRef = getCurrentTemplateRef();
      
      if (!currentRef.current) {
        throw new Error("Template reference not found");
      }

      const canvas = await html2canvas(currentRef.current, {
        backgroundColor: '#ffffff',
        scale: 2,
        useCORS: true,
        allowTaint: true,
        logging: false,
        width: currentRef.current.scrollWidth,
        height: currentRef.current.scrollHeight
      });

      // Convert canvas to blob
      canvas.toBlob((blob: Blob | null) => {
        if (!blob) {
          throw new Error("Failed to generate image");
        }

        // Create download link
        const url = URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        
        // Generate filename with timestamp
        const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-');
        link.download = `image-comparison-${activeTemplate}-${timestamp}.png`;
        
        // Trigger download
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        
        // Clean up
        URL.revokeObjectURL(url);
        
        toast({
          title: "Download Complete",
          description: `Your ${activeTemplate} comparison has been downloaded successfully.`,
          variant: "default"
        });
      }, 'image/png', 0.95);

    } catch (error) {
      console.error('Download failed:', error);
      toast({
        title: "Download Failed",
        description: "There was an error generating your comparison. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsDownloading(false);
    }
  }, [images.length, activeTemplate, toast]);

  const renderStandardTemplate = () => (
    <div ref={standardRef} className="space-y-4 p-6 bg-white">
      <h3 className="text-xl font-semibold text-center mb-6">Image Comparison</h3>
      {images.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {images.slice(0, 2).map((image, index) => (
            <div key={index} className="relative">
              <img
                src={image}
                alt={`Comparison ${index + 1}`}
                className="w-full h-64 object-cover rounded-lg"
                style={getFilterStyle()}
              />
              <div className="absolute bottom-2 left-2 bg-black bg-opacity-70 text-white px-2 py-1 rounded text-sm">
                {index === 0 ? 'Before' : 'After'}
              </div>
            </div>
          ))}
        </div>
      ) : (
        <div className="text-center text-gray-500 py-12">
          Upload images to see the comparison
        </div>
      )}
    </div>
  );

  const renderSliderTemplate = () => (
    <div ref={sliderRef} className="space-y-4 p-6 bg-white">
      <h3 className="text-xl font-semibold text-center mb-6">Before & After Slider</h3>
      {images.length >= 2 ? (
        <div className="relative w-full h-64 overflow-hidden rounded-lg">
          <img
            src={images[0]}
            alt="Before"
            className="absolute inset-0 w-full h-full object-cover"
            style={getFilterStyle()}
          />
          <div className="absolute inset-0 w-1/2 overflow-hidden">
            <img
              src={images[1]}
              alt="After"
              className="w-full h-full object-cover"
              style={getFilterStyle()}
            />
          </div>
          <div className="absolute inset-y-0 left-1/2 w-1 bg-white shadow-lg"></div>
          <div className="absolute bottom-2 left-2 bg-black bg-opacity-70 text-white px-2 py-1 rounded text-sm">
            Before
          </div>
          <div className="absolute bottom-2 right-2 bg-black bg-opacity-70 text-white px-2 py-1 rounded text-sm">
            After
          </div>
        </div>
      ) : (
        <div className="text-center text-gray-500 py-12">
          Upload at least 2 images for slider comparison
        </div>
      )}
    </div>
  );

  const renderGalleryTemplate = () => (
    <div ref={galleryRef} className="space-y-4 p-6 bg-white">
      <h3 className="text-xl font-semibold text-center mb-6">Image Gallery</h3>
      {images.length > 0 ? (
        <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
          {images.map((image, index) => (
            <div key={index} className="relative">
              <img
                src={image}
                alt={`Gallery image ${index + 1}`}
                className="w-full h-32 object-cover rounded-lg"
                style={getFilterStyle()}
              />
              <div className="absolute bottom-1 right-1 bg-black bg-opacity-70 text-white px-1 py-0.5 rounded text-xs">
                {index + 1}
              </div>
            </div>
          ))}
        </div>
      ) : (
        <div className="text-center text-gray-500 py-12">
          Upload images to create a gallery
        </div>
      )}
    </div>
  );

  return (
    <>
      <SEO
        title="Free Image Editor & Comparison Tool | Professional Photo Editing"
        description="Edit and compare images with our free online tool. Create before/after comparisons, adjust brightness, contrast, and more. Perfect for showcasing work transformations."
        canonicalUrl={location.pathname}
      />
      
      <Layout hideNav={false} fullWidth>
        <div className="container mx-auto px-4 py-8">
          <div className="max-w-6xl mx-auto">
            <div className="text-center mb-8">
              <h1 className="text-3xl font-bold mb-4">Image Editor & Comparison Tool</h1>
              <p className="text-lg text-muted-foreground">
                Upload, edit, and create professional image comparisons
              </p>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
              {/* Upload Section */}
              <Card className="lg:col-span-1">
                <CardContent className="p-6">
                  <h3 className="text-lg font-semibold mb-4">Upload Images</h3>
                  <div className="space-y-4">
                    <div>
                      <Label htmlFor="image-upload" className="cursor-pointer">
                        <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-primary transition-colors">
                          <Upload className="h-8 w-8 mx-auto mb-2 text-gray-400" />
                          <p className="text-sm text-gray-600">Click to upload images</p>
                        </div>
                      </Label>
                      <Input
                        id="image-upload"
                        type="file"
                        multiple
                        accept="image/*"
                        onChange={handleImageUpload}
                        className="hidden"
                      />
                    </div>

                    {/* Image List */}
                    {images.length > 0 && (
                      <div className="space-y-2">
                        <h4 className="font-medium">Uploaded Images ({images.length})</h4>
                        <div className="max-h-40 overflow-y-auto space-y-1">
                          {images.map((_, index) => (
                            <div key={index} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                              <span className="text-sm">Image {index + 1}</span>
                              <Button
                                size="sm"
                                variant="ghost"
                                onClick={() => removeImage(index)}
                                className="h-6 w-6 p-0"
                              >
                                ×
                              </Button>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}

                    {/* Template Selection */}
                    <div className="space-y-2">
                      <Label>Template</Label>
                      <Tabs value={activeTemplate} onValueChange={(value) => setActiveTemplate(value as any)}>
                        <TabsList className="grid w-full grid-cols-3">
                          <TabsTrigger value="standard">Standard</TabsTrigger>
                          <TabsTrigger value="slider">Slider</TabsTrigger>
                          <TabsTrigger value="gallery">Gallery</TabsTrigger>
                        </TabsList>
                      </Tabs>
                    </div>

                    {/* Download Button */}
                    <Button 
                      onClick={handleDownload}
                      disabled={images.length === 0 || isDownloading}
                      className="w-full"
                    >
                      {isDownloading ? (
                        <>
                          <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                          Generating...
                        </>
                      ) : (
                        <>
                          <Download className="h-4 w-4 mr-2" />
                          Download Comparison
                        </>
                      )}
                    </Button>
                  </div>
                </CardContent>
              </Card>

              {/* Edit Controls */}
              <Card className="lg:col-span-1">
                <CardContent className="p-6">
                  <h3 className="text-lg font-semibold mb-4">Edit Controls</h3>
                  <div className="space-y-6">
                    {/* Brightness */}
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <Label className="flex items-center gap-2">
                          <Sun className="h-4 w-4" />
                          Brightness
                        </Label>
                        <span className="text-sm text-muted-foreground">{brightness}%</span>
                      </div>
                      <Slider
                        value={[brightness]}
                        onValueChange={(value) => setBrightness(value[0])}
                        min={0}
                        max={200}
                        step={5}
                        className="w-full"
                      />
                    </div>

                    {/* Contrast */}
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <Label className="flex items-center gap-2">
                          <Contrast className="h-4 w-4" />
                          Contrast
                        </Label>
                        <span className="text-sm text-muted-foreground">{contrast}%</span>
                      </div>
                      <Slider
                        value={[contrast]}
                        onValueChange={(value) => setContrast(value[0])}
                        min={0}
                        max={200}
                        step={5}
                        className="w-full"
                      />
                    </div>

                    {/* Saturation */}
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <Label className="flex items-center gap-2">
                          <Palette className="h-4 w-4" />
                          Saturation
                        </Label>
                        <span className="text-sm text-muted-foreground">{saturation}%</span>
                      </div>
                      <Slider
                        value={[saturation]}
                        onValueChange={(value) => setSaturation(value[0])}
                        min={0}
                        max={200}
                        step={5}
                        className="w-full"
                      />
                    </div>

                    {/* Rotation */}
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <Label className="flex items-center gap-2">
                          <RotateCw className="h-4 w-4" />
                          Rotation
                        </Label>
                        <span className="text-sm text-muted-foreground">{rotation}°</span>
                      </div>
                      <Slider
                        value={[rotation]}
                        onValueChange={(value) => setRotation(value[0])}
                        min={-180}
                        max={180}
                        step={15}
                        className="w-full"
                      />
                    </div>

                    {/* Flip Controls */}
                    <div className="space-y-3">
                      <div className="flex items-center justify-between">
                        <Label>Flip Horizontal</Label>
                        <Switch
                          checked={flipHorizontal}
                          onCheckedChange={setFlipHorizontal}
                        />
                      </div>
                      <div className="flex items-center justify-between">
                        <Label>Flip Vertical</Label>
                        <Switch
                          checked={flipVertical}
                          onCheckedChange={setFlipVertical}
                        />
                      </div>
                    </div>

                    {/* Reset Button */}
                    <Button onClick={resetFilters} variant="outline" className="w-full">
                      Reset Filters
                    </Button>
                  </div>
                </CardContent>
              </Card>

              {/* Preview */}
              <Card className="lg:col-span-1">
                <CardContent className="p-0">
                  <div className="h-full">
                    {activeTemplate === 'standard' && renderStandardTemplate()}
                    {activeTemplate === 'slider' && renderSliderTemplate()}
                    {activeTemplate === 'gallery' && renderGalleryTemplate()}
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </Layout>
    </>
  );
};

export default ImageEditor;
