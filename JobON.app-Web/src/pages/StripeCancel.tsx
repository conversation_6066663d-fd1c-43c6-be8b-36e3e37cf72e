import React from 'react';
import { useNavigate } from 'react-router-dom';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { XCircle, ArrowLeft } from 'lucide-react';

const StripeCancel: React.FC = () => {
  const navigate = useNavigate();

  const handleReturnToPlans = () => {
    navigate('/for-providers');
  };

  const handleGoToDashboard = () => {
    navigate('/provider/dashboard');
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center p-4">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <div className="mx-auto mb-4">
            <XCircle className="h-16 w-16 text-orange-500" />
          </div>
          <CardTitle className="text-2xl">Payment Cancelled</CardTitle>
        </CardHeader>
        <CardContent className="text-center space-y-4">
          <p className="text-muted-foreground">
            Your payment was cancelled. No charges have been made to your account.
          </p>
          <p className="text-sm text-muted-foreground">
            You can return to the plans page to try again or continue using your current plan.
          </p>
          
          <div className="space-y-2 pt-4">
            <Button onClick={handleReturnToPlans} className="w-full">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Return to Plans
            </Button>
            <Button 
              variant="outline" 
              onClick={handleGoToDashboard}
              className="w-full"
            >
              Go to Dashboard
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default StripeCancel;
