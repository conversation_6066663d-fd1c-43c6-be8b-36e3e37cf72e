import React, { useState, useEffect } from 'react';
import { ServicePageTemplate } from '../components/ServicePageTemplate';
import { Button } from "@/components/ui/button";
import { Link } from 'react-router-dom';
import { ArrowRight, Check, ShieldCheck, Clock, Award, Handshake, Users, Fan, Thermometer, Snowflake, WrenchIcon, Workflow, Wrench, Building, Home, Briefcase, Settings } from 'lucide-react';
import { useIsMobile } from '@/hooks/use-mobile';
import { ServiceNeeds } from '@/components/ServiceNeeds';
import { BlogCard, BlogPost } from '@/components/BlogCard';
import { SEO } from '@/components/SEO';
import { fetchPosts } from '@/services/wordpressApi';
import {SERVICE_CATEGORY} from "@/types/enum.ts";

const HVACService: React.FC = () => {
  const isMobile = useIsMobile();
    const [hvacBlogPosts, setHvacBlogPosts] = useState<BlogPost[]>([])

    useEffect(() => {
        const loadInitialData = async () => {
            try {
                const { posts, totalPages: pages } = await fetchPosts(1, 3, SERVICE_CATEGORY.HVAC);
                setHvacBlogPosts(posts)
            } catch (err) {
                console.log(err);
            }
        };
        loadInitialData();
    }, []);

  const benefits = [{
    title: "Licensed & Certified HVAC Technicians",
    description: "All our HVAC professionals are fully licensed, certified, and insured to handle all types of heating and cooling systems.",
    icon: <ShieldCheck className="h-6 w-6 text-primary" />
  }, {
    title: "24/7 Emergency Service",
    description: "HVAC emergencies don't wait for business hours. We offer round-the-clock service for critical heating and cooling issues.",
    icon: <Clock className="h-6 w-6 text-primary" />
  }, {
    title: "Energy-Efficient Solutions",
    description: "We recommend and install energy-efficient HVAC solutions that save you money on your utility bills in the long run.",
    icon: <Award className="h-6 w-6 text-primary" />
  }, {
    title: "Satisfaction Guaranteed",
    description: "If you're not completely satisfied with our HVAC service, we'll make it right – guaranteed.",
    icon: <Handshake className="h-6 w-6 text-primary" />
  }, {
    title: "Experienced Professionals",
    description: "Our HVAC technicians have extensive experience with all makes and models of heating and cooling equipment.",
    icon: <Users className="h-6 w-6 text-primary" />
  }, {
    title: "Preventative Maintenance Plans",
    description: "Keep your HVAC system running efficiently with our customized preventative maintenance plans.",
    icon: <Fan className="h-6 w-6 text-primary" />
  }];
  const faqs = [{
    question: "How often should I service my HVAC system?",
    answer: "We recommend professional maintenance twice a year: once before the cooling season (spring) and once before the heating season (fall). Regular maintenance ensures optimal performance, efficiency, and extends the life of your system."
  }, {
    question: "What are signs that my HVAC system needs repair?",
    answer: "Common signs include unusual noises, reduced airflow, system cycling on and off frequently, higher than normal utility bills, inconsistent temperatures, and strange odors. If you notice any of these issues, it's best to have a professional technician inspect your system."
  }, {
    question: "How long do HVAC systems typically last?",
    answer: "With proper maintenance, air conditioners and heat pumps typically last 10-15 years, while furnaces can last 15-20 years. However, these are averages, and system lifespan can vary based on usage patterns, maintenance history, and local climate conditions."
  }, {
    question: "Should I repair or replace my old HVAC system?",
    answer: "This depends on several factors including the age of your system, repair costs, and efficiency. As a general rule, if your system is beyond 3/4 of its expected lifespan and the repair would cost more than 1/3 of a replacement, replacement is often more economical in the long run."
  }, {
    question: "What size HVAC system do I need for my home?",
    answer: "Proper sizing is crucial for efficiency and comfort. The right size depends on your home's square footage, insulation, window efficiency, local climate, ceiling height, and other factors. Our technicians perform a detailed load calculation to determine the appropriate size for your specific needs."
  }, {
    question: "Do you offer warranty on HVAC installations and repairs?",
    answer: "Yes, we provide comprehensive warranties on all our HVAC work. New installations typically come with a manufacturer's warranty on parts (often 10 years) and our labor warranty. Repairs include warranties on both parts and labor, varying by the specific service performed."
  }];
  const hvacEstimates = [{
    tier: "Residential HVAC Service",
    price: "$89-150",
    description: "For homes and apartments",
    features: ["Licensed HVAC technician", "System inspection & diagnosis", "Filter replacement", "Basic cleaning & adjustments", "90-day service warranty"]
  }, {
    tier: "Office HVAC Solutions",
    price: "$200-400",
    description: "For business environments",
    features: ["Expert HVAC technician", "Comprehensive system tune-up", "Refrigerant level check", "Ductwork inspection", "Component testing & repair", "1-year service warranty"],
    recommended: true
  }, {
    tier: "Commercial HVAC Systems",
    price: "$3,500-8,000+",
    description: "For large commercial spaces",
    features: ["Master HVAC technician", "New system installation", "Advanced thermostat setup", "System efficiency optimization", "Extended manufacturer warranty", "10-year parts coverage", "5-year labor warranty"]
  }];
  const commonHVACNeeds = [{
    icon: <Fan className="h-7 w-7 text-primary" />,
    label: "AC Repair"
  }, {
    icon: <Thermometer className="h-7 w-7 text-primary" />,
    label: "Heating Repair"
  }, {
    icon: <Wrench className="h-7 w-7 text-primary" />,
    label: "System Tune-Up"
  }, {
    icon: <Workflow className="h-7 w-7 text-primary" />,
    label: "New Installation"
  }, {
    icon: <WrenchIcon className="h-7 w-7 text-primary" />,
    label: "Preventative Maintenance"
  }, {
    icon: <Snowflake className="h-7 w-7 text-primary" />,
    label: "Refrigerant Service"
  }, {
    icon: <Fan className="h-7 w-7 text-primary" />,
    label: "Duct Cleaning"
  }, {
    icon: <Thermometer className="h-7 w-7 text-primary" />,
    label: "Thermostat Installation"
  }, {
    icon: <Settings className="h-7 w-7 text-primary" />,
    label: "Air Quality Solutions"
  }, {
    icon: <Fan className="h-7 w-7 text-primary" />,
    label: "Emergency Service"
  }];

  const hvacServiceSchema = {
    "@context": "https://schema.org",
    "@type": "Service",
    "name": "HVAC Services",
    "serviceType": "Heating, Ventilation, and Air Conditioning",
    "provider": {
      "@type": "Organization",
      "name": "JobON",
      "url": "https://jobon.app"
    },
    "description": "Professional HVAC installation, repair, and maintenance services for residential, commercial, and industrial properties.",
    "areaServed": "United States",
    "hasOfferCatalog": {
      "@type": "OfferCatalog",
      "name": "HVAC Services",
      "itemListElement": [{
        "@type": "Offer",
        "itemOffered": {
          "@type": "Service",
          "name": "AC Repair & Installation"
        }
      }, {
        "@type": "Offer",
        "itemOffered": {
          "@type": "Service",
          "name": "Heating System Repair"
        }
      }, {
        "@type": "Offer",
        "itemOffered": {
          "@type": "Service",
          "name": "System Maintenance"
        }
      }, {
        "@type": "Offer",
        "itemOffered": {
          "@type": "Service",
          "name": "Air Quality Solutions"
        }
      }]
    }
  };
  return <>
      <SEO title="HVAC Repair & Installation Near You – Compare Local Bids Fast" description="Get fast bids from licensed HVAC pros near you. Compare quotes for AC repair, heating installs, duct cleaning, and more—trusted, efficient, and local with JobON." localBusinessSchema={true} serviceType="HVAC" serviceSlug="hvac" canonicalUrl="/services/hvac" />

      <section className="relative bg-gradient-to-b from-blue-50 to-white dark:from-gray-900 dark:to-background pt-16 pb-6 md:pt-24 md:pb-12">
        <div className="container mx-auto px-3 lg:px-8">
          {isMobile ? <div className="w-full">
              <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden mb-3">
                <div className="relative h-44 overflow-hidden">
                  <img src="/lovable-uploads/02f7ba94-7570-4ee9-847a-b508bedc4f6b.png" alt="Professional HVAC service" className="w-full h-full object-cover object-center" style={{
                objectPosition: "center 30%"
              }} />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/50 to-transparent flex items-end">
                    <div className="p-3 text-white w-full">
                      <h1 className="text-xl font-bold mb-0.5 text-left">
                        Professional HVAC
                      </h1>
                      <h2 className="text-base font-medium text-white mb-1 text-left">
                        <span className="block">Expert Service</span>
                        <span className="block">Energy Efficient</span>
                      </h2>
                      <div className="flex items-center space-x-2">
                        <div className="flex">
                          {[1, 2, 3, 4, 5].map((_, i) => <svg key={i} className="w-3 h-3 text-amber-400 fill-amber-400" viewBox="0 0 24 24"><path d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z" /></svg>)}
                        </div>
                        <span className="text-xs font-medium text-white">4.9/5 · 2,186 reviews</span>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="p-2">
                  <p className="text-sm text-gray-800 dark:text-gray-300 mb-2 font-medium">
                    Professional HVAC services for homes, offices, and commercial buildings
                  </p>

                  <div className="mb-2">
                    <div className="flex shadow-lg rounded-xl overflow-hidden">


                    </div>
                  </div>

                  <div className="flex space-x-2">
                    <Link to="/create-job" className="flex-1">
                      <Button variant="default" size="default" className="w-full rounded-md font-semibold text-sm py-1.5">Post Job</Button>
                    </Link>
                    <Link to="/professionals/hvac" className="flex-1">
                      <Button variant="outline" size="default" className="w-full rounded-md font-semibold text-sm py-1.5 text-gray-800 dark:text-white border-gray-400">
                        Browse HVAC Pros
                      </Button>
                    </Link>
                  </div>
                </div>
              </div>

              <div className="bg-white dark:bg-gray-800 rounded-xl shadow p-1.5 mb-2 flex items-center justify-between">
                <div className="flex items-center">
                  <ShieldCheck className="h-5 w-5 text-green-500 mr-1" />
                  <div className="text-left">
                    <span className="block text-xs text-gray-600 dark:text-gray-400 font-medium">
                      Certified
                    </span>
                    <span className="font-bold text-xs text-black dark:text-white">
                      Technicians
                    </span>
                  </div>
                </div>
                <div className="w-px h-8 bg-gray-200 dark:bg-gray-700"></div>
                <div className="flex items-center">
                  <Clock className="h-5 w-5 text-blue-500 mr-1" />
                  <div className="text-left">
                    <span className="block text-xs text-gray-600 dark:text-gray-400 font-medium">Response</span>
                    <span className="font-bold text-xs text-black dark:text-white">&#60; 1hr</span>
                  </div>
                </div>
                <div className="w-px h-8 bg-gray-200 dark:bg-gray-700"></div>
                <div className="flex items-center">
                  <Award className="h-5 w-5 text-purple-500 mr-1" />
                  <div className="text-left">
                    <span className="block text-xs text-gray-600 dark:text-gray-400 font-medium">
                      Satisfaction
                    </span>
                    <span className="font-bold text-xs text-black dark:text-white">Guaranteed</span>
                  </div>
                </div>
              </div>
            </div> : <div className="flex flex-col lg:flex-row gap-12 items-center">
              <div className="w-full lg:w-1/2 space-y-8">
                <div className="space-y-4">
                  <span className="inline-block bg-primary/10 text-primary dark:bg-primary/20 dark:text-primary-hover px-4 py-1.5 rounded-full font-medium text-sm">Professional HVAC Services</span>
                  <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900 dark-heading leading-tight">
                    Expert Heating & Cooling,
                    <br className="hidden md:inline" />
                    <span className="text-primary mt-2 block dark:text-primary-hover dark:dark-mode-text-shadow">
                      For Every Environment.
                    </span>
                  </h1>
                  <div className="text-xl font-semibold text-gray-700 dark-subheading">
                    <div className="flex flex-col space-y-3 text-left">
                      <div className="flex items-center gap-2">
                        <div className="flex-shrink-0 h-2 w-2 bg-primary rounded-full"></div>
                        <span className="text-gray-900 dark:text-white">Homes, Offices & Commercial Buildings</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <div className="flex-shrink-0 h-2 w-2 bg-primary rounded-full"></div>
                        <span className="text-gray-900 dark:text-white">Certified & Insured Technicians</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <div className="flex-shrink-0 h-2 w-2 bg-primary rounded-full"></div>
                        <span className="text-gray-900 dark:text-white">Energy-Efficient Solutions</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <div className="flex-shrink-0 h-2 w-2 bg-primary rounded-full"></div>
                        <span className="text-gray-900 dark:text-white">24/7 Emergency Services</span>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="flex flex-col sm:flex-row gap-4 pt-6">
                  <Link to="/create-job" className="w-full sm:w-auto">
                    <Button variant="default" size="lg" className="text-lg font-medium w-full sm:w-auto px-8 py-6 h-auto rounded-xl shadow-lg hover:shadow-xl hover:translate-y-[-2px] transition-all">
                      Post a Project
                      <ArrowRight size={20} className="ml-2" />
                    </Button>
                  </Link>
                  <Link to="/professionals/hvac" className="w-full sm:w-auto">
                    <Button variant="secondary" size="lg" className="text-lg font-medium w-full sm:w-auto px-8 py-6 h-auto rounded-xl border-2 border-gray-400 dark:border-gray-500 bg-gray-50 dark:bg-gray-800 text-gray-800 dark:text-white hover:bg-primary/10 dark:hover:bg-primary/20 hover:border-primary dark:hover:border-primary hover:text-primary dark:hover:text-primary-foreground shadow-lg hover:shadow-xl hover:translate-y-[-2px] transition-all">
                      Browse HVAC Pros
                    </Button>
                  </Link>
                </div>

                <div className="pt-6 grid grid-cols-2 md:grid-cols-3 gap-4">
                  <div className="flex items-center gap-2 text-gray-700 dark:text-gray-300">
                    <Check className="h-5 w-5 text-green-500" />
                    <span>Instant Quotes</span>
                  </div>
                  <div className="flex items-center gap-2 text-gray-700 dark:text-gray-300">
                    <Check className="h-5 w-5 text-green-500" />
                    <span>Verified Techs</span>
                  </div>
                  <div className="flex items-center gap-2 text-gray-700 dark:text-gray-300">
                    <Check className="h-5 w-5 text-green-500" />
                    <span>100% Satisfaction</span>
                  </div>
                  <div className="flex items-center gap-2 text-gray-700 dark:text-gray-300">
                    <Check className="h-5 w-5 text-green-500" />
                    <span>Energy Efficient</span>
                  </div>
                  <div className="flex items-center gap-2 text-gray-700 dark:text-gray-300">
                    <Check className="h-5 w-5 text-green-500" />
                    <span>Transparent Pricing</span>
                  </div>
                  <div className="flex items-center gap-2 text-gray-700 dark:text-gray-300">
                    <Check className="h-5 w-5 text-green-500" />
                    <span>24/7 Availability</span>
                  </div>
                </div>
              </div>

              <div className="w-full lg:w-1/2 relative">
                <div className="absolute inset-0 bg-primary/10 dark:bg-primary/5 rounded-xl filter blur-xl opacity-70"></div>
                <div className="relative bg-white dark:bg-gray-800 p-6 rounded-2xl shadow-xl border border-gray-100 dark:border-gray-700 overflow-hidden">
                  <div className="grid grid-cols-2 gap-3">
                    <div className="aspect-[4/3] rounded-lg overflow-hidden">
                      <img src="/lovable-uploads/02f7ba94-7570-4ee9-847a-b508bedc4f6b.png" alt="Happy family sitting together on couch in living room" className="w-full h-full object-cover" />
                    </div>
                    <div className="aspect-[4/3] rounded-lg overflow-hidden">
                      <img src="/lovable-uploads/8f7bcb7b-b30f-4fc9-a3e2-ca2722359342.png" alt="Business professionals discussing HVAC plans on tablet" className="w-full h-full object-cover" />
                    </div>
                    <div className="col-span-2 aspect-[16/9] rounded-lg overflow-hidden">
                      <img src="/lovable-uploads/19173469-5504-4f75-8287-1eef8e8d9f66.png" alt="Happy family lying on floor together smiling" className="w-full h-full object-cover" />
                    </div>
                  </div>

                  <div className="mt-6 text-center">
                    <div className="flex justify-center mb-2">
                      <div className="flex text-amber-400">
                        <svg className="w-5 h-5 fill-current" viewBox="0 0 24 24"><path d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z" /></svg>
                        <svg className="w-5 h-5 fill-current" viewBox="0 0 24 24"><path d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z" /></svg>
                        <svg className="w-5 h-5 fill-current" viewBox="0 0 24 24"><path d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z" /></svg>
                        <svg className="w-5 h-5 fill-current" viewBox="0 0 24 24"><path d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z" /></svg>
                        <svg className="w-5 h-5 fill-current" viewBox="0 0 24 24"><path d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z" /></svg>
                      </div>
                      <span className="ml-2 text-sm font-medium text-gray-700 dark:text-gray-300">4.9/5 (2,186 reviews)</span>
                    </div>
                    <h3 className="font-bold text-xl mb-1 dark:text-white">Find Top Local HVAC Pros</h3>
                    <p className="text-gray-600 dark:text-gray-300 text-sm mb-4">Enter your ZIP code to get matched with professional HVAC technicians in your area</p>

                    <div className="flex shadow-lg rounded-xl overflow-hidden">
                      <div className="flex-1 flex items-center bg-gray-50 dark:bg-gray-700 p-4">
                        <input type="text" placeholder="Enter your ZIP code" className="w-full bg-transparent border-none focus:outline-none text-gray-900 dark:text-white placeholder:text-gray-400 dark:placeholder:text-gray-300 text-lg" />
                      </div>
                      <Button type="submit" className="px-8 h-auto rounded-none bg-primary hover:bg-primary/90 text-white transition-all">
                        <span>Search</span>
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            </div>}
        </div>
      </section>

      <section className="py-6 md:py-16 bg-white dark:bg-gray-800">
        <div className="container mx-auto px-3 lg:px-8">
          <div className="text-center mb-4 md:mb-12">
            <h2 className="text-2xl md:text-3xl font-bold mb-1.5 md:mb-3 text-black dark:text-white">Common Professional HVAC Services</h2>
            <p className="text-base md:text-lg text-gray-800 dark:text-gray-300 max-w-2xl mx-auto font-medium">
              Select from our most requested HVAC services
            </p>
          </div>

          <ServiceNeeds serviceId="hvac" needs={commonHVACNeeds} estimates={hvacEstimates} />
        </div>
      </section>

      <section className="py-6 md:py-16 bg-gray-50 dark:bg-gray-900">
        <div className="container mx-auto px-3 lg:px-8">
          <div className="text-center mb-4 md:mb-12">
            <h2 className="text-2xl md:text-3xl font-bold mb-1.5 md:mb-3 text-black dark:text-white">Recently Completed HVAC Projects</h2>
            <p className="text-base md:text-lg text-gray-800 dark:text-gray-300 max-w-2xl mx-auto">
              Browse through recently completed HVAC jobs by our network of professionals
            </p>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 md:gap-8">
            <div className="bg-white dark:bg-gray-800 rounded-xl overflow-hidden shadow-md hover:shadow-lg transition-all">
              <img src="/lovable-uploads/81ca20dd-aa4c-417f-b474-3c7e9f872d9c.png" alt="HVAC technicians installing an AC unit on a rooftop" className="w-full h-32 md:h-48 object-cover" />
              <div className="p-3">
                <div className="flex justify-between items-center mb-1.5">
                  <span className="text-xs md:text-sm font-medium text-primary bg-primary/10 dark:text-primary-hover dark:bg-primary/20 px-2 py-0.5 rounded-full">Complete</span>
                  <span className="text-xs md:text-sm font-medium text-gray-500 dark:text-gray-400">2 days ago</span>
                </div>
                <h3 className="font-bold text-base md:text-lg mb-1 md:mb-2 dark:text-white">Central AC Installation</h3>
                <p className="text-gray-600 dark:text-gray-300 text-xs md:text-sm mb-2 md:mb-4">Complete installation of new energy-efficient central air conditioning system in a 3-bedroom home.</p>
                <div className="flex items-center justify-between">
                  <span className="text-gray-700 dark:text-gray-300 font-medium text-xs md:text-sm">Palo Alto, CA</span>
                  <span className="font-bold text-base md:text-lg dark:text-white">$4,250</span>
                </div>
              </div>
            </div>

            <div className="bg-white dark:bg-gray-800 rounded-xl overflow-hidden shadow-md hover:shadow-lg transition-all">
              <img src="https://images.unsplash.com/photo-1504384308090-c894fdcc538d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1740&q=80" alt="Office HVAC maintenance" className="w-full h-32 md:h-48 object-cover" />
              <div className="p-3">
                <div className="flex justify-between items-center mb-1.5">
                  <span className="text-xs md:text-sm font-medium text-primary bg-primary/10 dark:text-primary-hover dark:bg-primary/20 px-2 py-0.5 rounded-full">Complete</span>
                  <span className="text-xs md:text-sm font-medium text-gray-500 dark:text-gray-400">1 week ago</span>
                </div>
                <h3 className="font-bold text-base md:text-lg mb-1 md:mb-2 dark:text-white">Office HVAC Upgrade</h3>
                <p className="text-gray-600 dark:text-gray-300 text-xs md:text-sm mb-2 md:mb-4">Complete upgrade of existing HVAC system in a multi-floor office building with improved zone control.</p>
                <div className="flex items-center justify-between">
                  <span className="text-gray-700 dark:text-gray-300 font-medium text-xs md:text-sm">San Francisco, CA</span>
                  <span className="font-bold text-base md:text-lg dark:text-white">$12,800</span>
                </div>
              </div>
            </div>

            <div className="bg-white dark:bg-gray-800 rounded-xl overflow-hidden shadow-md hover:shadow-lg transition-all">
              <img src="/lovable-uploads/c694f7b2-ea39-458d-a3e9-bfec87a7111f.png" alt="HVAC technician working on commercial chiller system" className="w-full h-32 md:h-48 object-cover" />
              <div className="p-3">
                <div className="flex justify-between items-center mb-1.5">
                  <span className="text-xs md:text-sm font-medium text-primary bg-primary/10 dark:text-primary-hover dark:bg-primary/20 px-2 py-0.5 rounded-full">Complete</span>
                  <span className="text-xs md:text-sm font-medium text-gray-500 dark:text-gray-400">2 weeks ago</span>
                </div>
                <h3 className="font-bold text-base md:text-lg mb-1 md:mb-2 dark:text-white">Commercial Chiller Replacement</h3>
                <p className="text-gray-600 dark:text-gray-300 text-xs md:text-sm mb-2 md:mb-4">Replacement of 300-ton chiller system for a large commercial building with minimal business disruption.</p>
                <div className="flex items-center justify-between">
                  <span className="text-gray-700 dark:text-gray-300 font-medium text-xs md:text-sm">San Jose, CA</span>
                  <span className="font-bold text-base md:text-lg dark:text-white">$42,500</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <section className="py-6 md:py-16 bg-white dark:bg-gray-800">
        <div className="container mx-auto px-3 lg:px-8">
          <div className="text-center mb-4 md:mb-12">
            <h2 className="text-2xl md:text-3xl font-bold mb-1.5 md:mb-3 text-black dark:text-white">Latest HVAC Tips & Guides</h2>
            <p className="text-base md:text-lg text-gray-800 dark:text-gray-300 max-w-2xl mx-auto">
              Expert advice and helpful resources for maintaining efficient heating and cooling systems
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-3 md:gap-6">
            {hvacBlogPosts.map(post => <BlogCard key={post.id} post={post} />)}
          </div>

          <div className="text-center mt-6 md:mt-12">
            <Link to="/blog">
              <Button variant="default" size={isMobile ? "default" : "lg"} className="font-medium">
                View All HVAC Articles
                <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
            </Link>
          </div>
        </div>
      </section>

      <ServicePageTemplate serviceId="hvac" title="Professional HVAC Services" subtitle="Expert heating, ventilation, and air conditioning solutions for comfort year-round" description="From emergency repairs to routine maintenance and new installations, our licensed HVAC specialists ensure your system runs efficiently and reliably." heroImage="https://images.unsplash.com/photo-1581094296167-b7a6e00c0525?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=2070&q=80" benefits={benefits} faqs={faqs} estimates={hvacEstimates} commonNeeds={commonHVACNeeds} hideEstimator={false} hideHero={true} professionalTitle="HVAC Pros" seoTitle="HVAC Repair & Installation Near You | Trusted Local Pros" customCta={<div className="flex flex-col sm:flex-row justify-center gap-3">
            <Link to="/create-job" className="w-full sm:w-auto">
              <Button variant="default" size="lg" className="text-lg font-medium w-full sm:w-auto px-6 py-6 h-auto rounded-xl shadow-lg hover:shadow-xl hover:translate-y-[-2px] transition-all">
                Post a Project
                <ArrowRight className="ml-2 h-5 w-5" />
              </Button>
            </Link>
            <Link to="/professionals/hvac" className="w-full sm:w-auto">
              <Button variant="secondary" size="lg" className="text-lg font-medium w-full sm:w-auto px-6 py-6 h-auto rounded-xl border-2 border-gray-400 dark:border-gray-500 bg-gray-50 dark:bg-gray-800 text-gray-800 dark:text-white hover:bg-primary/10 dark:hover:bg-primary/20 hover:border-primary dark:hover:border-primary hover:text-primary dark:hover:text-primary-foreground shadow-lg hover:shadow-xl hover:translate-y-[-2px] transition-all">
                Browse HVAC Pros
              </Button>
            </Link>
          </div>} />
    </>;
};
export default HVACService;