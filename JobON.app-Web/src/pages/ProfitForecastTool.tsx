import React, { useState, useEffect } from 'react';
import { Layout } from '@/components/Layout';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ArrowLeft, Download, LineChartIcon, TrendingUp, ChevronDown, ChevronUp, Info, FileText, Share2, Calculator } from 'lucide-react';
import { Link } from 'react-router-dom';
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, Line, LineChart as ReLineChart } from 'recharts';
import { Separator } from '@/components/ui/separator';
import { Slider } from '@/components/ui/slider';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Tooltip as UITooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import {SEO} from "@/components/SEO.tsx";

interface MonthlyData {
  name: string;
  revenue: number;
  expenses: number;
  profit: number;
}

interface SummaryData {
  total: {
    revenue: number;
    expenses: number;
    profit: number;
    growthRate: number;
  };
  average: {
    revenue: number;
    expenses: number;
    profit: number;
    profitMargin: number;
  };
}

const ProfitForecastTool: React.FC = () => {
  const [startingRevenue, setStartingRevenue] = useState<number>(5000);
  const [monthlyGrowthRate, setMonthlyGrowthRate] = useState<number>(5);
  const [costOfGoods, setCostOfGoods] = useState<number>(30);
  const [fixedCosts, setFixedCosts] = useState<number>(2000);
  const [forecastMonths, setForecastMonths] = useState<number>(12);
  const [seasonality, setSeasonality] = useState<string>("none");

  const [monthlyData, setMonthlyData] = useState<MonthlyData[]>([]);
  const [summaryData, setSummaryData] = useState<SummaryData>({
    total: { revenue: 0, expenses: 0, profit: 0, growthRate: 0 },
    average: { revenue: 0, expenses: 0, profit: 0, profitMargin: 0 }
  });

  const seasonalityPatterns = {
    none: Array(12).fill(1),
    summer: [0.8, 0.9, 1.0, 1.1, 1.2, 1.3, 1.4, 1.3, 1.1, 1.0, 0.9, 0.8],
    winter: [1.3, 1.2, 1.1, 1.0, 0.9, 0.8, 0.7, 0.8, 0.9, 1.0, 1.1, 1.2],
    spring: [1.1, 1.2, 1.3, 1.4, 1.3, 1.1, 1.0, 0.9, 0.8, 0.9, 0.9, 1.0],
    quarterly: [1.3, 1.1, 0.9, 0.7, 1.3, 1.1, 0.9, 0.7, 1.3, 1.1, 0.9, 0.7]
  };

  useEffect(() => {
    const months = [
      'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 
      'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'
    ];
    
    const data: MonthlyData[] = [];
    
    const currentMonth = new Date().getMonth();
    
    let runningRevenue = startingRevenue;
    let totalRevenue = 0;
    let totalExpenses = 0;
    let totalProfit = 0;
    
    for (let i = 0; i < forecastMonths; i++) {
      const monthIndex = (currentMonth + i) % 12;
      const monthName = months[monthIndex];
      
      const seasonalFactor = seasonality !== 'none' 
        ? seasonalityPatterns[seasonality as keyof typeof seasonalityPatterns][monthIndex]
        : 1;
      
      if (i > 0) {
        runningRevenue = runningRevenue * (1 + (monthlyGrowthRate / 100));
      }
      
      const adjustedRevenue = runningRevenue * seasonalFactor;
      const variableCosts = adjustedRevenue * (costOfGoods / 100);
      const totalCosts = variableCosts + fixedCosts;
      const profit = adjustedRevenue - totalCosts;
      
      totalRevenue += adjustedRevenue;
      totalExpenses += totalCosts;
      totalProfit += profit;
      
      data.push({
        name: monthName,
        revenue: Math.round(adjustedRevenue),
        expenses: Math.round(totalCosts),
        profit: Math.round(profit)
      });
    }
    
    const avgRevenue = totalRevenue / forecastMonths;
    const avgExpenses = totalExpenses / forecastMonths;
    const avgProfit = totalProfit / forecastMonths;
    const profitMargin = (totalProfit / totalRevenue) * 100;
    
    const growthRate = Math.pow(1 + (monthlyGrowthRate / 100), 12) - 1;
    
    setMonthlyData(data);
    setSummaryData({
      total: {
        revenue: Math.round(totalRevenue),
        expenses: Math.round(totalExpenses),
        profit: Math.round(totalProfit),
        growthRate: Math.round(growthRate * 100)
      },
      average: {
        revenue: Math.round(avgRevenue),
        expenses: Math.round(avgExpenses),
        profit: Math.round(avgProfit),
        profitMargin: Math.round(profitMargin * 10) / 10
      }
    });
  }, [startingRevenue, monthlyGrowthRate, costOfGoods, fixedCosts, forecastMonths, seasonality]);

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(value);
  };

  const downloadForecast = () => {
    alert("This would download a CSV or PDF report in a real application");
  };

  const formatYAxis = (value: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
      notation: value >= 10000 ? 'compact' : 'standard',
      compactDisplay: 'short'
    }).format(value);
  };

  return (
    <Layout>
      <SEO
          title="Profit Forecast Tool | Project Business Revenue and Growth Free"
          description="Forecast your business revenue, expenses, and profits easily with JobON's free Profit Forecast Tool. Plan your growth, visualize trends, and download projections!"
          localBusinessSchema={true}
          serviceType="profit forecast"
          serviceSlug="profit-forecast"
          canonicalUrl="/free-tools/profit-forecast"
      />
      <div className="pt-28 pb-24 px-6 md:px-12 bg-gradient-to-b from-gray-50 to-white dark:from-gray-900 dark:to-gray-800">
        <div className="container mx-auto max-w-6xl">
          <div className="mb-8">
            <Link to="/free-tools" className="inline-flex items-center text-primary hover:underline mb-4 text-lg">
              <ArrowLeft className="mr-2 h-5 w-5" />
              Back to Free Tools
            </Link>
          
            <div className="flex flex-col space-y-4">
              <Badge className="w-fit text-base py-1 px-4 bg-primary/10 text-primary border-none">Free Tool</Badge>
              <h1 className="text-4xl md:text-5xl font-bold tracking-tight">Profit Forecast Tool</h1>
              <p className="text-xl text-foreground/80 max-w-3xl leading-relaxed">
                Project your business revenue, expenses, and profit over time. Plan for growth and visualize financial trends.
              </p>
            </div>
          </div>

          <Tabs defaultValue="calculator" className="w-full">
            <TabsList className="mb-8 grid w-full max-w-lg grid-cols-2 mx-auto">
              <TabsTrigger value="calculator" className="text-lg py-3">Calculator</TabsTrigger>
              <TabsTrigger value="about" className="text-lg py-3">About Profit Forecasting</TabsTrigger>
            </TabsList>
            
            <TabsContent value="calculator">
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-10">
                <div className="lg:col-span-2">
                  <Card className="border-2 shadow-md">
                    <CardHeader className="pb-2">
                      <CardTitle className="text-2xl flex items-center">
                        <Calculator className="mr-3 h-6 w-6 text-primary" />
                        Enter Your Forecast Parameters
                      </CardTitle>
                      <CardDescription className="text-base">Adjust the values below to customize your profit forecast</CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-8 p-6">
                      <div className="space-y-5">
                        <div className="flex justify-between items-center">
                          <Label htmlFor="startingRevenue" className="text-base flex items-center font-medium">
                            Starting Monthly Revenue
                            <TooltipProvider>
                              <UITooltip>
                                <TooltipTrigger asChild>
                                  <Info className="ml-2 h-4 w-4 text-muted-foreground" />
                                </TooltipTrigger>
                                <TooltipContent className="p-3 w-64 text-base">
                                  <p className="max-w-xs">The monthly revenue you're starting with before applying growth</p>
                                </TooltipContent>
                              </UITooltip>
                            </TooltipProvider>
                          </Label>
                          <span className="text-right font-medium text-lg text-blue-600">${startingRevenue.toLocaleString()}</span>
                        </div>
                        <div className="flex items-center gap-6">
                          <Slider 
                            min={1000} 
                            max={50000} 
                            step={500} 
                            defaultValue={[5000]} 
                            value={[startingRevenue]}
                            className="flex-1"
                            onValueChange={(value) => setStartingRevenue(value[0])}
                          />
                          <Input 
                            type="number" 
                            id="startingRevenue" 
                            value={startingRevenue}
                            className="w-40 text-base" 
                            onChange={(e) => setStartingRevenue(Number(e.target.value))}
                          />
                        </div>
                      </div>
                      
                      <div className="space-y-5">
                        <div className="flex justify-between items-center">
                          <Label htmlFor="monthlyGrowthRate" className="text-base flex items-center font-medium">
                            Monthly Growth Rate (%)
                            <TooltipProvider>
                              <UITooltip>
                                <TooltipTrigger asChild>
                                  <Info className="ml-2 h-4 w-4 text-muted-foreground" />
                                </TooltipTrigger>
                                <TooltipContent className="p-3 w-64 text-base">
                                  <p className="max-w-xs">The percentage your revenue grows month over month</p>
                                </TooltipContent>
                              </UITooltip>
                            </TooltipProvider>
                          </Label>
                          <span className="text-right font-medium text-lg text-green-600">{monthlyGrowthRate}%</span>
                        </div>
                        <div className="flex items-center gap-6">
                          <Slider 
                            min={0} 
                            max={20} 
                            step={0.5} 
                            defaultValue={[5]} 
                            value={[monthlyGrowthRate]}
                            className="flex-1"
                            onValueChange={(value) => setMonthlyGrowthRate(value[0])}
                          />
                          <Input 
                            type="number" 
                            id="monthlyGrowthRate" 
                            value={monthlyGrowthRate}
                            className="w-40 text-base" 
                            onChange={(e) => setMonthlyGrowthRate(Number(e.target.value))}
                            step={0.5}
                          />
                        </div>
                      </div>
                      
                      <div className="space-y-5">
                        <div className="flex justify-between items-center">
                          <Label htmlFor="costOfGoods" className="text-base flex items-center font-medium">
                            Cost of Goods / Services (%)
                            <TooltipProvider>
                              <UITooltip>
                                <TooltipTrigger asChild>
                                  <Info className="ml-2 h-4 w-4 text-muted-foreground" />
                                </TooltipTrigger>
                                <TooltipContent className="p-3 w-64 text-base">
                                  <p className="max-w-xs">The percentage of revenue that goes to direct costs (materials, labor, etc.)</p>
                                </TooltipContent>
                              </UITooltip>
                            </TooltipProvider>
                          </Label>
                          <span className="text-right font-medium text-lg text-red-500">{costOfGoods}%</span>
                        </div>
                        <div className="flex items-center gap-6">
                          <Slider 
                            min={0} 
                            max={90} 
                            step={1} 
                            defaultValue={[30]} 
                            value={[costOfGoods]}
                            className="flex-1"
                            onValueChange={(value) => setCostOfGoods(value[0])}
                          />
                          <Input 
                            type="number" 
                            id="costOfGoods" 
                            value={costOfGoods}
                            className="w-40 text-base" 
                            onChange={(e) => setCostOfGoods(Number(e.target.value))}
                          />
                        </div>
                      </div>
                      
                      <div className="space-y-5">
                        <div className="flex justify-between items-center">
                          <Label htmlFor="fixedCosts" className="text-base flex items-center font-medium">
                            Fixed Monthly Costs
                            <TooltipProvider>
                              <UITooltip>
                                <TooltipTrigger asChild>
                                  <Info className="ml-2 h-4 w-4 text-muted-foreground" />
                                </TooltipTrigger>
                                <TooltipContent className="p-3 w-64 text-base">
                                  <p className="max-w-xs">Monthly costs that don't change with sales volume (rent, salaries, etc.)</p>
                                </TooltipContent>
                              </UITooltip>
                            </TooltipProvider>
                          </Label>
                          <span className="text-right font-medium text-lg text-red-500">${fixedCosts.toLocaleString()}</span>
                        </div>
                        <div className="flex items-center gap-6">
                          <Slider 
                            min={0} 
                            max={10000} 
                            step={100} 
                            defaultValue={[2000]} 
                            value={[fixedCosts]}
                            className="flex-1"
                            onValueChange={(value) => setFixedCosts(value[0])}
                          />
                          <Input 
                            type="number" 
                            id="fixedCosts" 
                            value={fixedCosts}
                            className="w-40 text-base" 
                            onChange={(e) => setFixedCosts(Number(e.target.value))}
                          />
                        </div>
                      </div>
                      
                      <div className="space-y-5">
                        <div className="flex justify-between items-center">
                          <Label htmlFor="forecastMonths" className="text-base flex items-center font-medium">
                            Forecast Period (Months)
                            <TooltipProvider>
                              <UITooltip>
                                <TooltipTrigger asChild>
                                  <Info className="ml-2 h-4 w-4 text-muted-foreground" />
                                </TooltipTrigger>
                                <TooltipContent className="p-3 w-64 text-base">
                                  <p className="max-w-xs">How many months to include in your forecast</p>
                                </TooltipContent>
                              </UITooltip>
                            </TooltipProvider>
                          </Label>
                          <span className="text-right font-medium text-lg">{forecastMonths} months</span>
                        </div>
                        <div className="flex items-center gap-6">
                          <Slider 
                            min={3} 
                            max={24} 
                            step={1} 
                            defaultValue={[12]} 
                            value={[forecastMonths]}
                            className="flex-1"
                            onValueChange={(value) => setForecastMonths(value[0])}
                          />
                          <Input 
                            type="number" 
                            id="forecastMonths" 
                            value={forecastMonths}
                            className="w-40 text-base" 
                            onChange={(e) => setForecastMonths(Number(e.target.value))}
                            min={1}
                          />
                        </div>
                      </div>
                      
                      <div className="space-y-5">
                        <div className="flex justify-between items-center">
                          <Label htmlFor="seasonality" className="text-base flex items-center font-medium">
                            Seasonality Pattern
                            <TooltipProvider>
                              <UITooltip>
                                <TooltipTrigger asChild>
                                  <Info className="ml-2 h-4 w-4 text-muted-foreground" />
                                </TooltipTrigger>
                                <TooltipContent className="p-3 w-64 text-base">
                                  <p className="max-w-xs">Apply seasonal fluctuations to your revenue forecast</p>
                                </TooltipContent>
                              </UITooltip>
                            </TooltipProvider>
                          </Label>
                        </div>
                        <Select value={seasonality} onValueChange={setSeasonality}>
                          <SelectTrigger className="w-full text-base h-12">
                            <SelectValue placeholder="Select seasonality pattern" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="none">No Seasonality</SelectItem>
                            <SelectItem value="summer">Summer Peak</SelectItem>
                            <SelectItem value="winter">Winter Peak</SelectItem>
                            <SelectItem value="spring">Spring Peak</SelectItem>
                            <SelectItem value="quarterly">Quarterly Cycle</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </CardContent>
                  </Card>
                  
                  <div className="mt-10">
                    <Card>
                      <CardHeader>
                        <CardTitle className="text-2xl">Forecast Insights</CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-6 p-6">
                        <div className="space-y-5">
                          <div className="flex gap-5 items-start">
                            <div className="p-3 rounded-full bg-primary/10 flex-shrink-0">
                              <TrendingUp className="h-7 w-7 text-primary" />
                            </div>
                            <div>
                              <h4 className="text-xl font-medium mb-2">Growth Trajectory</h4>
                              <p className="text-lg text-foreground/70">
                                With your current growth rate of {monthlyGrowthRate}% monthly, your business could grow by approximately {summaryData.total.growthRate}% annually.
                              </p>
                            </div>
                          </div>
                          
                          <div className="flex gap-5 items-start">
                            <div className="p-3 rounded-full bg-primary/10 flex-shrink-0">
                              <Info className="h-7 w-7 text-primary" />
                            </div>
                            <div>
                              <h4 className="text-xl font-medium mb-2">Profit Margin</h4>
                              <p className="text-lg text-foreground/70">
                                Your forecast indicates an average profit margin of {summaryData.average.profitMargin}%, which is 
                                {summaryData.average.profitMargin >= 20 ? ' strong' : summaryData.average.profitMargin >= 10 ? ' moderate' : ' low'} 
                                for most service businesses.
                              </p>
                            </div>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  </div>
                </div>

                <div>
                  <Card className="sticky top-24 border-2 shadow-lg bg-white dark:bg-gray-800">
                    <CardHeader className="pb-2">
                      <CardTitle className="flex items-center text-2xl">
                        <LineChartIcon className="mr-3 h-6 w-6 text-primary" />
                        Profit Forecast
                      </CardTitle>
                      <CardDescription className="text-base">Projected financial performance</CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-6 pt-2 p-6">
                      <Tabs defaultValue="chart" className="w-full">
                        <TabsList className="grid grid-cols-2 mb-6 w-full">
                          <TabsTrigger value="chart" className="text-lg py-2.5">Chart View</TabsTrigger>
                          <TabsTrigger value="table" className="text-lg py-2.5">Table View</TabsTrigger>
                        </TabsList>
                        
                        <TabsContent value="chart" className="space-y-8">
                          <div className="h-[500px] w-full">
                            <ResponsiveContainer width="100%" height="100%">
                              <ReLineChart
                                data={monthlyData}
                                margin={{
                                  top: 10,
                                  right: 10,
                                  left: 15,
                                  bottom: 5,
                                }}
                              >
                                <CartesianGrid strokeDasharray="3 3" />
                                <XAxis dataKey="name" tick={{ fontSize: 12 }} />
                                <YAxis 
                                  width={70} 
                                  tick={{ fontSize: 12 }} 
                                  tickFormatter={formatYAxis} 
                                />
                                <Tooltip 
                                  formatter={(value) => formatCurrency(Number(value))}
                                  contentStyle={{ fontSize: '14px', padding: '10px' }} 
                                  labelStyle={{ fontWeight: 'bold', marginBottom: '5px' }}
                                />
                                <Line 
                                  type="monotone" 
                                  dataKey="revenue" 
                                  name="Revenue" 
                                  stroke="#8884d8" 
                                  strokeWidth={2}
                                  activeDot={{ r: 8 }} 
                                />
                                <Line 
                                  type="monotone" 
                                  dataKey="expenses" 
                                  name="Expenses" 
                                  stroke="#82ca9d" 
                                  strokeWidth={2}
                                />
                                <Line 
                                  type="monotone" 
                                  dataKey="profit" 
                                  name="Profit" 
                                  stroke="#ff7300" 
                                  strokeWidth={2}
                                />
                              </ReLineChart>
                            </ResponsiveContainer>
                          </div>
                          
                          <div className="grid grid-cols-2 gap-4">
                            <Card className="shadow-sm">
                              <CardHeader className="pb-3">
                                <CardTitle className="text-lg font-bold text-center">Period Totals</CardTitle>
                              </CardHeader>
                              <CardContent className="space-y-3 px-4 pb-4">
                                <div className="flex justify-between items-center py-1">
                                  <span className="text-sm text-muted-foreground">Revenue</span>
                                  <span className="font-semibold text-sm">{formatCurrency(summaryData.total.revenue)}</span>
                                </div>
                                <Separator />
                                <div className="flex justify-between items-center py-1">
                                  <span className="text-sm text-muted-foreground">Expenses</span>
                                  <span className="font-semibold text-sm">{formatCurrency(summaryData.total.expenses)}</span>
                                </div>
                                <Separator />
                                <div className="flex justify-between items-center py-1">
                                  <span className="text-sm text-muted-foreground">Profit</span>
                                  <span className="font-bold text-primary text-sm">{formatCurrency(summaryData.total.profit)}</span>
                                </div>
                                <Separator />
                                <div className="flex justify-between items-center py-1">
                                  <span className="text-sm text-muted-foreground">Annual Growth</span>
                                  <span className={`font-semibold text-sm ${summaryData.total.growthRate >= 0 ? 'text-green-500' : 'text-red-500'}`}>
                                    {summaryData.total.growthRate}%
                                  </span>
                                </div>
                              </CardContent>
                            </Card>
                            
                            <Card className="shadow-sm">
                              <CardHeader className="pb-3">
                                <CardTitle className="text-lg font-bold text-center">Monthly Averages</CardTitle>
                              </CardHeader>
                              <CardContent className="space-y-3 px-4 pb-4">
                                <div className="flex justify-between items-center py-1">
                                  <span className="text-sm text-muted-foreground">Revenue</span>
                                  <span className="font-semibold text-sm">{formatCurrency(summaryData.average.revenue)}</span>
                                </div>
                                <Separator />
                                <div className="flex justify-between items-center py-1">
                                  <span className="text-sm text-muted-foreground">Expenses</span>
                                  <span className="font-semibold text-sm">{formatCurrency(summaryData.average.expenses)}</span>
                                </div>
                                <Separator />
                                <div className="flex justify-between items-center py-1">
                                  <span className="text-sm text-muted-foreground">Profit</span>
                                  <span className="font-bold text-primary text-sm">{formatCurrency(summaryData.average.profit)}</span>
                                </div>
                                <Separator />
                                <div className="flex justify-between items-center py-1">
                                  <span className="text-sm text-muted-foreground">Profit Margin</span>
                                  <span className={`font-semibold text-sm ${summaryData.average.profitMargin >= 0 ? 'text-green-500' : 'text-red-500'}`}>
                                    {summaryData.average.profitMargin}%
                                  </span>
                                </div>
                              </CardContent>
                            </Card>
                          </div>
                        </TabsContent>
                        
                        <TabsContent value="table">
                          <div className="border rounded-md overflow-x-auto max-h-[500px]">
                            <table className="min-w-full divide-y divide-border">
                              <thead className="bg-muted/50 sticky top-0">
                                <tr>
                                  <th className="px-4 py-3 text-left text-sm font-semibold">Month</th>
                                  <th className="px-4 py-3 text-right text-sm font-semibold">Revenue</th>
                                  <th className="px-4 py-3 text-right text-sm font-semibold">Expenses</th>
                                  <th className="px-4 py-3 text-right text-sm font-semibold">Profit</th>
                                </tr>
                              </thead>
                              <tbody className="divide-y divide-gray-200 dark:divide-gray-700">
                                {monthlyData.map((month, index) => (
                                  <tr key={index}>
                                    <td className="px-4 py-3 whitespace-nowrap text-sm">{month.name}</td>
                                    <td className="px-4 py-3 whitespace-nowrap text-sm text-right">{formatCurrency(month.revenue)}</td>
                                    <td className="px-4 py-3 whitespace-nowrap text-sm text-right">{formatCurrency(month.expenses)}</td>
                                    <td className={`px-4 py-3 whitespace-nowrap text-sm font-medium text-right ${month.profit >= 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'}`}>
                                      {formatCurrency(month.profit)}
                                    </td>
                                  </tr>
                                ))}
                              </tbody>
                              <tfoot className="sticky bottom-0 bg-muted/50">
                                <tr className="border-t-2 border-gray-300 dark:border-gray-600">
                                  <td className="px-4 py-3 whitespace-nowrap text-sm font-bold">Total</td>
                                  <td className="px-4 py-3 whitespace-nowrap text-sm font-bold text-right">{formatCurrency(summaryData.total.revenue)}</td>
                                  <td className="px-4 py-3 whitespace-nowrap text-sm font-bold text-right">{formatCurrency(summaryData.total.expenses)}</td>
                                  <td className={`px-4 py-3 whitespace-nowrap text-sm font-bold text-right ${summaryData.total.profit >= 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'}`}>
                                    {formatCurrency(summaryData.total.profit)}
                                  </td>
                                </tr>
                              </tfoot>
                            </table>
                          </div>
                        </TabsContent>
                      </Tabs>
                    </CardContent>
                    <CardFooter className="flex-col space-y-2 p-6 pt-2 border-t">
                      <Button className="w-full text-base py-6" variant="default" onClick={downloadForecast}>
                        <Download className="mr-2 h-5 w-5" />
                        Download Forecast
                      </Button>
                    </CardFooter>
                  </Card>
                </div>
              </div>
            </TabsContent>
            
            <TabsContent value="about">
              <Card className="border-2 shadow-md">
                <CardHeader>
                  <CardTitle className="text-2xl">Understanding Profit Forecasting</CardTitle>
                  <CardDescription className="text-lg">
                    Learn how profit forecasts help your business plan for the future and make informed decisions
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-8">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                    <div>
                      <h3 className="text-xl font-semibold mb-4">What is Profit Forecasting?</h3>
                      <p className="text-lg text-gray-700 leading-relaxed">
                        Profit forecasting is the process of estimating future financial performance of a business, 
                        including revenue, expenses, and ultimately profit. It helps business owners make strategic 
                        decisions about growth, investments, and cash flow management.
                      </p>
                      
                      <h3 className="text-xl font-semibold mt-8 mb-4">Key Benefits of Forecasting</h3>
                      <ul className="list-disc pl-5 space-y-3 text-lg text-gray-700">
                        <li><span className="font-medium">Strategic Planning:</span> Make informed decisions about growth initiatives and investments.</li>
                        <li><span className="font-medium">Cash Flow Management:</span> Anticipate cash needs and avoid liquidity problems.</li>
                        <li><span className="font-medium">Goal Setting:</span> Establish realistic financial targets and track progress.</li>
                        <li><span className="font-medium">Risk Assessment:</span> Identify potential financial challenges before they occur.</li>
                      </ul>
                    </div>
                    
                    <div className="bg-blue-50 p-8 rounded-xl">
                      <h3 className="text-xl font-semibold mb-6">Forecast Accuracy Factors</h3>
                      <div className="space-y-6">
                        <div>
                          <div className="flex justify-between text-base mb-2">
                            <span className="font-medium">Growth Assumptions</span>
                            <span>High Impact</span>
                          </div>
                          <div className="w-full bg-gray-200 rounded-full h-3">
                            <div className="bg-blue-600 h-3 rounded-full" style={{width: '90%'}}></div>
                          </div>
                        </div>
                        <div>
                          <div className="flex justify-between text-base mb-2">
                            <span className="font-medium">Competitive Landscape</span>
                            <span>Medium Impact</span>
                          </div>
                          <div className="w-full bg-gray-200 rounded-full h-3">
                            <div className="bg-blue-600 h-3 rounded-full" style={{width: '60%'}}></div>
                          </div>
                        </div>
                        <div>
                          <div className="flex justify-between text-base mb-2">
                            <span className="font-medium">Economic Conditions</span>
                            <span>Medium Impact</span>
                          </div>
                          <div className="w-full bg-gray-200 rounded-full h-3">
                            <div className="bg-blue-600 h-3 rounded-full" style={{width: '65%'}}></div>
                          </div>
                        </div>
                        <div>
                          <div className="flex justify-between text-base mb-2">
                            <span className="font-medium">Seasonality</span>
                            <span>Variable Impact</span>
                          </div>
                          <div className="w-full bg-gray-200 rounded-full h-3">
                            <div className="bg-blue-600 h-3 rounded-full" style={{width: '75%'}}></div>
                          </div>
                        </div>
                        <div>
                          <div className="flex justify-between text-base mb-2">
                            <span className="font-medium">Historical Data Accuracy</span>
                            <span>High Impact</span>
                          </div>
                          <div className="w-full bg-gray-200 rounded-full h-3">
                            <div className="bg-blue-600 h-3 rounded-full" style={{width: '85%'}}></div>
                          </div>
                        </div>
                      </div>
                      <p className="text-xs text-gray-500 mt-4">* Impact levels indicate how much each factor affects the overall accuracy of your forecast</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
              
              <div className="mt-12">
                <h2 className="text-3xl font-semibold mb-8 text-center">Tips for Better Profit Forecasting</h2>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mt-6">
                  <Card className="border-2 shadow-sm hover:shadow-md transition-all duration-300">
                    <CardHeader className="pb-2">
                      <CardTitle className="text-xl flex items-center">
                        <span className="p-2 mr-3 rounded-full bg-primary/10">
                          <LineChartIcon className="h-5 w-5 text-primary" />
                        </span>
                        Use Multiple Scenarios
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <p className="text-lg text-foreground/80 leading-relaxed">
                        Create best-case, worst-case, and most likely scenarios to understand the range of possible outcomes for your business.
                      </p>
                    </CardContent>
                  </Card>
                  <Card className="border-2 shadow-sm hover:shadow-md transition-all duration-300">
                    <CardHeader className="pb-2">
                      <CardTitle className="text-xl flex items-center">
                        <span className="p-2 mr-3 rounded-full bg-primary/10">
                          <TrendingUp className="h-5 w-5 text-primary" />
                        </span>
                        Review and Update Regularly
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <p className="text-lg text-foreground/80 leading-relaxed">
                        Compare your actual results against forecasts monthly or quarterly, and adjust your future projections based on real data.
                      </p>
                    </CardContent>
                  </Card>
                  <Card className="border-2 shadow-sm hover:shadow-md transition-all duration-300">
                    <CardHeader className="pb-2">
                      <CardTitle className="text-xl flex items-center">
                        <span className="p-2 mr-3 rounded-full bg-primary/10">
                          <FileText className="h-5 w-5 text-primary" />
                        </span>
                        Document Assumptions
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <p className="text-lg text-foreground/80 leading-relaxed">
                        Keep track of the assumptions behind your forecast, including market conditions and growth expectations, to better understand variances.
                      </p>
                    </CardContent>
                  </Card>
                </div>
              </div>
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </Layout>
  );
};

export default ProfitForecastTool;
