import React, { useState, useEffect } from 'react';
import { ServicePageTemplate } from '@/components/ServicePageTemplate';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { ServiceNeeds } from '@/components/ServiceNeeds';
import { BlogCard, BlogPost } from '@/components/BlogCard';
import { Badge } from '@/components/ui/badge';
import { Link } from 'react-router-dom';
import { Clock, Shield, Check, Heart, Star, ArrowRight, ChevronRight, DollarSign, Bug, Rat, Zap, Droplets, BookCheck, Bird, Feather, Award } from 'lucide-react';
import { useIsMobile } from '@/hooks/use-mobile';
import { SEO } from '@/components/SEO';
import { fetchPosts } from '@/services/wordpressApi';
import {SERVICE_CATEGORY} from "@/types/enum.ts";

const pestControlServices = [{
  icon: <Bug />,
  label: "Ant Control"
}, {
  icon: <Bug />,
  label: "Spider Control"
}, {
  icon: <Bug />,
  label: "Roach Control"
}, {
  icon: <Rat />,
  label: "Rodent Control"
}, {
  icon: <Zap />,
  label: "Termite Treatment"
}, {
  icon: <Droplets />,
  label: "Mosquito Control"
}, {
  icon: <Bird />,
  label: "Bed Bug Treatment"
}, {
  icon: <Feather />,
  label: "Wildlife Removal"
}, {
  icon: <Bug />,
  label: "Wasp & Hornet Removal"
}, {
  icon: <BookCheck />,
  label: "Commercial Services"
}];
const pestControlEstimates = [{
  tier: "Basic",
  price: "$99-149",
  description: "One-time treatment for minor pest issues",
  features: ["Licensed technicians", "30-day warranty", "Common pests treatment", "Exterior perimeter spray"]
}, {
  tier: "Standard",
  price: "$179-249",
  description: "Comprehensive treatment with follow-up",
  features: ["Licensed & insured pros", "90-day warranty", "Interior & exterior treatment", "Pest identification", "Prevention consultation"],
  recommended: true
}, {
  tier: "Premium",
  price: "$299-449",
  description: "Complete year-round protection",
  features: ["Senior technicians", "1-year warranty", "Quarterly service visits", "All pest coverage", "Preventative treatments", "Emergency service"]
}];

const PestControlService: React.FC = () => {
  const isMobile = useIsMobile();
    const [pestControlBlogPosts, setPestControlBlogPosts] = useState<BlogPost[]>([])

    useEffect(() => {
        const loadInitialData = async () => {
            try {
                const { posts, totalPages: pages } = await fetchPosts(1, 3, SERVICE_CATEGORY.PEST_CONTROL);
                setPestControlBlogPosts(posts)
            } catch (err) {
                console.log(err);
            }
        };
        loadInitialData();
    }, []);
  return <>
      <SEO title="Experienced Pest Control Experts Near You | Safe, Affordable Bids" description="Eliminate pests with help from certified pest control pros in your area. Get free quotes, compare providers, and book expert services through JobON." localBusinessSchema={true} serviceType="Pest Control" serviceSlug="pest-control" canonicalUrl="/services/pest-control" />
      <section className="relative bg-gradient-to-b from-blue-50 to-white dark:from-gray-900 dark:to-background pt-20 pb-8 md:pt-24 md:pb-12">
        <div className="container mx-auto px-4 lg:px-8">
          {isMobile ? <div className="w-full">
              <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden mb-6">
                <div className="relative h-52 overflow-hidden">
                  <img src="/lovable-uploads/607cafaa-73e0-4e78-9d8c-d4f466297851.png" alt="Professional pest control technician spraying for pests" className="w-full h-full object-cover object-center" />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/50 to-transparent flex items-end">
                    <div className="p-4 text-white w-full">
                      <h1 className="text-2xl font-bold mb-1 text-left">
                        Professional Pest Control
                      </h1>
                      <h2 className="text-lg font-medium text-blue-50 mb-2 text-left">
                        <span className="block">Expert Pest Elimination</span>
                        <span className="block">Safe & Effective Solutions</span>
                      </h2>
                      <div className="flex items-center space-x-2">
                        <div className="flex">
                          {[1, 2, 3, 4, 5].map((_, i) => <svg key={i} className="w-4 h-4 text-amber-400 fill-amber-400" viewBox="0 0 24 24"><path d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z" /></svg>)}
                        </div>
                        <span className="text-sm font-medium text-white">4.8/5 · 1,435 reviews</span>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="p-4">
                  <p className="text-sm text-gray-800 dark:text-gray-300 mb-4 font-medium">
                    Professional pest control services for homes, businesses, and commercial properties
                  </p>

                  <div className="flex flex-col space-y-3 mb-4">
                    <Link to="/create-job" className="w-full">
                      <Button variant="default" size="lg" className="w-full rounded-lg font-semibold text-base px-6 py-6 h-auto">
                        Get Free Quotes
                      </Button>
                    </Link>
                    <Link to="/professionals/pest-control" className="w-full">
                      <Button variant="outline" size="default" className="w-full rounded-lg font-semibold text-base py-2.5 text-gray-800 dark:text-white border-gray-400">
                        Browse Pest Control Pros
                      </Button>
                    </Link>
                  </div>
                </div>
              </div>

              <div className="bg-white dark:bg-gray-800 rounded-xl shadow p-3 mb-5 flex items-center justify-between">
                <div className="flex items-center">
                  <Shield className="h-5 w-5 text-green-500 mr-1.5" />
                  <div className="text-left">
                    <span className="block text-xs text-gray-600 dark:text-gray-400">
                      Licensed
                    </span>
                    <span className="font-bold text-xs text-black dark:text-white">
                      Technicians
                    </span>
                  </div>
                </div>
                <div className="w-px h-8 bg-gray-200 dark:bg-gray-700"></div>
                <div className="flex items-center">
                  <Clock className="h-5 w-5 text-blue-500 mr-1.5" />
                  <div className="text-left">
                    <span className="block text-xs text-gray-600 dark:text-gray-400">Same-Day</span>
                    <span className="font-bold text-xs text-black dark:text-white">Service</span>
                  </div>
                </div>
                <div className="w-px h-8 bg-gray-200 dark:bg-gray-700"></div>
                <div className="flex items-center">
                  <Award className="h-5 w-5 text-purple-500 mr-1.5" />
                  <div className="text-left">
                    <span className="block text-xs text-gray-600 dark:text-gray-400">
                      100%
                    </span>
                    <span className="font-bold text-xs text-black dark:text-white">Satisfaction</span>
                  </div>
                </div>
              </div>
            </div> : <div className="flex flex-col lg:flex-row gap-12 items-center">
              <div className="w-full lg:w-1/2 space-y-8">
                <div className="space-y-4">
                  <span className="inline-block bg-primary/10 text-primary dark:bg-primary/20 dark:text-primary-hover px-4 py-1.5 rounded-full font-medium text-sm">Professional Pest Control Services</span>
                  <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900 dark-heading leading-tight">
                    Expert Pest Control,
                    <br className="hidden md:inline" />
                    <span className="text-primary mt-2 block dark:text-primary-hover dark:dark-mode-text-shadow">
                      Peace of Mind.
                    </span>
                  </h1>
                  <div className="text-xl font-semibold text-gray-700 dark-subheading">
                    <div className="flex flex-col space-y-3 text-left">
                      <div className="flex items-center gap-2">
                        <div className="flex-shrink-0 h-2 w-2 bg-primary rounded-full"></div>
                        <span className="text-gray-900 dark:text-white">Homes, Businesses & Properties</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <div className="flex-shrink-0 h-2 w-2 bg-primary rounded-full"></div>
                        <span className="text-gray-900 dark:text-white">Licensed & Insured Professionals</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <div className="flex-shrink-0 h-2 w-2 bg-primary rounded-full"></div>
                        <span className="text-gray-900 dark:text-white">Customized Treatment Plans</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <div className="flex-shrink-0 h-2 w-2 bg-primary rounded-full"></div>
                        <span className="text-gray-900 dark:text-white">Eco-Friendly Options Available</span>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="flex flex-col sm:flex-row gap-4 pt-6">
                  <Link to="/create-job" className="w-full sm:w-auto">
                    <Button variant="default" size="lg" className="text-lg font-medium w-full sm:w-auto px-8 py-6 h-auto rounded-xl shadow-lg hover:shadow-xl hover:translate-y-[-2px] transition-all">
                      Post a Project
                      <ArrowRight size={20} className="ml-2" />
                    </Button>
                  </Link>
                  <Link to="/professionals/pest-control" className="w-full sm:w-auto">
                    <Button variant="secondary" size="lg" className="text-lg font-medium w-full sm:w-auto px-8 py-6 h-auto rounded-xl border-2 border-gray-400 dark:border-gray-500 bg-gray-50 dark:bg-gray-800 text-gray-800 dark:text-white hover:bg-primary/10 dark:hover:bg-primary/20 hover:border-primary dark:hover:border-primary hover:text-primary dark:hover:text-primary-foreground shadow-lg hover:shadow-xl hover:translate-y-[-2px] transition-all">
                      Browse Pest Pros
                    </Button>
                  </Link>
                </div>

                <div className="pt-6 grid grid-cols-2 md:grid-cols-3 gap-4">
                  <div className="flex items-center gap-2 text-gray-700 dark:text-gray-300">
                    <Check className="h-5 w-5 text-green-500" />
                    <span>Instant Quotes</span>
                  </div>
                  <div className="flex items-center gap-2 text-gray-700 dark:text-gray-300">
                    <Check className="h-5 w-5 text-green-500" />
                    <span>Verified Technicians</span>
                  </div>
                  <div className="flex items-center gap-2 text-gray-700 dark:text-gray-300">
                    <Check className="h-5 w-5 text-green-500" />
                    <span>100% Satisfaction</span>
                  </div>
                  <div className="flex items-center gap-2 text-gray-700 dark:text-gray-300">
                    <Check className="h-5 w-5 text-green-500" />
                    <span>Customized Plans</span>
                  </div>
                  <div className="flex items-center gap-2 text-gray-700 dark:text-gray-300">
                    <Check className="h-5 w-5 text-green-500" />
                    <span>Transparent Pricing</span>
                  </div>
                  <div className="flex items-center gap-2 text-gray-700 dark:text-gray-300">
                    <Check className="h-5 w-5 text-green-500" />
                    <span>Bonded & Insured</span>
                  </div>
                </div>
              </div>

              <div className="w-full lg:w-1/2 relative">
                <div className="absolute inset-0 bg-primary/10 dark:bg-primary/5 rounded-xl filter blur-xl opacity-70"></div>
                <div className="relative bg-white dark:bg-gray-800 p-6 rounded-2xl shadow-xl border border-gray-100 dark:border-gray-700 overflow-hidden">
                  <div className="grid grid-cols-2 gap-3">
                    <div className="aspect-[4/3] rounded-lg overflow-hidden">
                      <img src="/lovable-uploads/607cafaa-73e0-4e78-9d8c-d4f466297851.png" alt="Pest control professional with protective equipment" className="w-full h-full object-cover" />
                    </div>
                    <div className="aspect-[4/3] rounded-lg overflow-hidden">
                      <img src="/lovable-uploads/6f3dce3b-4c53-4ffd-9c4d-1e90b55e2eb5.png" alt="Pest control technician treating baseboards" className="w-full h-full object-cover" />
                    </div>
                    <div className="col-span-2 aspect-[16/9] rounded-lg overflow-hidden">
                      <img src="/lovable-uploads/0139a956-a8bc-47ba-88ad-fc95faf71caa.png" alt="Pest control specialist applying treatment" className="w-full h-full object-cover" />
                    </div>
                  </div>

                  <div className="mt-6 text-center">
                    <div className="flex justify-center mb-2">
                      <div className="flex text-amber-400">
                        <svg className="w-5 h-5 fill-current" viewBox="0 0 24 24"><path d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z" /></svg>
                        <svg className="w-5 h-5 fill-current" viewBox="0 0 24 24"><path d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z" /></svg>
                        <svg className="w-5 h-5 fill-current" viewBox="0 0 24 24"><path d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z" /></svg>
                        <svg className="w-5 h-5 fill-current" viewBox="0 0 24 24"><path d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z" /></svg>
                        <svg className="w-5 h-5 fill-current" viewBox="0 0 24 24"><path d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z" /></svg>
                      </div>
                      <span className="ml-2 text-sm font-medium text-gray-700 dark:text-gray-300">4.8/5 (1,435 reviews)</span>
                    </div>
                    <h3 className="font-bold text-xl mb-1 dark:text-white">Find Top Local Pest Controllers</h3>
                    <p className="text-gray-600 dark:text-gray-300 text-sm mb-4">Enter your ZIP code to get matched with professional pest control experts in your area</p>

                    <div className="flex shadow-lg rounded-xl overflow-hidden">
                      <div className="flex-1 flex items-center bg-gray-50 dark:bg-gray-700 p-4">
                        <input type="text" placeholder="Enter your ZIP code" className="w-full bg-transparent border-none focus:outline-none text-gray-900 dark:text-white placeholder:text-gray-400 dark:placeholder:text-gray-300 text-lg" />
                      </div>
                      <Button type="submit" className="px-8 h-auto rounded-none bg-primary hover:bg-primary/90 text-white transition-all">
                        <span>Search</span>
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            </div>}
        </div>
      </section>

      <section className="py-8 md:py-16 bg-white dark:bg-gray-800">
        <div className="container mx-auto px-4 lg:px-8">
          <div className="text-center mb-6 md:mb-12">
            <h2 className="text-2xl md:text-3xl font-bold mb-2 md:mb-3 text-black dark:text-white">Common Professional Pest Control Services</h2>
            <p className="text-base md:text-lg text-gray-800 dark:text-gray-300 max-w-2xl mx-auto font-medium">
              Select from our most requested pest control services
            </p>
          </div>

          <ServiceNeeds serviceId="pest-control" needs={pestControlServices} estimates={pestControlEstimates} />
        </div>
      </section>

      <section className="py-8 md:py-16 bg-gray-50 dark:bg-gray-900">
        <div className="container mx-auto px-4 lg:px-8">
          <div className="text-center mb-6 md:mb-12">
            <h2 className="text-2xl md:text-3xl font-bold mb-2 md:mb-3 text-black dark:text-white">Recently Completed Pest Control Projects</h2>
            <p className="text-base md:text-lg text-gray-800 dark:text-gray-300 max-w-2xl mx-auto">
              Browse through recently completed pest control jobs by our network of professionals
            </p>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 md:gap-8">
            <div className="bg-white dark:bg-gray-800 rounded-xl overflow-hidden shadow-md hover:shadow-lg transition-all">
              <img src="https://images.unsplash.com/photo-1564013799919-ab600027ffc6" alt="Residential pest control" className="w-full h-36 md:h-48 object-cover" />
              <div className="p-4">
                <div className="flex justify-between items-center mb-2">
                  <span className="text-xs md:text-sm font-medium text-primary bg-primary/10 dark:text-primary-hover dark:bg-primary/20 px-2 py-0.5 rounded-full">Complete</span>
                  <span className="text-xs md:text-sm font-medium text-gray-500 dark:text-gray-400">2 days ago</span>
                </div>
                <h3 className="font-bold text-base md:text-lg mb-1 md:mb-2 dark:text-white">Residential Pest Control</h3>
                <p className="text-gray-600 dark:text-gray-300 text-xs md:text-sm mb-3 md:mb-4">Complete pest elimination for a 4-bedroom home with ongoing quarterly maintenance.</p>
                <div className="flex items-center justify-between">
                  <span className="text-gray-700 dark:text-gray-300 font-medium text-xs md:text-sm">Atlanta, GA</span>
                  <span className="font-bold text-base md:text-lg dark:text-white">$219</span>
                </div>
              </div>
            </div>

            <div className="bg-white dark:bg-gray-800 rounded-xl overflow-hidden shadow-md hover:shadow-lg transition-all">
              <img src="https://images.unsplash.com/photo-1466978913421-dad2ebd01d17" alt="Commercial rodent control" className="w-full h-36 md:h-48 object-cover" />
              <div className="p-4">
                <div className="flex justify-between items-center mb-2">
                  <span className="text-xs md:text-sm font-medium text-primary bg-primary/10 dark:text-primary-hover dark:bg-primary/20 px-2 py-0.5 rounded-full">Complete</span>
                  <span className="text-xs md:text-sm font-medium text-gray-500 dark:text-gray-400">5 days ago</span>
                </div>
                <h3 className="font-bold text-base md:text-lg mb-1 md:mb-2 dark:text-white">Commercial Rodent Control</h3>
                <p className="text-gray-600 dark:text-gray-300 text-xs md:text-sm mb-3 md:mb-4">Comprehensive rodent management system for a restaurant with follow-up monitoring.</p>
                <div className="flex items-center justify-between">
                  <span className="text-gray-700 dark:text-gray-300 font-medium text-xs md:text-sm">Chicago, IL</span>
                  <span className="font-bold text-base md:text-lg dark:text-white">$425</span>
                </div>
              </div>
            </div>

            <div className="bg-white dark:bg-gray-800 rounded-xl overflow-hidden shadow-md hover:shadow-lg transition-all">
              <img src="https://images.unsplash.com/photo-1568605114967-8130f3a36994" alt="Termite treatment" className="w-full h-36 md:h-48 object-cover" />
              <div className="p-4">
                <div className="flex justify-between items-center mb-2">
                  <span className="text-xs md:text-sm font-medium text-primary bg-primary/10 dark:text-primary-hover dark:bg-primary/20 px-2 py-0.5 rounded-full">Complete</span>
                  <span className="text-xs md:text-sm font-medium text-gray-500 dark:text-gray-400">1 week ago</span>
                </div>
                <h3 className="font-bold text-base md:text-lg mb-1 md:mb-2 dark:text-white">Termite Treatment</h3>
                <p className="text-gray-600 dark:text-gray-300 text-xs md:text-sm mb-3 md:mb-4">Full property termite barrier installation with 10-year protection guarantee.</p>
                <div className="flex items-center justify-between">
                  <span className="text-gray-700 dark:text-gray-300 font-medium text-xs md:text-sm">Phoenix, AZ</span>
                  <span className="font-bold text-base md:text-lg dark:text-white">$975</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <section className="py-8 md:py-16 bg-white dark:bg-gray-800">
        <div className="container mx-auto px-4 lg:px-8">
          <div className="text-center mb-6 md:mb-12">
            <h2 className="text-2xl md:text-3xl font-bold mb-2 md:mb-3 text-black dark:text-white">Latest Pest Control Tips & Guides</h2>
            <p className="text-base md:text-lg text-gray-800 dark:text-gray-300 max-w-2xl mx-auto">
              Expert advice and helpful resources for maintaining a pest-free environment
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {pestControlBlogPosts.map(post => <BlogCard key={post.id} post={post} />)}
          </div>

          <div className="text-center mt-8 md:mt-12">
            <Link to="/blog">
              <Button variant="default" size={isMobile ? "default" : "lg"} className="font-medium">
                View All Pest Control Articles
                <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
            </Link>
          </div>
        </div>
      </section>

      <ServicePageTemplate serviceId="pest-control" title="Professional Pest Control Services" subtitle="Expert pest management solutions to keep your home and business pest-free" description="From residential pest removal to commercial pest management, our licensed technicians use safe, effective methods to eliminate unwanted pests." heroImage="/lovable-uploads/607cafaa-73e0-4e78-9d8c-d4f466297851.png" benefits={[{
      title: "Licensed & Insured Professionals",
      description: "All our pest control specialists are fully licensed, insured, and undergo rigorous background checks.",
      icon: <Shield />
    }, {
      title: "Satisfaction Guaranteed",
      description: "We stand behind our work with a 100% satisfaction guarantee on all our pest control services.",
      icon: <Check />
    }, {
      title: "Safe & Eco-Friendly Solutions",
      description: "We use products that are safe for your family, pets, and the environment while effectively eliminating pests.",
      icon: <Heart />
    }, {
      title: "Fast Response Time",
      description: "Our team responds quickly to urgent pest issues, often providing same-day or next-day service.",
      icon: <Clock />
    }, {
      title: "Customized Treatment Plans",
      description: "We create personalized pest control strategies tailored to your specific needs and situation.",
      icon: <DollarSign />
    }, {
      title: "Preventative Strategies",
      description: "We don't just eliminate current problems but help prevent future infestations with proactive measures.",
      icon: <Star />
    }]} faqs={[{
      question: "How often should I get pest control service?",
      answer: "Most homes benefit from quarterly pest control services to maintain a pest-free environment year-round. However, this can vary depending on your location, property type, and pest history. Some situations may require monthly visits while others may only need bi-annual treatments."
    }, {
      question: "Are pest control treatments safe for children and pets?",
      answer: "Modern pest control methods use targeted applications and low-toxicity products that are safe for families and pets when properly applied. Our technicians are trained to use the safest effective methods and will provide specific instructions about any waiting periods before re-entry after treatment."
    }, {
      question: "How long does it take to see results from pest control treatment?",
      answer: "For most common pests like ants or spiders, you'll typically see a significant reduction within 24-48 hours. More persistent pests like bed bugs or severe infestations may require multiple treatments over 2-3 weeks for complete elimination."
    }, {
      question: "Do you offer organic or eco-friendly pest control options?",
      answer: "Yes, we provide eco-friendly and organic pest control options that use botanical insecticides and integrated pest management techniques. These methods effectively control pests while minimizing the impact on the environment and non-target organisms."
    }]} estimates={pestControlEstimates} commonNeeds={[]} // Emptied commonNeeds since we're displaying it separately
    hideEstimator={false} hideHero={true} professionalTitle="Pest Pros" seoTitle="Pest Control Experts Near You | Safe, Affordable Bids" customCta={<div className="flex flex-col sm:flex-row justify-center gap-4">
            <Link to="/create-job" className="w-full sm:w-auto">
              <Button variant="default" size="lg" className="text-lg font-medium w-full sm:w-auto px-8 py-6 h-auto rounded-xl shadow-lg hover:shadow-xl hover:translate-y-[-2px] transition-all">
                Post a Project
                <ArrowRight className="ml-2 h-5 w-5" />
              </Button>
            </Link>
            <Link to="/professionals/pest-control" className="w-full sm:w-auto">
              <Button variant="secondary" size="lg" className="text-lg font-medium w-full sm:w-auto px-8 py-6 h-auto rounded-xl border-2 border-gray-400 dark:border-gray-500 bg-gray-50 dark:bg-gray-800 text-gray-800 dark:text-white hover:bg-primary/10 dark:hover:bg-primary/20 hover:border-primary dark:hover:border-primary hover:text-primary dark:hover:text-primary-foreground shadow-lg hover:shadow-xl hover:translate-y-[-2px] transition-all">
                Browse Pest Pros
              </Button>
            </Link>
          </div>} />

      <section className="py-8 md:py-16 bg-gray-50 dark:bg-gray-900">
        <div className="container mx-auto px-4 lg:px-8">
          <div className="bg-primary/10 dark:bg-primary/20 rounded-2xl p-8 md:p-12">
            <div className="max-w-3xl mx-auto text-center">
              <h2 className="text-2xl md:text-3xl font-bold text-gray-900 dark:text-white mb-4">
                Ready to Get Started with Your Pest Control Project?
              </h2>
              <p className="text-lg text-gray-700 dark:text-gray-300 mb-8">
                Join thousands of satisfied customers who trust us for their pest management needs
              </p>
              <div className="flex flex-col sm:flex-row justify-center gap-4">
                <Link to="/create-job" className="w-full sm:w-auto">
                  <Button variant="default" size="lg" className="text-lg font-medium w-full sm:w-auto px-8 py-6 h-auto rounded-xl shadow-lg hover:shadow-xl hover:translate-y-[-2px] transition-all">
                    Get a Free Quote
                    <ArrowRight className="ml-2 h-5 w-5" />
                  </Button>
                </Link>
                <Link to="/pest-control/professionals" className="w-full sm:w-auto">
                  <Button variant="secondary" size="lg" className="text-lg font-medium w-full sm:w-auto px-8 py-6 h-auto rounded-xl border-2 border-gray-400 dark:border-gray-500 bg-gray-50 dark:bg-gray-800 text-gray-800 dark:text-white hover:bg-primary/10 dark:hover:bg-primary/20 hover:border-primary dark:hover:border-primary hover:text-primary dark:hover:text-primary-foreground shadow-lg hover:shadow-xl hover:translate-y-[-2px] transition-all">
                    Browse Pest Pros
                  </Button>
                </Link>
              </div>
            </div>
          </div>
        </div>
      </section>
    </>;
};
export default PestControlService;
