
import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Layout } from '@/components/Layout';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Separator } from '@/components/ui/separator';
import { useToast } from '@/hooks/use-toast';
import { useIsMobile } from '@/hooks/use-mobile';
import {
  ArrowLeft,
  Eye,
  MessageSquare,
  Phone,
  Star,
  Calendar,
  MapPin,
  DollarSign,
  Users,
  Clock
} from 'lucide-react';

// Types for bids and job data
interface Bid {
  id: string;
  providerId: string;
  providerName: string;
  providerAvatar?: string;
  providerRating: number;
  providerReviews: number;
  amount: number;
  message: string;
  submittedAt: string;
  estimatedCompletion: string;
  isVerified: boolean;
  responseTime: string;
}

interface JobStats {
  totalViews: number;
  totalBids: number;
  averageBid: number;
  viewsToday: number;
}

interface JobInfo {
  id: string;
  title: string;
  description: string;
  location: string;
  budget: number;
  postedAt: string;
  status: string;
}

const JobBids = () => {
  const { jobId } = useParams<{ jobId: string }>();
  const navigate = useNavigate();
  const isMobile = useIsMobile();
  const { toast } = useToast();
  
  const [bids, setBids] = useState<Bid[]>([]);
  const [jobStats, setJobStats] = useState<JobStats | null>(null);
  const [jobInfo, setJobInfo] = useState<JobInfo | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchJobBidsData = async () => {
      if (!jobId) return;

      try {
        setLoading(true);
        
        // Simulate API call - replace with actual API call
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // Mock data
        const mockJobInfo: JobInfo = {
          id: jobId,
          title: "Bathroom Sink Repair",
          description: "Need to fix a leaking bathroom sink faucet and replace the trap.",
          location: "San Francisco, CA",
          budget: 200,
          postedAt: "2 hours ago",
          status: "open"
        };

        const mockStats: JobStats = {
          totalViews: 47,
          totalBids: 8,
          averageBid: 185,
          viewsToday: 12
        };

        const mockBids: Bid[] = [
          {
            id: "1",
            providerId: "p1",
            providerName: "Mike Johnson",
            providerRating: 4.9,
            providerReviews: 156,
            amount: 175,
            message: "I have 10+ years of plumbing experience and can fix your sink today. I'll replace the faucet and trap with high-quality parts.",
            submittedAt: "1 hour ago",
            estimatedCompletion: "Same day",
            isVerified: true,
            responseTime: "Usually responds in 2 hours"
          },
          {
            id: "2",
            providerId: "p2",
            providerName: "Sarah Davis",
            providerRating: 4.8,
            providerReviews: 89,
            amount: 190,
            message: "Licensed plumber with same-day service. I can diagnose and fix the issue quickly with a 1-year warranty on parts.",
            submittedAt: "3 hours ago",
            estimatedCompletion: "Today",
            isVerified: true,
            responseTime: "Usually responds in 1 hour"
          },
          {
            id: "3",
            providerId: "p3",
            providerName: "Tom Wilson",
            providerRating: 4.7,
            providerReviews: 203,
            amount: 220,
            message: "Professional plumber available this afternoon. I'll bring all necessary tools and provide a detailed quote.",
            submittedAt: "5 hours ago",
            estimatedCompletion: "Within 24 hours",
            isVerified: false,
            responseTime: "Usually responds in 4 hours"
          }
        ];

        setJobInfo(mockJobInfo);
        setJobStats(mockStats);
        setBids(mockBids);
      } catch (error) {
        console.error('Error fetching job bids:', error);
        toast({
          title: "Error",
          description: "Failed to load job bids",
          variant: "destructive"
        });
      } finally {
        setLoading(false);
      }
    };

    fetchJobBidsData();
  }, [jobId, toast]);

  const handleAcceptBid = (bidId: string, providerName: string) => {
    toast({
      title: "Bid Accepted",
      description: `You've accepted ${providerName}'s bid. They will be notified shortly.`,
    });
  };

  const handleContactProvider = (providerId: string, providerName: string) => {
    toast({
      title: "Message Sent",
      description: `Your message to ${providerName} has been sent.`,
    });
  };

  if (loading) {
    return (
      <Layout>
        <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
          <div className="animate-pulse">
            {/* Loading skeleton */}
            <div className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 p-4">
              <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded w-1/3 mb-2"></div>
              <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/4"></div>
            </div>
            <div className="p-4 space-y-4">
              {[1, 2, 3].map(i => (
                <div key={i} className="h-32 bg-gray-200 dark:bg-gray-700 rounded"></div>
              ))}
            </div>
          </div>
        </div>
      </Layout>
    );
  }

  if (!jobInfo || !jobStats) {
    return (
      <Layout>
        <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">Job not found</h1>
            <p className="text-gray-600 dark:text-gray-400 mb-4">This job may have been removed or is no longer available.</p>
            <Button onClick={() => navigate('/customer/dashboard')}>Back to Dashboard</Button>
          </div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      {/* Header */}
      <div className="bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-700 sticky top-0 z-40">
        <div className="container mx-auto px-4 py-4 max-w-7xl">
          <div className="flex items-center gap-4">
            <Button 
              variant="ghost" 
              size="sm" 
              onClick={() => navigate(-1)}
              className="gap-2"
            >
              <ArrowLeft className="h-4 w-4" />
              {isMobile ? "Back" : "Back to Job"}
            </Button>
            <Separator orientation="vertical" className="h-6" />
            <div className="flex-1 min-w-0">
              <h1 className="text-lg md:text-xl font-bold text-gray-900 dark:text-white truncate">
                {jobInfo.title}
              </h1>
              <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400">
                <MapPin className="h-3 w-3" />
                <span>{jobInfo.location}</span>
                <span>•</span>
                <span>Posted {jobInfo.postedAt}</span>
              </div>
            </div>
            {jobInfo.status === 'open' && (
              <Badge className="bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300">
                Open
              </Badge>
            )}
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="bg-gray-50 dark:bg-gray-900 min-h-screen">
        <div className="container mx-auto px-4 py-6 max-w-7xl">
          {/* Stats Cards */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
            <Card>
              <CardContent className="p-4 text-center">
                <div className="flex items-center justify-center mb-2">
                  <Eye className="h-5 w-5 text-blue-600 dark:text-blue-400" />
                </div>
                <div className="text-2xl font-bold text-gray-900 dark:text-white">{jobStats.totalViews}</div>
                <div className="text-sm text-gray-600 dark:text-gray-400">Total Views</div>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="p-4 text-center">
                <div className="flex items-center justify-center mb-2">
                  <Users className="h-5 w-5 text-green-600 dark:text-green-400" />
                </div>
                <div className="text-2xl font-bold text-gray-900 dark:text-white">{jobStats.totalBids}</div>
                <div className="text-sm text-gray-600 dark:text-gray-400">Total Bids</div>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="p-4 text-center">
                <div className="flex items-center justify-center mb-2">
                  <DollarSign className="h-5 w-5 text-yellow-600 dark:text-yellow-400" />
                </div>
                <div className="text-2xl font-bold text-gray-900 dark:text-white">${jobStats.averageBid}</div>
                <div className="text-sm text-gray-600 dark:text-gray-400">Avg Bid</div>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="p-4 text-center">
                <div className="flex items-center justify-center mb-2">
                  <Clock className="h-5 w-5 text-purple-600 dark:text-purple-400" />
                </div>
                <div className="text-2xl font-bold text-gray-900 dark:text-white">{jobStats.viewsToday}</div>
                <div className="text-sm text-gray-600 dark:text-gray-400">Views Today</div>
              </CardContent>
            </Card>
          </div>

          {/* Bids List */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
                Received Bids ({bids.length})
              </h2>
              <Button variant="outline" size="sm">
                Sort by Price
              </Button>
            </div>
            
            {bids.length === 0 ? (
              <Card>
                <CardContent className="py-12 text-center">
                  <Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">No bids yet</h3>
                  <p className="text-gray-600 dark:text-gray-400 mb-4">
                    Professionals are viewing your job. Bids should start coming in soon!
                  </p>
                </CardContent>
              </Card>
            ) : (
              bids.map((bid) => (
                <Card key={bid.id} className="overflow-hidden">
                  <CardContent className="p-6">
                    <div className="flex flex-col md:flex-row gap-4">
                      {/* Provider Info */}
                      <div className="flex items-start gap-4 flex-1">
                        <Avatar className="h-12 w-12">
                          <AvatarImage src={bid.providerAvatar} />
                          <AvatarFallback className="bg-blue-100 text-blue-600 font-medium">
                            {bid.providerName.charAt(0)}
                          </AvatarFallback>
                        </Avatar>
                        
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center gap-2 mb-1">
                            <h3 className="font-semibold text-gray-900 dark:text-white truncate">
                              {bid.providerName}
                            </h3>
                            {bid.isVerified && (
                              <Badge variant="secondary" className="text-xs">Verified</Badge>
                            )}
                          </div>
                          
                          <div className="flex items-center gap-2 mb-2">
                            <div className="flex items-center gap-1">
                              <Star className="h-4 w-4 text-yellow-400 fill-current" />
                              <span className="text-sm font-medium">{bid.providerRating}</span>
                            </div>
                            <span className="text-sm text-gray-600 dark:text-gray-400">
                              ({bid.providerReviews} reviews)
                            </span>
                            <span className="text-sm text-gray-600 dark:text-gray-400">•</span>
                            <span className="text-sm text-gray-600 dark:text-gray-400">
                              {bid.responseTime}
                            </span>
                          </div>
                          
                          <p className="text-gray-700 dark:text-gray-300 text-sm mb-3 line-clamp-3">
                            {bid.message}
                          </p>
                          
                          <div className="flex flex-wrap gap-4 text-sm text-gray-600 dark:text-gray-400">
                            <div className="flex items-center gap-1">
                              <Calendar className="h-3 w-3" />
                              <span>Can start: {bid.estimatedCompletion}</span>
                            </div>
                            <div className="flex items-center gap-1">
                              <Clock className="h-3 w-3" />
                              <span>Submitted {bid.submittedAt}</span>
                            </div>
                          </div>
                        </div>
                      </div>
                      
                      {/* Bid Amount & Actions */}
                      <div className="flex flex-col items-end gap-3 md:min-w-[200px]">
                        <div className="text-right">
                          <div className="text-2xl font-bold text-gray-900 dark:text-white">
                            ${bid.amount}
                          </div>
                          <div className="text-sm text-gray-600 dark:text-gray-400">
                            {bid.amount < jobInfo.budget ? 'Under budget' : 
                             bid.amount === jobInfo.budget ? 'Matches budget' : 'Over budget'}
                          </div>
                        </div>
                        
                        <div className="flex flex-col gap-2 w-full md:w-auto">
                          <Button 
                            onClick={() => handleAcceptBid(bid.id, bid.providerName)}
                            className="w-full md:w-32"
                          >
                            Accept Bid
                          </Button>
                          <div className="flex gap-2">
                            <Button 
                              variant="outline" 
                              size="sm"
                              onClick={() => handleContactProvider(bid.providerId, bid.providerName)}
                              className="flex-1 md:w-16"
                            >
                              <MessageSquare className="h-4 w-4 md:mr-0 mr-2" />
                              <span className="md:hidden">Message</span>
                            </Button>
                            <Button 
                              variant="outline" 
                              size="sm"
                              className="flex-1 md:w-16"
                            >
                              <Phone className="h-4 w-4 md:mr-0 mr-2" />
                              <span className="md:hidden">Call</span>
                            </Button>
                          </div>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))
            )}
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default JobBids;
