import React from 'react';
import { Link } from 'react-router-dom';
import { Navbar } from '@/components/Navbar';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { CheckCircle, ArrowRight, Briefcase, DollarSign, Shield, Clock, Star } from 'lucide-react';
import { Separator } from '@/components/ui/separator';
import { useIsMobile } from '@/hooks/use-mobile';
import { SEO } from '@/components/SEO';
import { BlogCard, BlogPost } from '@/components/BlogCard';
import { AuroraBackground } from '@/components/ui/aurora-background';

const ProLanding = () => {
  const isMobile = useIsMobile();

  const proLandingSchema = {
    "@context": "https://schema.org",
    "@type": "WebPage",
    "name": "Join JobON as a Service Professional",
    "description": "Connect with customers looking for your services, set your own rates, and grow your business with JobON.",
    "mainEntity": {
      "@type": "ProfessionalService",
      "name": "JobON Professional Network",
      "description": "Connect with customers looking for your services and take control of your work schedule.",
      "provider": {
        "@type": "Organization",
        "name": "JobON",
        "url": "https://jobon.app"
      },
      "serviceType": [
        "Handyman Services", 
        "Plumbing", 
        "Electrical", 
        "HVAC", 
        "Cleaning Services", 
        "Solar Installation"
      ],
      "areaServed": "United States"
    }
  };

  const proBlogPosts: BlogPost[] = [
    {
      id: '1',
      title: '5 Ways to Grow Your Service Business This Year',
      excerpt: 'Learn proven strategies to expand your client base, increase revenue, and build a sustainable service business in today\'s competitive market.',
      category: 'Business Growth',
      slug: 'grow-service-business',
      date: '2025-03-01T09:30:00',
      author: 'Michael Chen',
      commentCount: 24,
      image: 'https://images.unsplash.com/photo-1454165804606-c3d57bc86b40?ixlib=rb-4.0.3'
    },
    {
      id: '2',
      title: 'How to Price Your Services for Maximum Profit',
      excerpt: 'Setting the right prices can make or break your business. Discover how to calculate your costs and set prices that reflect your true value.',
      category: 'Pricing Strategy',
      slug: 'service-pricing-strategy',
      date: '2025-02-14T10:15:00',
      author: 'Sarah Johnson',
      commentCount: 18,
      image: 'https://images.unsplash.com/photo-**********-8d04cb21ed1c?ixlib=rb-4.0.3'
    },
    {
      id: '3',
      title: 'Building Your Online Reputation as a Service Professional',
      excerpt: 'In the digital age, your online reputation is everything. Learn how to build and maintain a stellar online presence that attracts clients.',
      category: 'Marketing',
      slug: 'online-reputation-building',
      date: '2025-01-21T11:45:00',
      author: 'David Park',
      commentCount: 12,
      image: 'https://images.unsplash.com/photo-**********-669a67965ba0?ixlib=rb-4.0.3'
    },
    {
      id: '4',
      title: 'Time Management Secrets for Busy Service Providers',
      excerpt: 'Master your schedule and boost productivity with these proven time management techniques designed specifically for service professionals.',
      category: 'Productivity',
      slug: 'time-management-for-service-pros',
      date: '2024-12-18T14:20:00',
      author: 'Lisa Martinez',
      commentCount: 9,
      image: 'https://images.unsplash.com/photo-1506784983877-45594efa4cbe?ixlib=rb-4.0.3'
    },
    {
      id: '5',
      title: 'Customer Communication: The Ultimate Guide for Service Pros',
      excerpt: 'Learn how to communicate effectively with clients from initial contact to project completion, building trust and ensuring satisfaction.',
      category: 'Client Relations',
      slug: 'customer-communication-guide',
      date: '2024-12-05T09:10:00',
      author: 'James Wilson',
      commentCount: 15,
      image: 'https://images.unsplash.com/photo-1573164713988-8665fc963095?ixlib=rb-4.0.3'
    },
    {
      id: '6',
      title: 'Tax Strategies for Independent Contractors and Small Businesses',
      excerpt: 'Maximize your deductions and minimize your tax burden with these financial strategies designed for service professionals and small business owners.',
      category: 'Finance',
      slug: 'tax-strategies-service-pros',
      date: '2024-11-30T15:30:00',
      author: 'Emily Richards',
      commentCount: 21,
      image: 'https://images.unsplash.com/photo-**********-6726b3ff3f42?ixlib=rb-4.0.3'
    },
    {
      id: '7',
      title: 'Digital Tools Every Modern Service Provider Needs',
      excerpt: 'From scheduling apps to payment processing, discover the essential digital tools that will streamline your operations and delight your clients.',
      category: 'Technology',
      slug: 'digital-tools-service-providers',
      date: '2024-11-15T12:15:00',
      author: 'Alex Thompson',
      commentCount: 16,
      image: 'https://images.unsplash.com/photo-*************-f06f85e504b3?ixlib=rb-4.0.3'
    },
    {
      id: '8',
      title: 'Legal Essentials for Service Businesses: Contracts & Insurance',
      excerpt: 'Protect your service business with proper contracts and insurance coverage. Learn what you need and how to get it without breaking the bank.',
      category: 'Legal',
      slug: 'legal-essentials-service-business',
      date: '2024-10-28T10:40:00',
      author: 'Robert Nelson',
      commentCount: 14,
      image: 'https://images.unsplash.com/photo-*************-d10d557cf95f?ixlib=rb-4.0.3'
    }
  ];

  return (
    <div className="min-h-screen flex flex-col">
      <SEO 
        title="Join as a Pro Service Provider" 
        description="Join JobON as a professional service provider. Connect with customers, set your own rates, choose your jobs, and grow your business."
        schema={proLandingSchema}
        canonicalUrl="/pro-landing"
      />
      
      <Navbar />
      
      <AuroraBackground className="pt-20 pb-12 px-4">
        <div className="container mx-auto max-w-4xl text-center">
          <h1 className="text-3xl md:text-5xl font-bold mb-4">Grow Your Business with JobON</h1>
          <p className="text-lg text-muted-foreground mb-8 max-w-2xl mx-auto">
            Connect with customers looking for your services and take control of your work schedule.
          </p>
          
          <div className="flex flex-col sm:flex-row justify-center gap-4 mb-8">
            <Button size="lg" variant="success" asChild className="rounded-full">
              <Link to="/auth">Apply to Join Now</Link>
            </Button>
            <Button size="lg" variant="outline" asChild className="rounded-full">
              <Link to="/how-it-works">How It Works</Link>
            </Button>
          </div>
          
          <div className="mt-8 grid grid-cols-2 gap-4 max-w-md mx-auto text-sm">
            <div className="flex items-center gap-2">
              <CheckCircle className="h-5 w-5 text-success" />
              <span>Free to join</span>
            </div>
            <div className="flex items-center gap-2">
              <CheckCircle className="h-5 w-5 text-success" />
              <span>Set your own rates</span>
            </div>
            <div className="flex items-center gap-2">
              <CheckCircle className="h-5 w-5 text-success" />
              <span>Choose your jobs</span>
            </div>
            <div className="flex items-center gap-2">
              <CheckCircle className="h-5 w-5 text-success" />
              <span>Get paid faster</span>
            </div>
          </div>
        </div>
      </AuroraBackground>
      
      <section className="py-16 px-4 bg-background">
        <div className="container mx-auto max-w-4xl">
          <h2 className="text-2xl md:text-3xl font-bold text-center mb-10">How It Works</h2>
          
          <div className="space-y-8 md:space-y-10">
            <div className="flex gap-4 items-start">
              <div className="bg-primary/10 rounded-full p-3 flex-shrink-0">
                <span className="font-bold">1</span>
              </div>
              <div>
                <h3 className="font-semibold text-lg mb-1">Apply to join JobON</h3>
                <p className="text-muted-foreground">Complete our simple application process. We'll review your qualifications within 24-48 hours.</p>
              </div>
            </div>
            
            <div className="flex gap-4 items-start">
              <div className="bg-primary/10 rounded-full p-3 flex-shrink-0">
                <span className="font-bold">2</span>
              </div>
              <div>
                <h3 className="font-semibold text-lg mb-1">Set up your profile</h3>
                <p className="text-muted-foreground">Showcase your skills, experience, and availability to attract customers.</p>
              </div>
            </div>
            
            <div className="flex gap-4 items-start">
              <div className="bg-primary/10 rounded-full p-3 flex-shrink-0">
                <span className="font-bold">3</span>
              </div>
              <div>
                <h3 className="font-semibold text-lg mb-1">Browse and apply for jobs</h3>
                <p className="text-muted-foreground">Find jobs that match your skills and schedule, then submit your bid.</p>
              </div>
            </div>
            
            <div className="flex gap-4 items-start">
              <div className="bg-primary/10 rounded-full p-3 flex-shrink-0">
                <span className="font-bold">4</span>
              </div>
              <div>
                <h3 className="font-semibold text-lg mb-1">Complete jobs and get paid</h3>
                <p className="text-muted-foreground">Deliver quality service and receive payment through our secure platform.</p>
              </div>
            </div>
          </div>
          
          <div className="mt-12 text-center">
            <Button variant="success" size="lg" asChild className="rounded-full">
              <Link to="/auth" className="flex items-center gap-2">
                Apply Now <ArrowRight className="h-4 w-4" />
              </Link>
            </Button>
          </div>
        </div>
      </section>
      
      <section className="py-16 px-4 bg-muted/30">
        <div className="container mx-auto max-w-5xl">
          <h2 className="text-2xl md:text-3xl font-bold text-center mb-10">Why Join JobON?</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <Card className="bg-card border-0 shadow-sm hover:shadow transition-all">
              <CardContent className="pt-6">
                <Briefcase className="h-12 w-12 text-success mb-4" />
                <h3 className="font-semibold text-lg mb-2">Regular Work</h3>
                <p className="text-muted-foreground">Access a steady stream of job opportunities in your service area.</p>
              </CardContent>
            </Card>
            
            <Card className="bg-card border-0 shadow-sm hover:shadow transition-all">
              <CardContent className="pt-6">
                <DollarSign className="h-12 w-12 text-success mb-4" />
                <h3 className="font-semibold text-lg mb-2">Competitive Rates</h3>
                <p className="text-muted-foreground">Set your own prices and earn what you deserve for your expertise.</p>
              </CardContent>
            </Card>
            
            <Card className="bg-card border-0 shadow-sm hover:shadow transition-all">
              <CardContent className="pt-6">
                <Shield className="h-12 w-12 text-success mb-4" />
                <h3 className="font-semibold text-lg mb-2">Verified Customers</h3>
                <p className="text-muted-foreground">Work with vetted customers who value quality professional service.</p>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>
      
      <section className="py-16 px-4 bg-background">
        <div className="container mx-auto max-w-4xl">
          <h2 className="text-2xl md:text-3xl font-bold text-center mb-10">What Professionals Say</h2>
          
          <div className="space-y-6">
            <Card className="border-0 shadow-sm">
              <CardContent className="pt-6">
                <div className="flex items-center gap-1 mb-3 text-yellow-400">
                  <Star className="h-5 w-5 fill-current" />
                  <Star className="h-5 w-5 fill-current" />
                  <Star className="h-5 w-5 fill-current" />
                  <Star className="h-5 w-5 fill-current" />
                  <Star className="h-5 w-5 fill-current" />
                </div>
                <p className="italic mb-4">"Since joining JobON, I've been able to grow my plumbing business by 40%. The platform makes finding clients and managing jobs so much easier."</p>
                <div className="flex items-center gap-3">
                  <div className="h-10 w-10 rounded-full bg-primary/20 flex items-center justify-center">
                    <span className="font-semibold">JD</span>
                  </div>
                  <div>
                    <p className="font-semibold">John Davis</p>
                    <p className="text-sm text-muted-foreground">Plumber, 2 years on JobON</p>
                  </div>
                </div>
              </CardContent>
            </Card>
            
            <Card className="border-0 shadow-sm">
              <CardContent className="pt-6">
                <div className="flex items-center gap-1 mb-3 text-yellow-400">
                  <Star className="h-5 w-5 fill-current" />
                  <Star className="h-5 w-5 fill-current" />
                  <Star className="h-5 w-5 fill-current" />
                  <Star className="h-5 w-5 fill-current" />
                  <Star className="h-5 w-5 fill-current" />
                </div>
                <p className="italic mb-4">"The flexibility JobON offers is perfect for my electrician business. I can choose jobs that fit my schedule and expertise, and the payment process is quick and reliable."</p>
                <div className="flex items-center gap-3">
                  <div className="h-10 w-10 rounded-full bg-primary/20 flex items-center justify-center">
                    <span className="font-semibold">SR</span>
                  </div>
                  <div>
                    <p className="font-semibold">Sarah Rodriguez</p>
                    <p className="text-sm text-muted-foreground">Electrician, 1.5 years on JobON</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>
      
      <section className="py-16 px-4 bg-background">
        <div className="container mx-auto max-w-5xl">
          <div className="text-center mb-10">
            <h2 className="text-2xl md:text-3xl font-bold">Pro Resources & Tips</h2>
            <p className="text-muted-foreground mt-2 max-w-2xl mx-auto">
              Expert advice and insights to help you grow your service business
            </p>
          </div>
          
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-4 md:gap-6">
            {proBlogPosts.slice(0, isMobile ? 4 : 8).map((post) => (
              <div key={post.id} className="h-full">
                <BlogCard post={post} compact={true} />
              </div>
            ))}
          </div>
          
          <div className="text-center mt-10">
            <Button variant="outline" asChild>
              <Link to="/blog" className="flex items-center gap-2">
                Browse All Resources <ArrowRight className="h-4 w-4" />
              </Link>
            </Button>
          </div>
        </div>
      </section>
      
      <section className="py-16 px-4 bg-success text-success-foreground">
        <div className="container mx-auto max-w-4xl text-center">
          <h2 className="text-2xl md:text-3xl font-bold mb-4">Ready to Grow Your Business?</h2>
          <p className="mb-8 max-w-2xl mx-auto">Join thousands of professionals who have expanded their client base and increased their income with JobON.</p>
          <div className="flex justify-center">
            <Button variant="secondary" size="lg" asChild className="rounded-full">
              <Link to="/auth">Apply to Join Now</Link>
            </Button>
          </div>
          <p className="mt-6 text-sm opacity-90 flex items-center justify-center gap-1">
            <Clock className="h-4 w-4" /> Applications processed within 24-48 hours
          </p>
        </div>
      </section>
      
      <footer className="bg-background border-t py-8 px-4">
        <div className="container mx-auto max-w-4xl">
          <div className="text-center">
            <p className="text-muted-foreground text-sm">
              © {new Date().getFullYear()} JobON. All rights reserved.
            </p>
            <div className="flex justify-center gap-4 mt-4">
              <Link to="/terms" className="text-sm text-muted-foreground hover:text-primary">Terms</Link>
              <Link to="/privacy" className="text-sm text-muted-foreground hover:text-primary">Privacy</Link>
              <Link to="/faq" className="text-sm text-muted-foreground hover:text-primary">FAQ</Link>
              <Link to="/contact" className="text-sm text-muted-foreground hover:text-primary">Contact</Link>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default ProLanding;
