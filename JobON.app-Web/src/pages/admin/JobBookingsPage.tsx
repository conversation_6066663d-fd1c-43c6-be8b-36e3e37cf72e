
import React, { useEffect, useState } from 'react';
import { JobBookingsTable } from '@/components/admin/job-bookings/JobBookingsTable';
import { JobBooking, JobBookingResponse } from '@/services/jobBookingService';
import { apiService } from '@/services/api';
import { useAuth } from '@/features/auth/hooks/useAuth';
import { useToast } from '@/components/ui/use-toast';

const JobBookingsPage: React.FC = () => {
  const { token } = useAuth();
  const { toast } = useToast();
  const [bookings, setBookings] = useState<JobBooking[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [pagination, setPagination] = useState({
    current_page: 1,
    per_page: 10,
    total: 0,
    last_page: 1,
  });

  const fetchBookings = async (page: number = 1, search?: string) => {
    setIsLoading(true);
    try {
      let endpoint = `/api/job-bookings?page=${page}&per_page=${pagination.per_page}`;

      if (search) {
        endpoint += `&search=${encodeURIComponent(search)}`;
      }

      const response = await apiService<JobBookingResponse>(endpoint, {
        method: 'GET',
        headers: {
          'Authorization': token || '',
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        },
        requiresAuth: true,
        includeCredentials: true
      });

      if (response.isSuccess && response.data) {
        setBookings(response.data.data);
        setPagination({
          current_page: response.data.meta.current_page,
          per_page: response.data.meta.per_page,
          total: response.data.meta.total,
          last_page: response.data.meta.last_page,
        });
      } else {
        toast({
          title: 'Error',
          description: response.error || 'Failed to fetch job bookings',
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('Error fetching job bookings:', error);
      toast({
        title: 'Error',
        description: 'An unexpected error occurred',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchBookings(pagination.current_page, searchQuery);
  }, []);

  const handlePageChange = (page: number) => {
    fetchBookings(page, searchQuery);
  };

  const handleSearch = (query: string) => {
    setSearchQuery(query);
    fetchBookings(1, query);
  };

  return (
    <div className="container mx-auto py-6 space-y-6">
      <h1 className="text-3xl font-bold">Job Bookings</h1>
      <JobBookingsTable
        bookings={bookings}
        isLoading={isLoading}
        pagination={pagination}
        onPageChange={handlePageChange}
        onSearch={handleSearch}
      />
    </div>
  );
};

export default JobBookingsPage;
