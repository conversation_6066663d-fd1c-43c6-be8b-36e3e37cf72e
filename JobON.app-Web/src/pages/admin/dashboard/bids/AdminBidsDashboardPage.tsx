import React, { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '@/features/auth/hooks/useAuth';
import { BidOverview } from '@/components/admin/BidOverview';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Loader2, Shield, AlertTriangle } from 'lucide-react';

/**
 * Admin Bids Dashboard Page
 *
 * This page provides comprehensive bid management functionality for administrators,
 * including viewing all bids, statistics, and management actions.
 *
 * Features:
 * - GET /api/admin/bids endpoint integration
 * - Pagination support (page, per_page parameters)
 * - Filtering by status, provider_id, service_request_id
 * - Comprehensive bid statistics and analytics
 * - Searchable and filterable bid table
 * - Admin-only access control
 */
const AdminBidsDashboardPage: React.FC = () => {
  const { isAuthenticated, isAdmin, isLoading: authLoading, user } = useAuth();
  const navigate = useNavigate();

  // Check authentication and admin role
  useEffect(() => {
    if (!authLoading) {
      if (!isAuthenticated) {
        navigate('/auth');
        return;
      }

      if (!isAdmin) {
        navigate('/dashboard');
        return;
      }
    }
  }, [isAuthenticated, isAdmin, authLoading, navigate]);

  // Show loading state while checking authentication
  if (authLoading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="flex items-center space-x-2">
          <Loader2 className="h-6 w-6 animate-spin" />
          <span>Loading...</span>
        </div>
      </div>
    );
  }

  // Show error if not authenticated or not admin
  if (!isAuthenticated || !isAdmin) {
    return (
      <div className="p-6">
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            Access denied. This page is only accessible to administrators.
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header Section */}
      <div className="mb-6">
        <div className="flex items-center space-x-2 mb-2">
          <Shield className="h-6 w-6 text-primary" />
          <h1 className="text-2xl font-bold text-gray-900">Admin Bids Dashboard</h1>
        </div>
        <p className="text-gray-600">
          Comprehensive bid management and oversight for administrators.
          View all bids with pagination, filtering, and detailed analytics.
        </p>
      </div>
      {/* Main Bid Overview Component */}
      <BidOverview />
    </div>
  );
};

export default AdminBidsDashboardPage;