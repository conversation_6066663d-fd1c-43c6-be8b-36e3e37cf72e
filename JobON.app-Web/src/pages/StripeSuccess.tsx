import React, { useEffect, useState } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { CheckCircle, Loader2, AlertCircle } from 'lucide-react';
import { useAuth } from '@/features/auth/hooks/useAuth';
import { stripeService } from '@/services/stripeService';
import { useToast } from '@/hooks/use-toast';

const StripeSuccess: React.FC = () => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const { user, token, refreshRoles } = useAuth();
  const { toast } = useToast();
  const [isVerifying, setIsVerifying] = useState(true);
  const [verificationStatus, setVerificationStatus] = useState<'success' | 'error' | 'pending'>('pending');

  const sessionId = searchParams.get('session_id');

  useEffect(() => {
    const verifyPayment = async () => {
      if (!sessionId) {
        setVerificationStatus('error');
        setIsVerifying(false);
        toast({
          title: 'Error',
          description: 'No session ID found in URL',
          variant: 'destructive',
        });
        return;
      }

      try {
        const result = await stripeService.verifyPaymentSession(sessionId, token || undefined);
        
        if (result.success) {
          setVerificationStatus('success');
          
          // Refresh user roles to reflect the new subscription
          if (refreshRoles) {
            await refreshRoles();
          }
          
          toast({
            title: 'Payment Successful!',
            description: 'Your subscription has been activated successfully.',
            variant: 'default',
          });
        } else {
          setVerificationStatus('error');
          toast({
            title: 'Verification Failed',
            description: result.message || 'Failed to verify payment',
            variant: 'destructive',
          });
        }
      } catch (error) {
        console.error('Error verifying payment:', error);
        setVerificationStatus('error');
        toast({
          title: 'Error',
          description: 'An error occurred while verifying your payment',
          variant: 'destructive',
        });
      } finally {
        setIsVerifying(false);
      }
    };

    verifyPayment();
  }, [sessionId, token, refreshRoles, toast]);

  const handleContinue = () => {
    // Navigate to provider dashboard or plans page
    navigate('/provider/dashboard');
  };

  const handleRetry = () => {
    // Navigate back to plans page
    navigate('/for-providers');
  };

  if (isVerifying) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center p-4">
        <Card className="w-full max-w-md">
          <CardContent className="pt-6">
            <div className="text-center space-y-4">
              <Loader2 className="h-12 w-12 animate-spin text-primary mx-auto" />
              <h2 className="text-xl font-semibold">Verifying Payment</h2>
              <p className="text-muted-foreground">
                Please wait while we confirm your subscription...
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center p-4">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <div className="mx-auto mb-4">
            {verificationStatus === 'success' ? (
              <CheckCircle className="h-16 w-16 text-green-500" />
            ) : (
              <AlertCircle className="h-16 w-16 text-red-500" />
            )}
          </div>
          <CardTitle className="text-2xl">
            {verificationStatus === 'success' ? 'Payment Successful!' : 'Payment Verification Failed'}
          </CardTitle>
        </CardHeader>
        <CardContent className="text-center space-y-4">
          {verificationStatus === 'success' ? (
            <>
              <p className="text-muted-foreground">
                Thank you for your subscription! Your account has been upgraded and you now have access to all premium features.
              </p>
              <div className="space-y-2">
                <Button onClick={handleContinue} className="w-full">
                  Go to Dashboard
                </Button>
                <Button 
                  variant="outline" 
                  onClick={() => navigate('/provider/plans')}
                  className="w-full"
                >
                  View Plan Details
                </Button>
              </div>
            </>
          ) : (
            <>
              <p className="text-muted-foreground">
                We encountered an issue verifying your payment. Please contact support if you believe this is an error.
              </p>
              <div className="space-y-2">
                <Button onClick={handleRetry} className="w-full">
                  Try Again
                </Button>
                <Button 
                  variant="outline" 
                  onClick={() => navigate('/provider/dashboard')}
                  className="w-full"
                >
                  Go to Dashboard
                </Button>
              </div>
            </>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default StripeSuccess;
