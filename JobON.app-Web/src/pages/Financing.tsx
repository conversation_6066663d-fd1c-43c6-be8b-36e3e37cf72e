
import React from 'react';
import { Layout } from '@/components/Layout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { CreditCard, DollarSign, CircleCheck, PiggyBank, Wallet, HandHeart, Handshake, Home, FileText, Sparkles } from 'lucide-react';
import { Link } from 'react-router-dom';
import { SEO } from "@/components/SEO.tsx";

const Financing = () => {
  return <>
    <SEO title="Flexible Home Service Financing – Low Interest, Pay Later Plans" description="Finance your next project with JobON. Choose low-interest or monthly payment plans for home repairs, upgrades, or installations—no prepayment penalties." localBusinessSchema={true} serviceType="Financing" serviceSlug="financing" canonicalUrl="/financing" />
    <Layout>
      {/* Hero Section */}
      <div className="flex-1 pt-16 md:pt-24 pb-8 md:pb-16 relative overflow-hidden bg-blue-50">
        <div className="container mx-auto px-4 md:px-6 relative z-10">
          <div className="max-w-4xl mx-auto text-center">
            <div className="flex items-center justify-center mb-6 md:mb-8">
              <div className="p-3 md:p-4 rounded-full bg-blue-600 shadow-lg">
                <HandHeart className="h-8 w-8 md:h-12 md:w-12 text-white" />
              </div>
            </div>
            <h1 className="text-3xl md:text-5xl lg:text-6xl font-bold tracking-tight mb-4 md:mb-6 text-blue-600">
              Flexible Financing
            </h1>
            <p className="text-lg md:text-xl lg:text-2xl text-gray-600 dark:text-gray-300 mb-6 md:mb-8 max-w-3xl mx-auto leading-relaxed px-4">
              Transform your home today and pay over time with our flexible, low-interest financing options designed for your peace of mind.
            </p>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 md:px-6 -mt-4 md:-mt-8 relative z-20 bg-blue-50 pb-8 md:pb-0">
        <div className="max-w-6xl mx-auto">
          {/* Payment options cards */}
          <div className="grid gap-6 md:gap-8 md:grid-cols-2 mb-12 md:mb-16">
            <Card className="relative overflow-hidden border shadow-lg bg-white dark:bg-gray-800 transform transition-all duration-300 hover:scale-105 hover:shadow-xl">
              <div className="absolute top-0 left-0 w-full h-1 bg-blue-600"></div>
              <CardHeader className="relative z-10 pb-4 p-4 md:p-6">
                <div className="mb-4 p-3 rounded-full bg-blue-600 w-fit">
                  <CreditCard className="h-6 w-6 md:h-8 md:w-8 text-white" />
                </div>
                <CardTitle className="text-xl md:text-2xl text-gray-900 dark:text-white">Low Interest If Paid In Full</CardTitle>
                <CardDescription className="text-base md:text-lg text-gray-600 dark:text-gray-300">
                  Pay for your service in installments with reduced interest charges if paid in full within the promotional period.
                </CardDescription>
              </CardHeader>
              <CardContent className="relative z-10 p-4 md:p-6 pt-0">
                <ul className="space-y-3">
                  <li className="flex items-start">
                    <CircleCheck className="h-5 w-5 md:h-6 md:w-6 text-blue-600 mr-3 flex-shrink-0 mt-0.5" />
                    <span className="text-sm md:text-base text-gray-700 dark:text-gray-200">Flexible payment terms (3, 6, or 12 months)</span>
                  </li>
                  <li className="flex items-start">
                    <CircleCheck className="h-5 w-5 md:h-6 md:w-6 text-blue-600 mr-3 flex-shrink-0 mt-0.5" />
                    <span className="text-sm md:text-base text-gray-700 dark:text-gray-200">No prepayment penalties</span>
                  </li>
                  <li className="flex items-start">
                    <CircleCheck className="h-5 w-5 md:h-6 md:w-6 text-blue-600 mr-3 flex-shrink-0 mt-0.5" />
                    <span className="text-sm md:text-base text-gray-700 dark:text-gray-200">Quick approval process</span>
                  </li>
                </ul>
              </CardContent>
            </Card>

            <Card className="relative overflow-hidden border shadow-lg bg-white dark:bg-gray-800 transform transition-all duration-300 hover:scale-105 hover:shadow-xl">
              <div className="absolute top-0 left-0 w-full h-1 bg-green-600"></div>
              <CardHeader className="relative z-10 pb-4 p-4 md:p-6">
                <div className="mb-4 p-3 rounded-full bg-green-600 w-fit">
                  <PiggyBank className="h-6 w-6 md:h-8 md:w-8 text-white" />
                </div>
                <CardTitle className="text-xl md:text-2xl text-gray-900 dark:text-white">Low Monthly Payments</CardTitle>
                <CardDescription className="text-base md:text-lg text-gray-600 dark:text-gray-300">
                  Spread the cost of larger home improvement projects with affordable monthly payments.
                </CardDescription>
              </CardHeader>
              <CardContent className="relative z-10 p-4 md:p-6 pt-0">
                <ul className="space-y-3">
                  <li className="flex items-start">
                    <CircleCheck className="h-5 w-5 md:h-6 md:w-6 text-green-600 mr-3 flex-shrink-0 mt-0.5" />
                    <span className="text-sm md:text-base text-gray-700 dark:text-gray-200">Competitive interest rates</span>
                  </li>
                  <li className="flex items-start">
                    <CircleCheck className="h-5 w-5 md:h-6 md:w-6 text-green-600 mr-3 flex-shrink-0 mt-0.5" />
                    <span className="text-sm md:text-base text-gray-700 dark:text-gray-200">Terms up to 60 months</span>
                  </li>
                  <li className="flex items-start">
                    <CircleCheck className="h-5 w-5 md:h-6 md:w-6 text-green-600 mr-3 flex-shrink-0 mt-0.5" />
                    <span className="text-sm md:text-base text-gray-700 dark:text-gray-200">Finance amounts from $500 to $50,000</span>
                  </li>
                </ul>
              </CardContent>
            </Card>
          </div>

          {/* How It Works section */}
          <div className="bg-white dark:bg-gray-800 rounded-2xl md:rounded-3xl p-6 md:p-8 lg:p-12 mb-12 md:mb-16 shadow-lg border border-gray-200 dark:border-gray-700 relative overflow-hidden">
            <div className="relative z-10">
              <div className="flex items-center justify-center mb-6 md:mb-8">
                <div className="p-3 rounded-full bg-purple-600 mr-2 md:mr-4">
                  <Handshake className="h-6 w-6 md:h-8 md:w-8 text-white" />
                </div>
                <h2 className="text-2xl md:text-3xl lg:text-4xl font-bold text-purple-600">
                  How It Works
                </h2>
              </div>
              
              <div className="grid gap-6 md:gap-8 md:grid-cols-3 relative">
                {/* Connecting lines for desktop only */}
                <div className="hidden md:block absolute top-12 left-1/3 right-1/3 h-0.5 bg-purple-300 dark:bg-purple-600"></div>
                
                <div className="text-center relative animate-fade-in">
                  <div className="w-16 h-16 md:w-24 md:h-24 rounded-full bg-blue-600 flex items-center justify-center mx-auto mb-4 md:mb-6 shadow-lg transform transition-transform duration-300 hover:scale-110">
                    <Home className="h-6 w-6 md:h-10 md:w-10 text-white" />
                  </div>
                  <h3 className="text-lg md:text-xl font-bold mb-2 md:mb-3 text-gray-900 dark:text-white">Choose a Service</h3>
                  <p className="text-sm md:text-base text-gray-600 dark:text-gray-300 leading-relaxed px-2">Select the home service you need from our marketplace of trusted professionals.</p>
                </div>
                
                <div className="text-center relative animate-fade-in" style={{
                  animationDelay: "0.2s"
                }}>
                  <div className="w-16 h-16 md:w-24 md:h-24 rounded-full bg-purple-600 flex items-center justify-center mx-auto mb-4 md:mb-6 shadow-lg transform transition-transform duration-300 hover:scale-110">
                    <FileText className="h-6 w-6 md:h-10 md:w-10 text-white" />
                  </div>
                  <h3 className="text-lg md:text-xl font-bold mb-2 md:mb-3 text-gray-900 dark:text-white">Apply for Financing</h3>
                  <p className="text-sm md:text-base text-gray-600 dark:text-gray-300 leading-relaxed px-2">Complete our simple application to check your financing options in minutes.</p>
                </div>
                
                <div className="text-center animate-fade-in" style={{
                  animationDelay: "0.4s"
                }}>
                  <div className="w-16 h-16 md:w-24 md:h-24 rounded-full bg-green-600 flex items-center justify-center mx-auto mb-4 md:mb-6 shadow-lg transform transition-transform duration-300 hover:scale-110">
                    <Sparkles className="h-6 w-6 md:h-10 md:w-10 text-white" />
                  </div>
                  <h3 className="text-lg md:text-xl font-bold mb-2 md:mb-3 text-gray-900 dark:text-white">Get Your Service</h3>
                  <p className="text-sm md:text-base text-gray-600 dark:text-gray-300 leading-relaxed px-2">Approved? Schedule your service and enjoy flexible payment options.</p>
                </div>
              </div>
            </div>
          </div>

          {/* Call to action section */}
          <div style={{
            animationDelay: "0.6s"
          }} className="text-center p-6 md:p-8 lg:p-12 rounded-2xl md:rounded-3xl shadow-lg border border-gray-200 dark:border-gray-700 relative overflow-hidden animate-fade-in bg-white">
            <div className="relative z-10">
              <div className="flex justify-center mb-6 md:mb-8">
                <div className="p-3 md:p-4 rounded-full shadow-lg bg-blue-600">
                  <Wallet className="h-8 w-8 md:h-12 md:w-12 text-white" />
                </div>
              </div>
              <h3 className="text-2xl md:text-3xl lg:text-4xl font-bold mb-4 md:mb-6 text-black">
                Ready to Get Started?
              </h3>
              <p className="text-base md:text-xl mb-6 md:mb-8 max-w-2xl mx-auto leading-relaxed text-black px-4">
                Take the first step toward improving your home with flexible financing options that fit your budget and lifestyle.
              </p>
              <Button asChild size="lg" className="w-full md:w-auto px-8 md:px-12 py-4 md:py-6 h-auto text-lg md:text-xl font-semibold rounded-xl md:rounded-2xl shadow-lg bg-blue-600 hover:bg-blue-700 text-white border-0 transform transition-all duration-300 hover:scale-105 hover:shadow-xl min-h-[56px]" scrollToTop={true}>
                <Link to="/financing/rate-check">
                  Check Your Rate
                </Link>
              </Button>
              <p className="text-sm mt-4 md:mt-6 flex items-center justify-center text-black">
                <CircleCheck className="h-4 w-4 md:h-5 md:w-5 mr-2 text-blue-600 bg-primary-DEFAULT" />
                Checking your rate won't affect your credit score.
              </p>

              <div className="mt-8 md:mt-12 text-center">
                <p className="mb-3 md:mb-4 text-sm md:text-base text-black">
                  Need more information about our services?
                </p>
                <Button asChild variant="link" className="text-base md:text-lg text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300 font-semibold min-h-[44px]" scrollToTop={true}>
                  <Link to="/how-it-works">
                    See how JobON works →
                  </Link>
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </Layout>
  </>;
};

export default Financing;
