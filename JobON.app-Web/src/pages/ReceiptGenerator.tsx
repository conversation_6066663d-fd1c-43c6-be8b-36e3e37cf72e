
import React, { useState } from 'react';
import { Layout } from '@/components/Layout';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Textarea } from '@/components/ui/textarea';
import { ArrowLeft, Download, Send, Plus, Trash, Receipt, FileText, Printer, HelpCircle } from 'lucide-react';
import { Link } from 'react-router-dom';
import { Separator } from '@/components/ui/separator';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useToast } from "@/hooks/use-toast";
import { Badge } from '@/components/ui/badge';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { Toaster } from "sonner";
import { toast } from "sonner";
import { 
  Table,
  TableHeader,
  TableBody,
  TableFooter,
  TableHead,
  TableRow,
  TableCell,
  TableCaption,
} from '@/components/ui/table';
import {SEO} from "@/components/SEO.tsx";

interface ReceiptItem {
  id: string;
  description: string;
  quantity: number;
  unitPrice: number;
}

const ReceiptGenerator: React.FC = () => {
  const [receiptTemplate, setReceiptTemplate] = useState<string>("standard");
  const [businessName, setBusinessName] = useState<string>('');
  const [businessAddress, setBusinessAddress] = useState<string>('');
  const [businessPhone, setBusinessPhone] = useState<string>('');
  const [businessEmail, setBusinessEmail] = useState<string>('');
  const [receiptNumber, setReceiptNumber] = useState<string>('001');
  const [customerName, setCustomerName] = useState<string>('');
  const [customerAddress, setCustomerAddress] = useState<string>('');
  const [customerPhone, setCustomerPhone] = useState<string>('');
  const [customerEmail, setCustomerEmail] = useState<string>('');
  const [date, setDate] = useState<string>(new Date().toISOString().split('T')[0]);
  const [paymentMethod, setPaymentMethod] = useState<string>('');
  const [items, setItems] = useState<ReceiptItem[]>([
    { id: '1', description: '', quantity: 1, unitPrice: 0 }
  ]);
  const [notes, setNotes] = useState<string>('');
  const [taxRate, setTaxRate] = useState<number>(8);

  // Calculate subtotal
  const subtotal = items.reduce((sum, item) => sum + (item.quantity * item.unitPrice), 0);
  
  // Calculate tax
  const tax = subtotal * (taxRate / 100);
  
  // Calculate total
  const total = subtotal + tax;

  const formatCurrency = (amount: number): string => {
    return amount.toLocaleString('en-US', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    });
  };

  // Add new item
  const addItem = () => {
    const newId = (items.length + 1).toString();
    setItems([...items, { id: newId, description: '', quantity: 1, unitPrice: 0 }]);
  };

  // Remove item
  const removeItem = (id: string) => {
    if (items.length > 1) {
      setItems(items.filter(item => item.id !== id));
    }
  };

  // Update item
  const updateItem = (id: string, field: keyof ReceiptItem, value: string | number) => {
    setItems(items.map(item => {
      if (item.id === id) {
        return { ...item, [field]: value };
      }
      return item;
    }));
  };

  // Download receipt as PDF (demo functionality)
  const downloadReceipt = () => {
    toast.success("Receipt Downloaded", {
      description: "Your receipt has been downloaded successfully.",
    });
  };

  // Email receipt (demo functionality)
  const emailReceipt = () => {
    toast.success("Receipt Sent", {
      description: "Your receipt has been sent successfully.",
    });
  };

  // Handle template change
  const handleTemplateChange = (template: string) => {
    setReceiptTemplate(template);
  };

  return (
    <Layout>
      <SEO
          title="Receipt Generator | Create Free Professional Receipts Instantly"
          description="Easily create professional receipts for your customers with JobON’s free Receipt Generator. Customize, preview, and download your receipts in minutes!"
          localBusinessSchema={true}
          serviceType="Receipt generator"
          serviceSlug="receipt-generator"
          canonicalUrl="/free-tools/receipt-generator"
      />
      <Toaster richColors />
      <div className="pt-20 pb-20 px-6 md:px-12 bg-gradient-to-b from-blue-50 to-white dark:from-gray-900 dark:to-gray-800">
        <div className="container mx-auto max-w-6xl">
          {/* Header with back button */}
          <div className="mb-8">
            <Link to="/free-tools" className="inline-flex items-center text-primary hover:underline mb-4 text-base md:text-lg">
              <ArrowLeft className="mr-2 h-5 w-5 md:h-6 md:w-6" />
              Back to Free Tools
            </Link>
          
            {/* Modified Header with Free Tool Badge */}
            <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4 relative">
              <div>
                <div className="flex items-center gap-3">
                  <h1 className="text-3xl md:text-5xl font-bold mt-2 mb-3 text-gray-800 dark:text-white">Receipt Generator</h1>
                  <Badge variant="default" className="bg-gradient-to-r from-primary to-blue-500 text-white px-3 py-1 rounded-full text-xs font-semibold shadow-md whitespace-nowrap">
                    Free Tool
                  </Badge>
                </div>
                <p className="text-foreground/70 text-lg md:text-xl max-w-3xl">
                  Create professional receipts for your customers quickly and easily.
                </p>
              </div>
            </div>
          </div>

          {/* Template Selection */}
          <div className="mb-12">
            <h2 className="text-2xl font-semibold mb-5 flex items-center">
              <Receipt className="mr-3 h-5 w-5 text-primary" />
              Choose a Receipt Template
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <Card 
                className={`cursor-pointer transition-all border-2 ${receiptTemplate === 'standard' ? 'border-primary bg-primary/5' : 'hover:border-primary'}`}
                onClick={() => handleTemplateChange('standard')}
              >
                <CardHeader className="pb-3">
                  <CardTitle className="text-lg">Standard Receipt</CardTitle>
                  {receiptTemplate === 'standard' && <Badge className="absolute top-3 right-3">Selected</Badge>}
                </CardHeader>
                <CardContent className="flex justify-center pb-6">
                  <Receipt className={`h-16 w-16 ${receiptTemplate === 'standard' ? 'text-primary' : 'opacity-40'}`} />
                </CardContent>
              </Card>
              
              <Card 
                className={`cursor-pointer transition-all border-2 ${receiptTemplate === 'professional' ? 'border-primary bg-primary/5' : 'hover:border-primary'}`}
                onClick={() => handleTemplateChange('professional')}
              >
                <CardHeader className="pb-3">
                  <CardTitle className="text-lg">Professional Receipt</CardTitle>
                  {receiptTemplate === 'professional' && <Badge className="absolute top-3 right-3">Selected</Badge>}
                </CardHeader>
                <CardContent className="flex justify-center pb-6">
                  <FileText className={`h-16 w-16 ${receiptTemplate === 'professional' ? 'text-primary' : 'opacity-40'}`} />
                </CardContent>
              </Card>
              
              <Card 
                className={`cursor-pointer transition-all border-2 ${receiptTemplate === 'detailed' ? 'border-primary bg-primary/5' : 'hover:border-primary'}`}
                onClick={() => handleTemplateChange('detailed')}
              >
                <CardHeader className="pb-3">
                  <CardTitle className="text-lg">Detailed Receipt</CardTitle>
                  {receiptTemplate === 'detailed' && <Badge className="absolute top-3 right-3">Selected</Badge>}
                </CardHeader>
                <CardContent className="flex justify-center pb-6">
                  <FileText className={`h-16 w-16 ${receiptTemplate === 'detailed' ? 'text-primary' : 'opacity-40'}`} />
                </CardContent>
              </Card>
            </div>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-10">
            {/* Receipt Form */}
            <div className="lg:col-span-2">
              <Card className="border-2 border-gray-100 dark:border-gray-700 shadow-lg rounded-2xl overflow-hidden transition-all hover:shadow-xl">
                <CardHeader className="pb-4 bg-gradient-to-r from-gray-50 to-blue-50 dark:from-gray-800 dark:to-gray-700">
                  <CardTitle className="text-2xl md:text-3xl">Receipt Information</CardTitle>
                  <CardDescription className="text-base md:text-lg">Enter the details for your receipt</CardDescription>
                </CardHeader>
                <CardContent className="space-y-8 p-6 md:p-8">
                  {/* Business Details */}
                  <div className="space-y-4">
                    <h3 className="font-medium text-lg">Business Information</h3>
                    <div>
                      <Label htmlFor="businessName" className="mb-1.5 block text-base flex items-center">
                        Business Name
                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <HelpCircle className="ml-2 h-4 w-4 text-muted-foreground" />
                            </TooltipTrigger>
                            <TooltipContent className="p-3 max-w-xs text-sm">
                              <p>Your business or company name</p>
                            </TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                      </Label>
                      <Input 
                        id="businessName" 
                        value={businessName}
                        onChange={(e) => setBusinessName(e.target.value)}
                        placeholder="Your Business Name"
                        className="mb-3"
                      />
                    </div>
                    {(receiptTemplate === 'professional' || receiptTemplate === 'detailed') && (
                      <>
                        <div>
                          <Label htmlFor="businessAddress" className="mb-1.5 block text-base flex items-center">
                            Business Address
                            <TooltipProvider>
                              <Tooltip>
                                <TooltipTrigger asChild>
                                  <HelpCircle className="ml-2 h-4 w-4 text-muted-foreground" />
                                </TooltipTrigger>
                                <TooltipContent className="p-3 max-w-xs text-sm">
                                  <p>Physical address of your business</p>
                                </TooltipContent>
                              </Tooltip>
                            </TooltipProvider>
                          </Label>
                          <Input 
                            id="businessAddress" 
                            value={businessAddress}
                            onChange={(e) => setBusinessAddress(e.target.value)}
                            placeholder="Business Address"
                            className="mb-3"
                          />
                        </div>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div>
                            <Label htmlFor="businessPhone" className="mb-1.5 block text-base flex items-center">
                              Phone
                              <TooltipProvider>
                                <Tooltip>
                                  <TooltipTrigger asChild>
                                    <HelpCircle className="ml-2 h-4 w-4 text-muted-foreground" />
                                  </TooltipTrigger>
                                  <TooltipContent className="p-3 max-w-xs text-sm">
                                    <p>Business contact phone</p>
                                  </TooltipContent>
                                </Tooltip>
                              </TooltipProvider>
                            </Label>
                            <Input 
                              id="businessPhone" 
                              value={businessPhone}
                              onChange={(e) => setBusinessPhone(e.target.value)}
                              placeholder="Phone Number"
                            />
                          </div>
                          <div>
                            <Label htmlFor="businessEmail" className="mb-1.5 block text-base flex items-center">
                              Email
                              <TooltipProvider>
                                <Tooltip>
                                  <TooltipTrigger asChild>
                                    <HelpCircle className="ml-2 h-4 w-4 text-muted-foreground" />
                                  </TooltipTrigger>
                                  <TooltipContent className="p-3 max-w-xs text-sm">
                                    <p>Business contact email</p>
                                  </TooltipContent>
                                </Tooltip>
                              </TooltipProvider>
                            </Label>
                            <Input 
                              id="businessEmail" 
                              value={businessEmail}
                              onChange={(e) => setBusinessEmail(e.target.value)}
                              placeholder="Email"
                            />
                          </div>
                        </div>
                      </>
                    )}
                    <div>
                      <Label htmlFor="receiptNumber" className="mb-1.5 block text-base flex items-center">
                        Receipt Number
                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <HelpCircle className="ml-2 h-4 w-4 text-muted-foreground" />
                            </TooltipTrigger>
                            <TooltipContent className="p-3 max-w-xs text-sm">
                              <p>Unique identifier for this receipt</p>
                            </TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                      </Label>
                      <Input 
                        id="receiptNumber" 
                        value={receiptNumber}
                        onChange={(e) => setReceiptNumber(e.target.value)}
                        placeholder="e.g. 00001"
                      />
                    </div>
                  </div>

                  <Separator />

                  {/* Customer Details */}
                  <div className="space-y-4">
                    <h3 className="font-medium text-lg">Customer Information</h3>
                    <div>
                      <Label htmlFor="customerName" className="mb-1.5 block text-base flex items-center">
                        Customer Name
                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <HelpCircle className="ml-2 h-4 w-4 text-muted-foreground" />
                            </TooltipTrigger>
                            <TooltipContent className="p-3 max-w-xs text-sm">
                              <p>Full name of the customer</p>
                            </TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                      </Label>
                      <Input 
                        id="customerName" 
                        value={customerName}
                        onChange={(e) => setCustomerName(e.target.value)}
                        placeholder="Customer Name"
                        className="mb-3"
                      />
                    </div>
                    {(receiptTemplate === 'professional' || receiptTemplate === 'detailed') && (
                      <>
                        <div>
                          <Label htmlFor="customerAddress" className="mb-1.5 block text-base flex items-center">
                            Customer Address
                            <TooltipProvider>
                              <Tooltip>
                                <TooltipTrigger asChild>
                                  <HelpCircle className="ml-2 h-4 w-4 text-muted-foreground" />
                                </TooltipTrigger>
                                <TooltipContent className="p-3 max-w-xs text-sm">
                                  <p>Physical address of the customer</p>
                                </TooltipContent>
                              </Tooltip>
                            </TooltipProvider>
                          </Label>
                          <Input 
                            id="customerAddress" 
                            value={customerAddress}
                            onChange={(e) => setCustomerAddress(e.target.value)}
                            placeholder="Customer Address"
                            className="mb-3"
                          />
                        </div>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div>
                            <Label htmlFor="customerPhone" className="mb-1.5 block text-base flex items-center">
                              Phone
                              <TooltipProvider>
                                <Tooltip>
                                  <TooltipTrigger asChild>
                                    <HelpCircle className="ml-2 h-4 w-4 text-muted-foreground" />
                                  </TooltipTrigger>
                                  <TooltipContent className="p-3 max-w-xs text-sm">
                                    <p>Customer contact phone</p>
                                  </TooltipContent>
                                </Tooltip>
                              </TooltipProvider>
                            </Label>
                            <Input 
                              id="customerPhone" 
                              value={customerPhone}
                              onChange={(e) => setCustomerPhone(e.target.value)}
                              placeholder="Phone Number"
                            />
                          </div>
                          <div>
                            <Label htmlFor="customerEmail" className="mb-1.5 block text-base flex items-center">
                              Email
                              <TooltipProvider>
                                <Tooltip>
                                  <TooltipTrigger asChild>
                                    <HelpCircle className="ml-2 h-4 w-4 text-muted-foreground" />
                                  </TooltipTrigger>
                                  <TooltipContent className="p-3 max-w-xs text-sm">
                                    <p>Customer email address</p>
                                  </TooltipContent>
                                </Tooltip>
                              </TooltipProvider>
                            </Label>
                            <Input 
                              id="customerEmail" 
                              value={customerEmail}
                              onChange={(e) => setCustomerEmail(e.target.value)}
                              placeholder="Email"
                            />
                          </div>
                        </div>
                      </>
                    )}
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="date" className="mb-1.5 block text-base flex items-center">
                          Date
                          <TooltipProvider>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <HelpCircle className="ml-2 h-4 w-4 text-muted-foreground" />
                              </TooltipTrigger>
                              <TooltipContent className="p-3 max-w-xs text-sm">
                                <p>Date of the transaction</p>
                              </TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                        </Label>
                        <Input 
                          id="date" 
                          type="date"
                          value={date}
                          onChange={(e) => setDate(e.target.value)}
                        />
                      </div>
                      <div>
                        <Label htmlFor="paymentMethod" className="mb-1.5 block text-base flex items-center">
                          Payment Method
                          <TooltipProvider>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <HelpCircle className="ml-2 h-4 w-4 text-muted-foreground" />
                              </TooltipTrigger>
                              <TooltipContent className="p-3 max-w-xs text-sm">
                                <p>How the customer paid</p>
                              </TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                        </Label>
                        <Input 
                          id="paymentMethod" 
                          value={paymentMethod}
                          onChange={(e) => setPaymentMethod(e.target.value)}
                          placeholder="e.g. Cash, Credit Card, Check"
                        />
                      </div>
                    </div>
                  </div>

                  <Separator />

                  {/* Items */}
                  <div className="space-y-4">
                    <div className="flex justify-between items-center">
                      <h3 className="font-medium text-lg">Items</h3>
                      <Button type="button" variant="outline" size="sm" onClick={addItem}>
                        <Plus className="h-4 w-4 mr-1.5" /> Add Item
                      </Button>
                    </div>
                    
                    <div className="space-y-4 bg-gray-50 dark:bg-gray-800/50 p-4 rounded-md">
                      <div className="grid grid-cols-12 gap-2 text-sm font-medium text-muted-foreground pb-1 border-b">
                        <div className="col-span-5">Description</div>
                        <div className="col-span-2 text-center">Qty</div>
                        <div className="col-span-3 text-center">Unit Price</div>
                        <div className="col-span-2 text-right">Total</div>
                      </div>
                      
                      <div className="max-h-64 overflow-y-auto space-y-4 pr-2">
                        {items.map((item) => (
                          <div key={item.id} className="grid grid-cols-12 gap-2 items-center">
                            <div className="col-span-5">
                              <Input 
                                value={item.description}
                                onChange={(e) => updateItem(item.id, 'description', e.target.value)}
                                placeholder="Item Description"
                              />
                            </div>
                            <div className="col-span-2">
                              <Input 
                                type="number"
                                value={item.quantity}
                                onChange={(e) => updateItem(item.id, 'quantity', parseInt(e.target.value))}
                                min="1"
                                className="text-center"
                              />
                            </div>
                            <div className="col-span-3">
                              <Input 
                                type="number"
                                value={item.unitPrice}
                                onChange={(e) => updateItem(item.id, 'unitPrice', parseFloat(e.target.value))}
                                min="0"
                                step="0.01"
                                className="text-right"
                              />
                            </div>
                            <div className="col-span-2 flex items-center justify-between">
                              <Button 
                                type="button" 
                                variant="ghost" 
                                size="icon"
                                onClick={() => removeItem(item.id)}
                                disabled={items.length === 1}
                                className="h-8 w-8"
                              >
                                <Trash className="h-4 w-4 text-red-500" />
                              </Button>
                              <div className="font-medium">
                                ${formatCurrency(item.quantity * item.unitPrice)}
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>

                    {/* Tax Rate (for Professional and Detailed) */}
                    {(receiptTemplate === 'professional' || receiptTemplate === 'detailed') && (
                      <div className="flex items-center justify-end gap-3 mt-4">
                        <Label htmlFor="taxRate" className="text-sm whitespace-nowrap">Tax Rate (%)</Label>
                        <Input
                          id="taxRate"
                          type="number"
                          value={taxRate}
                          onChange={(e) => setTaxRate(parseFloat(e.target.value))}
                          min="0"
                          max="100"
                          step="0.1"
                          className="w-24 text-right"
                        />
                      </div>
                    )}
                  </div>

                  <Separator />

                  {/* Notes */}
                  <div>
                    <Label htmlFor="notes" className="mb-1.5 block text-base flex items-center">
                      Notes
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <HelpCircle className="ml-2 h-4 w-4 text-muted-foreground" />
                          </TooltipTrigger>
                          <TooltipContent className="p-3 max-w-xs text-sm">
                            <p>Any additional information or thank you message</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </Label>
                    <Textarea 
                      id="notes" 
                      value={notes}
                      onChange={(e) => setNotes(e.target.value)}
                      placeholder="Thank you for your business!"
                      rows={3}
                    />
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Receipt Preview */}
            <div className="lg:col-span-1">
              <Card className="border-2 border-primary/10 sticky top-28 shadow-lg bg-white dark:bg-gray-800 rounded-2xl overflow-hidden">
                <CardHeader className="pb-2 bg-gradient-to-r from-primary/5 to-blue-500/5">
                  <CardTitle className="text-xl">Receipt Preview</CardTitle>
                  <CardDescription className="text-base">How your receipt will look</CardDescription>
                </CardHeader>
                <CardContent className="p-6">
                  <div className="max-h-[36rem] overflow-y-auto">
                    {/* Standard Receipt Template */}
                    {receiptTemplate === 'standard' && (
                      <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 p-6 rounded-md shadow-sm">
                        <div className="text-center mb-4">
                          <h2 className="text-xl font-bold">{businessName || "Your Business Name"}</h2>
                          <div className="text-sm text-gray-500 dark:text-gray-400">Receipt #{receiptNumber}</div>
                          <div className="text-sm text-gray-500 dark:text-gray-400">Date: {date}</div>
                        </div>
                        
                        <div className="mb-4">
                          <div className="text-sm font-medium">Customer: {customerName || "Customer Name"}</div>
                          <div className="text-sm">Payment Method: {paymentMethod || "Payment Method"}</div>
                        </div>
                        
                        <div className="overflow-x-auto">
                          <table className="w-full mb-4 text-sm">
                            <thead>
                              <tr className="border-b dark:border-gray-700">
                                <th className="text-left py-2">Description</th>
                                <th className="text-center py-2">Qty</th>
                                <th className="text-right py-2">Price</th>
                                <th className="text-right py-2">Amount</th>
                              </tr>
                            </thead>
                            <tbody>
                              {items.map((item) => (
                                <tr key={item.id} className="border-b dark:border-gray-700">
                                  <td className="py-2">{item.description || "Item Description"}</td>
                                  <td className="text-center py-2">{item.quantity}</td>
                                  <td className="text-right py-2">${formatCurrency(item.unitPrice)}</td>
                                  <td className="text-right py-2">${formatCurrency(item.quantity * item.unitPrice)}</td>
                                </tr>
                              ))}
                            </tbody>
                            <tfoot>
                              <tr>
                                <td colSpan={3} className="text-right pt-2">Subtotal:</td>
                                <td className="text-right pt-2">${formatCurrency(subtotal)}</td>
                              </tr>
                              <tr>
                                <td colSpan={3} className="text-right pt-1">Tax ({taxRate}%):</td>
                                <td className="text-right pt-1">${formatCurrency(tax)}</td>
                              </tr>
                              <tr className="font-bold">
                                <td colSpan={3} className="text-right pt-2">Total:</td>
                                <td className="text-right pt-2">${formatCurrency(total)}</td>
                              </tr>
                            </tfoot>
                          </table>
                        </div>
                        
                        {notes && (
                          <div className="text-sm mt-4 border-t dark:border-gray-700 pt-2">
                            <div className="font-medium">Notes:</div>
                            <div>{notes}</div>
                          </div>
                        )}
                      </div>
                    )}

                    {/* Professional Receipt Template */}
                    {receiptTemplate === 'professional' && (
                      <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 p-4 rounded-md shadow-sm">
                        <div className="border-b pb-3 mb-3 dark:border-gray-700">
                          <div className="mb-1">
                            <h2 className="text-lg font-bold">{businessName || "Your Business Name"}</h2>
                            <div className="text-xs text-gray-500 dark:text-gray-400">{businessAddress || "Business Address"}</div>
                            <div className="text-xs text-gray-500 dark:text-gray-400">{businessPhone || "Phone"} | {businessEmail || "Email"}</div>
                          </div>
                          <div className="text-right">
                            <div className="text-sm font-bold text-gray-700 dark:text-gray-300">RECEIPT #{receiptNumber}</div>
                            <div className="text-xs text-gray-500 dark:text-gray-400">Date: {date}</div>
                          </div>
                        </div>
                        
                        <div className="mb-3 text-sm">
                          <div>
                            <div className="font-medium mb-1">Bill To:</div>
                            <div className="text-sm">{customerName || "Customer Name"}</div>
                            <div className="text-xs text-gray-500 dark:text-gray-400">{customerAddress || "Customer Address"}</div>
                            <div className="text-xs text-gray-500 dark:text-gray-400">{customerPhone || "Phone"}</div>
                          </div>
                          <div className="mt-2">
                            <div className="font-medium mb-1">Payment Method:</div>
                            <div className="text-sm">{paymentMethod || "Payment Method"}</div>
                          </div>
                        </div>
                        
                        <div className="overflow-x-auto text-xs mt-3">
                          <table className="w-full mb-2">
                            <thead>
                              <tr className="bg-gray-50 dark:bg-gray-700">
                                <th className="text-left py-2 px-1">Description</th>
                                <th className="text-center py-2 px-1">Qty</th>
                                <th className="text-right py-2 px-1">Price</th>
                                <th className="text-right py-2 px-1">Amount</th>
                              </tr>
                            </thead>
                            <tbody>
                              {items.map((item, index) => (
                                <tr 
                                  key={item.id} 
                                  className={index % 2 === 0 ? "" : "bg-gray-50 dark:bg-gray-800/50"}
                                >
                                  <td className="py-1 px-1">{item.description || "Item"}</td>
                                  <td className="text-center py-1 px-1">{item.quantity}</td>
                                  <td className="text-right py-1 px-1">${formatCurrency(item.unitPrice)}</td>
                                  <td className="text-right py-1 px-1">${formatCurrency(item.quantity * item.unitPrice)}</td>
                                </tr>
                              ))}
                            </tbody>
                          </table>
                        </div>
                        
                        <div className="text-xs mt-2">
                          <div className="flex justify-between py-1">
                            <div>Subtotal</div>
                            <div>${formatCurrency(subtotal)}</div>
                          </div>
                          <div className="flex justify-between py-1">
                            <div>Tax ({taxRate}%)</div>
                            <div>${formatCurrency(tax)}</div>
                          </div>
                          <div className="flex justify-between py-1 font-bold border-t dark:border-gray-700 mt-1 pt-1">
                            <div>Total</div>
                            <div>${formatCurrency(total)}</div>
                          </div>
                        </div>
                        
                        {notes && (
                          <div className="text-xs mt-3 pt-2 border-t dark:border-gray-700">
                            <div className="font-medium mb-1">Notes:</div>
                            <div className="break-words">{notes}</div>
                          </div>
                        )}
                      </div>
                    )}
                    
                    {/* Detailed Receipt Template */}
                    {receiptTemplate === 'detailed' && (
                      <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 p-3 rounded-md shadow-sm">
                        <div className="text-center border-b dark:border-gray-700 pb-2 mb-2">
                          <h2 className="text-base font-bold mb-1">{businessName || "Your Business Name"}</h2>
                          <div className="text-xs text-gray-500 dark:text-gray-400 mb-1">{businessAddress || "Business Address"}</div>
                          <div className="text-xs text-gray-500 dark:text-gray-400 mb-1">
                            {businessPhone || "Phone"} | {businessEmail || "Email"}
                          </div>
                          <div className="mt-2 bg-gray-50 dark:bg-gray-700 py-1 px-2 rounded-md inline-block">
                            <div className="text-sm font-bold">RECEIPT #{receiptNumber}</div>
                          </div>
                        </div>
                        
                        <div className="grid grid-cols-1 gap-2 mb-3 text-xs">
                          <div className="border dark:border-gray-700 rounded-md p-2">
                            <div className="font-medium text-gray-500 dark:text-gray-400 mb-1">BILL TO:</div>
                            <div className="font-medium">{customerName || "Customer Name"}</div>
                            <div>{customerAddress || "Customer Address"}</div>
                            <div>{customerPhone || "Phone"}</div>
                          </div>
                          <div className="border dark:border-gray-700 rounded-md p-2">
                            <div className="font-medium text-gray-500 dark:text-gray-400 mb-1">RECEIPT DETAILS:</div>
                            <div className="grid grid-cols-2 gap-1">
                              <div>Date:</div>
                              <div>{date}</div>
                              <div>Payment:</div>
                              <div>{paymentMethod || "Payment Method"}</div>
                            </div>
                          </div>
                        </div>
                        
                        <div className="mb-3 border dark:border-gray-700 rounded-md overflow-hidden text-xs">
                          <table className="w-full">
                            <thead>
                              <tr className="bg-gray-50 dark:bg-gray-700">
                                <th className="text-left py-1 px-1">#</th>
                                <th className="text-left py-1 px-1">Item</th>
                                <th className="text-center py-1 px-1">Qty</th>
                                <th className="text-right py-1 px-1">Price</th>
                                <th className="text-right py-1 px-1">Total</th>
                              </tr>
                            </thead>
                            <tbody>
                              {items.map((item, index) => (
                                <tr key={item.id} className={index % 2 === 0 ? "" : "bg-gray-50 dark:bg-gray-800/50"}>
                                  <td className="py-1 px-1">{index + 1}</td>
                                  <td className="py-1 px-1">{item.description || "Item"}</td>
                                  <td className="text-center py-1 px-1">{item.quantity}</td>
                                  <td className="text-right py-1 px-1">${formatCurrency(item.unitPrice)}</td>
                                  <td className="text-right py-1 px-1">${formatCurrency(item.quantity * item.unitPrice)}</td>
                                </tr>
                              ))}
                            </tbody>
                            <tfoot>
                              <tr className="border-t dark:border-gray-700">
                                <td colSpan={4} className="text-right py-1 px-1">Subtotal:</td>
                                <td className="text-right py-1 px-1">${formatCurrency(subtotal)}</td>
                              </tr>
                              <tr>
                                <td colSpan={4} className="text-right py-1 px-1">Tax ({taxRate}%):</td>
                                <td className="text-right py-1 px-1">${formatCurrency(tax)}</td>
                              </tr>
                              <tr className="font-bold">
                                <td colSpan={4} className="text-right py-1 px-1">Total:</td>
                                <td className="text-right py-1 px-1">${formatCurrency(total)}</td>
                              </tr>
                            </tfoot>
                          </table>
                        </div>
                        
                        {notes && (
                          <div className="text-xs mt-3 p-2 border dark:border-gray-700 rounded-md">
                            <div className="font-medium mb-1">Notes:</div>
                            <div className="break-words">{notes}</div>
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                </CardContent>
                <CardFooter className="flex justify-center gap-4 p-4 border-t bg-gray-50 dark:bg-gray-900/50">
                  <Button onClick={downloadReceipt} className="flex items-center gap-1">
                    <Download className="h-4 w-4" /> 
                    Download
                  </Button>
                  <Button variant="outline" onClick={emailReceipt} className="flex items-center gap-1">
                    <Send className="h-4 w-4" /> 
                    Email
                  </Button>
                </CardFooter>
              </Card>
            </div>
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default ReceiptGenerator;
