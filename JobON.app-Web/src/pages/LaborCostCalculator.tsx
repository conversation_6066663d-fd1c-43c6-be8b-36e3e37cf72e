import React, { useState, useEffect } from 'react';
import { Layout } from '@/components/Layout';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Slider } from '@/components/ui/slider';
import { ArrowLeft, HelpCircle, FileText, Calculator, Share2, Clock, Users, DollarSign, Settings, Scissors, Home } from 'lucide-react';
import { Link } from 'react-router-dom';
import { Separator } from '@/components/ui/separator';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { Badge } from '@/components/ui/badge';
import { toast } from "sonner";
import { Toaster } from "@/components/ui/sonner";
import {SEO} from "@/components/SEO.tsx";

const formatCurrency = (amount: number): string => {
  return amount.toLocaleString('en-US', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  });
};

const LaborCostCalculator: React.FC = () => {
  // State for form inputs
  const [hourlyRate, setHourlyRate] = useState<number>(25);
  const [numberOfWorkers, setNumberOfWorkers] = useState<number>(2);
  const [hoursPerDay, setHoursPerDay] = useState<number>(8);
  const [daysPerWeek, setDaysPerWeek] = useState<number>(5);
  const [weeksPerProject, setWeeksPerProject] = useState<number>(2);
  
  // Calculated values
  const [dailyCost, setDailyCost] = useState<number>(0);
  const [weeklyCost, setWeeklyCost] = useState<number>(0);
  const [totalProjectCost, setTotalProjectCost] = useState<number>(0);

  // State for custom rate settings
  const [showRateSettings, setShowRateSettings] = useState(false);
  const [overtimeMultiplier, setOvertimeMultiplier] = useState(1.5);
  const [weekendRateMultiplier, setWeekendRateMultiplier] = useState(1.25);
  
  // State to track if settings have been modified
  const [settingsApplied, setSettingsApplied] = useState(false);

  // Calculate values when inputs change
  useEffect(() => {
    const calculatedDailyCost = hourlyRate * numberOfWorkers * hoursPerDay;
    setDailyCost(calculatedDailyCost);
    
    const calculatedWeeklyCost = calculatedDailyCost * daysPerWeek;
    setWeeklyCost(calculatedWeeklyCost);
    
    const calculatedTotalProjectCost = calculatedWeeklyCost * weeksPerProject;
    setTotalProjectCost(calculatedTotalProjectCost);

  }, [hourlyRate, numberOfWorkers, hoursPerDay, daysPerWeek, weeksPerProject, settingsApplied]);

  const handleSaveSettings = () => {
    // Toggle settings applied to trigger recalculation
    setSettingsApplied(prev => !prev);
    
    // Hide the settings panel
    setShowRateSettings(false);
    
    // Display success toast
    toast.success("Settings saved successfully", {
      description: "Your custom rates have been applied to the calculator.",
    });
  };

  // Function to cancel settings changes
  const handleCancelSettings = () => {
    // Close settings panel
    setShowRateSettings(false);
    
    // Display info toast
    toast.info("Changes canceled", {
      description: "No changes were made to your custom rates.",
    });
  };

  return (
    <Layout>
      <SEO
          title="Labor Cost Calculator | Estimate Project Labor Costs Fast"
          description="Quickly estimate your labor costs with JobON’s free Labor Cost Calculator. Enter your hourly rate, team size, and project time to get instant cost estimates!"
          localBusinessSchema={true}
          serviceType="Labor cost calculator"
          serviceSlug="labor-cost-calculator"
          canonicalUrl="/free-tools/labor-cost-calculator"
      />
      <Toaster richColors />
      <div className="pt-20 pb-20 px-6 md:px-12 bg-gradient-to-b from-blue-50 to-white dark:from-gray-900 dark:to-gray-800">
        <div className="container mx-auto max-w-6xl">
          {/* Header with back button */}
          <div className="mb-8">
            <Link to="/free-tools" className="inline-flex items-center text-primary hover:underline mb-4 text-base md:text-lg">
              <ArrowLeft className="mr-2 h-5 w-5 md:h-6 md:w-6" />
              Back to Free Tools
            </Link>
          
            {/* Modified Header with Free Tool Badge */}
            <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4 relative">
              <div>
                <div className="flex items-center gap-3">
                  <h1 className="text-3xl md:text-5xl font-bold mt-2 mb-3 text-gray-800 dark:text-white">Labor Cost Calculator</h1>
                  <Badge variant="default" className="bg-gradient-to-r from-primary to-blue-500 text-white px-3 py-1 rounded-full text-xs font-semibold shadow-md whitespace-nowrap">
                    Free Tool
                  </Badge>
                </div>
                <p className="text-foreground/70 text-lg md:text-xl max-w-3xl">
                  Estimate your labor costs for a project by entering your hourly rate, number of workers, and time requirements.
                </p>
              </div>
              
              {/* Custom Rates Button */}
              <Button 
                onClick={() => setShowRateSettings(!showRateSettings)} 
                className="gap-2 bg-gradient-to-r from-primary to-blue-600 hover:from-primary/90 hover:to-blue-500 text-white shadow-md transition-all text-base md:text-lg"
                size="lg"
              >
                <Settings className="h-5 w-5" />
                Customize Rates
              </Button>
            </div>
          </div>

          {/* Related Calculators */}
          <div className="mb-8">
            <h2 className="text-xl font-semibold mb-4">Related Calculators</h2>
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
              <Link to="/free-tools/lawn-care-calculator" className="block">
                <Card className="hover:border-primary hover:shadow-md transition-all">
                  <CardContent className="flex items-center p-4">
                    <div className="w-12 h-12 rounded-full bg-green-100 dark:bg-green-900 flex items-center justify-center mr-4">
                      <Scissors className="h-6 w-6 text-green-600 dark:text-green-400" />
                    </div>
                    <div>
                      <h3 className="font-medium">Lawn Care Calculator</h3>
                      <p className="text-sm text-muted-foreground">Estimate lawn care service costs</p>
                    </div>
                  </CardContent>
                </Card>
              </Link>
              
              <Link to="/free-tools/house-cleaning-calculator" className="block">
                <Card className="hover:border-primary hover:shadow-md transition-all">
                  <CardContent className="flex items-center p-4">
                    <div className="w-12 h-12 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center mr-4">
                      <Home className="h-6 w-6 text-blue-600 dark:text-blue-400" />
                    </div>
                    <div>
                      <h3 className="font-medium">House Cleaning Calculator</h3>
                      <p className="text-sm text-muted-foreground">Calculate house cleaning costs</p>
                    </div>
                  </CardContent>
                </Card>
              </Link>
            </div>
          </div>

          {/* Custom Rate Settings Panel - Visible when settings button is clicked */}
          {showRateSettings && (
            <Card className="mb-8 border-2 border-gray-100 dark:border-gray-700 shadow-lg rounded-2xl overflow-hidden transition-all hover:shadow-xl">
              <CardHeader className="bg-gradient-to-r from-gray-50 to-blue-50 dark:from-gray-800 dark:to-gray-700">
                <CardTitle className="text-2xl flex items-center gap-2">
                  <DollarSign className="h-5 w-5 text-primary" />
                  Custom Rate Settings
                </CardTitle>
                <CardDescription className="text-base">Customize rate multipliers for different scenarios</CardDescription>
              </CardHeader>
              <CardContent className="pt-6 p-6 md:p-8">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-2">
                    <Label htmlFor="overtimeMultiplier" className="flex items-center text-base">
                      Overtime Rate Multiplier
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <HelpCircle className="ml-2 h-4 w-4 md:h-5 md:w-5 text-muted-foreground" />
                          </TooltipTrigger>
                          <TooltipContent className="p-3 max-w-xs text-sm md:text-base">
                            <p>Multiplier for overtime hours (typically 1.5x regular rate)</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </Label>
                    <div className="flex items-center gap-4">
                      <Slider 
                        min={1} 
                        max={3} 
                        step={0.1} 
                        value={[overtimeMultiplier]}
                        onValueChange={(value) => setOvertimeMultiplier(value[0])}
                      />
                      <Input 
                        type="number" 
                        id="overtimeMultiplier" 
                        value={overtimeMultiplier}
                        className="w-24 text-base md:text-lg" 
                        onChange={(e) => setOvertimeMultiplier(Number(e.target.value))}
                      />
                    </div>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="weekendRateMultiplier" className="flex items-center text-base">
                      Weekend Rate Multiplier
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <HelpCircle className="ml-2 h-4 w-4 md:h-5 md:w-5 text-muted-foreground" />
                          </TooltipTrigger>
                          <TooltipContent className="p-3 max-w-xs text-sm md:text-base">
                            <p>Multiplier for weekend work (typically 1.25x regular rate)</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </Label>
                    <div className="flex items-center gap-4">
                      <Slider 
                        min={1} 
                        max={3} 
                        step={0.1} 
                        value={[weekendRateMultiplier]}
                        onValueChange={(value) => setWeekendRateMultiplier(value[0])}
                      />
                      <Input 
                        type="number" 
                        id="weekendRateMultiplier" 
                        value={weekendRateMultiplier}
                        className="w-24 text-base md:text-lg" 
                        onChange={(e) => setWeekendRateMultiplier(Number(e.target.value))}
                      />
                    </div>
                  </div>
                </div>
                <div className="flex justify-end mt-6">
                  <Button 
                    onClick={handleCancelSettings} 
                    variant="outline" 
                    className="mr-2"
                  >
                    Cancel
                  </Button>
                  <Button 
                    onClick={handleSaveSettings}
                    className="bg-blue-600 hover:bg-blue-700 text-white"
                  >
                    Save Settings
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-10">
            {/* Calculator Inputs */}
            <div className="lg:col-span-2">
              <Card className="border-2 border-gray-100 dark:border-gray-700 shadow-lg rounded-2xl overflow-hidden transition-all hover:shadow-xl">
                <CardHeader className="pb-4 bg-gradient-to-r from-gray-50 to-blue-50 dark:from-gray-800 dark:to-gray-700">
                  <CardTitle className="text-2xl md:text-3xl">Enter Your Numbers</CardTitle>
                  <CardDescription className="text-base md:text-lg">Input your labor details to calculate the total cost</CardDescription>
                </CardHeader>
                <CardContent className="space-y-8 p-6 md:p-8">
                  {/* Hourly Rate */}
                  <div className="space-y-4">
                    <div className="flex justify-between items-center">
                      <Label htmlFor="hourlyRate" className="text-base md:text-xl flex items-center font-medium">
                        Hourly Rate 
                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <HelpCircle className="ml-2 h-4 w-4 md:h-5 md:w-5 text-muted-foreground" />
                            </TooltipTrigger>
                            <TooltipContent className="p-3 max-w-xs text-sm md:text-base">
                              <p>Average hourly rate paid to workers</p>
                            </TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                      </Label>
                      <span className="text-right font-medium text-lg md:text-2xl">${formatCurrency(hourlyRate)}</span>
                    </div>
                    <div className="flex items-center gap-6">
                      <Slider 
                        min={10} 
                        max={100} 
                        step={1} 
                        defaultValue={[25]} 
                        value={[hourlyRate]}
                        onValueChange={(value) => setHourlyRate(value[0])}
                        className="flex-1"
                      />
                      <Input 
                        type="number" 
                        id="hourlyRate" 
                        value={hourlyRate}
                        className="w-24 text-base md:text-lg" 
                        onChange={(e) => setHourlyRate(Number(e.target.value))}
                      />
                    </div>
                  </div>
                  
                  {/* Number of Workers */}
                  <div className="space-y-4">
                    <div className="flex justify-between items-center">
                      <Label htmlFor="numberOfWorkers" className="text-base md:text-xl flex items-center font-medium">
                        Number of Workers
                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <HelpCircle className="ml-2 h-4 w-4 md:h-5 md:w-5 text-muted-foreground" />
                            </TooltipTrigger>
                            <TooltipContent className="p-3 max-w-xs text-sm md:text-base">
                              <p>Total number of workers on the project</p>
                            </TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                      </Label>
                      <span className="text-right font-medium text-lg md:text-2xl">{numberOfWorkers}</span>
                    </div>
                    <div className="flex items-center gap-6">
                      <Slider 
                        min={1} 
                        max={20} 
                        step={1} 
                        defaultValue={[2]} 
                        value={[numberOfWorkers]}
                        onValueChange={(value) => setNumberOfWorkers(value[0])}
                        className="flex-1"
                      />
                      <Input 
                        type="number" 
                        id="numberOfWorkers" 
                        value={numberOfWorkers}
                        className="w-24 text-base md:text-lg" 
                        onChange={(e) => setNumberOfWorkers(Number(e.target.value))}
                      />
                    </div>
                  </div>
                  
                  {/* Hours Per Day */}
                  <div className="space-y-4">
                    <div className="flex justify-between items-center">
                      <Label htmlFor="hoursPerDay" className="text-base md:text-xl flex items-center font-medium">
                        Hours Per Day
                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <HelpCircle className="ml-2 h-4 w-4 md:h-5 md:w-5 text-muted-foreground" />
                            </TooltipTrigger>
                            <TooltipContent className="p-3 max-w-xs text-sm md:text-base">
                              <p>Average working hours per day</p>
                            </TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                      </Label>
                      <span className="text-right font-medium text-lg md:text-2xl">{hoursPerDay}</span>
                    </div>
                    <div className="flex items-center gap-6">
                      <Slider 
                        min={1} 
                        max={12} 
                        step={1} 
                        defaultValue={[8]} 
                        value={[hoursPerDay]}
                        onValueChange={(value) => setHoursPerDay(value[0])}
                        className="flex-1"
                      />
                      <Input 
                        type="number" 
                        id="hoursPerDay" 
                        value={hoursPerDay}
                        className="w-24 text-base md:text-lg" 
                        onChange={(e) => setHoursPerDay(Number(e.target.value))}
                      />
                    </div>
                  </div>
                  
                  {/* Days Per Week */}
                  <div className="space-y-4">
                    <div className="flex justify-between items-center">
                      <Label htmlFor="daysPerWeek" className="text-base md:text-xl flex items-center font-medium">
                        Days Per Week
                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <HelpCircle className="ml-2 h-4 w-4 md:h-5 md:w-5 text-muted-foreground" />
                            </TooltipTrigger>
                            <TooltipContent className="p-3 max-w-xs text-sm md:text-base">
                              <p>Number of working days per week</p>
                            </TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                      </Label>
                      <span className="text-right font-medium text-lg md:text-2xl">{daysPerWeek}</span>
                    </div>
                    <div className="flex items-center gap-6">
                      <Slider 
                        min={1} 
                        max={7} 
                        step={1} 
                        defaultValue={[5]} 
                        value={[daysPerWeek]}
                        onValueChange={(value) => setDaysPerWeek(value[0])}
                        className="flex-1"
                      />
                      <Input 
                        type="number" 
                        id="daysPerWeek" 
                        value={daysPerWeek}
                        className="w-24 text-base md:text-lg" 
                        onChange={(e) => setDaysPerWeek(Number(e.target.value))}
                      />
                    </div>
                  </div>
                  
                  {/* Weeks Per Project */}
                  <div className="space-y-4">
                    <div className="flex justify-between items-center">
                      <Label htmlFor="weeksPerProject" className="text-base md:text-xl flex items-center font-medium">
                        Weeks Per Project
                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <HelpCircle className="ml-2 h-4 w-4 md:h-5 md:w-5 text-muted-foreground" />
                            </TooltipTrigger>
                            <TooltipContent className="p-3 max-w-xs text-sm md:text-base">
                              <p>Total number of weeks for project completion</p>
                            </TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                      </Label>
                      <span className="text-right font-medium text-lg md:text-2xl">{weeksPerProject}</span>
                    </div>
                    <div className="flex items-center gap-6">
                      <Slider 
                        min={1} 
                        max={52} 
                        step={1} 
                        defaultValue={[2]} 
                        value={[weeksPerProject]}
                        onValueChange={(value) => setWeeksPerProject(value[0])}
                        className="flex-1"
                      />
                      <Input 
                        type="number" 
                        id="weeksPerProject" 
                        value={weeksPerProject}
                        className="w-24 text-base md:text-lg" 
                        onChange={(e) => setWeeksPerProject(Number(e.target.value))}
                      />
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Results Card */}
            <div>
              <Card className="border-2 border-primary/10 sticky top-28 shadow-lg bg-white dark:bg-gray-800 rounded-2xl overflow-hidden animate-fade-in">
                <CardHeader className="pb-2 bg-gradient-to-r from-primary/5 to-blue-500/5">
                  <CardTitle className="text-xl md:text-2xl">Labor Cost Results</CardTitle>
                  <CardDescription className="text-base">Based on your inputs</CardDescription>
                </CardHeader>
                <CardContent className="space-y-6 p-6">
                  {/* Total Project Cost */}
                  <div className="text-center py-8 bg-gradient-to-r from-primary/5 to-blue-500/5 rounded-xl border border-primary/10 transform hover:scale-105 transition-transform duration-300">
                    <div className="text-4xl md:text-5xl font-bold text-primary mb-1 animate-scale-in px-4 break-words">
                      ${formatCurrency(totalProjectCost)}
                    </div>
                    <p className="text-sm md:text-base text-muted-foreground">Total Project Labor Cost</p>
                  </div>
                  
                  <Separator className="my-2" />
                  
                  <div className="space-y-3 text-base md:text-lg">
                    <div className="flex justify-between py-1">
                      <span className="text-muted-foreground">Daily Cost:</span>
                      <span className="font-medium">${formatCurrency(dailyCost)}</span>
                    </div>
                    <div className="flex justify-between py-1">
                      <span className="text-muted-foreground">Weekly Cost:</span>
                      <span className="font-medium">${formatCurrency(weeklyCost)}</span>
                    </div>
                    <div className="flex justify-between py-1">
                      <span className="text-muted-foreground">Cost Per Worker:</span>
                      <span className="font-medium">${formatCurrency(totalProjectCost / numberOfWorkers)}</span>
                    </div>
                  </div>
                </CardContent>
                <CardFooter className="flex-col space-y-3 pt-2 p-6">
                  <Button className="w-full text-base md:text-lg py-6" variant="default">
                    <FileText className="mr-2 h-5 w-5" />
                    Save as PDF
                  </Button>
                  <Button className="w-full text-base md:text-lg py-6" variant="outline">
                    <Share2 className="mr-2 h-5 w-5" />
                    Share Results
                  </Button>
                </CardFooter>
              </Card>
            </div>
          </div>
          
          {/* Tips Section */}
          <div className="mt-16">
            <h2 className="text-2xl md:text-3xl font-semibold mb-8">Tips to Optimize Labor Costs</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              <Card className="shadow-sm hover:shadow-md transition-shadow border-2 border-gray-100 dark:border-gray-700 rounded-xl hover:border-primary/10">
                <CardHeader className="pb-2">
                  <CardTitle className="text-lg md:text-xl flex items-center">
                    <span className="p-2 mr-3 rounded-full bg-gradient-to-r from-primary/10 to-blue-500/10">
                      <Users className="h-5 w-5 md:h-6 md:w-6 text-primary" />
                    </span>
                    Improve Crew Efficiency
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-foreground/80 leading-relaxed text-base md:text-lg">
                    Consider cross-training workers to handle multiple tasks and reduce idle time. Implement clear daily objectives to keep teams focused.
                  </p>
                </CardContent>
              </Card>
              <Card className="shadow-sm hover:shadow-md transition-shadow border-2 border-gray-100 dark:border-gray-700 rounded-xl hover:border-primary/10">
                <CardHeader className="pb-2">
                  <CardTitle className="text-lg md:text-xl flex items-center">
                    <span className="p-2 mr-3 rounded-full bg-gradient-to-r from-primary/10 to-blue-500/10">
                      <Clock className="h-5 w-5 md:h-6 md:w-6 text-primary" />
                    </span>
                    Optimize Work Scheduling
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-foreground/80 leading-relaxed text-base md:text-lg">
                    Group similar tasks together to reduce setup and transition times. Schedule work to minimize overtime hours while still meeting project deadlines.
                  </p>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default LaborCostCalculator;
