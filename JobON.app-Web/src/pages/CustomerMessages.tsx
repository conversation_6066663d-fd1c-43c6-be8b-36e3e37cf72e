import React from 'react';
import { Chat<PERSON>rovider } from '@/contexts/ChatContext';
import { UniversalChat } from '@/components/chat';
import { useAuth } from '@/features/auth/hooks/useAuth';
import { useAutoSelectChat } from '@/hooks/useAutoSelectChat';

/**
 * Customer Messages Page
 *
 * Dedicated page for customer messaging functionality using the Universal Chat component.
 */
const CustomerMessages: React.FC = () => {
  const { user } = useAuth();

  return (
    <ChatProvider>
      <CustomerMessagesContent userId={user?.id || 'customer-user'} />
    </ChatProvider>
  );
};

// Separate component to use hooks inside ChatProvider
const CustomerMessagesContent: React.FC<{ userId: string }> = ({ userId }) => {
  // Auto-select chat based on URL parameters
  const { userIdParam, isAutoSelecting } = useAutoSelectChat();

  return (
    <div className="h-[calc(100vh-80px)] bg-white rounded-lg shadow-sm border">
      <UniversalChat
        userRole="customer"
        userId={userId}
        variant="full"
        theme="customer"
        onChatSelect={(chatId) => {
          // console.log('Customer selected chat:', chatId);
        }}
        onMessageSent={(message) => {
          // console.log('Customer sent message:', message);
        }}
        onError={(error) => {
          // console.error('Customer chat error:', error);
        }}
      />
    </div>
  );
};

export default CustomerMessages;