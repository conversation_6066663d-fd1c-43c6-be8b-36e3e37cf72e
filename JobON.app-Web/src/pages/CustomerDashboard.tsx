"use client";

import { useLocation, useParams } from "react-router-dom";
import { useIsMobile } from "@/hooks/use-mobile";
import { ResponsiveMessageInterface } from "@/components/customer/ResponsiveMessageInterface";

// Import the necessary components for the customer dashboard content
import { CustomerActiveJobs } from "@/components/customer/CustomerActiveJobs";
import { CustomerCompletedJobs } from "@/components/customer/CustomerCompletedJobs";
import { CustomerMessages } from "@/components/customer/CustomerMessages";
import { CustomerCalendar } from "@/components/customer/CustomerCalendar";
import { CustomerPayments } from "@/components/customer/CustomerPayments";
import { CustomerReviews } from "@/components/customer/CustomerReviews";
import { CustomerRewards } from "@/components/customer/CustomerRewards";
import { CustomerReferrals } from "@/components/customer/CustomerReferrals";
import { CustomerProfile } from "@/components/customer/CustomerProfile";
import { CustomerSettings } from "@/components/customer/CustomerSettings";
import { DashboardHome } from "@/components/customer/DashboardHome";
import { MobileCustomerDashboard } from "@/components/customer/MobileCustomerDashboard";
import { MobileActiveJobs } from "@/components/customer/job-management/MobileActiveJobs";
import { MobileCompletedJobs } from "@/components/customer/job-management/MobileCompletedJobs";
import FindProMobile from "./FindProMobile";
import { JobManage } from "@/components/customer/job-management/JobManage";

function getTabFromSearch(search: string) {
  const params = new URLSearchParams(search);
  return params.get("tab");
}

// Main CustomerDashboard component that will be used in routes
const CustomerDashboard = ({
  children
}: {
  children?: React.ReactNode;
}) => {
  const location = useLocation();
  const currentTab = getTabFromSearch(location.search);
  const {
    page
  } = useParams<{
    page?: string;
  }>();
  const isMobile = useIsMobile();



  // Function to render the appropriate content based on the current tab
  const renderContent = () => {
    if (children) {
      return children;
    }

    // Check if we're on the find-pro path
    if (page === "find-pro") {
      return <FindProMobile />;
    }

    // For mobile: return mobile-specific components when available
    if (isMobile) {
      switch (currentTab) {
        case 'active-jobs':
          return <MobileActiveJobs />;
        case 'completed-jobs':
          return <MobileCompletedJobs />;
        case 'messages':
          return <ResponsiveMessageInterface />;
        case 'payments':
          return <CustomerPayments />;
        case 'reviews':
          return <CustomerReviews />;
        case 'rewards':
          return <CustomerRewards />;
        case 'settings':
          return <CustomerSettings />;
        case 'manage-job':
          return <JobManage />;  
        // Will render MobileCustomerSettings inside
        default:
          // For other tabs not yet having mobile versions, fall through to the desktop components
          break;
      }
    }

    // Return the appropriate component based on the current tab
    switch (currentTab) {
      case 'active-jobs':
        return <CustomerActiveJobs />;
      case 'completed-jobs':
        return isMobile ? <MobileCompletedJobs /> : <CustomerCompletedJobs />;
      case 'messages':
        return isMobile ? <ResponsiveMessageInterface /> : <CustomerMessages />;
      case 'calendar':
        return <CustomerCalendar />;
      case 'payments':
        return <CustomerPayments />;
      case 'reviews':
        return <CustomerReviews />;
      case 'rewards':
        return <CustomerRewards />;
      case 'referrals':
        return <CustomerReferrals />;
      case 'profile':
        return <CustomerProfile />;
      case 'settings':
        return <CustomerSettings />;
      default:
        return isMobile ? <MobileCustomerDashboard /> : <DashboardHome />;
    }
  };

  // Since CustomerDashboard is now wrapped by CustomerLayout, we just return the content
  // The CustomerLayout handles both mobile and desktop layouts including the sidebar
  return renderContent();
};
export default CustomerDashboard;