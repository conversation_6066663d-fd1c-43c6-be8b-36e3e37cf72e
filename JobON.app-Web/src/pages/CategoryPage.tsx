
import React from 'react';
import { useParams } from 'react-router-dom';
import { Layout } from '@/components/Layout';

const CategoryPage = () => {
  const { categoryId } = useParams();

  return (
    <Layout>
      <div className="container mx-auto px-4 py-8">
        <h1 className="text-2xl font-bold mb-6">Category: {categoryId}</h1>
        <p>Category page content coming soon...</p>
      </div>
    </Layout>
  );
};

export default CategoryPage;
