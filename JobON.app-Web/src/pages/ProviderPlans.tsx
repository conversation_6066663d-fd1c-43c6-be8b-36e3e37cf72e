
import React, { useState, useEffect } from 'react';
import { ProviderDashboardLayout } from '@/components/provider/ProviderDashboardLayout';
import { SubscriptionPlanCard } from '@/components/provider/subscription/SubscriptionPlanCard';
import { BusinessCertificationDialog } from '@/components/provider/BusinessCertificationDialog';
import { useToast } from '@/components/ui/use-toast';
import { useAuth } from '@/features/auth/hooks/useAuth';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

import { Loader2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { planService } from '@/services/planService';
import { ProviderPlan } from '@/components/admin/provider-tier/schemas';

const ProviderPlans = () => {
  const { toast } = useToast();
  const { user } = useAuth();
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [selectedPlan, setSelectedPlan] = useState<string>('');
  const [plans, setPlans] = useState<ProviderPlan[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<string>('monthly');

  // Get current subscription plan identifier and duration from user data
  const getCurrentSubscriptionPlan = () => {
    if (!user?.current_subscription) {
      return { name: 'starter', duration: 'monthly' };
    }
    // Return the plan name and duration from the current subscription
    return {
      name: user.current_subscription.plan.name.toLowerCase(),
      duration: user.current_subscription.plan.duration?.toLowerCase() || 'monthly'
    };
  };

  // Helper function to get current user plan name only
  const getCurrentUserPlan = () => {
    if (!user?.current_subscription) {
      return 'starter'; // Default to starter if no subscription
    }
    return user.current_subscription.plan.name.toLowerCase();
  };

  // Helper function to get plan hierarchy for comparison
  const getPlanLevel = (planName: string) => {
    const plan = planName.toLowerCase();
    switch (plan) {
      case 'starter':
      case 'free':
        return 1;
      case 'pro':
      case 'professional':
        return 2;
      case 'elite':
      case 'enterprise':
        return 3;
      default:
        return 1;
    }
  };
  
  // Set initial active tab based on user's current subscription
  useEffect(() => {
    if (user?.current_subscription) {
      const currentSubscription = getCurrentSubscriptionPlan();
      setActiveTab(currentSubscription.duration);
    }
  }, [user]);

  // Fetch plans from API
  useEffect(() => {
    const fetchPlans = async () => {
      try {
        setLoading(true);
        setError(null);
        const response = await planService.getPlans(1, 50);
        
        if (response.success && response.data?.data) {
          // Filter only active plans
          const activePlans = response.data.data.filter(plan => 
            plan.features?.some(feature => 
              feature.text.toLowerCase().includes('active') ||
              feature.text.toLowerCase().includes('available')
            ) !== false // Include plans that don't have explicit status
          );
          setPlans(activePlans);
        } else {
          setError('Failed to load subscription plans');
        }
      } catch (err) {
        console.error('Error fetching plans:', err);
        setError('Failed to load subscription plans');
      } finally {
        setLoading(false);
      }
    };

    fetchPlans();
  }, []);
  
  // Filter plans by duration
  const getFilteredPlans = (duration: string) => {
    return plans.filter(plan => {
      const planDuration = plan.duration?.toLowerCase() || 'monthly';
      return planDuration === duration;
    });
  };

  // Transform API plan data to SubscriptionPlanCard props
  const transformPlanToCardProps = (plan: ProviderPlan) => {
    const basePrice = plan.price || 0;
    const currentSubscription = getCurrentSubscriptionPlan();
    const currentPlan = getCurrentUserPlan();

    // Check if this plan is the current subscription
    // Compare both plan names and duration (case-insensitive)
    const isCurrentPlan = currentSubscription.name === plan.name.toLowerCase() &&
                         currentSubscription.duration === (plan.duration?.toLowerCase() || 'monthly');

    // Get plan levels for comparison
    const currentLevel = getPlanLevel(currentPlan);
    const targetLevel = getPlanLevel(plan.name);

    // Determine button text based on plan comparison
    let ctaText = '';
    if (isCurrentPlan) {
      ctaText = 'Current Plan';
    } else if (targetLevel > currentLevel) {
      ctaText = `Upgrade to ${plan.name}`;
    } else if (targetLevel < currentLevel) {
      ctaText = `Downgrade to ${plan.name}`;
    } else {
      ctaText = Number(plan.price) === 0 ? 'Get Started' : `Switch to ${plan.name}`;
    }

    return {
      title: plan.name,
      price: Number(plan.price) === 0 ? 'Free' : `$${plan.price}`,
      description: plan.description || '',
      commission: plan.commission || '0%',
      isCurrentPlan: isCurrentPlan,
      isPopular: plan.name.toLowerCase() === 'pro',
      features: plan.features || [],
      ctaText: ctaText,
      onSelect: () => handleCheckout(plan.id || plan.name.toLowerCase()),
      disabled: isCurrentPlan,
    };
  };
  
  // Retry function for error state
  const retryFetch = () => {
    const fetchPlans = async () => {
      try {
        setLoading(true);
        setError(null);
        const response = await planService.getPlans(1, 50);
        
        if (response.success && response.data?.data) {
          const activePlans = response.data.data.filter(plan => 
            plan.features?.some(feature => 
              feature.text.toLowerCase().includes('active') ||
              feature.text.toLowerCase().includes('available')
            ) !== false
          );
          setPlans(activePlans);
        } else {
          setError('Failed to load subscription plans');
        }
      } catch (err) {
        console.error('Error fetching plans:', err);
        setError('Failed to load subscription plans');
      } finally {
        setLoading(false);
      }
    };

    fetchPlans();
  };
  
  // Show dialog to confirm business certification upload
  const handleCheckout = (planId: string) => {
    setSelectedPlan(planId);
    setIsDialogOpen(true);
  };

  const handleCloseDialog = () => {
    setIsDialogOpen(false);
    setSelectedPlan('');
  };
  
  return (
    <ProviderDashboardLayout pageTitle="Plans & Pricing">
      <div className="space-y-6 pb-12">
        <div className="text-center mb-8">
          <h2 className="text-2xl font-bold text-gray-900">Manage Your Plan</h2>
          <p className="text-gray-600 mt-2">Upgrade, downgrade, or manage your subscription plan</p>
        </div>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-2 mb-8 max-w-md mx-auto">
            <TabsTrigger value="monthly" className="text-sm font-medium">
              Monthly
            </TabsTrigger>
            <TabsTrigger value="yearly" className="text-sm font-medium">
              Yearly
            </TabsTrigger>
          </TabsList>

          <TabsContent value="monthly" className="space-y-6">
            {loading ? (
              <div className="flex justify-center items-center py-12">
                <Loader2 className="h-8 w-8 animate-spin" />
                <span className="ml-2 text-lg">Loading plans...</span>
              </div>
            ) : error ? (
              <div className="text-center py-12">
                <p className="text-red-600 mb-4">{error}</p>
                <Button onClick={retryFetch} variant="outline">
                  Try Again
                </Button>
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                {getFilteredPlans('monthly').map((plan) => {
                  const cardProps = transformPlanToCardProps(plan);
                  return (
                    <SubscriptionPlanCard
                      key={`monthly-${plan.id || plan.name}`}
                      {...cardProps}
                    />
                  );
                })}
              </div>
            )}
          </TabsContent>

          <TabsContent value="yearly" className="space-y-6">
            {loading ? (
              <div className="flex justify-center items-center py-12">
                <Loader2 className="h-8 w-8 animate-spin" />
                <span className="ml-2 text-lg">Loading plans...</span>
              </div>
            ) : error ? (
              <div className="text-center py-12">
                <p className="text-red-600 mb-4">{error}</p>
                <Button onClick={retryFetch} variant="outline">
                  Try Again
                </Button>
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                {getFilteredPlans('yearly').map((plan) => {
                  const cardProps = transformPlanToCardProps(plan);
                  return (
                    <SubscriptionPlanCard
                      key={`yearly-${plan.id || plan.name}`}
                      {...cardProps}
                    />
                  );
                })}
              </div>
            )}
          </TabsContent>
        </Tabs>
      </div>
      
      {/* Business Certification Dialog */}
      <BusinessCertificationDialog
        isOpen={isDialogOpen}
        onClose={handleCloseDialog}
        planName={selectedPlan}
      />
    </ProviderDashboardLayout>
  );
};

export default ProviderPlans;
