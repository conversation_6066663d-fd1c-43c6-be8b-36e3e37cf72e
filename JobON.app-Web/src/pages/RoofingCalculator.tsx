
import React, { useState, useEffect } from 'react';
import { Layout } from '@/components/Layout';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Slider } from '@/components/ui/slider';
import { ArrowLeft, HelpCircle, FileText, Calculator, Share2, Ruler, Home, Settings, DollarSign } from 'lucide-react';
import { Link } from 'react-router-dom';
import { Separator } from '@/components/ui/separator';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogDescription, DialogFooter, Di<PERSON>Header, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { toast } from "sonner";
import { ServicePageTemplate } from '@/components/ServicePageTemplate';
import {SEO} from "@/components/SEO.tsx";

const RoofingCalculator: React.FC = () => {
  // State for form inputs
  const [roofSize, setRoofSize] = useState<number>(1500);
  const [roofPitch, setRoofPitch] = useState<string>("medium");
  const [shingleType, setShingleType] = useState<string>("asphalt");
  const [complexity, setComplexity] = useState<string>("moderate");
  const [region, setRegion] = useState<string>("midwest");
  
  // Calculated values
  const [materialCost, setMaterialCost] = useState<number>(0);
  const [laborCost, setLaborCost] = useState<number>(0);
  const [totalCost, setTotalCost] = useState<number>(0);
  const [costPerSquare, setCostPerSquare] = useState<number>(0);

  // Material costs per square (100 sq ft) - Moved from hardcoded to state
  const [shingleCosts, setShingleCosts] = useState({
    asphalt: 80,
    metal: 300,
    tile: 350,
    slate: 600,
    wood: 450
  });

  // Labor multipliers based on pitch and complexity - Moved from hardcoded to state
  const [pitchMultipliers, setPitchMultipliers] = useState({
    low: 1.0,
    medium: 1.25,
    steep: 1.5
  });
  
  const [complexityMultipliers, setComplexityMultipliers] = useState({
    simple: 1.0,
    moderate: 1.2,
    complex: 1.5
  });
  
  // Region cost adjustments - Moved from hardcoded to state
  const [regionMultipliers, setRegionMultipliers] = useState({
    midwest: 1.0,
    northeast: 1.2,
    west: 1.3,
    south: 0.9
  });

  // Base labor cost per square - New state for price configuration
  const [baseLabor, setBaseLabor] = useState<number>(75);

  // Dialog state for price settings
  const [isPriceSettingsOpen, setIsPriceSettingsOpen] = useState<boolean>(false);

  // Calculate values when inputs change
  useEffect(() => {
    // Convert roofing size to squares (1 square = 100 sq ft)
    const roofSquares = roofSize / 100;
    
    // Calculate material cost
    const baseMaterialCost = roofSquares * shingleCosts[shingleType as keyof typeof shingleCosts];
    
    // Calculate labor cost with multipliers
    const calculatedLabor = roofSquares * baseLabor; // $75 per square base labor
    const adjustedLabor = calculatedLabor * 
      pitchMultipliers[roofPitch as keyof typeof pitchMultipliers] * 
      complexityMultipliers[complexity as keyof typeof complexityMultipliers] *
      regionMultipliers[region as keyof typeof regionMultipliers];
    
    // Set calculated values
    setMaterialCost(baseMaterialCost);
    setLaborCost(adjustedLabor);
    setTotalCost(baseMaterialCost + adjustedLabor);
    setCostPerSquare((baseMaterialCost + adjustedLabor) / roofSquares);

  }, [
    roofSize, 
    roofPitch, 
    shingleType, 
    complexity, 
    region, 
    shingleCosts, 
    pitchMultipliers, 
    complexityMultipliers, 
    regionMultipliers,
    baseLabor
  ]);

  // Handle saving price settings
  const handleSavePriceSettings = () => {
    setIsPriceSettingsOpen(false);
    toast.success("Price settings updated!", {
      description: "Your custom pricing has been applied to the calculator.",
    });
  };

  return (
    <Layout>
      <SEO
          title="Roofing Cost Calculator | Estimate Roof Replacement Costs Free"
          description="Estimate your roofing project costs quickly with JobON’s free Roofing Cost Calculator. Enter your roof size, material, and region to get an instant price estimate!"
          localBusinessSchema={true}
          serviceType="roofing calculator"
          serviceSlug="roofing-calculator"
          canonicalUrl="/free-tools/roofing-calculator"
      />
      <div className="pt-20 pb-20 px-6 md:px-12 bg-gradient-to-b from-blue-50 to-white dark:from-gray-900 dark:to-gray-800">
        <div className="container mx-auto max-w-6xl">
          {/* Header with back button */}
          <div className="mb-8">
            <Link to="/free-tools" className="inline-flex items-center text-primary hover:underline mb-4 text-base md:text-lg">
              <ArrowLeft className="mr-2 h-5 w-5 md:h-6 md:w-6" />
              Back to Free Tools
            </Link>
          
            {/* Free Tool Badge */}
            <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4 relative">
              <div className="relative">
                <div className="absolute -top-6 -left-2 bg-gradient-to-r from-primary to-blue-500 text-white px-3 py-1 rounded-full text-xs font-semibold shadow-md transform -rotate-3">
                  Free Tool
                </div>
                <h1 className="text-3xl md:text-5xl font-bold mt-2 mb-3 text-gray-800 dark:text-white">Roofing Cost Calculator</h1>
                <p className="text-foreground/70 text-lg md:text-xl max-w-3xl">
                  Estimate your roofing project costs based on size, materials, complexity, and location.
                </p>
              </div>
              
              {/* Price Settings Button */}
              <Dialog open={isPriceSettingsOpen} onOpenChange={setIsPriceSettingsOpen}>
                <DialogTrigger asChild>
                  <Button 
                    className="gap-2 bg-gradient-to-r from-primary to-blue-600 hover:from-primary/90 hover:to-blue-500 text-white shadow-md transition-all text-base md:text-lg"
                    size="lg"
                  >
                    <DollarSign className="h-5 w-5" />
                    Customize Pricing
                  </Button>
                </DialogTrigger>
                <DialogContent className="max-w-2xl">
                  <DialogHeader>
                    <DialogTitle className="flex items-center gap-2">
                      <DollarSign className="h-5 w-5 text-primary" />
                      Customize Pricing
                    </DialogTitle>
                    <DialogDescription>
                      Adjust the pricing factors used to calculate roofing costs.
                    </DialogDescription>
                  </DialogHeader>

                  <div className="grid gap-6 py-4">
                    {/* Material Costs */}
                    <div>
                      <h3 className="font-medium mb-3">Material Costs (per square - 100 sq ft)</h3>
                      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
                        {Object.entries(shingleCosts).map(([key, value]) => (
                          <div className="space-y-2" key={key}>
                            <Label htmlFor={`material-${key}`} className="capitalize">{key} ($ per square)</Label>
                            <Input 
                              id={`material-${key}`}
                              type="number"
                              value={value}
                              onChange={(e) => setShingleCosts(prev => ({
                                ...prev,
                                [key]: parseFloat(e.target.value)
                              }))}
                            />
                          </div>
                        ))}
                      </div>
                    </div>

                    <Separator />

                    {/* Labor Settings */}
                    <div>
                      <h3 className="font-medium mb-3">Labor Settings</h3>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                        <div className="space-y-2">
                          <Label htmlFor="baseLabor">Base Labor Cost ($ per square)</Label>
                          <Input 
                            id="baseLabor"
                            type="number"
                            value={baseLabor}
                            onChange={(e) => setBaseLabor(parseFloat(e.target.value))}
                          />
                        </div>
                      </div>

                      <h4 className="text-sm font-medium mb-2">Roof Pitch Multipliers</h4>
                      <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 mb-4">
                        {Object.entries(pitchMultipliers).map(([key, value]) => (
                          <div className="space-y-2" key={key}>
                            <Label htmlFor={`pitch-${key}`} className="capitalize">{key} Pitch</Label>
                            <Input 
                              id={`pitch-${key}`}
                              type="number"
                              step="0.1"
                              value={value}
                              onChange={(e) => setPitchMultipliers(prev => ({
                                ...prev,
                                [key]: parseFloat(e.target.value)
                              }))}
                            />
                          </div>
                        ))}
                      </div>

                      <h4 className="text-sm font-medium mb-2">Roof Complexity Multipliers</h4>
                      <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 mb-4">
                        {Object.entries(complexityMultipliers).map(([key, value]) => (
                          <div className="space-y-2" key={key}>
                            <Label htmlFor={`complexity-${key}`} className="capitalize">{key} Complexity</Label>
                            <Input 
                              id={`complexity-${key}`}
                              type="number"
                              step="0.1"
                              value={value}
                              onChange={(e) => setComplexityMultipliers(prev => ({
                                ...prev,
                                [key]: parseFloat(e.target.value)
                              }))}
                            />
                          </div>
                        ))}
                      </div>
                    </div>

                    <Separator />

                    {/* Region Multipliers */}
                    <div>
                      <h3 className="font-medium mb-3">Region Cost Multipliers</h3>
                      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                        {Object.entries(regionMultipliers).map(([key, value]) => (
                          <div className="space-y-2" key={key}>
                            <Label htmlFor={`region-${key}`} className="capitalize">{key}</Label>
                            <Input 
                              id={`region-${key}`}
                              type="number"
                              step="0.1"
                              value={value}
                              onChange={(e) => setRegionMultipliers(prev => ({
                                ...prev,
                                [key]: parseFloat(e.target.value)
                              }))}
                            />
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>

                  <DialogFooter>
                    <Button variant="outline" onClick={() => setIsPriceSettingsOpen(false)}>
                      Cancel
                    </Button>
                    <Button onClick={handleSavePriceSettings}>
                      Save Changes
                    </Button>
                  </DialogFooter>
                </DialogContent>
              </Dialog>
            </div>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-10">
            {/* Calculator Inputs */}
            <div className="lg:col-span-2">
              <Card className="border-2 border-gray-100 dark:border-gray-700 shadow-lg rounded-2xl overflow-hidden transition-all hover:shadow-xl">
                <CardHeader className="pb-4 bg-gradient-to-r from-gray-50 to-blue-50 dark:from-gray-800 dark:to-gray-700">
                  <CardTitle className="text-2xl md:text-3xl">Roof Details</CardTitle>
                  <CardDescription className="text-base md:text-lg">Enter your roof specifications to calculate costs</CardDescription>
                </CardHeader>
                <CardContent className="space-y-8 p-6 md:p-8">
                  {/* Roof Size */}
                  <div className="space-y-4">
                    <div className="flex justify-between items-center">
                      <Label htmlFor="roofSize" className="text-base md:text-xl flex items-center font-medium">
                        Roof Size (sq ft) 
                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <HelpCircle className="ml-2 h-4 w-4 md:h-5 md:w-5 text-muted-foreground" />
                            </TooltipTrigger>
                            <TooltipContent className="p-3 max-w-xs text-sm md:text-base">
                              <p>Total square footage of your roof surface</p>
                            </TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                      </Label>
                      <span className="text-right font-medium text-lg md:text-2xl">{roofSize} sq ft</span>
                    </div>
                    <div className="flex items-center gap-6">
                      <Slider 
                        min={500} 
                        max={5000} 
                        step={100} 
                        defaultValue={[1500]} 
                        value={[roofSize]}
                        onValueChange={(value) => setRoofSize(value[0])}
                        className="flex-1"
                      />
                      <Input 
                        type="number" 
                        id="roofSize" 
                        value={roofSize}
                        className="w-28 md:w-36 text-base md:text-lg" 
                        onChange={(e) => setRoofSize(Number(e.target.value))}
                      />
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                    {/* Roof Pitch */}
                    <div className="space-y-3">
                      <Label htmlFor="roofPitch" className="text-base md:text-xl flex items-center font-medium">
                        Roof Pitch
                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <HelpCircle className="ml-2 h-4 w-4 md:h-5 md:w-5 text-muted-foreground" />
                            </TooltipTrigger>
                            <TooltipContent className="p-3 max-w-xs text-sm md:text-base">
                              <p>Steepness of your roof affects labor costs</p>
                            </TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                      </Label>
                      <Select value={roofPitch} onValueChange={setRoofPitch}>
                        <SelectTrigger className="w-full text-base md:text-lg h-12">
                          <SelectValue placeholder="Select roof pitch" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="low" className="text-base md:text-lg">Low (1/12 to 3/12)</SelectItem>
                          <SelectItem value="medium" className="text-base md:text-lg">Medium (4/12 to 8/12)</SelectItem>
                          <SelectItem value="steep" className="text-base md:text-lg">Steep (9/12 or greater)</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    
                    {/* Shingle Type */}
                    <div className="space-y-3">
                      <Label htmlFor="shingleType" className="text-base md:text-xl flex items-center font-medium">
                        Roofing Material
                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <HelpCircle className="ml-2 h-4 w-4 md:h-5 md:w-5 text-muted-foreground" />
                            </TooltipTrigger>
                            <TooltipContent className="p-3 max-w-xs text-sm md:text-base">
                              <p>Different materials have different costs</p>
                            </TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                      </Label>
                      <Select value={shingleType} onValueChange={setShingleType}>
                        <SelectTrigger className="w-full text-base md:text-lg h-12">
                          <SelectValue placeholder="Select roofing material" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="asphalt" className="text-base md:text-lg">Asphalt Shingles</SelectItem>
                          <SelectItem value="metal" className="text-base md:text-lg">Metal Roofing</SelectItem>
                          <SelectItem value="tile" className="text-base md:text-lg">Tile Roofing</SelectItem>
                          <SelectItem value="slate" className="text-base md:text-lg">Slate Roofing</SelectItem>
                          <SelectItem value="wood" className="text-base md:text-lg">Wood Shakes</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                    {/* Complexity */}
                    <div className="space-y-3">
                      <Label htmlFor="complexity" className="text-base md:text-xl flex items-center font-medium">
                        Roof Complexity
                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <HelpCircle className="ml-2 h-4 w-4 md:h-5 md:w-5 text-muted-foreground" />
                            </TooltipTrigger>
                            <TooltipContent className="p-3 max-w-xs text-sm md:text-base">
                              <p>More complex roofs with dormers, valleys, and angles cost more</p>
                            </TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                      </Label>
                      <Select value={complexity} onValueChange={setComplexity}>
                        <SelectTrigger className="w-full text-base md:text-lg h-12">
                          <SelectValue placeholder="Select complexity" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="simple" className="text-base md:text-lg">Simple (Basic gable)</SelectItem>
                          <SelectItem value="moderate" className="text-base md:text-lg">Moderate (Hip roof, few valleys)</SelectItem>
                          <SelectItem value="complex" className="text-base md:text-lg">Complex (Multiple levels, dormers, valleys)</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    
                    {/* Region */}
                    <div className="space-y-3">
                      <Label htmlFor="region" className="text-base md:text-xl flex items-center font-medium">
                        Your Region
                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <HelpCircle className="ml-2 h-4 w-4 md:h-5 md:w-5 text-muted-foreground" />
                            </TooltipTrigger>
                            <TooltipContent className="p-3 max-w-xs text-sm md:text-base">
                              <p>Labor and material costs vary by region</p>
                            </TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                      </Label>
                      <Select value={region} onValueChange={setRegion}>
                        <SelectTrigger className="w-full text-base md:text-lg h-12">
                          <SelectValue placeholder="Select your region" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="midwest" className="text-base md:text-lg">Midwest</SelectItem>
                          <SelectItem value="northeast" className="text-base md:text-lg">Northeast</SelectItem>
                          <SelectItem value="west" className="text-base md:text-lg">West Coast</SelectItem>
                          <SelectItem value="south" className="text-base md:text-lg">South</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Results Card */}
            <div>
              <Card className="border-2 border-primary/10 sticky top-28 shadow-lg bg-white dark:bg-gray-800 rounded-2xl overflow-hidden animate-fade-in">
                <CardHeader className="pb-2 bg-gradient-to-r from-primary/5 to-blue-500/5">
                  <CardTitle className="text-xl md:text-2xl">Cost Estimate</CardTitle>
                  <CardDescription className="text-base">Based on your specifications</CardDescription>
                </CardHeader>
                <CardContent className="space-y-6 p-6">
                  {/* Total Cost */}
                  <div className="text-center py-8 bg-gradient-to-r from-primary/5 to-blue-500/5 rounded-xl border border-primary/10 transform hover:scale-105 transition-transform duration-300">
                    <div className="text-5xl md:text-6xl font-bold text-primary mb-1 animate-scale-in">
                      ${totalCost.toLocaleString(undefined, {minimumFractionDigits: 0, maximumFractionDigits: 0})}
                    </div>
                    <p className="text-sm md:text-base text-muted-foreground">Total Roofing Cost</p>
                  </div>
                  
                  <Separator className="my-2" />
                  
                  <div className="space-y-3 text-base md:text-lg">
                    <div className="flex justify-between py-1">
                      <span className="text-muted-foreground">Material Cost:</span>
                      <span className="font-medium">${materialCost.toLocaleString(undefined, {minimumFractionDigits: 0, maximumFractionDigits: 0})}</span>
                    </div>
                    <div className="flex justify-between py-1">
                      <span className="text-muted-foreground">Labor Cost:</span>
                      <span className="font-medium">${laborCost.toLocaleString(undefined, {minimumFractionDigits: 0, maximumFractionDigits: 0})}</span>
                    </div>
                    <div className="flex justify-between py-1">
                      <span className="text-muted-foreground">Cost Per Square <span className="text-xs md:text-sm">(100 sq ft)</span>:</span>
                      <span className="font-medium">${costPerSquare.toLocaleString(undefined, {minimumFractionDigits: 0, maximumFractionDigits: 0})}</span>
                    </div>
                  </div>
                </CardContent>
                <CardFooter className="flex-col space-y-3 pt-2 p-6">
                  <Button className="w-full text-base md:text-lg py-6" variant="default">
                    <FileText className="mr-2 h-5 w-5" />
                    Save Estimate
                  </Button>
                  <Button className="w-full text-base md:text-lg py-6" variant="outline">
                    <Share2 className="mr-2 h-5 w-5" />
                    Share Results
                  </Button>
                </CardFooter>
              </Card>
            </div>
          </div>
          
          {/* Tips Section */}
          <div className="mt-16">
            <h2 className="text-2xl md:text-3xl font-semibold mb-8">Roofing Cost Considerations</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              <Card className="shadow-sm hover:shadow-md transition-shadow border-2 border-gray-100 dark:border-gray-700 rounded-xl hover:border-primary/10">
                <CardHeader className="pb-2">
                  <CardTitle className="text-lg md:text-xl flex items-center">
                    <span className="p-2 mr-3 rounded-full bg-gradient-to-r from-primary/10 to-blue-500/10">
                      <Home className="h-5 w-5 md:h-6 md:w-6 text-primary" />
                    </span>
                    Material Selection
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-foreground/80 leading-relaxed text-base md:text-lg">
                    While asphalt shingles are the most budget-friendly option, premium materials like slate or metal offer greater longevity and durability, potentially saving money in the long term.
                  </p>
                </CardContent>
              </Card>
              <Card className="shadow-sm hover:shadow-md transition-shadow border-2 border-gray-100 dark:border-gray-700 rounded-xl hover:border-primary/10">
                <CardHeader className="pb-2">
                  <CardTitle className="text-lg md:text-xl flex items-center">
                    <span className="p-2 mr-3 rounded-full bg-gradient-to-r from-primary/10 to-blue-500/10">
                      <Calculator className="h-5 w-5 md:h-6 md:w-6 text-primary" />
                    </span>
                    Timing Your Project
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-foreground/80 leading-relaxed text-base md:text-lg">
                    Roofers are typically less busy during fall and winter months in many regions. Scheduling your project during these off-peak seasons can sometimes result in better rates.
                  </p>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default RoofingCalculator;
