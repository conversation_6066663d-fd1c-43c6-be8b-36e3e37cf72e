import React, { useState } from 'react';
import { Layout } from '@/components/Layout';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { Button } from "@/components/ui/button";
import { Link } from 'react-router-dom';
import { Search, Mail, ArrowRight } from 'lucide-react';
import {SEO} from "@/components/SEO.tsx";

const FAQ = () => {
  const [searchQuery, setSearchQuery] = useState('');

  // Customer FAQs
  const customerFaqs = [
    {
      question: "How do I find the right service provider?",
      answer: "You can search for service providers based on your specific needs, location, and budget. Each provider has ratings and reviews from previous customers to help you make an informed decision."
    },
    {
      question: "What if I'm not satisfied with the service?",
      answer: "Your satisfaction is our priority. If you're not happy with the service provided, please contact our customer support team within 48 hours, and we'll work to resolve the issue. We offer our Peace of Mind Guarantee on all services booked through our platform."
    },
    {
      question: "How does pricing work?",
      answer: "Service providers set their own rates, which are clearly displayed before you book. You can compare quotes from multiple providers to find the best option for your budget. There are no hidden fees, and you only pay for the services you book."
    },
    {
      question: "Can I reschedule or cancel a service?",
      answer: "Yes, you can reschedule or cancel a service through your account dashboard. Please note that cancellation policies vary by provider and are clearly stated at the time of booking. In most cases, cancellations made at least 24 hours in advance are eligible for a full refund."
    },
    {
      question: "Is there a fee to post a project?",
      answer: "No, posting a project on JobON is completely free. We only charge a small service fee when you hire a provider through our platform."
    },
    {
      question: "How does the buy now, pay later financing work?",
      answer: "Our flexible financing options allow you to spread the cost of your project over time. We offer low interest rates for qualified customers and various term options to fit your budget. You can apply directly through our platform when booking your service."
    },
    {
      question: "Are service providers background-checked?",
      answer: "Yes, all service providers on JobON undergo a thorough verification process, including identity verification, credential verification, and background checks where applicable. We also continuously monitor reviews to ensure quality service."
    },
    {
      question: "What happens if a provider damages my property?",
      answer: "All service providers on JobON are required to have insurance coverage. If any damage occurs during service, please document it with photos and contact our customer support team immediately. We'll work with you and the provider to resolve the issue promptly."
    },
  ];

  // Provider FAQs
  const providerFaqs = [
    {
      question: "How do I join JobON as a service provider?",
      answer: "To join as a service provider, create a free Starter account, complete your profile with your qualifications and services offered, and undergo our verification process. You can upgrade to Pro or Elite plans to access more features."
    },
    {
      question: "What fees does JobON charge providers?",
      answer: "Our commission structure varies by plan: Starter has a 20% commission, Pro has a 15% commission at $199/month, and Elite has a 12% commission at $399/month."
    },
    {
      question: "How do I get more jobs and improve my visibility?",
      answer: "Maintain a high rating by providing excellent service. Pro and Elite plans offer priority bidding, top job placement, and more tools to help you stand out. Upgrade your plan to increase your chances of getting more jobs."
    },
    {
      question: "When and how do I get paid?",
      answer: "Payout speed depends on your plan: Starter accounts receive funds in 14 business days, Pro in 5 days, and Elite the next business day."
    },
    {
      question: "What is the Scheduling Assistant?",
      answer: "Available on Pro and Elite plans, the Scheduling Assistant helps you manage appointments, send automated reminders, and reduce no-shows."
    },
    {
      question: "Can I offer multiple services?",
      answer: "Yes, you can offer multiple services under your profile as long as you have the appropriate qualifications and experience. Pro and Elite plans provide more tools to showcase your diverse skills."
    },
    {
      question: "How do bids work?",
      answer: "Each plan offers different bidding opportunities. Starter has basic bids, Pro offers priority bidding, and Elite provides top placement and unlimited bids."
    },
    {
      question: "Can I upgrade or downgrade my plan?",
      answer: "Yes, you can upgrade or downgrade your plan at any time. Changes will take effect at the start of your next billing cycle."
    },
  ];

  // Combined FAQs for search
  const allFaqs = [...customerFaqs, ...providerFaqs];

  // Filter FAQs based on search query
  const filteredCustomerFaqs = customerFaqs.filter(faq => 
    faq.question.toLowerCase().includes(searchQuery.toLowerCase()) || 
    faq.answer.toLowerCase().includes(searchQuery.toLowerCase())
  );
  
  const filteredProviderFaqs = providerFaqs.filter(faq => 
    faq.question.toLowerCase().includes(searchQuery.toLowerCase()) || 
    faq.answer.toLowerCase().includes(searchQuery.toLowerCase())
  );

  return (
    <Layout>
      <SEO
          title="Frequently Asked Questions | JobON Support for Customers & Providers"
          description="Find answers to common questions about using JobON. Learn how posting works, pricing, provider vetting, financing options, and more."
          localBusinessSchema={true}
          serviceType="faq"
          serviceSlug="faq"
          canonicalUrl="/faq"
      />
      <div className="relative py-20 bg-gray-50 dark:bg-gray-900/50">
        {/* Background elements */}
        <div className="absolute inset-0 bg-gradient-to-br from-primary/5 via-white to-primary/5 dark:from-primary/10 dark:via-background dark:to-primary/5 z-0"></div>
        
        <div className="container mx-auto px-6 md:px-12 relative z-10">
          {/* Header section */}
          <div className="max-w-3xl mx-auto text-center mb-16">
            <h1 className="text-4xl md:text-5xl font-bold text-foreground mb-6 tracking-tight">
              Frequently Asked Questions
            </h1>
            <p className="text-xl text-foreground/70 max-w-2xl mx-auto">
              Find answers to common questions about using JobON for your home, business, and professional service needs.
            </p>
          </div>

          {/* Search bar */}
          <div className="max-w-2xl mx-auto mb-12">
            <div className="relative">
              <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-muted-foreground" />
              <input
                type="text"
                placeholder="Search for questions..."
                className="w-full py-3 pl-12 pr-4 rounded-full border border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-primary"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
          </div>

          {/* Tabs for Customer and Provider FAQs */}
          <div className="max-w-4xl mx-auto">
            <Tabs defaultValue="customer" className="w-full">
              <div className="flex justify-center mb-8">
                <TabsList className="grid w-full max-w-md grid-cols-2">
                  <TabsTrigger value="customer">For Customers</TabsTrigger>
                  <TabsTrigger value="provider">For Providers</TabsTrigger>
                </TabsList>
              </div>

              {/* Customer FAQs */}
              <TabsContent value="customer">
                <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-100 dark:border-gray-700 p-6">
                  <Accordion type="single" collapsible className="w-full">
                    {filteredCustomerFaqs.length > 0 ? (
                      filteredCustomerFaqs.map((faq, index) => (
                        <AccordionItem key={index} value={`customer-${index}`}>
                          <AccordionTrigger className="text-lg font-medium py-4">
                            {faq.question}
                          </AccordionTrigger>
                          <AccordionContent className="text-foreground/80 text-base leading-relaxed">
                            {faq.answer}
                          </AccordionContent>
                        </AccordionItem>
                      ))
                    ) : (
                      <div className="text-center py-8">
                        <p className="text-foreground/70">No matching questions found.</p>
                        <p className="mt-2">Try another search term or browse all questions by clearing the search.</p>
                      </div>
                    )}
                  </Accordion>
                </div>
              </TabsContent>

              {/* Provider FAQs */}
              <TabsContent value="provider">
                <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-100 dark:border-gray-700 p-6">
                  <Accordion type="single" collapsible className="w-full">
                    {filteredProviderFaqs.length > 0 ? (
                      filteredProviderFaqs.map((faq, index) => (
                        <AccordionItem key={index} value={`provider-${index}`}>
                          <AccordionTrigger className="text-lg font-medium py-4">
                            {faq.question}
                          </AccordionTrigger>
                          <AccordionContent className="text-foreground/80 text-base leading-relaxed">
                            {faq.answer}
                          </AccordionContent>
                        </AccordionItem>
                      ))
                    ) : (
                      <div className="text-center py-8">
                        <p className="text-foreground/70">No matching questions found.</p>
                        <p className="mt-2">Try another search term or browse all questions by clearing the search.</p>
                      </div>
                    )}
                  </Accordion>
                </div>
              </TabsContent>
            </Tabs>
          </div>

          {/* Contact section */}
          <div className="max-w-3xl mx-auto mt-16 text-center">
            <h2 className="text-2xl font-semibold mb-4">Still have questions?</h2>
            <p className="text-foreground/70 mb-6">
              If you couldn't find the answer you're looking for, our support team is here to help.
            </p>
            <div className="flex flex-col sm:flex-row justify-center gap-4">
              <Button asChild size="lg" className="gap-2">
                <Link to="/contact">
                  <Mail className="h-5 w-5" />
                  Contact Support
                </Link>
              </Button>
              <Button asChild variant="outline" size="lg" className="gap-2">
                <Link to="/how-it-works">
                  Learn How It Works
                  <ArrowRight className="h-5 w-5" />
                </Link>
              </Button>
            </div>
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default FAQ;
