
import React, { useState } from 'react';
import { ProviderDashboardLayout } from '@/components/provider/ProviderDashboardLayout';
import { PerformanceMetricCard } from '@/components/provider/performance/PerformanceMetricCard';
import { MobileMetricCard } from '@/components/provider/dashboard/MobileMetricCard';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { TrendingUp, Briefcase, Calendar, DollarSign, Star, Award, ChevronDown } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { badgeConfigs } from '@/components/provider/badges/ProviderBadgeCard';
import { useIsMobile } from '@/hooks/use-mobile';

const ProviderPerformance = () => {
  const isMobile = useIsMobile();
  const [activeSection, setActiveSection] = useState<string | null>(null);
  
  const toggleSection = (section: string) => {
    setActiveSection(activeSection === section ? null : section);
  };
  
  // In a real app, this data would come from an API
  
  return (
    <ProviderDashboardLayout pageTitle="Performance">
      <div className="space-y-6">
        {/* Overview Header */}
        <Card className={`${isMobile ? 'bg-gradient-to-r from-blue-500 to-indigo-600 text-white border-0' : 'bg-gradient-to-r from-indigo-50 to-blue-50 border-0'}`}>
          <CardHeader>
            <div className="flex items-start justify-between">
              <div>
                <CardTitle className="text-xl md:text-2xl">Performance Dashboard</CardTitle>
                <CardDescription className={`${isMobile ? 'text-blue-100' : 'text-base'} mt-2`}>
                  Track your metrics, monitor growth, identify opportunities
                </CardDescription>
              </div>
              <div className={`h-14 w-14 rounded-full ${isMobile ? 'bg-white/20' : 'bg-indigo-100'} flex items-center justify-center`}>
                <TrendingUp className={`h-8 w-8 ${isMobile ? 'text-white' : 'text-indigo-600'}`} />
              </div>
            </div>
          </CardHeader>
        </Card>
      
        {/* Performance Metrics */}
        <div>
          {isMobile ? (
            <div className="space-y-6">
              <div className="flex justify-between items-center">
                <h2 className="text-lg font-semibold">Key Performance Metrics</h2>
                <Tabs defaultValue="month" className="w-[180px]">
                  <TabsList className="grid grid-cols-3 h-8">
                    <TabsTrigger value="week" className="text-xs">Week</TabsTrigger>
                    <TabsTrigger value="month" className="text-xs">Month</TabsTrigger>
                    <TabsTrigger value="year" className="text-xs">Year</TabsTrigger>
                  </TabsList>
                </Tabs>
              </div>
              
              {/* Fixing the structure to ensure TabsContent is within Tabs */}
              <Tabs defaultValue="month">
                <TabsContent value="week" className="m-0">
                  <div className="grid grid-cols-2 gap-3">
                    <MobileMetricCard
                      title="Leads"
                      value="12"
                      icon={<Calendar className="h-4 w-4" />}
                      color="blue"
                    />
                    <MobileMetricCard
                      title="Bids"
                      value="9"
                      icon={<Briefcase className="h-4 w-4" />}
                      color="purple"
                    />
                    <MobileMetricCard
                      title="Jobs Won"
                      value="4"
                      icon={<Award className="h-4 w-4" />}
                      color="green"
                    />
                    <MobileMetricCard
                      title="Completed"
                      value="3"
                      icon={<Briefcase className="h-4 w-4" />}
                      color="amber"
                    />
                    <MobileMetricCard
                      title="Rating"
                      value="4.9"
                      icon={<Star className="h-4 w-4" />}
                      color="amber"
                    />
                    <MobileMetricCard
                      title="Revenue"
                      value="$1,250"
                      icon={<DollarSign className="h-4 w-4" />}
                      color="green"
                    />
                  </div>
                </TabsContent>
                
                <TabsContent value="month" className="m-0">
                  <div className="grid grid-cols-2 gap-3">
                    <MobileMetricCard
                      title="Leads"
                      value="42"
                      icon={<Calendar className="h-4 w-4" />}
                      color="blue"
                    />
                    <MobileMetricCard
                      title="Bids"
                      value="36"
                      icon={<Briefcase className="h-4 w-4" />}
                      color="purple"
                    />
                    <MobileMetricCard
                      title="Jobs Won"
                      value="18"
                      icon={<Award className="h-4 w-4" />}
                      color="green"
                    />
                    <MobileMetricCard
                      title="Completed"
                      value="15"
                      icon={<Briefcase className="h-4 w-4" />}
                      color="amber"
                    />
                    <MobileMetricCard
                      title="Rating"
                      value="4.8"
                      icon={<Star className="h-4 w-4" />}
                      color="amber"
                    />
                    <MobileMetricCard
                      title="Revenue"
                      value="$4,850"
                      icon={<DollarSign className="h-4 w-4" />}
                      color="green"
                    />
                  </div>
                </TabsContent>
                
                <TabsContent value="year" className="m-0">
                  <div className="grid grid-cols-2 gap-3">
                    <MobileMetricCard
                      title="Leads"
                      value="156"
                      icon={<Calendar className="h-4 w-4" />}
                      color="blue"
                    />
                    <MobileMetricCard
                      title="Bids"
                      value="134"
                      icon={<Briefcase className="h-4 w-4" />}
                      color="purple"
                    />
                    <MobileMetricCard
                      title="Jobs Won"
                      value="72"
                      icon={<Award className="h-4 w-4" />}
                      color="green"
                    />
                    <MobileMetricCard
                      title="Completed"
                      value="65"
                      icon={<Briefcase className="h-4 w-4" />}
                      color="amber"
                    />
                    <MobileMetricCard
                      title="Rating"
                      value="4.85"
                      icon={<Star className="h-4 w-4" />}
                      color="amber"
                    />
                    <MobileMetricCard
                      title="Revenue"
                      value="$23,450"
                      icon={<DollarSign className="h-4 w-4" />}
                      color="green"
                    />
                  </div>
                </TabsContent>
              </Tabs>
            </div>
          ) : (
            <Tabs defaultValue="month" className="w-full">
              <div className="flex justify-between items-center mb-4">
                <h2 className="text-xl font-semibold">Key Performance Metrics</h2>
                <TabsList>
                  <TabsTrigger value="week">This Week</TabsTrigger>
                  <TabsTrigger value="month">This Month</TabsTrigger>
                  <TabsTrigger value="year">This Year</TabsTrigger>
                </TabsList>
              </div>
              
              <TabsContent value="week" className="m-0">
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  <PerformanceMetricCard
                    title="Leads Received"
                    value="12"
                    icon={<Calendar className="h-4 w-4" />}
                    trend={{ value: 20, label: "vs last week" }}
                    color="blue"
                  />
                  <PerformanceMetricCard
                    title="Bids Submitted"
                    value="9"
                    icon={<Briefcase className="h-4 w-4" />}
                    trend={{ value: 12.5, label: "vs last week" }}
                    color="purple"
                    progress={{ value: 75, label: "Bid Rate" }}
                  />
                  <PerformanceMetricCard
                    title="Jobs Won"
                    value="4"
                    icon={<Award className="h-4 w-4" />}
                    trend={{ value: 33, label: "vs last week" }}
                    color="green"
                    progress={{ value: 44, label: "Win Rate" }}
                  />
                  <PerformanceMetricCard
                    title="Jobs Completed"
                    value="3"
                    icon={<Briefcase className="h-4 w-4" />}
                    trend={{ value: 0, label: "vs last week" }}
                    color="amber"
                  />
                  <PerformanceMetricCard
                    title="Average Rating"
                    value="4.9"
                    icon={<Star className="h-4 w-4" />}
                    description="Based on 5 reviews"
                    color="amber"
                  />
                  <PerformanceMetricCard
                    title="Revenue"
                    value="$1,250"
                    icon={<DollarSign className="h-4 w-4" />}
                    trend={{ value: 15, label: "vs last week" }}
                    color="green"
                  />
                </div>
              </TabsContent>
              
              <TabsContent value="month" className="m-0">
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  <PerformanceMetricCard
                    title="Leads Received"
                    value="42"
                    icon={<Calendar className="h-4 w-4" />}
                    trend={{ value: 15, label: "vs last month" }}
                    color="blue"
                  />
                  <PerformanceMetricCard
                    title="Bids Submitted"
                    value="36"
                    icon={<Briefcase className="h-4 w-4" />}
                    trend={{ value: 8, label: "vs last month" }}
                    color="purple"
                    progress={{ value: 86, label: "Bid Rate" }}
                  />
                  <PerformanceMetricCard
                    title="Jobs Won"
                    value="18"
                    icon={<Award className="h-4 w-4" />}
                    trend={{ value: 20, label: "vs last month" }}
                    color="green"
                    progress={{ value: 50, label: "Win Rate" }}
                  />
                  <PerformanceMetricCard
                    title="Jobs Completed"
                    value="15"
                    icon={<Briefcase className="h-4 w-4" />}
                    trend={{ value: 25, label: "vs last month" }}
                    color="amber"
                  />
                  <PerformanceMetricCard
                    title="Average Rating"
                    value="4.8"
                    icon={<Star className="h-4 w-4" />}
                    description="Based on 28 reviews"
                    color="amber"
                  />
                  <PerformanceMetricCard
                    title="Revenue"
                    value="$4,850"
                    icon={<DollarSign className="h-4 w-4" />}
                    trend={{ value: 18, label: "vs last month" }}
                    color="green"
                  />
                </div>
              </TabsContent>
              
              <TabsContent value="year" className="m-0">
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  <PerformanceMetricCard
                    title="Leads Received"
                    value="156"
                    icon={<Calendar className="h-4 w-4" />}
                    trend={{ value: 45, label: "vs last year" }}
                    color="blue"
                  />
                  <PerformanceMetricCard
                    title="Bids Submitted"
                    value="134"
                    icon={<Briefcase className="h-4 w-4" />}
                    trend={{ value: 32, label: "vs last year" }}
                    color="purple"
                    progress={{ value: 86, label: "Bid Rate" }}
                  />
                  <PerformanceMetricCard
                    title="Jobs Won"
                    value="72"
                    icon={<Award className="h-4 w-4" />}
                    trend={{ value: 28, label: "vs last year" }}
                    color="green"
                    progress={{ value: 54, label: "Win Rate" }}
                  />
                  <PerformanceMetricCard
                    title="Jobs Completed"
                    value="65"
                    icon={<Briefcase className="h-4 w-4" />}
                    trend={{ value: 30, label: "vs last year" }}
                    color="amber"
                  />
                  <PerformanceMetricCard
                    title="Average Rating"
                    value="4.85"
                    icon={<Star className="h-4 w-4" />}
                    description="Based on 118 reviews"
                    color="amber"
                  />
                  <PerformanceMetricCard
                    title="Revenue"
                    value="$23,450"
                    icon={<DollarSign className="h-4 w-4" />}
                    trend={{ value: 38, label: "vs last year" }}
                    color="green"
                  />
                </div>
              </TabsContent>
            </Tabs>
          )}
        </div>
        
        {/* Recent Achievements - Mobile Specific */}
        {isMobile ? (
          <div className="space-y-4">
            <div className="flex justify-between items-center" onClick={() => toggleSection('achievements')}>
              <h2 className="text-lg font-semibold">Recent Achievements</h2>
              <ChevronDown className={`h-5 w-5 transition-transform ${activeSection === 'achievements' ? 'rotate-180' : ''}`} />
            </div>
            
            {activeSection === 'achievements' && (
              <div className="space-y-3 animate-fade-in">
                <div className="p-3 rounded-lg bg-gradient-to-r from-green-50 to-green-100 border border-green-200">
                  <div className="flex items-center gap-3">
                    <div className="h-10 w-10 rounded-full bg-green-100 flex items-center justify-center">
                      <Award className="h-5 w-5 text-green-600" />
                    </div>
                    <div>
                      <h3 className="font-medium text-sm">Badge: Fast Responder</h3>
                      <p className="text-xs text-gray-600 line-clamp-2">
                        {badgeConfigs.fast_responder.criteria}
                      </p>
                    </div>
                  </div>
                  <div className="mt-2 flex justify-end">
                    <Badge className="bg-green-100 text-green-800 text-xs">April 10, 2025</Badge>
                  </div>
                </div>
                
                <div className="p-3 rounded-lg bg-gradient-to-r from-blue-50 to-blue-100 border border-blue-200">
                  <div className="flex items-center gap-3">
                    <div className="h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center">
                      <Star className="h-5 w-5 text-blue-600" />
                    </div>
                    <div>
                      <h3 className="font-medium text-sm">Milestone: 50 5-star Reviews</h3>
                      <p className="text-xs text-gray-600 line-clamp-2">
                        You've received 50 perfect ratings from customers!
                      </p>
                    </div>
                  </div>
                  <div className="mt-2 flex justify-end">
                    <Badge className="bg-blue-100 text-blue-800 text-xs">April 5, 2025</Badge>
                  </div>
                </div>
                
                <div className="p-3 rounded-lg bg-gradient-to-r from-purple-50 to-purple-100 border border-purple-200">
                  <div className="flex items-center gap-3">
                    <div className="h-10 w-10 rounded-full bg-purple-100 flex items-center justify-center">
                      <DollarSign className="h-5 w-5 text-purple-600" />
                    </div>
                    <div>
                      <h3 className="font-medium text-sm">Milestone: $10,000 in Earnings</h3>
                      <p className="text-xs text-gray-600 line-clamp-2">
                        You've reached $10,000 in total earnings on JobOn!
                      </p>
                    </div>
                  </div>
                  <div className="mt-2 flex justify-end">
                    <Badge className="bg-purple-100 text-purple-800 text-xs">March 28, 2025</Badge>
                  </div>
                </div>
              </div>
            )}
          </div>
        ) : (
          <div className="space-y-4">
            <h2 className="text-xl font-semibold">Recent Achievements</h2>
            <Card>
              <CardContent className="p-6 space-y-4">
                <div className="flex flex-col md:flex-row md:items-center md:justify-between p-4 bg-green-50 rounded-lg">
                  <div className="flex items-center mb-4 md:mb-0">
                    <div className="h-10 w-10 rounded-full bg-green-100 flex items-center justify-center mr-3">
                      <Award className="h-5 w-5 text-green-600" />
                    </div>
                    <div>
                      <h3 className="font-semibold">Badge Earned: Fast Responder</h3>
                      <p className="text-sm text-muted-foreground">
                        {badgeConfigs.fast_responder.criteria}
                      </p>
                    </div>
                  </div>
                  <Badge className="bg-green-100 text-green-800 hover:bg-green-200 self-start md:self-center">April 10, 2025</Badge>
                </div>
                
                <div className="flex flex-col md:flex-row md:items-center md:justify-between p-4 bg-blue-50 rounded-lg">
                  <div className="flex items-center mb-4 md:mb-0">
                    <div className="h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center mr-3">
                      <Star className="h-5 w-5 text-blue-600" />
                    </div>
                    <div>
                      <h3 className="font-semibold">Milestone: 50 5-star Reviews</h3>
                      <p className="text-sm text-muted-foreground">
                        You've received 50 perfect ratings from customers!
                      </p>
                    </div>
                  </div>
                  <Badge className="bg-blue-100 text-blue-800 hover:bg-blue-200 self-start md:self-center">April 5, 2025</Badge>
                </div>
                
                <div className="flex flex-col md:flex-row md:items-center md:justify-between p-4 bg-purple-50 rounded-lg">
                  <div className="flex items-center mb-4 md:mb-0">
                    <div className="h-10 w-10 rounded-full bg-purple-100 flex items-center justify-center mr-3">
                      <DollarSign className="h-5 w-5 text-purple-600" />
                    </div>
                    <div>
                      <h3 className="font-semibold">Milestone: $10,000 in Earnings</h3>
                      <p className="text-sm text-muted-foreground">
                        You've reached $10,000 in total earnings on JobOn!
                      </p>
                    </div>
                  </div>
                  <Badge className="bg-purple-100 text-purple-800 hover:bg-purple-200 self-start md:self-center">March 28, 2025</Badge>
                </div>
              </CardContent>
            </Card>
          </div>
        )}
      </div>
    </ProviderDashboardLayout>
  );
};

export default ProviderPerformance;
