
import React from 'react';
import { ProviderDashboardLayout } from '@/components/provider/ProviderDashboardLayout';
import { ProviderBadgeCard, ProviderBadge } from '@/components/provider/badges/ProviderBadgeCard';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Award } from 'lucide-react';

const ProviderBadges = () => {
  // Sample badge data - in a real app, this would come from an API
  const badges: ProviderBadge[] = [
    {
      type: 'fast_responder',
      earned: true,
      earnedDate: 'March 15, 2025'
    },
    {
      type: 'top_rated',
      earned: true,
      earnedDate: 'April 2, 2025'
    },
    {
      type: 'consistent_closer',
      earned: false,
      progress: 82
    },
    {
      type: 'high_earner',
      earned: false,
      progress: 45
    },
    {
      type: 'customer_favorite',
      earned: false,
      progress: 30
    },
    {
      type: 'completion_pro',
      earned: true,
      earnedDate: 'March 22, 2025'
    }
  ];

  // Separate earned and in-progress badges
  const earnedBadges = badges.filter(badge => badge.earned);
  const inProgressBadges = badges.filter(badge => !badge.earned);

  return (
    <ProviderDashboardLayout pageTitle="Badges & Rewards">
      <div className="space-y-6">
        {/* Introduction Card */}
        <Card className="bg-gradient-to-r from-blue-50 to-purple-50 border-0">
          <CardHeader>
            <div className="flex items-start justify-between">
              <div>
                <CardTitle className="text-xl md:text-2xl">Provider Badges</CardTitle>
                <CardDescription className="text-base mt-2">
                  Badges recognize your achievements and excellence as a service provider on JobOn. 
                  Earn badges to build customer trust and stand out from other providers.
                </CardDescription>
              </div>
              <div className="h-14 w-14 rounded-full bg-indigo-100 flex items-center justify-center">
                <Award className="h-8 w-8 text-indigo-600" />
              </div>
            </div>
          </CardHeader>
        </Card>

        {/* Earned Badges Section */}
        <div className="space-y-4">
          <h2 className="text-xl font-semibold">Your Earned Badges</h2>
          {earnedBadges.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {earnedBadges.map((badge, index) => (
                <ProviderBadgeCard key={index} badge={badge} />
              ))}
            </div>
          ) : (
            <Card>
              <CardContent className="p-8 text-center">
                <p className="text-muted-foreground">You haven't earned any badges yet. Complete jobs and provide excellent service to earn badges!</p>
              </CardContent>
            </Card>
          )}
        </div>

        {/* Available Badges Section */}
        <div className="space-y-4">
          <h2 className="text-xl font-semibold">Badges In Progress</h2>
          {inProgressBadges.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {inProgressBadges.map((badge, index) => (
                <ProviderBadgeCard key={index} badge={badge} />
              ))}
            </div>
          ) : (
            <Card>
              <CardContent className="p-8 text-center">
                <p className="text-muted-foreground">You've earned all available badges. Great job!</p>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </ProviderDashboardLayout>
  );
};

export default ProviderBadges;
