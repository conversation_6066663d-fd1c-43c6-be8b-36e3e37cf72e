
import React from 'react';
import { Layout } from '@/components/Layout';
import { <PERSON><PERSON><PERSON>, ClipboardCheck, MessageSquare, CreditCard, ShieldCheck, Award, CheckCircle, Star, Clock, Heart, Users, Home, Wrench, Building } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Link } from 'react-router-dom';
import { Badge } from "@/components/ui/badge";
import { <PERSON> } from '@/components/Hero';
import {SEO} from "@/components/SEO.tsx";

const HowItWorks = () => {
  return (
    <Layout>
      <SEO
          title="How JobON Works – Post Projects, Compare Bids & Hire Fast"
          description="See how Job<PERSON> helps you hire trusted local pros in 3 easy steps. Post a job, get competitive bids, and complete your home or business project fast."
          localBusinessSchema={true}
          serviceType="How JobON Works"
          serviceSlug="how-jobon-works"
          canonicalUrl="/how-it-works"
      />
      {/* Custom Hero Section */}
      <Hero 
        hideButtons={false}
        hideSearchBar={true}
        hideServiceIcons={true}
        title={
          <div className="flex flex-col items-center">
            <span className="text-base md:text-lg font-medium px-4 py-1.5 bg-primary/10 rounded-full text-primary inline-flex items-center mb-4">
              <Award className="h-4 w-4 mr-2" />
              Trusted by Thousands of Customers
            </span>
            <span className="leading-tight">
              How <span className="text-primary">JobON</span> Works
            </span>
          </div>
        }
        subtitle={
          <div className="mt-4">
            Get your home and business service needs met in three simple steps.<br />
            Post your job, receive competitive bids, and get the job done.
          </div>
        }
        showLocationSearch={false}
      />
      
      <div className="relative py-12 md:py-16 overflow-hidden">
        {/* Background decoration */}
        <div className="absolute inset-0 z-0">
          <div className="absolute top-0 right-0 w-64 h-64 bg-primary/5 rounded-full filter blur-3xl"></div>
          <div className="absolute bottom-0 left-20 w-72 h-72 bg-blue-500/5 rounded-full filter blur-3xl"></div>
        </div>
        
        <div className="container mx-auto px-6 md:px-12 relative z-10 mb-16">
          {/* Process steps with illustrations and real-life scenarios */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-6xl mx-auto">
            {/* Step 1 */}
            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-100 dark:border-gray-700 overflow-hidden group hover:shadow-xl transition-all duration-300">
              <div className="h-40 bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 relative overflow-hidden">
                <div className="absolute inset-0 bg-[url('/public/lovable-uploads/ec613df5-e625-4dfa-baf7-5c156a1ac4eb.png')] bg-center bg-cover opacity-10"></div>
                <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-20 h-20 bg-white dark:bg-gray-800 rounded-full flex items-center justify-center shadow-md">
                  <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center">
                    <ClipboardCheck className="h-8 w-8 text-primary" strokeWidth={1.5} />
                  </div>
                </div>
                <div className="absolute top-4 right-4 bg-primary text-white text-sm font-bold w-8 h-8 rounded-full flex items-center justify-center">
                  1
                </div>
              </div>
              
              <div className="p-6">
                <h3 className="text-2xl font-bold mb-3 text-center">Post Your Project</h3>
                <p className="text-gray-600 dark:text-gray-300 mb-4">
                  Describe your project in detail, including what needs to be done, when you need it, and any specific requirements for your home or business.
                </p>
                
                {/* Real-life scenario - more relatable */}
                <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4 mt-4 relative">
                  <div className="absolute -top-3 -left-3 bg-white dark:bg-gray-800 rounded-full p-1 shadow">
                    <Users className="h-5 w-5 text-primary" />
                  </div>
                  <h4 className="text-sm font-semibold mb-2 pl-2">Real Customer Experience</h4>
                  <p className="text-sm italic text-gray-600 dark:text-gray-300">
                    "I needed to fix a leaky faucet at home and update the lighting in my small business. I posted both projects on JobON and had quotes within hours!"
                  </p>
                  <div className="mt-2 flex items-center">
                    <div className="flex">
                      {[...Array(5)].map((_, i) => (
                        <Star key={i} className="h-3 w-3 text-yellow-500 fill-yellow-500" />
                      ))}
                    </div>
                    <span className="text-xs ml-2">— Sarah T., Seattle</span>
                  </div>
                </div>
              </div>
            </div>
            
            {/* Step 2 */}
            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-100 dark:border-gray-700 overflow-hidden group hover:shadow-xl transition-all duration-300">
              <div className="h-40 bg-gradient-to-br from-indigo-50 to-purple-50 dark:from-indigo-900/20 dark:to-purple-900/20 relative overflow-hidden">
                <div className="absolute inset-0 bg-[url('/public/lovable-uploads/ec613df5-e625-4dfa-baf7-5c156a1ac4eb.png')] bg-center bg-cover opacity-10"></div>
                <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-20 h-20 bg-white dark:bg-gray-800 rounded-full flex items-center justify-center shadow-md">
                  <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center">
                    <MessageSquare className="h-8 w-8 text-primary" strokeWidth={1.5} />
                  </div>
                </div>
                <div className="absolute top-4 right-4 bg-primary text-white text-sm font-bold w-8 h-8 rounded-full flex items-center justify-center">
                  2
                </div>
              </div>
              
              <div className="p-6">
                <h3 className="text-2xl font-bold mb-3 text-center">Receive Competitive Bids</h3>
                <p className="text-gray-600 dark:text-gray-300 mb-4">
                  Professional service providers specializing in residential and commercial services will submit their bids. Compare quotes, reviews, and credentials to find your perfect match.
                </p>
                
                {/* Compare quotes visualization - more relatable */}
                <div className="bg-indigo-50 dark:bg-indigo-900/20 rounded-lg p-4 mt-4 relative">
                  <div className="absolute -top-3 -left-3 bg-white dark:bg-gray-800 rounded-full p-1 shadow">
                    <Wrench className="h-5 w-5 text-primary" />
                  </div>
                  <h4 className="text-sm font-semibold mb-2 pl-2">Easy Comparison</h4>
                  <div className="grid grid-cols-2 gap-3 text-xs">
                    <div className="bg-white dark:bg-gray-700 p-2 rounded shadow-sm">
                      <div className="font-bold text-primary mb-1">Provider A</div>
                      <div className="flex items-center justify-between">
                        <span>Price:</span> 
                        <span>$120</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span>Rating:</span>
                        <div className="flex">
                          {[...Array(5)].map((_, i) => (
                            <Star key={i} className="h-2 w-2 text-yellow-500 fill-yellow-500" />
                          ))}
                        </div>
                      </div>
                    </div>
                    <div className="bg-white dark:bg-gray-700 p-2 rounded shadow-sm">
                      <div className="font-bold text-green-600 dark:text-green-400 mb-1">Provider B</div>
                      <div className="flex items-center justify-between">
                        <span>Price:</span> 
                        <span>$95</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span>Rating:</span>
                        <div className="flex">
                          {[...Array(4)].map((_, i) => (
                            <Star key={i} className="h-2 w-2 text-yellow-500 fill-yellow-500" />
                          ))}
                          <Star className="h-2 w-2 text-gray-300 dark:text-gray-500" />
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            
            {/* Step 3 */}
            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-100 dark:border-gray-700 overflow-hidden group hover:shadow-xl transition-all duration-300">
              <div className="h-40 bg-gradient-to-br from-green-50 to-blue-50 dark:from-green-900/20 dark:to-blue-900/20 relative overflow-hidden">
                <div className="absolute inset-0 bg-[url('/public/lovable-uploads/ec613df5-e625-4dfa-baf7-5c156a1ac4eb.png')] bg-center bg-cover opacity-10"></div>
                <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-20 h-20 bg-white dark:bg-gray-800 rounded-full flex items-center justify-center shadow-md">
                  <div className="w-16 h-16 bg-success/10 rounded-full flex items-center justify-center">
                    <CreditCard className="h-8 w-8 text-success" strokeWidth={1.5} />
                  </div>
                </div>
                <div className="absolute top-4 right-4 bg-success text-white text-sm font-bold w-8 h-8 rounded-full flex items-center justify-center">
                  3
                </div>
              </div>
              
              <div className="p-6">
                <h3 className="text-2xl font-bold mb-3 text-center">Pay Securely</h3>
                <p className="text-gray-600 dark:text-gray-300 mb-4">
                  Choose your provider and pay securely through our platform. Use our flexible payment options including buy now, pay later financing for both residential and commercial projects.
                </p>
                
                {/* Payment options - more relatable */}
                <div className="bg-green-50 dark:bg-green-900/20 rounded-lg p-4 mt-4 relative">
                  <div className="absolute -top-3 -left-3 bg-white dark:bg-gray-800 rounded-full p-1 shadow">
                    <div className="flex">
                      <Home className="h-5 w-5 text-success" />
                      <Building className="h-5 w-5 text-success -ml-1" />
                    </div>
                  </div>
                  <h4 className="text-sm font-semibold mb-2 pl-2">Project Complete!</h4>
                  <div className="flex flex-col gap-2">
                    <div className="flex items-center gap-2">
                      <Badge variant="outline" className="bg-white/50 dark:bg-gray-800/50">
                        <CheckCircle className="h-3 w-3 mr-1 text-success" /> Upfront Payment
                      </Badge>
                      <span className="text-xs">or</span>
                      <Badge variant="outline" className="bg-white/50 dark:bg-gray-800/50">
                        <Clock className="h-3 w-3 mr-1 text-primary" /> Pay Later
                      </Badge>
                    </div>
                    <p className="text-xs text-gray-600 dark:text-gray-300 mt-1">
                      "The financing option let me spread my renovation costs over 12 months with low interest for both my home and office spaces!"
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        {/* Interactive timeline */}
        <div className="container mx-auto px-6 md:px-12 relative z-10 mb-16">
          <div className="max-w-4xl mx-auto">
            <div className="relative">
              <div className="h-2 bg-gray-200 dark:bg-gray-700 rounded-full">
                <div className="h-2 bg-primary rounded-full" style={{ width: '100%' }}></div>
              </div>
              
              {/* Timeline nodes with animations */}
              <div className="absolute -top-2 left-0 w-6 h-6 bg-primary rounded-full flex items-center justify-center transform translate-y-0 animate-pulse" style={{ animationDuration: '3s' }}>
                <div className="w-3 h-3 bg-white rounded-full"></div>
              </div>
              
              <div className="absolute -top-2 left-1/2 transform -translate-x-1/2 w-6 h-6 bg-primary rounded-full flex items-center justify-center transform translate-y-0 animate-pulse" style={{ animationDuration: '3s', animationDelay: '0.5s' }}>
                <div className="w-3 h-3 bg-white rounded-full"></div>
              </div>
              
              <div className="absolute -top-2 right-0 w-6 h-6 bg-success rounded-full flex items-center justify-center transform translate-y-0 animate-pulse" style={{ animationDuration: '3s', animationDelay: '1s' }}>
                <div className="w-3 h-3 bg-white rounded-full"></div>
              </div>
              
              {/* Labels under timeline */}
              <div className="flex justify-between mt-4 text-sm">
                <div className="text-center">
                  <span className="text-primary font-medium">Post Project</span>
                  <p className="text-gray-500 dark:text-gray-400 text-xs mt-1">5 mins</p>
                </div>
                <div className="text-center">
                  <span className="text-primary font-medium">Compare Bids</span>
                  <p className="text-gray-500 dark:text-gray-400 text-xs mt-1">24-48 hrs</p>
                </div>
                <div className="text-center">
                  <span className="text-success font-medium">Job Complete</span>
                  <p className="text-gray-500 dark:text-gray-400 text-xs mt-1">On your schedule</p>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        {/* CTAs - more emotionally engaging */}
        <div className="container mx-auto px-6 md:px-12 relative z-10 mb-16">
          <div className="max-w-4xl mx-auto">
            <div className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/30 dark:to-indigo-900/30 rounded-2xl p-8 md:p-10 shadow-lg border border-gray-100 dark:border-gray-700">
              <div className="flex flex-col md:flex-row items-center justify-between gap-8">
                <div className="text-center md:text-left">
                  <div className="flex items-center justify-center md:justify-start mb-4">
                    <Heart className="h-6 w-6 text-red-500 mr-2 animate-pulse" />
                    <h3 className="text-2xl font-bold">Ready to Get Started?</h3>
                  </div>
                  <p className="text-gray-600 dark:text-gray-300 mb-4">
                    Join thousands of satisfied customers who trust JobON for their residential and commercial service needs.
                  </p>
                  <div className="flex flex-wrap gap-2 justify-center md:justify-start">
                    <Badge variant="secondary" className="bg-white/50 dark:bg-gray-800/50">
                      <ShieldCheck className="h-3 w-3 mr-1 text-success" /> Safe & Secure
                    </Badge>
                    <Badge variant="secondary" className="bg-white/50 dark:bg-gray-800/50">
                      <Clock className="h-3 w-3 mr-1 text-primary" /> Save Time
                    </Badge>
                    <Badge variant="secondary" className="bg-white/50 dark:bg-gray-800/50">
                      <Star className="h-3 w-3 mr-1 text-yellow-500" /> Verified Pros
                    </Badge>
                  </div>
                </div>
                
                <div className="flex flex-col sm:flex-row gap-4">
                  <Button 
                    asChild 
                    variant="default" 
                    size="lg" 
                    className="text-base font-medium px-8 py-6 h-auto rounded-lg shadow-md bg-success hover:bg-success/90 text-white"
                  >
                    <Link to="/create-job" className="flex items-center justify-center">
                      Post Your Project
                      <ArrowRight className="ml-2 h-5 w-5" />
                    </Link>
                  </Button>
                  <Button 
                    asChild 
                    variant="outline" 
                    size="lg" 
                    className="text-base font-medium px-8 py-6 h-auto rounded-lg bg-white dark:bg-transparent border border-gray-300 dark:border-gray-600 text-gray-800 dark:text-white hover:bg-gray-50 dark:hover:bg-gray-800"
                  >
                    <Link to="/financing">
                      Learn About Financing
                    </Link>
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        {/* Testimonials or FAQ section - more emotionally engaging */}
        <section className="container mx-auto px-6 md:px-12 relative z-10 py-10">
          <div className="max-w-3xl mx-auto text-center mb-12">
            <div className="inline-block rounded-full bg-primary/10 p-2 mb-4">
              <MessageSquare className="h-6 w-6 text-primary" />
            </div>
            <h2 className="text-3xl md:text-4xl font-bold mb-4">Frequently Asked Questions</h2>
            <p className="text-lg text-foreground/70">
              Everything you need to know about using JobON
            </p>
          </div>
          
          <div className="max-w-4xl mx-auto space-y-6">
            {/* FAQ items with emotional responses */}
            {[
              {
                question: "How do I know which service provider to choose?",
                answer: "You can compare bids based on price, provider ratings, reviews from other customers, and credentials. We verify all service providers on our platform to ensure quality service for both home and business needs. Many customers tell us the detailed reviews helped them find the perfect match for their project."
              },
              {
                question: "What if I'm not satisfied with the service?",
                answer: "Your satisfaction is our priority. If you're not happy with the service provided, please contact our customer support team within 48 hours, and we'll work to resolve the issue. We stand behind our service providers and want you to love the results for both residential and commercial projects."
              },
              {
                question: "How does the buy now, pay later financing work?",
                answer: "Our flexible financing options allow you to spread the cost of your project over time. We offer low interest rates for qualified customers and various term options to fit your budget. Many homeowners and business owners tell us this has made their dream renovations possible without financial stress."
              },
              {
                question: "Is there a fee to post a project?",
                answer: "No, posting a project on JobON is completely free. We only charge a small service fee when you hire a provider through our platform. We believe finding the right help for your home or business should be accessible to everyone."
              }
            ].map((faq, index) => (
              <div key={index} className="bg-white dark:bg-gray-800/90 rounded-xl p-6 shadow-sm border border-gray-100 dark:border-gray-700 hover:shadow-md transition-all">
                <h3 className="text-xl font-semibold mb-3">{faq.question}</h3>
                <p className="text-foreground/70">{faq.answer}</p>
              </div>
            ))}
          </div>
          
          <div className="text-center mt-12">
            <Button 
              asChild 
              variant="link" 
              size="lg"
              className="font-medium text-primary"
            >
              <Link to="/faq" className="flex items-center justify-center">
                View All FAQs
                <ArrowRight className="ml-2 h-4 w-4" />
              </Link>
            </Button>
          </div>
        </section>
      </div>
    </Layout>
  );
};

export default HowItWorks;
