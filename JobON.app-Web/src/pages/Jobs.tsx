import React, { useState, useEffect } from 'react';
import { Layout } from '@/components/Layout';
import { JobCard } from '@/components/JobCard';
import { SearchBar } from '@/components/SearchBar';
import { Filter, X, Briefcase } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { SEO } from '@/components/SEO';
import { useIsMobile } from '@/hooks/use-mobile';
import { FilterSidebar } from '@/components/FilterSidebar';
import FindJobsSignupDialog from '@/components/FindJobsSignupDialog';
import { Drawer, DrawerContent, DrawerHeader, DrawerTitle } from "@/components/ui/drawer";
import { Pagination } from "@/components/ui/pagination";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { apiService } from "@/services/api.ts";
import { useSearchParams } from 'react-router-dom';
import { Skeleton } from "@/components/ui/skeleton";
import { formatJobTimeline } from '@/utils/jobCardUtils';
interface FilterState {
  categories: string[];
  locations: string[];
  salary: [number, number];
  tags: string[];
  service?: string | null;
}
export interface JobDataType {
  jobId: string;
  projectCode: string;
  createdAt: string;
  status: string;
  jobType: string;
  property: {
    type: string;
  };
  service: {
    category: string;
    tasks: string[];
    customTask: string | null;
  };
  description: string | null;
  schedule: {
    date: string;
    timePreference: string;
    frequency: string;
    recurringFrequency: string | null;
  };
  budget: number | null;
  location: {
    address: string;
    city: string;
    state: string;
    zipCode: string;
  };
  contact: {
    fullName: string;
    email: string;
    phone: string;
  };
  assets: {
    uuid: string;
    file_name: string;
    url: string;
    mime_type: string;
    file_size: number;
  }[];
  user: null;
}
interface responseType {
  data: JobDataType[];
  pagination: {
    current_page: number;
    last_page: number;
    per_page: number;
    total: number;
  };
  success: boolean;
  error: null;
  isSuccess: boolean;
  status: number;
}
const JobCardSkeleton = () => {
  return <div className="bg-white flex flex-col justify-between dark:bg-gray-800 shadow-lg rounded-lg overflow-hidden border border-gray-300 dark:border-gray-700">
      <div>
        {/* Category header */}
        <div className="bg-primary/5 dark:bg-primary/10 px-3 py-2 border-b border-gray-100 dark:border-gray-700 flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Skeleton className="h-5 w-5 rounded-full" />
            <Skeleton className="h-4 w-24" />
          </div>
          <Skeleton className="h-4 w-16" />
        </div>

        {/* Main content */}
        <div className="p-3">
          <Skeleton className="h-5 w-3/4 mb-1" />
          <Skeleton className="h-4 w-full mb-1" />
          <Skeleton className="h-4 w-2/3 mb-2" />

          {/* Tags Section */}
          <div className="flex flex-wrap gap-1 mb-2">
            <Skeleton className="h-4 w-16" />
            <Skeleton className="h-4 w-12" />
          </div>

          {/* Job Details */}
          <div className="flex flex-col gap-2 text-xs mb-3">
            <Skeleton className="h-3.5 w-24" />
            <Skeleton className="h-3.5 w-32" />
            <Skeleton className="h-3.5 w-28" />
          </div>
        </div>
      </div>
      {/* CTA Button */}
      <div className="p-3">
        <Skeleton className="h-9 w-full" />
      </div>
    </div>;
};
const Jobs = () => {
  const isMobile = useIsMobile();
  const [searchParams, setSearchParams] = useSearchParams();
  const [jobs, setJobs] = useState<JobDataType[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [filterVisible, setFilterVisible] = useState(!isMobile);
  const [filterDrawerOpen, setFilterDrawerOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  // Get page from URL or default to 1
  const initialPage = parseInt(searchParams.get('page') || '1', 10);
  const [currentPage, setCurrentPage] = useState(initialPage);

  // Add jobs per page state - get from URL or default to 10
  const initialJobsPerPage = parseInt(searchParams.get('per_page') || '10', 10);
  const [jobsPerPage, setJobsPerPage] = useState(initialJobsPerPage);
  const [pagination, setPagination] = useState({
    current_page: initialPage,
    last_page: 1,
    per_page: initialJobsPerPage,
    total: 0
  });
  const [filters, setFilters] = useState<FilterState>({
    categories: [],
    locations: [],
    salary: [0, 100000],
    tags: [],
    service: null
  });
  const handleSearch = (searchText: string) => {
    setSearchQuery(searchText);
  };
  const handleFilterChange = (filterData: FilterState) => {
    const serviceChanged = filters.service !== filterData.service;
    setFilters(filterData);
    if (serviceChanged) {
      setCurrentPage(1);
      setSearchParams({
        page: '1',
        per_page: jobsPerPage.toString()
      });
      fetchBookings(1, filterData.service, jobsPerPage);
    }
  };
  const handleJobsPerPageChange = (value: string) => {
    const newJobsPerPage = parseInt(value, 10);
    setJobsPerPage(newJobsPerPage);
    setCurrentPage(1);
    setSearchParams({
      page: '1',
      per_page: newJobsPerPage.toString()
    });
    fetchBookings(1, filters.service, newJobsPerPage);
  };
  const handleCloseFilterDrawer = () => {
    setFilterDrawerOpen(false);
  };
  useEffect(() => {
    setFilterVisible(!isMobile);
  }, [isMobile]);
  const fetchBookings = async (page = 1, serviceFilter = filters.service, perPage = jobsPerPage) => {
    handleCloseFilterDrawer();
    try {
      setIsLoading(true);
      let endpoint = `api/job-bookings?page=${page}&per_page=${perPage}`;
      if (serviceFilter) {
        endpoint += `&category=${serviceFilter}`;
      }
      const response = await apiService(endpoint, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        },
        requiresAuth: true,
        includeCredentials: true
      });
      const apiResponse = response as unknown as {
        data: responseType | null;
      }; // More generic initial cast
      if (apiResponse && apiResponse.data && apiResponse.data.success) {
        const data = apiResponse.data; // data is now responseType
        setJobs(data.data || []); // Ensure jobs is an array
        if (data.pagination) {
          setPagination(data.pagination);
        }
      } else {
        console.error('Failed to fetch jobs or no data:', apiResponse?.data?.error || 'Unknown error');
        setJobs([]); // Clear jobs on error or no success
        setPagination(prev => ({
          ...prev,
          total: 0,
          last_page: prev.current_page,
          current_page: 1
        }));
      }
    } catch (error) {
      console.error('Error fetching job bookings:', error);
      setJobs([]);
      setPagination(prev => ({
        ...prev,
        total: 0,
        last_page: prev.current_page,
        current_page: 1
      }));
    } finally {
      setIsLoading(false);
    }
  };
  const handlePageChange = (page: number) => {
    setSearchParams({
      page: page.toString(),
      per_page: jobsPerPage.toString()
    });
    setCurrentPage(page);
    fetchBookings(page, filters.service, jobsPerPage);
  };
  useEffect(() => {
    fetchBookings(initialPage, filters.service, jobsPerPage);
  }, [initialPage, filters.service, jobsPerPage]);
  return <Layout>
      <SEO title="Find Service Jobs | Place Bids on Projects | JobON" description="Browse available projects and place bids as a service provider. Find opportunities in plumbing, electrical, cleaning, and more professional services." />

      <FindJobsSignupDialog delayInMs={10000} />

      <div className="pb-10 pt-24">
        <div className="bg-gray-50 dark:bg-gray-900 py-4 w-full">
          <div className="container mx-auto px-3 sm:px-4">
            <div className="flex flex-col md:flex-row md:items-center justify-between gap-4 mb-4">
              <div className="flex items-center gap-2">
                <Briefcase className="w-6 h-6 text-primary" />
                <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
                  Available Projects
                </h1>
              </div>

              <Button className="hidden md:flex items-center gap-2" size="lg" onClick={() => window.location.href = '/create-job'}>
                <span className="text-lg">+</span> Post a Project
              </Button>
            </div>

            <p className="text-sm md:text-base text-gray-600 dark:text-gray-300 mb-6 max-w-2xl">
              Find and bid on projects that match your service expertise. Connect with customers looking for quality professional service.
            </p>

            <div className="w-full">
              <SearchBar variant="zipcode" placeholder="Search jobs..." locationPlaceholder="Enter ZIP code" onZipSubmit={handleSearch} className="w-full mb-4" />
            </div>
          </div>
        </div>
        <div className="container mx-auto px-3 sm:px-4 py-4">
          <div className="flex flex-col md:flex-row gap-4">
            {!isMobile && <div className="w-full md:w-64 md:flex-shrink-0">
                  <div className="sticky top-24">
                    <FilterSidebar onFilterChange={handleFilterChange} jobsPerPage={jobsPerPage} onJobsPerPageChange={handleJobsPerPageChange} />
                  </div>
                </div>}

            <div className="flex-1 w-full">
              <div className="flex items-center justify-between mb-4">
                <p className="text-sm text-gray-600 dark:text-gray-300">
                  {jobs.length} projects available
                </p>
                {isMobile && <Button variant="outline" size="sm" className="flex items-center gap-1.5" onClick={() => setFilterDrawerOpen(true)}>
                      <Filter className="h-4 w-4" />
                      Filters
                    </Button>}
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {isLoading ? Array(jobsPerPage).fill(0).map((_, index) => <JobCardSkeleton key={`skeleton-${index}`} />) : jobs.map(jobData => <JobCard key={jobData.jobId} id={jobData.jobId} title={jobData.service.category} description={jobData.description ?? ''} price={0} location={jobData.location.address} dueDate={'2 days ago'} status={'open'} category={jobData?.service?.category} isUrgent={false} isNew={true} isHighPaying={false} isRecurring={false} isNearby={false} detail={jobData} />)}
              </div>

              {pagination.total > 0 && !isLoading && <div className="mt-8">
                  <Pagination totalItems={pagination.total} itemsPerPage={pagination.per_page} currentPage={pagination.current_page} onPageChange={handlePageChange} />
                </div>}
            </div>
          </div>
        </div>

        {isMobile && <Drawer open={filterDrawerOpen} onOpenChange={setFilterDrawerOpen}>
              <DrawerContent className="max-h-[80vh]">
                <DrawerHeader className="border-b">
                  <DrawerTitle className="flex justify-between items-center">
                    <div className="flex items-center">
                      <Filter className="h-5 w-5 mr-2" />
                      Filter Projects
                    </div>
                    <Button variant="ghost" size="icon" onClick={() => setFilterDrawerOpen(false)} className="h-8 w-8 rounded-full">
                      <X className="h-4 w-4" />
                    </Button>
                  </DrawerTitle>
                </DrawerHeader>
                <div className="p-4 overflow-y-auto">
                  <FilterSidebar onFilterChange={handleFilterChange} jobsPerPage={jobsPerPage} onJobsPerPageChange={handleJobsPerPageChange} />
                </div>
              </DrawerContent>
            </Drawer>}

        {isMobile && <div className="fixed bottom-20 right-4 z-30">
              
            </div>}
      </div>
    </Layout>;
};
export default Jobs;