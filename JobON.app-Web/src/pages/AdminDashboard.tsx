import React, { useEffect, useState } from 'react';
import { userStatsService } from '@/services/userStatsService';
import { useAuth } from '@/features/auth/hooks/useAuth';
import { toast } from 'sonner';
import { useNavigate, useLocation, Navigate } from "react-router-dom";
import { Search, Bell, Menu, ChevronRight, LayoutDashboard, Users, User, Calendar, Building, Star, MessageSquare, Mail, Check, DollarSign, ArrowUpRight, ArrowDownRight, TrendingUp, LineChart } from "lucide-react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Sheet, SheetContent, SheetTrigger } from "@/components/ui/sheet";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { ProjectsTable } from "@/components/admin/ProjectsTable";
import { ProvidersTable } from "@/components/admin/ProvidersTable";
import { CustomerManagement } from "@/components/admin/CustomerManagement";
import { DashboardStats } from "@/components/admin/DashboardStats";
import { AdminMessagingInterface } from "@/components/admin/messaging/AdminMessagingInterface";
import { AdminSettings } from "@/components/admin/settings";
import { JobsManagement } from "@/components/admin/JobsManagement";
import { PaymentsManagement } from "@/components/admin/PaymentsManagement";
import { ReviewsManagement } from "@/components/admin/ReviewsManagement";
import { RewardsManagement } from "@/components/admin/RewardsManagement";
import { ReferralsManagement } from "@/components/admin/ReferralsManagement";
import { NotificationCenter } from "@/components/admin/shared/NotificationCenter";
import { ProviderInvitations } from "@/components/admin/ProviderInvitations";
import { EmailTrackingDashboard } from "@/components/admin/EmailTrackingDashboard";
import { useIsMobile } from "@/hooks/use-mobile";
import { AdminSidebar } from "@/components/admin/AdminSidebar";
import { AdminSidebarMobile } from "@/components/admin/AdminSidebarMobile";
import { useMediaQuery } from "@/hooks/use-media-query";
import { cn } from "@/lib/utils";
import { ADMIN_PAGE_TITLES } from "@/lib/constants";
import Bookings from "@/pages/Bookings.tsx";
import Business from "@/pages/Business.tsx";
import AdminJobOversight from "@/pages/AdminJobOversight.tsx";
import { ThemeToggle } from '@/components/ThemeToggle';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from "@/components/ui/card";

const AdminDashboard = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const path = location.pathname;
  const isMobile = useIsMobile();
  const isTablet = useMediaQuery("(max-width: 1024px)");
  const [isSheetOpen, setIsSheetOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const { token } = useAuth();
  interface ActiveUsersState {
    activeProviders: number;
    activeCustomers: number;
  }

  const [activeUsers, setActiveUsers] = useState<ActiveUsersState>({ activeProviders: 0, activeCustomers: 0 });
  const [isLoadingStats, setIsLoadingStats] = useState(true);

  // Fetch active users count
  useEffect(() => {
    const fetchActiveUsers = async () => {
      setIsLoadingStats(true);
      try {
        // Convert null to undefined for the service calls
        const tokenOrUndefined = token || undefined;
        
        // Fetch active providers (role_id=3)
        const providersResponse = await userStatsService.getActiveUsersCount(tokenOrUndefined, 3);
        // Fetch active customers (role_id=2)
        const customersResponse = await userStatsService.getActiveUsersCount(tokenOrUndefined, 2);

        if (providersResponse.isSuccess && customersResponse.isSuccess) {
          setActiveUsers({
            activeProviders: providersResponse.data?.activeProviders || 0,
            activeCustomers: customersResponse.data?.activeCustomers || 0
          });
        } else {
          console.error('Failed to fetch active users count:',
            providersResponse.error || customersResponse.error);
          toast.error('Failed to load user statistics');
        }
      } catch (error) {
        console.error('Error fetching active users count:', error);
        toast.error('Failed to load user statistics');
      } finally {
        setIsLoadingStats(false);
      }
    };

    fetchActiveUsers();
  }, [token]);

  useEffect(() => {
    // Scroll to top when path changes
    window.scrollTo(0, 0);
  }, [path]);

  // Redirect to the dashboard page if we're at /admin/dashboard
  if (path === "/admin/dashboard") {
    return <Navigate to="/admin" replace />;
  }

  // Close the mobile sidebar sheet
  const closeMobileSidebar = () => {
    setIsSheetOpen(false);
  };

  // Choose appropriate content based on path
  const renderContent = () => {
    switch (path) {
      case "/admin":
        return <div className="space-y-6">
            <DashboardStats />
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-4 lg:gap-6">
              <div className="lg:col-span-2">
                <ProjectsTable />
              </div>
              <div className="lg:col-span-1">
                <NotificationCenter />
              </div>
            </div>
          </div>;
      case "/admin/providers":
        return <ProvidersTable />;
      case "/admin/provider-invitations":
        return <ProviderInvitations />;
      case "/admin/customers":
        return <CustomerManagement />;
      case "/admin/jobs":
        return <JobsManagement />;
      case "/admin/job-oversight":
        return <AdminJobOversight />;
      case "/admin/payments":
        return <PaymentsManagement />;
      case "/admin/messages":
        return <AdminMessagingInterface />;
      case "/admin/reviews":
        return <ReviewsManagement />;
      case "/admin/rewards":
        return <RewardsManagement />;
      case "/admin/referrals":
        return <ReferralsManagement />;
      case "/admin/email-tracking":
        return <EmailTrackingDashboard />;
      case "/admin/settings":
        return <AdminSettings />;
      case "/admin/bookings":
        return <Bookings />;
      case "/admin/business":
        return <Business />;
      default:
        return <div>Page not found</div>;
    }
  };

  // Get page title based on path - fix index signature issue
  const getPageTitle = () => {
    if (path === "/admin/job-oversight") {
      return "Job Oversight Dashboard";
    }
    const route = path === "/admin" ? "dashboard" : path.replace("/admin/", "");
    
    // Type-safe access to ADMIN_PAGE_TITLES
    const validRoutes = [
      'dashboard', 'providers', 'provider-invitations', 'customers', 'jobs', 
      'job-oversight', 'payments', 'reviews', 'rewards', 'referrals', 
      'messages', 'settings', 'business', 'bookings', 'email-tracking'
    ] as const;
    
    type ValidRoute = typeof validRoutes[number];
    
    if (validRoutes.includes(route as ValidRoute)) {
      return ADMIN_PAGE_TITLES[route as ValidRoute];
    }
    
    return "Admin Dashboard";
  };

  interface StatsCardProps {
    title: string;
    value: string;
    icon: React.ElementType;
    color: string;
    bgColor: string;
    onClick?: () => void;
  }

  // Mobile dashboard card for enhanced UI
  const StatsCard = ({ title, value, icon: Icon, color, bgColor, onClick }: StatsCardProps) => (
    <div
      onClick={onClick}
      className={`rounded-xl p-5 shadow-md transition-all duration-300 hover:shadow-lg active:scale-95 animate-fade-in ${bgColor} relative overflow-hidden group`}
    >
      <div className="flex justify-between items-start">
        <div>
          <p className={`text-sm font-medium ${color}`}>{title}</p>
          <h3 className={`text-3xl font-bold mt-1 ${color}`}>{value}</h3>
        </div>
        <div className={`w-12 h-12 rounded-full flex items-center justify-center bg-white/20 backdrop-blur`}>
          <Icon className={`h-6 w-6 ${color}`} />
        </div>
      </div>
      <div className="absolute bottom-2 right-3 opacity-0 group-hover:opacity-100 transition-opacity">
        <ChevronRight className={`h-5 w-5 ${color}`} />
      </div>
    </div>
  );

  interface QuickNavCardProps {
    icon: React.ElementType;
    title: string;
    path: string;
    color: string;
    bgColor: string;
  }

  // Quick nav cards for mobile dashboard with enhanced UI based on the image
  const QuickNavCard = ({ icon: Icon, title, path, color, bgColor }: QuickNavCardProps) => (
    <button
      onClick={() => navigate(path)}
      className={`relative overflow-hidden rounded-xl shadow-md transition-all duration-300
                hover:shadow-lg active:scale-95 animate-fade-in
                ${bgColor} p-4 flex flex-col items-center`}
    >
      <div className="mb-2">
        <Icon className={`h-6 w-6 ${color}`} />
      </div>
      <p className="text-sm font-medium text-gray-700 dark:text-gray-200">{title}</p>
      <ChevronRight className="absolute bottom-2 right-2 h-4 w-4 text-gray-400" />
    </button>
  );

  // Revenue insights card for mobile dashboard
  const RevenueInsightCard = () => {
    const revenueData = {
      current: "$24,320",
      previous: "$23,150",
      percentChange: 5.1,
      isPositive: true,
      timeframe: "This Month"
    };

    return (
      <Card className="bg-gradient-to-r from-purple-50 to-indigo-50 dark:from-purple-900/20 dark:to-indigo-900/20 border-none shadow-md overflow-hidden animate-fade-in">
        <CardContent className="p-5">
          <div className="flex justify-between items-start mb-3">
            <h3 className="font-semibold text-gray-800 dark:text-gray-100">Revenue Insights</h3>
            <div className="p-2 rounded-full bg-purple-100 dark:bg-purple-900/30">
              <DollarSign className="h-4 w-4 text-purple-600 dark:text-purple-400" />
            </div>
          </div>

          <div className="mb-4">
            <p className="text-sm text-gray-500 dark:text-gray-400">{revenueData.timeframe}</p>
            <div className="flex items-baseline mt-1">
              <h2 className="text-3xl font-bold text-gray-900 dark:text-white">{revenueData.current}</h2>
              <div className="flex items-center ml-3">
                {revenueData.isPositive ? (
                  <ArrowUpRight className="h-4 w-4 text-green-500 mr-1" />
                ) : (
                  <ArrowDownRight className="h-4 w-4 text-red-500 mr-1" />
                )}
                <span className={`text-sm font-medium ${revenueData.isPositive ? 'text-green-500' : 'text-red-500'}`}>
                  {revenueData.percentChange}%
                </span>
              </div>
            </div>
          </div>

          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span className="text-gray-600 dark:text-gray-300">Job Commissions</span>
              <span className="font-medium text-gray-900 dark:text-white">$14,850</span>
            </div>
            <div className="flex justify-between text-sm">
              <span className="text-gray-600 dark:text-gray-300">Subscription Fees</span>
              <span className="font-medium text-gray-900 dark:text-white">$8,470</span>
            </div>
            <div className="flex justify-between text-sm">
              <span className="text-gray-600 dark:text-gray-300">Premium Listings</span>
              <span className="font-medium text-gray-900 dark:text-white">$1,000</span>
            </div>
          </div>

          <div className="mt-5">
            <Button
              onClick={() => navigate("/admin/payments")}
              variant="outline"
              className="w-full justify-between bg-white dark:bg-gray-800 border-purple-100 dark:border-purple-900/30 hover:bg-purple-50 dark:hover:bg-purple-900/50"
            >
              <span className="flex items-center">
                <LineChart className="h-4 w-4 mr-2 text-purple-600" />
                View Detailed Reports
              </span>
              <ChevronRight className="h-4 w-4 text-gray-400" />
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  };

  return (
    <div className="flex flex-col overflow-hidden">
      <div className={cn("flex-1 overflow-y-auto p-4", !isMobile && "md:p-6")}>
        {path === "/admin" && isMobile ? (
          <div className="space-y-5">
            <div className="p-4 rounded-xl bg-gradient-to-r from-blue-500 to-indigo-600 text-white shadow-md animate-fade-in">
              <h2 className="text-lg font-semibold mb-1">Welcome back, Admin</h2>
              <p className="text-sm text-blue-100">Here's what's happening today</p>
            </div>

            {/* Enhanced stats grid inspired by the provided image */}
            <div className="grid grid-cols-2 gap-3">
              <StatsCard
                title="Active Providers"
                value={isLoadingStats ? "..." : activeUsers.activeProviders.toString()}
                icon={Users}
                color="text-blue-700"
                bgColor="bg-blue-50"
                onClick={() => navigate("/admin/providers")}
              />
              <StatsCard
                title="Sent Invites"
                value="1"
                icon={Mail}
                color="text-purple-700"
                bgColor="bg-purple-50"
                onClick={() => navigate("/admin/provider-invitations")}
              />
              <StatsCard
                title="Opened Jobs"
                value="1"
                icon={Mail}
                color="text-amber-700"
                bgColor="bg-amber-50"
                onClick={() => navigate("/admin/jobs")}
              />
              <StatsCard
                title="Active Customers"
                value={isLoadingStats ? "..." : activeUsers.activeCustomers.toString()}
                icon={User}
                color="text-green-700"
                bgColor="bg-green-50"
                onClick={() => navigate("/admin/customers")}
              />
            </div>

            {/* Revenue Insight Card */}
            <RevenueInsightCard />

            <div>
              <h3 className="text-lg font-medium mb-3 text-gray-800 dark:text-gray-200">Quick Access</h3>
              <div className="grid grid-cols-2 gap-3">
                <QuickNavCard
                  icon={Users}
                  title="Providers"
                  path="/admin/providers"
                  color="text-indigo-600"
                  bgColor="bg-indigo-50"
                />
                <QuickNavCard
                  icon={MessageSquare}
                  title="Messages"
                  path="/admin/messages"
                  color="text-green-600"
                  bgColor="bg-green-50"
                />
                <QuickNavCard
                  icon={Calendar}
                  title="Bookings"
                  path="/admin/bookings"
                  color="text-amber-600"
                  bgColor="bg-amber-50"
                />
                <QuickNavCard
                  icon={Star}
                  title="Reviews"
                  path="/admin/reviews"
                  color="text-purple-600"
                  bgColor="bg-purple-50"
                />
                <QuickNavCard
                  icon={DollarSign}
                  title="Payments"
                  path="/admin/payments"
                  color="text-emerald-600"
                  bgColor="bg-emerald-50"
                />
                <QuickNavCard
                  icon={TrendingUp}
                  title="Overview"
                  path="/admin/job-oversight"
                  color="text-blue-600"
                  bgColor="bg-blue-50"
                />
              </div>
            </div>

            <div className="rounded-xl bg-white dark:bg-gray-800 shadow-md overflow-hidden animate-slide-in">
              <div className="p-4 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 border-b border-gray-100 dark:border-gray-700">
                <h3 className="font-semibold text-gray-900 dark:text-gray-100">Recent Activity</h3>
              </div>
              <NotificationCenter />
            </div>

            <Button
              onClick={() => navigate("/admin/providers")}
              className="w-full py-6 rounded-xl bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700 shadow-md"
            >
              View All Providers
            </Button>
          </div>
        ) : (
          renderContent()
        )}
      </div>
    </div>
  );
};

export default AdminDashboard;
