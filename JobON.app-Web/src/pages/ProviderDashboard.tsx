
import React, { useEffect } from 'react';
import { DashboardHome } from '@/components/provider/dashboard/DashboardHome';
import { useNavigate } from 'react-router-dom';
import { useIsMobile } from '@/hooks/use-mobile';
import { useAuth } from '@/features/auth/hooks/useAuth';

const ProviderDashboard = () => {
  const navigate = useNavigate();
  const isMobile = useIsMobile();
  const { isAuthenticated } = useAuth();

  // Check if user is authenticated as a provider
  useEffect(() => {
    // Clear the onboarding checklist hidden state on login if needed
    localStorage.removeItem('onboardingChecklistHidden');
  }, []);

  return (
    <div className="p-4 md:p-6 bg-gray-50 dark:bg-gray-900 min-h-[calc(100vh-65px)] flex-1 overflow-auto">
      <DashboardHome />
    </div>
  );
};

export default ProviderDashboard;
