
import React from 'react';
import { ProviderDashboardLayout } from '@/components/provider/ProviderDashboardLayout';
import { LeadDetailsView } from '@/components/provider/leads/LeadDetailsView';
import { useParams } from 'react-router-dom';

const ProviderLeadDetails = () => {
  const { jobId } = useParams<{ jobId: string }>();

  return (
    <ProviderDashboardLayout pageTitle="Lead Details">
      <LeadDetailsView jobId={jobId || ''} />
    </ProviderDashboardLayout>
  );
};

export default ProviderLeadDetails;
