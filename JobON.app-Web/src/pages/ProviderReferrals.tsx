
import React from 'react';
import { ProviderDashboardLayout } from '@/components/provider/ProviderDashboardLayout';
import { ReferralProgramCard } from '@/components/provider/referrals/ReferralProgramCard';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Users } from 'lucide-react';

const ProviderReferrals = () => {
  // In a real app, this would come from an API or user settings
  const referralLink = "https://jobon.com/join?ref=john_plumbing123";
  
  // Mock referral stats
  const referralStats = {
    invited: 7,
    activated: 3,
    rewards: 150
  };
  
  return (
    <ProviderDashboardLayout pageTitle="Referral Program">
      <div className="space-y-6">
        {/* Introduction Card */}
        <Card className="bg-gradient-to-r from-blue-50 to-indigo-50 border-0">
          <CardHeader>
            <div className="flex items-start justify-between">
              <div>
                <CardTitle className="text-xl md:text-2xl">Provider Referral Program</CardTitle>
                <CardDescription className="text-base mt-2">
                  Refer other service professionals to JobOn and earn $50 when they complete their first job.
                  Share your unique link with your network to start earning rewards.
                </CardDescription>
              </div>
              <div className="h-14 w-14 rounded-full bg-blue-100 flex items-center justify-center">
                <Users className="h-8 w-8 text-blue-600" />
              </div>
            </div>
          </CardHeader>
        </Card>
        
        {/* Referral Program Card */}
        <ReferralProgramCard 
          referralLink={referralLink}
          referralStats={referralStats}
        />
        
        {/* How It Works Section */}
        <div className="space-y-4">
          <h2 className="text-xl font-semibold">How It Works</h2>
          <Card>
            <CardContent className="p-6">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="text-center p-4">
                  <div className="bg-blue-100 text-blue-800 rounded-full w-10 h-10 flex items-center justify-center mx-auto mb-4">1</div>
                  <h3 className="font-semibold mb-2">Share Your Link</h3>
                  <p className="text-sm text-muted-foreground">
                    Share your unique referral link with other service providers in your network.
                  </p>
                </div>
                
                <div className="text-center p-4">
                  <div className="bg-blue-100 text-blue-800 rounded-full w-10 h-10 flex items-center justify-center mx-auto mb-4">2</div>
                  <h3 className="font-semibold mb-2">They Join JobOn</h3>
                  <p className="text-sm text-muted-foreground">
                    When they sign up using your link, they'll be connected to your referral.
                  </p>
                </div>
                
                <div className="text-center p-4">
                  <div className="bg-blue-100 text-blue-800 rounded-full w-10 h-10 flex items-center justify-center mx-auto mb-4">3</div>
                  <h3 className="font-semibold mb-2">Earn Rewards</h3>
                  <p className="text-sm text-muted-foreground">
                    After they complete their first job, you'll receive a $50 reward automatically.
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
        
        {/* Referral Tips */}
        <div className="space-y-4">
          <h2 className="text-xl font-semibold">Referral Tips</h2>
          <Card>
            <CardContent className="p-6">
              <ul className="space-y-3">
                <li className="flex gap-3">
                  <div className="bg-green-100 text-green-800 rounded-full min-w-8 h-8 flex items-center justify-center mt-0.5">✓</div>
                  <div>
                    <strong>Reach out to colleagues:</strong> Other service providers you've worked with in the past may be looking for more jobs.
                  </div>
                </li>
                <li className="flex gap-3">
                  <div className="bg-green-100 text-green-800 rounded-full min-w-8 h-8 flex items-center justify-center mt-0.5">✓</div>
                  <div>
                    <strong>Share on social media:</strong> Post your referral link on your business social media accounts.
                  </div>
                </li>
                <li className="flex gap-3">
                  <div className="bg-green-100 text-green-800 rounded-full min-w-8 h-8 flex items-center justify-center mt-0.5">✓</div>
                  <div>
                    <strong>Highlight the benefits:</strong> Tell potential referrals about the new customer leads they can gain through JobOn.
                  </div>
                </li>
                <li className="flex gap-3">
                  <div className="bg-green-100 text-green-800 rounded-full min-w-8 h-8 flex items-center justify-center mt-0.5">✓</div>
                  <div>
                    <strong>Industry meetups:</strong> Share your success with JobOn at local industry events and gatherings.
                  </div>
                </li>
              </ul>
            </CardContent>
          </Card>
        </div>
      </div>
    </ProviderDashboardLayout>
  );
};

export default ProviderReferrals;
