import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Link, useNavigate } from 'react-router-dom';
import { Layout } from '@/components/Layout';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Separator } from '@/components/ui/separator';
import { Checkbox } from '@/components/ui/checkbox';
import { Textarea } from '@/components/ui/textarea';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { AlertCircle, ArrowLeft, ArrowRight, Building, Calendar, CheckCircle, FileText, Info, Lock, Mail, MapPin, Phone, Shield, User } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';

// Import plan-related components
import { SubscriptionPlanCard } from '@/components/provider/subscription/SubscriptionPlanCard';

// Step types
type StepType = 'plan' | 'personal' | 'company' | 'services' | 'verification' | 'review';

// Define the schema for the provider signup form
const personalInfoSchema = z.object({
  firstName: z.string().min(2, { message: "First name must be at least 2 characters" }),
  lastName: z.string().min(2, { message: "Last name must be at least 2 characters" }),
  email: z.string().email({ message: "Please enter a valid email address" }),
  phone: z.string().min(10, { message: "Please enter a valid phone number" }),
  password: z.string().min(8, { message: "Password must be at least 8 characters" }),
  confirmPassword: z.string().min(8, { message: "Password must be at least 8 characters" }),
}).refine((data) => data.password === data.confirmPassword, {
  message: "Passwords don't match",
  path: ["confirmPassword"],
});

const companyInfoSchema = z.object({
  companyName: z.string().min(2, { message: "Company name must be at least 2 characters" }),
  companyType: z.string().min(1, { message: "Please select a company type" }),
  yearEstablished: z.string()
    .refine((val) => !isNaN(Number(val)), { message: "Year must be a number" })
    .refine((val) => Number(val) >= 1900 && Number(val) <= new Date().getFullYear(), { 
      message: `Year must be between 1900 and ${new Date().getFullYear()}` 
    }),
  address: z.string().min(5, { message: "Address must be at least 5 characters" }),
  city: z.string().min(2, { message: "City is required" }),
  state: z.string().min(2, { message: "State is required" }),
  zipCode: z.string().min(5, { message: "Please enter a valid zip code" }),
  website: z.string().optional(),
  description: z.string().min(20, { message: "Please provide a brief description of your company (at least 20 characters)" }),
  employeeCount: z.string().min(1, { message: "Please select the number of employees" }),
});

const servicesSchema = z.object({
  categories: z.array(z.string()).min(1, { message: "Select at least one service category" }),
  serviceArea: z.array(z.string()).min(1, { message: "Select at least one service area" }),
  serviceRadius: z.number().min(1, { message: "Please specify your service radius" }),
  experience: z.string().min(1, { message: "Please select your years of experience" }),
  availability: z.array(z.string()).min(1, { message: "Select at least one availability option" }),
});

const verificationSchema = z.object({
  licenseNumber: z.string().optional(),
  insurancePolicy: z.string().optional(),
  taxId: z.string().optional(),
  agreeTOS: z.boolean().refine(val => val === true, {
    message: "You must agree to the terms of service"
  }),
  agreeBackground: z.boolean().refine(val => val === true, {
    message: "You must agree to the background check policy"
  }),
});

type PersonalInfoValues = z.infer<typeof personalInfoSchema>;
type CompanyInfoValues = z.infer<typeof companyInfoSchema>;
type ServicesInfoValues = z.infer<typeof servicesSchema>;
type VerificationValues = z.infer<typeof verificationSchema>;

const ProviderSignup = () => {
  // State for tracking the current step and selected plan
  const [currentStep, setCurrentStep] = useState<StepType>('plan');
  const [selectedPlan, setSelectedPlan] = useState<string>('starter');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { toast } = useToast();
  const navigate = useNavigate();

  // Initialize forms for each step
  const personalInfoForm = useForm<PersonalInfoValues>({
    resolver: zodResolver(personalInfoSchema),
    defaultValues: {
      firstName: '',
      lastName: '',
      email: '',
      phone: '',
      password: '',
      confirmPassword: '',
    }
  });

  const companyInfoForm = useForm<CompanyInfoValues>({
    resolver: zodResolver(companyInfoSchema),
    defaultValues: {
      companyName: '',
      companyType: '',
      yearEstablished: '',
      address: '',
      city: '',
      state: '',
      zipCode: '',
      website: '',
      description: '',
      employeeCount: '',
    }
  });

  const servicesForm = useForm<ServicesInfoValues>({
    resolver: zodResolver(servicesSchema),
    defaultValues: {
      categories: [],
      serviceArea: [],
      serviceRadius: 25,
      experience: '',
      availability: [],
    }
  });

  const verificationForm = useForm<VerificationValues>({
    resolver: zodResolver(verificationSchema),
    defaultValues: {
      licenseNumber: '',
      insurancePolicy: '',
      taxId: '',
      agreeTOS: false,
      agreeBackground: false,
    }
  });

  // Handle plan selection
  const handleSelectPlan = (plan: string) => {
    setSelectedPlan(plan);
    setCurrentStep('personal');
    window.scrollTo(0, 0);
  };

  // Handle form submission for each step
  const handlePersonalInfoSubmit = (data: PersonalInfoValues) => {
    console.log("Personal info submitted:", data);
    setCurrentStep('company');
    window.scrollTo(0, 0);
  };

  const handleCompanyInfoSubmit = (data: CompanyInfoValues) => {
    console.log("Company info submitted:", data);
    setCurrentStep('services');
    window.scrollTo(0, 0);
  };

  const handleServicesSubmit = (data: ServicesInfoValues) => {
    console.log("Services info submitted:", data);
    setCurrentStep('verification');
    window.scrollTo(0, 0);
  };

  const handleVerificationSubmit = (data: VerificationValues) => {
    console.log("Verification info submitted:", data);
    setCurrentStep('review');
    window.scrollTo(0, 0);
  };

  // Final submission
  const handleFinalSubmit = async () => {
    setIsSubmitting(true);
    
    // Combine all form data
    const finalData = {
      plan: selectedPlan,
      personal: personalInfoForm.getValues(),
      company: companyInfoForm.getValues(),
      services: servicesForm.getValues(),
      verification: verificationForm.getValues(),
    };
    
    console.log("Final submission data:", finalData);
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      toast({
        title: "Registration successful!",
        description: "Your application has been submitted for verification. We'll review your information within 24-48 hours.",
      });
      
      // Store basic auth info in localStorage for demo purposes
      localStorage.setItem('isAuthenticated', 'true');
      localStorage.setItem('userName', `${finalData.personal.firstName} ${finalData.personal.lastName}`);
      localStorage.setItem('userEmail', finalData.personal.email);
      localStorage.setItem('userType', 'professional');
      
      // Redirect to the provider onboarding workflow
      navigate('/pro-workflow');
    } catch (error) {
      console.error("Registration error:", error);
      toast({
        variant: "destructive",
        title: "Registration failed",
        description: "There was an error submitting your application. Please try again later.",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Navigate to previous step
  const handlePrevStep = () => {
    if (currentStep === 'personal') setCurrentStep('plan');
    else if (currentStep === 'company') setCurrentStep('personal');
    else if (currentStep === 'services') setCurrentStep('company');
    else if (currentStep === 'verification') setCurrentStep('services');
    else if (currentStep === 'review') setCurrentStep('verification');
    
    window.scrollTo(0, 0);
  };

  // Company type options
  const companyTypes = [
    { value: 'soleProprietor', label: 'Sole Proprietor' },
    { value: 'llc', label: 'LLC' },
    { value: 'corporation', label: 'Corporation' },
    { value: 'partnership', label: 'Partnership' },
    { value: 'other', label: 'Other' },
  ];

  // Employee count options
  const employeeCountOptions = [
    { value: 'justMe', label: 'Just me' },
    { value: '2-5', label: '2-5 employees' },
    { value: '6-10', label: '6-10 employees' },
    { value: '11-25', label: '11-25 employees' },
    { value: '26+', label: '26+ employees' },
  ];

  // Service categories
  const serviceCategories = [
    { value: 'cleaning', label: 'Cleaning' },
    { value: 'electrician', label: 'Electrician' },
    { value: 'plumbing', label: 'Plumbing' },
    { value: 'landscaping', label: 'Landscaping' },
    { value: 'roofing', label: 'Roofing' },
    { value: 'handyman', label: 'Handyman' },
    { value: 'hvac', label: 'HVAC' },
    { value: 'pest', label: 'Pest Control' },
    { value: 'appliance', label: 'Appliance Repair' },
    { value: 'painting', label: 'Painting' },
    { value: 'flooring', label: 'Flooring' },
    { value: 'remodeling', label: 'Remodeling' },
  ];

  // Experience options
  const experienceOptions = [
    { value: '0-1', label: 'Less than 1 year' },
    { value: '1-3', label: '1-3 years' },
    { value: '3-5', label: '3-5 years' },
    { value: '5-10', label: '5-10 years' },
    { value: '10+', label: '10+ years' },
  ];

  // Availability options
  const availabilityOptions = [
    { id: 'weekdays', label: 'Weekdays' },
    { id: 'weekends', label: 'Weekends' },
    { id: 'evenings', label: 'Evenings' },
    { id: 'emergency', label: 'Emergency/24hr Service' },
  ];

  // Service area options (nearby cities/areas)
  const serviceAreas = [
    { id: 'local', label: 'Local area only' },
    { id: 'county', label: 'Entire county' },
    { id: 'statewide', label: 'Statewide' },
    { id: 'multistate', label: 'Multi-state' },
  ];

  // Step indicators
  const steps = [
    { id: 'plan', label: 'Select Plan' },
    { id: 'personal', label: 'Personal Info' },
    { id: 'company', label: 'Company Details' },
    { id: 'services', label: 'Services' },
    { id: 'verification', label: 'Verification' },
    { id: 'review', label: 'Review' },
  ];

  // Render subscription plans
  const renderPlans = () => {
    const planFeatures = {
      starter: [
        { included: true, text: 'Basic profile listing' },
        { included: true, text: 'Bid on up to 10 jobs monthly' },
        { included: true, text: 'Customer reviews & ratings' },
        { included: false, text: 'Featured in search results' },
        { included: false, text: 'Customer contact info access' },
        { included: false, text: 'Priority customer support' },
      ],
      pro: [
        { included: true, text: 'Enhanced profile with portfolio' },
        { included: true, text: 'Bid on up to 50 jobs monthly' },
        { included: true, text: 'Customer reviews & ratings' },
        { included: true, text: 'Featured in search results' },
        { included: true, text: 'Customer contact info access' },
        { included: false, text: 'Priority customer support' },
      ],
      elite: [
        { included: true, text: 'Premium profile with verification badge' },
        { included: true, text: 'Unlimited job bids' },
        { included: true, text: 'Customer reviews & ratings' },
        { included: true, text: 'Top placement in search results' },
        { included: true, text: 'Customer contact info access' },
        { included: true, text: 'Priority customer support' },
      ],
    };

    return (
      <div className="space-y-6">
        <div className="text-center mb-8">
          <h2 className="text-2xl font-bold">Choose Your Subscription Plan</h2>
          <p className="text-muted-foreground">
            Select the plan that best fits your business needs
          </p>
        </div>
        
        <div className="grid md:grid-cols-3 gap-6">
          <SubscriptionPlanCard
            title="Starter"
            price="Free"
            description="Perfect for those just getting started"
            commission="15%"
            features={planFeatures.starter}
            ctaText="Select Starter Plan"
            onSelect={() => handleSelectPlan('starter')}
            highlighted={selectedPlan === 'starter'}
          />
          
          <SubscriptionPlanCard
            title="Pro"
            price="$29.99"
            description="For growing service businesses"
            commission="10%"
            features={planFeatures.pro}
            ctaText="Select Pro Plan"
            onSelect={() => handleSelectPlan('pro')}
            highlighted={selectedPlan === 'pro'}
            isPopular={true}
          />
          
          <SubscriptionPlanCard
            title="Elite"
            price="$79.99"
            description="For established professionals"
            commission="8%"
            features={planFeatures.elite}
            ctaText="Select Elite Plan"
            onSelect={() => handleSelectPlan('elite')}
            highlighted={selectedPlan === 'elite'}
          />
        </div>
      </div>
    );
  };

  // Render personal information form
  const renderPersonalInfo = () => {
    return (
      <Form {...personalInfoForm}>
        <form onSubmit={personalInfoForm.handleSubmit(handlePersonalInfoSubmit)} className="space-y-6">
          <div className="grid md:grid-cols-2 gap-6">
            <FormField
              control={personalInfoForm.control}
              name="firstName"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>First Name</FormLabel>
                  <FormControl>
                    <Input placeholder="John" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            
            <FormField
              control={personalInfoForm.control}
              name="lastName"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Last Name</FormLabel>
                  <FormControl>
                    <Input placeholder="Doe" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
          
          <FormField
            control={personalInfoForm.control}
            name="email"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Email Address</FormLabel>
                <FormControl>
                  <Input type="email" placeholder="<EMAIL>" {...field} />
                </FormControl>
                <FormDescription>
                  We'll send your verification link to this email
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
          
          <FormField
            control={personalInfoForm.control}
            name="phone"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Phone Number</FormLabel>
                <FormControl>
                  <Input placeholder="(*************" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          
          <div className="grid md:grid-cols-2 gap-6">
            <FormField
              control={personalInfoForm.control}
              name="password"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Password</FormLabel>
                  <FormControl>
                    <Input type="password" placeholder="••••••••" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            
            <FormField
              control={personalInfoForm.control}
              name="confirmPassword"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Confirm Password</FormLabel>
                  <FormControl>
                    <Input type="password" placeholder="••••••••" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
          
          <div className="flex justify-between pt-4">
            <Button type="button" variant="outline" onClick={handlePrevStep}>
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Plans
            </Button>
            <Button type="submit">
              Continue to Company Info
              <ArrowRight className="ml-2 h-4 w-4" />
            </Button>
          </div>
        </form>
      </Form>
    );
  };

  // Render company information form
  const renderCompanyInfo = () => {
    return (
      <Form {...companyInfoForm}>
        <form onSubmit={companyInfoForm.handleSubmit(handleCompanyInfoSubmit)} className="space-y-6">
          <FormField
            control={companyInfoForm.control}
            name="companyName"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Company Name</FormLabel>
                <FormControl>
                  <Input placeholder="Your Company LLC" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          
          <div className="grid md:grid-cols-2 gap-6">
            <FormField
              control={companyInfoForm.control}
              name="companyType"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Company Type</FormLabel>
                  <Select onValueChange={field.onChange} defaultValue={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select company type" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {companyTypes.map((type) => (
                        <SelectItem key={type.value} value={type.value}>
                          {type.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />
            
            <FormField
              control={companyInfoForm.control}
              name="yearEstablished"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Year Established</FormLabel>
                  <FormControl>
                    <Input placeholder={new Date().getFullYear().toString()} {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
          
          <FormField
            control={companyInfoForm.control}
            name="address"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Street Address</FormLabel>
                <FormControl>
                  <Input placeholder="123 Main Street" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          
          <div className="grid md:grid-cols-3 gap-6">
            <FormField
              control={companyInfoForm.control}
              name="city"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>City</FormLabel>
                  <FormControl>
                    <Input placeholder="New York" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            
            <FormField
              control={companyInfoForm.control}
              name="state"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>State</FormLabel>
                  <FormControl>
                    <Input placeholder="NY" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            
            <FormField
              control={companyInfoForm.control}
              name="zipCode"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>ZIP Code</FormLabel>
                  <FormControl>
                    <Input placeholder="10001" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
          
          <FormField
            control={companyInfoForm.control}
            name="website"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Company Website (Optional)</FormLabel>
                <FormControl>
                  <Input placeholder="https://yourcompany.com" {...field} />
                </FormControl>
                <FormDescription>
                  Having a website adds credibility to your profile
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
          
          <FormField
            control={companyInfoForm.control}
            name="description"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Business Description</FormLabel>
                <FormControl>
                  <Textarea 
                    placeholder="Tell potential customers about your business, expertise, and what makes you unique..." 
                    className="min-h-[120px]"
                    {...field} 
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          
          <FormField
            control={companyInfoForm.control}
            name="employeeCount"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Number of Employees</FormLabel>
                <Select onValueChange={field.onChange} defaultValue={field.value}>
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Select number of employees" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {employeeCountOptions.map((option) => (
                      <SelectItem key={option.value} value={option.value}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />
          
          <div className="flex justify-between pt-4">
            <Button type="button" variant="outline" onClick={handlePrevStep}>
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Personal Info
            </Button>
            <Button type="submit">
              Continue to Services
              <ArrowRight className="ml-2 h-4 w-4" />
            </Button>
          </div>
        </form>
      </Form>
    );
  };

  // Render services form
  const renderServices = () => {
    return (
      <Form {...servicesForm}>
        <form onSubmit={servicesForm.handleSubmit(handleServicesSubmit)} className="space-y-6">
          <FormField
            control={servicesForm.control}
            name="categories"
            render={() => (
              <FormItem>
                <div className="mb-4">
                  <FormLabel>Service Categories</FormLabel>
                  <FormDescription>
                    Select all services that your business provides
                  </FormDescription>
                </div>
                <div className="grid md:grid-cols-3 gap-3">
                  {serviceCategories.map((category) => (
                    <FormField
                      key={category.value}
                      control={servicesForm.control}
                      name="categories"
                      render={({ field }) => {
                        const isChecked = field.value?.includes(category.value);
                        return (
                          <FormItem
                            key={category.value}
                            className={`flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4 cursor-pointer ${
                              isChecked ? 'bg-primary/5 border-primary' : ''
                            }`}
                            onClick={() => {
                              const updatedValue = isChecked
                                ? field.value?.filter((value) => value !== category.value)
                                : [...field.value, category.value];
                              field.onChange(updatedValue);
                            }}
                          >
                            <FormControl>
                              <Checkbox
                                checked={isChecked}
                                onCheckedChange={(checked) => {
                                  const updatedValue = checked
                                    ? [...field.value, category.value]
                                    : field.value?.filter((value) => value !== category.value);
                                  field.onChange(updatedValue);
                                }}
                                onClick={(e) => e.stopPropagation()}
                              />
                            </FormControl>
                            <FormLabel className="font-normal cursor-pointer">
                              {category.label}
                            </FormLabel>
                          </FormItem>
                        );
                      }}
                    />
                  ))}
                </div>
                <FormMessage />
              </FormItem>
            )}
          />
          
          <div className="grid md:grid-cols-2 gap-6">
            <FormField
              control={servicesForm.control}
              name="serviceArea"
              render={() => (
                <FormItem>
                  <div className="mb-4">
                    <FormLabel>Service Area</FormLabel>
                    <FormDescription>
                      How far are you willing to travel for jobs?
                    </FormDescription>
                  </div>
                  <div className="grid gap-3">
                    {serviceAreas.map((area) => (
                      <FormField
                        key={area.id}
                        control={servicesForm.control}
                        name="serviceArea"
                        render={({ field }) => {
                          const isChecked = field.value?.includes(area.id);
                          return (
                            <FormItem
                              key={area.id}
                              className={`flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4 cursor-pointer ${
                                isChecked ? 'bg-primary/5 border-primary' : ''
                              }`}
                              onClick={() => {
                                const updatedValue = isChecked
                                  ? field.value?.filter((value) => value !== area.id)
                                  : [...field.value, area.id];
                                field.onChange(updatedValue);
                              }}
                            >
                              <FormControl>
                                <Checkbox
                                  checked={isChecked}
                                  onCheckedChange={(checked) => {
                                    const updatedValue = checked
                                      ? [...field.value, area.id]
                                      : field.value?.filter((value) => value !== area.id);
                                    field.onChange(updatedValue);
                                  }}
                                  onClick={(e) => e.stopPropagation()}
                                />
                              </FormControl>
                              <FormLabel className="font-normal cursor-pointer">
                                {area.label}
                              </FormLabel>
                            </FormItem>
                          );
                        }}
                      />
                    ))}
                  </div>
                  <FormMessage />
                </FormItem>
              )}
            />
            
            <FormField
              control={servicesForm.control}
              name="serviceRadius"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Service Radius (miles)</FormLabel>
                  <FormControl>
                    <Input
                      type="number"
                      {...field}
                      onChange={(e) => field.onChange(Number(e.target.value))}
                    />
                  </FormControl>
                  <FormDescription>
                    Maximum distance you're willing to travel from your business address
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
          
          <FormField
            control={servicesForm.control}
            name="experience"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Years of Experience</FormLabel>
                <Select onValueChange={field.onChange} defaultValue={field.value}>
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Select years of experience" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {experienceOptions.map((option) => (
                      <SelectItem key={option.value} value={option.value}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />
          
          <FormField
            control={servicesForm.control}
            name="availability"
            render={() => (
              <FormItem>
                <div className="mb-4">
                  <FormLabel>Availability</FormLabel>
                  <FormDescription>
                    Select all times when you're typically available for work
                  </FormDescription>
                </div>
                <div className="grid md:grid-cols-2 gap-3">
                  {availabilityOptions.map((option) => (
                    <FormField
                      key={option.id}
                      control={servicesForm.control}
                      name="availability"
                      render={({ field }) => {
                        const isChecked = field.value?.includes(option.id);
                        return (
                          <FormItem
                            key={option.id}
                            className={`flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4 cursor-pointer ${
                              isChecked ? 'bg-primary/5 border-primary' : ''
                            }`}
                            onClick={() => {
                              const updatedValue = isChecked
                                ? field.value?.filter((value) => value !== option.id)
                                : [...field.value, option.id];
                              field.onChange(updatedValue);
                            }}
                          >
                            <FormControl>
                              <Checkbox
                                checked={isChecked}
                                onCheckedChange={(checked) => {
                                  const updatedValue = checked
                                    ? [...field.value, option.id]
                                    : field.value?.filter((value) => value !== option.id);
                                  field.onChange(updatedValue);
                                }}
                                onClick={(e) => e.stopPropagation()}
                              />
                            </FormControl>
                            <FormLabel className="font-normal cursor-pointer">
                              {option.label}
                            </FormLabel>
                          </FormItem>
                        );
                      }}
                    />
                  ))}
                </div>
                <FormMessage />
              </FormItem>
            )}
          />
          
          <div className="flex justify-between pt-4">
            <Button type="button" variant="outline" onClick={handlePrevStep}>
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Company Info
            </Button>
            <Button type="submit">
              Continue to Verification
              <ArrowRight className="ml-2 h-4 w-4" />
            </Button>
          </div>
        </form>
      </Form>
    );
  };

  // Render verification form
  const renderVerification = () => {
    return (
      <Form {...verificationForm}>
        <form onSubmit={verificationForm.handleSubmit(handleVerificationSubmit)} className="space-y-6">
          <Alert className="bg-blue-50 border-blue-200">
            <Info className="h-4 w-4 text-blue-500" />
            <AlertTitle className="text-blue-800">Verification enhances your credibility</AlertTitle>
            <AlertDescription className="text-blue-700">
              Providers with verified credentials receive up to 75% more job requests and have a higher success rate with bids.
            </AlertDescription>
          </Alert>
          
          <FormField
            control={verificationForm.control}
            name="licenseNumber"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Professional License Number (Optional)</FormLabel>
                <FormControl>
                  <Input placeholder="e.g., **********" {...field} />
                </FormControl>
                <FormDescription>
                  If your profession requires licensing, please provide your license number
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
          
          <FormField
            control={verificationForm.control}
            name="insurancePolicy"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Insurance Policy (Optional)</FormLabel>
                <FormControl>
                  <Input placeholder="e.g., ABC12345678" {...field} />
                </FormControl>
                <FormDescription>
                  If you have an insurance policy, please provide the policy number
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
          
          <FormField
            control={verificationForm.control}
            name="taxId"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Tax ID (Optional)</FormLabel>
                <FormControl>
                  <Input placeholder="e.g., 123-45-6789" {...field} />
                </FormControl>
                <FormDescription>
                  If you have a tax ID, please provide the ID number
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
          
          <FormField
            control={verificationForm.control}
            name="agreeTOS"
            render={({ field }) => (
              <FormItem>
                <FormLabel>
                  <Checkbox
                    checked={field.value}
                    onCheckedChange={field.onChange}
                  />
                  I agree to the terms of service
                </FormLabel>
                <FormMessage />
              </FormItem>
            )}
          />
          
          <FormField
            control={verificationForm.control}
            name="agreeBackground"
            render={({ field }) => (
              <FormItem>
                <FormLabel>
                  <Checkbox
                    checked={field.value}
                    onCheckedChange={field.onChange}
                  />
                  I agree to the background check policy
                </FormLabel>
                <FormMessage />
              </FormItem>
            )}
          />
          
          <div className="flex justify-between pt-4">
            <Button type="button" variant="outline" onClick={handlePrevStep}>
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Services
            </Button>
            <Button type="submit">
              Submit
              <ArrowRight className="ml-2 h-4 w-4" />
            </Button>
          </div>
        </form>
      </Form>
    );
  };

  return (
    <Layout>
      <div className="container mx-auto px-4 py-8">
        {currentStep === 'plan' && renderPlans()}
        {currentStep === 'personal' && renderPersonalInfo()}
        {currentStep === 'company' && renderCompanyInfo()}
        {currentStep === 'services' && renderServices()}
        {currentStep === 'verification' && renderVerification()}
        {currentStep === 'review' && (
          <div className="flex justify-center">
            <Button onClick={handleFinalSubmit}>
              Submit
            </Button>
          </div>
        )}
      </div>
    </Layout>
  );
};

export default ProviderSignup;
