
// src/pages/PlumbingProfessionals.tsx
import React, { useEffect } from 'react';
import ProfessionalsPage from "@/components/ProfessionalsPage/ProfessionalsPage.tsx";
import { useGeolocation } from '@/hooks/use-geolocation';
import { useSearchParams, useNavigate } from 'react-router-dom';

const PlumbingProfessionals = () => {
  const { zipCode: detectedZipCode, loading } = useGeolocation();
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  
  // If we have a detected zipCode and there's no zip in the URL, add it
  useEffect(() => {
    if (detectedZipCode && !loading && !searchParams.has('zip')) {
      console.log("Auto-applying detected zipcode to plumbing search:", detectedZipCode);
      const newSearchParams = new URLSearchParams(searchParams);
      newSearchParams.set('zip', detectedZipCode);
      navigate(`?${newSearchParams.toString()}`, { replace: true });
    }
  }, [detectedZipCode, loading, searchParams, navigate]);

  return (
    <ProfessionalsPage
      serviceId="plumbing"
      serviceName="Plumbers"
      pageTitle="Top-Rated Plumbers Near You | Compare Local Plumbing Bids"
      pageDescription="Find licensed plumbers near you for repairs, installations, or emergencies. Compare bids, check reviews, and book trusted professionals through JobON today."
      gradientBackground="linear-gradient(to bottom, #eff6ff 0%, #ffffff 100%)"
    />
  );
};

export default PlumbingProfessionals;
