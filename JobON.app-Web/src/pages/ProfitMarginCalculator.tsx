
import React, { useState, useEffect, useRef } from 'react';
import { Layout } from '@/components/Layout';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Slider } from '@/components/ui/slider';
import { ArrowLeft, HelpCircle, FileText, Calculator, Share2, DollarSign, Percent, TrendingUp, Download, Eye } from 'lucide-react';
import { Link } from 'react-router-dom';
import { Separator } from '@/components/ui/separator';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Tabs, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from '@/components/ui/badge';
import {SEO} from "@/components/SEO.tsx";

const ProfitMarginCalculator: React.FC = () => {
  // State for form inputs
  const [revenue, setRevenue] = useState<number>(1000);
  const [costs, setCosts] = useState<number>(700);
  const [taxRate, setTaxRate] = useState<number>(20);
  const [companyName, setCompanyName] = useState<string>('');
  const [businessType, setBusinessType] = useState<string>('');
  
  // Calculated values
  const [grossProfit, setGrossProfit] = useState<number>(0);
  const [grossMargin, setGrossMargin] = useState<number>(0);
  const [netProfit, setNetProfit] = useState<number>(0);
  const [netMargin, setNetMargin] = useState<number>(0);
  const [taxAmount, setTaxAmount] = useState<number>(0);
  const [reportDate, setReportDate] = useState<string>('');

  // Set report date on component mount
  useEffect(() => {
    const now = new Date();
    setReportDate(now.toLocaleDateString('en-US', { 
      year: 'numeric', 
      month: 'long', 
      day: 'numeric' 
    }));
  }, []);

  // Calculate values when inputs change
  useEffect(() => {
    // Calculate gross profit
    const calculatedGrossProfit = revenue - costs;
    setGrossProfit(calculatedGrossProfit);
    
    // Calculate gross margin percentage
    const calculatedGrossMargin = revenue > 0 ? (calculatedGrossProfit / revenue) * 100 : 0;
    setGrossMargin(calculatedGrossMargin);
    
    // Calculate tax amount
    const calculatedTaxAmount = Math.max(0, calculatedGrossProfit * (taxRate / 100));
    setTaxAmount(calculatedTaxAmount);
    
    // Calculate net profit after tax
    const calculatedNetProfit = calculatedGrossProfit - calculatedTaxAmount;
    setNetProfit(calculatedNetProfit);
    
    // Calculate net margin percentage
    const calculatedNetMargin = revenue > 0 ? (calculatedNetProfit / revenue) * 100 : 0;
    setNetMargin(calculatedNetMargin);

  }, [revenue, costs, taxRate]);

  // Reference to PDF example for download simulation
  const pdfExample = useRef<HTMLDivElement>(null);

  // Function to simulate downloading a PDF
  const handleDownloadPDF = () => {
    alert("In a production environment, this would download a PDF report with your profit margin details.");
  };

  // Function to simulate sharing results
  const handleShareResults = () => {
    alert("In a production environment, this would open sharing options for your profit margin results.");
  };

  return (
    <Layout>
      <SEO
          title="Profit Margin Calculator | Calculate Gross and Net Profit Fast"
          description="Quickly calculate your gross and net profit margins with JobON’s free Profit Margin Calculator. Enter your revenue and costs to get instant results today!"
          localBusinessSchema={true}
          serviceType="profit calculator"
          serviceSlug="profit-calculator"
          canonicalUrl="/free-tools/profit-calculator"
      />
      <div className="pt-28 pb-24 px-6 md:px-12 bg-gradient-to-b from-gray-50 to-white dark:from-gray-900 dark:to-gray-800">
        <div className="container mx-auto max-w-5xl">
          {/* Header with back button */}
          <div className="mb-8">
            <Link to="/free-tools" className="inline-flex items-center text-primary hover:underline mb-4 text-lg">
              <ArrowLeft className="mr-2 h-5 w-5" />
              Back to Free Tools
            </Link>
          
            {/* Calculator Title */}
            <div className="flex flex-col space-y-4">
              <Badge className="w-fit text-base py-1 px-4 bg-primary/10 text-primary border-none">Free Tool</Badge>
              <h1 className="text-4xl md:text-5xl font-bold tracking-tight">Profit Margin Calculator</h1>
              <p className="text-xl text-foreground/80 max-w-3xl leading-relaxed">
                Calculate your business's profit margins with our easy-to-use calculator. Enter your revenue and costs to see your gross and net profit margins.
              </p>
            </div>
          </div>

          <Tabs defaultValue="calculator" className="w-full">
            <TabsList className="mb-8 grid w-full max-w-lg grid-cols-2 mx-auto">
              <TabsTrigger value="calculator" className="text-lg py-3">Calculator</TabsTrigger>
              <TabsTrigger value="about" className="text-lg py-3">About Profit Margins</TabsTrigger>
            </TabsList>
            
            <TabsContent value="calculator">
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-10">
                {/* Calculator Inputs */}
                <div className="lg:col-span-2">
                  <Card className="border-2 shadow-md hover:shadow-lg transition-all duration-300">
                    <CardHeader className="bg-gradient-to-r from-primary/5 to-primary/10 rounded-t-lg">
                      <CardTitle className="flex items-center text-2xl">
                        <Calculator className="mr-3 h-6 w-6 text-primary" />
                        Enter Your Numbers
                      </CardTitle>
                      <CardDescription className="text-base">Input your revenue and costs to calculate your profit margins</CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-8 pt-6">
                      {/* Business Info Section */}
                      <div className="space-y-4">
                        <h3 className="text-sm font-medium text-gray-500">BUSINESS INFORMATION (OPTIONAL)</h3>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div>
                            <Label htmlFor="companyName" className="text-base mb-1">Company Name</Label>
                            <Input 
                              id="companyName" 
                              placeholder="Your Company Name"
                              value={companyName}
                              className="text-lg py-6"
                              onChange={(e) => setCompanyName(e.target.value)}
                            />
                          </div>
                          <div>
                            <Label htmlFor="businessType" className="text-base mb-1">Type of Business</Label>
                            <Input 
                              id="businessType" 
                              placeholder="e.g. Retail, Service, Manufacturing"
                              value={businessType}
                              className="text-lg py-6"
                              onChange={(e) => setBusinessType(e.target.value)}
                            />
                          </div>
                        </div>
                      </div>
                      
                      <Separator className="my-2" />

                      {/* Revenue */}
                      <div className="space-y-4">
                        <div className="flex justify-between items-center">
                          <Label htmlFor="revenue" className="text-lg flex items-center font-medium">
                            Revenue 
                            <TooltipProvider>
                              <Tooltip>
                                <TooltipTrigger asChild>
                                  <HelpCircle className="ml-2 h-5 w-5 text-muted-foreground" />
                                </TooltipTrigger>
                                <TooltipContent className="p-3">
                                  <p className="max-w-xs">Total income from sales before any expenses</p>
                                </TooltipContent>
                              </Tooltip>
                            </TooltipProvider>
                          </Label>
                          <span className="text-right font-medium text-lg text-blue-600">${revenue.toLocaleString()}</span>
                        </div>
                        <div className="flex items-center gap-6">
                          <Slider 
                            min={0} 
                            max={5000} 
                            step={50} 
                            defaultValue={[1000]} 
                            value={[revenue]}
                            className="flex-1"
                            onValueChange={(value) => setRevenue(value[0])}
                          />
                          <Input 
                            type="number" 
                            id="revenue" 
                            value={revenue}
                            className="w-32 text-lg" 
                            onChange={(e) => setRevenue(Number(e.target.value))}
                          />
                        </div>
                      </div>
                      
                      {/* Costs */}
                      <div className="space-y-4">
                        <div className="flex justify-between items-center">
                          <Label htmlFor="costs" className="text-lg flex items-center font-medium">
                            Costs
                            <TooltipProvider>
                              <Tooltip>
                                <TooltipTrigger asChild>
                                  <HelpCircle className="ml-2 h-5 w-5 text-muted-foreground" />
                                </TooltipTrigger>
                                <TooltipContent className="p-3">
                                  <p className="max-w-xs">Total costs including direct costs, labor, materials, etc.</p>
                                </TooltipContent>
                              </Tooltip>
                            </TooltipProvider>
                          </Label>
                          <span className="text-right font-medium text-lg text-red-500">${costs.toLocaleString()}</span>
                        </div>
                        <div className="flex items-center gap-6">
                          <Slider 
                            min={0} 
                            max={5000} 
                            step={50} 
                            defaultValue={[700]} 
                            value={[costs]}
                            className="flex-1"
                            onValueChange={(value) => setCosts(value[0])}
                          />
                          <Input 
                            type="number" 
                            id="costs" 
                            value={costs}
                            className="w-32 text-lg" 
                            onChange={(e) => setCosts(Number(e.target.value))}
                          />
                        </div>
                      </div>
                      
                      {/* Tax Rate */}
                      <div className="space-y-4">
                        <div className="flex justify-between items-center">
                          <Label htmlFor="taxRate" className="text-lg flex items-center font-medium">
                            Tax Rate 
                            <TooltipProvider>
                              <Tooltip>
                                <TooltipTrigger asChild>
                                  <HelpCircle className="ml-2 h-5 w-5 text-muted-foreground" />
                                </TooltipTrigger>
                                <TooltipContent className="p-3">
                                  <p className="max-w-xs">Your effective tax rate as a percentage</p>
                                </TooltipContent>
                              </Tooltip>
                            </TooltipProvider>
                          </Label>
                          <span className="text-right font-medium text-lg text-gray-700">{taxRate}%</span>
                        </div>
                        <div className="flex items-center gap-6">
                          <Slider 
                            min={0} 
                            max={50} 
                            step={1} 
                            defaultValue={[20]} 
                            value={[taxRate]}
                            className="flex-1"
                            onValueChange={(value) => setTaxRate(value[0])}
                          />
                          <Input 
                            type="number" 
                            id="taxRate" 
                            value={taxRate}
                            className="w-32 text-lg" 
                            onChange={(e) => setTaxRate(Number(e.target.value))}
                          />
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>

                {/* Results Card */}
                <div>
                  <Card className="sticky top-24 border-2 shadow-lg bg-white dark:bg-gray-800">
                    <CardHeader className="pb-2 bg-gradient-to-r from-primary/5 to-primary/10 rounded-t-lg">
                      <CardTitle className="flex items-center text-2xl">
                        <TrendingUp className="mr-3 h-6 w-6 text-primary" />
                        Profit Margins
                      </CardTitle>
                      <CardDescription className="text-base">Based on your inputs</CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-6 pt-6">
                      {/* Gross Margin */}
                      <div className="text-center py-6 bg-primary/5 rounded-lg border border-primary/10">
                        <div className="text-5xl font-bold text-primary mb-1">
                          {grossMargin.toFixed(1)}%
                        </div>
                        <p className="text-base text-muted-foreground">Gross Profit Margin</p>
                      </div>
                      
                      <Separator className="my-2" />
                      
                      <div className="space-y-4">
                        <div className="flex justify-between py-1">
                          <span className="text-base text-muted-foreground flex items-center">
                            <DollarSign className="h-5 w-5 mr-1" /> Gross Profit:
                          </span>
                          <span className="font-medium text-lg">${grossProfit.toFixed(2)}</span>
                        </div>
                        <div className="flex justify-between py-1">
                          <span className="text-base text-muted-foreground flex items-center">
                            <DollarSign className="h-5 w-5 mr-1" /> Tax Amount:
                          </span>
                          <span className="font-medium text-lg">${taxAmount.toFixed(2)}</span>
                        </div>
                        <div className="flex justify-between py-1">
                          <span className="text-base text-muted-foreground flex items-center">
                            <DollarSign className="h-5 w-5 mr-1" /> Net Profit:
                          </span>
                          <span className="font-medium text-lg">${netProfit.toFixed(2)}</span>
                        </div>
                        <div className="flex justify-between py-1">
                          <span className="text-base text-muted-foreground flex items-center">
                            <Percent className="h-5 w-5 mr-1" /> Net Margin:
                          </span>
                          <span className="font-medium text-lg">{netMargin.toFixed(1)}%</span>
                        </div>
                      </div>
                    </CardContent>
                    <CardFooter className="flex-col space-y-3 p-6 pt-2 border-t bg-gray-50 rounded-b-lg">
                      <Dialog>
                        <DialogTrigger asChild>
                          <Button className="w-full text-base py-6" variant="default">
                            <Eye className="mr-2 h-5 w-5" />
                            Preview PDF Report
                          </Button>
                        </DialogTrigger>
                        <DialogContent className="max-w-3xl max-h-[90vh] overflow-auto">
                          <DialogHeader>
                            <DialogTitle>Profit Margin Report Preview</DialogTitle>
                            <DialogDescription>
                              This is how your PDF report will look when downloaded
                            </DialogDescription>
                          </DialogHeader>
                          <div ref={pdfExample} className="p-6 border rounded-lg bg-white">
                            <div className="text-center mb-8">
                              <h1 className="text-2xl font-bold text-gray-800">Profit Margin Analysis</h1>
                              <p className="text-gray-600">{reportDate}</p>
                              {companyName && <p className="font-medium mt-2">{companyName}</p>}
                              {businessType && <p className="text-sm text-gray-600">{businessType}</p>}
                            </div>
                            
                            <div className="mb-8">
                              <h2 className="text-xl font-semibold border-b pb-2 mb-4">Financial Summary</h2>
                              <div className="grid grid-cols-2 gap-4">
                                <div className="border rounded-lg p-4 text-center">
                                  <div className="text-sm text-gray-600">Revenue</div>
                                  <div className="text-2xl font-bold text-blue-600">${revenue.toLocaleString()}</div>
                                </div>
                                <div className="border rounded-lg p-4 text-center">
                                  <div className="text-sm text-gray-600">Costs</div>
                                  <div className="text-2xl font-bold text-red-500">${costs.toLocaleString()}</div>
                                </div>
                              </div>
                            </div>
                            
                            <div className="mb-8">
                              <h2 className="text-xl font-semibold border-b pb-2 mb-4">Profit Analysis</h2>
                              <table className="w-full text-left">
                                <tbody>
                                  <tr className="border-b">
                                    <th className="py-2">Gross Profit</th>
                                    <td className="py-2 text-right">${grossProfit.toFixed(2)}</td>
                                  </tr>
                                  <tr className="border-b">
                                    <th className="py-2">Gross Margin</th>
                                    <td className="py-2 text-right">{grossMargin.toFixed(1)}%</td>
                                  </tr>
                                  <tr className="border-b">
                                    <th className="py-2">Tax Amount (at {taxRate}%)</th>
                                    <td className="py-2 text-right">${taxAmount.toFixed(2)}</td>
                                  </tr>
                                  <tr className="border-b">
                                    <th className="py-2">Net Profit</th>
                                    <td className="py-2 text-right">${netProfit.toFixed(2)}</td>
                                  </tr>
                                  <tr className="border-b">
                                    <th className="py-2">Net Margin</th>
                                    <td className="py-2 text-right">{netMargin.toFixed(1)}%</td>
                                  </tr>
                                </tbody>
                              </table>
                            </div>
                            
                            <div className="mb-8">
                              <h2 className="text-xl font-semibold border-b pb-2 mb-4">Visualization</h2>
                              <div className="flex flex-col space-y-3">
                                <div>
                                  <div className="flex justify-between text-sm mb-1">
                                    <span>Revenue: ${revenue}</span>
                                    <span>100%</span>
                                  </div>
                                  <div className="w-full bg-gray-200 rounded-full h-4">
                                    <div className="bg-blue-600 h-4 rounded-full w-full"></div>
                                  </div>
                                </div>
                                <div>
                                  <div className="flex justify-between text-sm mb-1">
                                    <span>Costs: ${costs}</span>
                                    <span>{(costs/revenue*100).toFixed(1)}%</span>
                                  </div>
                                  <div className="w-full bg-gray-200 rounded-full h-4">
                                    <div 
                                      className="bg-red-500 h-4 rounded-full" 
                                      style={{width: `${(costs/revenue*100).toFixed(1)}%`}}
                                    ></div>
                                  </div>
                                </div>
                                <div>
                                  <div className="flex justify-between text-sm mb-1">
                                    <span>Gross Profit: ${grossProfit.toFixed(2)}</span>
                                    <span>{grossMargin.toFixed(1)}%</span>
                                  </div>
                                  <div className="w-full bg-gray-200 rounded-full h-4">
                                    <div 
                                      className="bg-green-500 h-4 rounded-full" 
                                      style={{width: `${grossMargin.toFixed(1)}%`}}
                                    ></div>
                                  </div>
                                </div>
                                <div>
                                  <div className="flex justify-between text-sm mb-1">
                                    <span>Net Profit: ${netProfit.toFixed(2)}</span>
                                    <span>{netMargin.toFixed(1)}%</span>
                                  </div>
                                  <div className="w-full bg-gray-200 rounded-full h-4">
                                    <div 
                                      className="bg-indigo-500 h-4 rounded-full" 
                                      style={{width: `${netMargin.toFixed(1)}%`}}
                                    ></div>
                                  </div>
                                </div>
                              </div>
                            </div>
                            
                            <div className="text-center text-sm text-gray-500 mt-8">
                              <p>Generated by JobON Profit Margin Calculator</p>
                              <p>{reportDate}</p>
                            </div>
                          </div>
                        </DialogContent>
                      </Dialog>
                      
                      <Button className="w-full text-base py-6" variant="default" onClick={handleDownloadPDF}>
                        <Download className="mr-2 h-5 w-5" />
                        Download as PDF
                      </Button>
                      <Button className="w-full text-base py-6" variant="outline" onClick={handleShareResults}>
                        <Share2 className="mr-2 h-5 w-5" />
                        Share Results
                      </Button>
                    </CardFooter>
                  </Card>
                </div>
              </div>
            </TabsContent>
            
            <TabsContent value="about">
              <Card className="border-2 shadow-md">
                <CardHeader>
                  <CardTitle className="text-2xl">Understanding Profit Margins</CardTitle>
                  <CardDescription className="text-lg">
                    Learn what profit margins mean for your business and how to improve them
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-8">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                    <div>
                      <h3 className="text-xl font-semibold mb-4">What is a Profit Margin?</h3>
                      <p className="text-lg text-gray-700 leading-relaxed">
                        Profit margin is a percentage measurement of profit that expresses the amount a company earns per dollar of sales. 
                        If a company has a 20% profit margin, it means that it keeps $0.20 from each dollar of sales, while spending 
                        $0.80 to produce the product or service.
                      </p>
                      
                      <h3 className="text-xl font-semibold mt-8 mb-4">Types of Profit Margins</h3>
                      <ul className="list-disc pl-5 space-y-3 text-lg text-gray-700">
                        <li><span className="font-medium">Gross Profit Margin:</span> Revenue minus cost of goods sold (COGS), divided by revenue.</li>
                        <li><span className="font-medium">Operating Profit Margin:</span> Revenue minus COGS and operating expenses, divided by revenue.</li>
                        <li><span className="font-medium">Net Profit Margin:</span> Revenue minus all expenses (including taxes and interest), divided by revenue.</li>
                      </ul>
                    </div>
                    
                    <div className="bg-blue-50 p-8 rounded-xl">
                      <h3 className="text-xl font-semibold mb-6">Industry Benchmark Profit Margins</h3>
                      <div className="space-y-6">
                        <div>
                          <div className="flex justify-between text-base mb-2">
                            <span className="font-medium">Retail</span>
                            <span>2-5%</span>
                          </div>
                          <div className="w-full bg-gray-200 rounded-full h-3">
                            <div className="bg-blue-600 h-3 rounded-full" style={{width: '5%'}}></div>
                          </div>
                        </div>
                        <div>
                          <div className="flex justify-between text-base mb-2">
                            <span className="font-medium">Food Service</span>
                            <span>3-9%</span>
                          </div>
                          <div className="w-full bg-gray-200 rounded-full h-3">
                            <div className="bg-blue-600 h-3 rounded-full" style={{width: '9%'}}></div>
                          </div>
                        </div>
                        <div>
                          <div className="flex justify-between text-base mb-2">
                            <span className="font-medium">Manufacturing</span>
                            <span>10-15%</span>
                          </div>
                          <div className="w-full bg-gray-200 rounded-full h-3">
                            <div className="bg-blue-600 h-3 rounded-full" style={{width: '15%'}}></div>
                          </div>
                        </div>
                        <div>
                          <div className="flex justify-between text-base mb-2">
                            <span className="font-medium">Software/Tech</span>
                            <span>15-25%</span>
                          </div>
                          <div className="w-full bg-gray-200 rounded-full h-3">
                            <div className="bg-blue-600 h-3 rounded-full" style={{width: '25%'}}></div>
                          </div>
                        </div>
                        <div>
                          <div className="flex justify-between text-base mb-2">
                            <span className="font-medium">Professional Services</span>
                            <span>20-35%</span>
                          </div>
                          <div className="w-full bg-gray-200 rounded-full h-3">
                            <div className="bg-blue-600 h-3 rounded-full" style={{width: '35%'}}></div>
                          </div>
                        </div>
                      </div>
                      <p className="text-xs text-gray-500 mt-4">* These are approximate ranges and can vary widely based on specific business models and market conditions</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
              
              {/* Profit Tips */}
              <div className="mt-12">
                <h2 className="text-3xl font-semibold mb-8 text-center">Tips to Improve Your Profit Margins</h2>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mt-6">
                  <Card className="border-2 shadow-sm hover:shadow-md transition-all duration-300">
                    <CardHeader className="pb-2">
                      <CardTitle className="text-xl flex items-center">
                        <span className="p-2 mr-3 rounded-full bg-primary/10">
                          <DollarSign className="h-5 w-5 text-primary" />
                        </span>
                        Reduce Operating Costs
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <p className="text-lg text-foreground/80 leading-relaxed">
                        Look for opportunities to reduce unnecessary expenses without compromising quality. Consider negotiating with suppliers for better rates.
                      </p>
                    </CardContent>
                  </Card>
                  <Card className="border-2 shadow-sm hover:shadow-md transition-all duration-300">
                    <CardHeader className="pb-2">
                      <CardTitle className="text-xl flex items-center">
                        <span className="p-2 mr-3 rounded-full bg-primary/10">
                          <TrendingUp className="h-5 w-5 text-primary" />
                        </span>
                        Raise Prices Strategically
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <p className="text-lg text-foreground/80 leading-relaxed">
                        If you provide exceptional value, consider small, strategic price increases. Focus on communicating the value you provide to customers.
                      </p>
                    </CardContent>
                  </Card>
                  <Card className="border-2 shadow-sm hover:shadow-md transition-all duration-300">
                    <CardHeader className="pb-2">
                      <CardTitle className="text-xl flex items-center">
                        <span className="p-2 mr-3 rounded-full bg-primary/10">
                          <Calculator className="h-5 w-5 text-primary" />
                        </span>
                        Track All Expenses
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <p className="text-lg text-foreground/80 leading-relaxed">
                        Maintain detailed records of all expenses to identify areas where costs can be reduced. Use accounting software to automate tracking.
                      </p>
                    </CardContent>
                  </Card>
                </div>
              </div>
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </Layout>
  );
};

export default ProfitMarginCalculator;
