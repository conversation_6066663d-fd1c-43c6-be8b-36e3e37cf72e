
import React from 'react';
import { Layout } from '@/components/Layout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { useToast } from '@/hooks/use-toast';
import { CreditCard, Shield, Clock, CheckCircle, Info, MapPin } from 'lucide-react';
import { SEO } from "@/components/SEO";
import { AddressAutocompleteInput } from '@/components/AddressAutocompleteInput';

const formSchema = z.object({
  fullName: z.string().min(2, 'Full name must be at least 2 characters'),
  email: z.string().email('Please enter a valid email address'),
  phone: z.string().min(10, 'Please enter a valid phone number'),
  address: z.string().min(5, 'Please enter your complete address'),
  city: z.string().min(2, 'City is required'),
  state: z.string().min(2, 'State is required'),
  zipCode: z.string().min(5, 'ZIP code is required'),
  projectType: z.string().min(1, 'Please select a project type'),
  estimatedCost: z.string().min(1, 'Please enter estimated project cost'),
  timeline: z.string().min(1, 'Please select preferred timeline'),
  annualIncome: z.string().min(1, 'Please select your annual income range'),
  employmentStatus: z.string().min(1, 'Please select employment status'),
  financingType: z.string().min(1, 'Please select financing preference'),
  paymentTerms: z.string().min(1, 'Please select preferred payment terms'),
  bnplProvider: z.string().optional(),
  agreeTerms: z.boolean().refine(val => val, 'You must agree to the terms'),
  agreeCredit: z.boolean().refine(val => val, 'You must agree to credit check authorization')
});

type FormData = z.infer<typeof formSchema>;

const FinancingRateCheck = () => {
  const { toast } = useToast();
  
  const form = useForm<FormData>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      agreeTerms: false,
      agreeCredit: false
    }
  });

  const watchFinancingType = form.watch('financingType');
  const watchEstimatedCost = form.watch('estimatedCost');

  // Check if eligible for instant BNPL approval (amounts under $2000)
  const isEligibleForInstantBNPL = () => {
    const cost = watchEstimatedCost;
    if (cost === '500-2000') return true;
    return false;
  };

  const onSubmit = async (data: FormData) => {
    try {
      // Simulate form submission
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      if (data.financingType === 'bnpl' && isEligibleForInstantBNPL()) {
        toast({
          title: "Instantly Pre-Approved!",
          description: "Congratulations! You're pre-approved for Buy Now, Pay Later financing. Complete your project and pay in installments."
        });
      } else {
        toast({
          title: "Application Submitted Successfully!",
          description: "We'll review your information and get back to you within 24 hours with your financing options."
        });
      }
      
      form.reset();
    } catch (error) {
      toast({
        title: "Submission Failed",
        description: "Please try again or contact support for assistance.",
        variant: "destructive"
      });
    }
  };

  const handleAddressSelect = (addressData: any) => {
    form.setValue('address', addressData.address);
    form.setValue('city', addressData.city);
    form.setValue('state', addressData.state);
    form.setValue('zipCode', addressData.zipCode);
  };

  return (
    <>
      <SEO 
        title="Check Your Financing Rate - Buy Now Pay Later Options - JobON" 
        description="Get pre-qualified for home service financing with Buy Now Pay Later options. Quick approval, flexible payment terms, no impact to your credit score." 
        canonicalUrl="/financing/rate-check" 
      />
      <Layout>
        <div className="flex-1 pt-16 md:pt-24 pb-8 md:pb-16 bg-gray-50">
          <div className="container mx-auto px-4 md:px-6 py-6 md:py-[40px]">
            <div className="max-w-4xl mx-auto">
              {/* Header */}
              <div className="text-center mb-8 md:mb-12">
                <div className="flex items-center justify-center mb-4 md:mb-6">
                  <div className="p-3 rounded-full bg-blue-600 shadow-lg">
                    <CreditCard className="h-6 w-6 md:h-8 md:w-8 text-white" />
                  </div>
                </div>
                <h1 className="text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 mb-3 md:mb-4">
                  Check Your Rate
                </h1>
                <p className="text-lg md:text-xl text-gray-600 max-w-2xl mx-auto px-4">
                  Get instant Buy Now, Pay Later approval or traditional financing options. 
                  Checking your rate won't affect your credit score.
                </p>
              </div>

              {/* Features */}
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 md:gap-6 mb-8 md:mb-12">
                <div className="text-center p-3 md:p-0">
                  <Shield className="h-6 w-6 md:h-8 md:w-8 text-blue-600 mx-auto mb-2 md:mb-3" />
                  <h3 className="font-semibold text-gray-900 mb-1 md:mb-2 text-sm md:text-base">Secure & Private</h3>
                  <p className="text-gray-600 text-xs md:text-sm">Your information is encrypted and protected</p>
                </div>
                <div className="text-center p-3 md:p-0">
                  <Clock className="h-6 w-6 md:h-8 md:w-8 text-blue-600 mx-auto mb-2 md:mb-3" />
                  <h3 className="font-semibold text-gray-900 mb-1 md:mb-2 text-sm md:text-base">Instant Approval</h3>
                  <p className="text-gray-600 text-xs md:text-sm">BNPL approval in under 60 seconds</p>
                </div>
                <div className="text-center p-3 md:p-0">
                  <CheckCircle className="h-6 w-6 md:h-8 md:w-8 text-blue-600 mx-auto mb-2 md:mb-3" />
                  <h3 className="font-semibold text-gray-900 mb-1 md:mb-2 text-sm md:text-base">No Credit Impact</h3>
                  <p className="text-gray-600 text-xs md:text-sm">Soft credit check only during pre-qualification</p>
                </div>
                <div className="text-center p-3 md:p-0">
                  <CreditCard className="h-6 w-6 md:h-8 md:w-8 text-blue-600 mx-auto mb-2 md:mb-3" />
                  <h3 className="font-semibold text-gray-900 mb-1 md:mb-2 text-sm md:text-base">Flexible Terms</h3>
                  <p className="text-gray-600 text-xs md:text-sm">Weekly, bi-weekly, or monthly payments</p>
                </div>
              </div>

              {/* BNPL Notice */}
              <Card className="mb-6 md:mb-8 bg-green-50 border-green-200">
                <CardContent className="p-4 md:p-6">
                  <div className="flex items-start space-x-3">
                    <CheckCircle className="h-5 w-5 md:h-6 md:w-6 text-green-600 mt-0.5 flex-shrink-0" />
                    <div>
                      <h3 className="font-semibold text-green-900 mb-2 text-sm md:text-base">Buy Now, Pay Later Available!</h3>
                      <p className="text-green-800 text-sm md:text-base">
                        Get instant approval for projects under $2,000 with flexible payment options. 
                        Pay in 4 interest-free installments or choose weekly/monthly plans.
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Form */}
              <Card className="shadow-lg">
                <CardHeader className="p-4 md:p-6">
                  <CardTitle className="text-lg md:text-xl">Financing Application</CardTitle>
                  <CardDescription className="text-sm md:text-base">
                    Complete the form below to check your BNPL and traditional financing options
                  </CardDescription>
                </CardHeader>
                <CardContent className="p-4 md:p-6 pt-0">
                  <Form {...form}>
                    <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                      {/* Personal Information */}
                      <div className="space-y-4">
                        <h3 className="text-base md:text-lg font-semibold text-gray-900">Personal Information</h3>
                        
                        <FormField
                          control={form.control}
                          name="fullName"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel className="text-sm md:text-base">Full Name</FormLabel>
                              <FormControl>
                                <Input placeholder="Enter your full name" className="input-touch text-base" {...field} />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                        
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <FormField
                            control={form.control}
                            name="email"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel className="text-sm md:text-base">Email Address</FormLabel>
                                <FormControl>
                                  <Input type="email" placeholder="<EMAIL>" className="input-touch text-base" {...field} />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                          <FormField
                            control={form.control}
                            name="phone"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel className="text-sm md:text-base">Phone Number</FormLabel>
                                <FormControl>
                                  <Input placeholder="(*************" className="input-touch text-base" {...field} />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        </div>

                        <FormField
                          control={form.control}
                          name="address"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel className="text-sm md:text-base">Address</FormLabel>
                              <FormControl>
                                <AddressAutocompleteInput
                                  value={field.value}
                                  onAddressSelect={handleAddressSelect}
                                  placeholder="Start typing your address..."
                                  className="input-touch text-base"
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                          <FormField
                            control={form.control}
                            name="city"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel className="text-sm md:text-base">City</FormLabel>
                                <FormControl>
                                  <Input placeholder="City" className="input-touch text-base bg-gray-50" {...field} readOnly />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                          <FormField
                            control={form.control}
                            name="state"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel className="text-sm md:text-base">State</FormLabel>
                                <FormControl>
                                  <Input placeholder="State" className="input-touch text-base bg-gray-50" {...field} readOnly />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                          <FormField
                            control={form.control}
                            name="zipCode"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel className="text-sm md:text-base">ZIP Code</FormLabel>
                                <FormControl>
                                  <Input placeholder="12345" className="input-touch text-base bg-gray-50" {...field} readOnly />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        </div>
                      </div>

                      {/* Project Information */}
                      <div className="space-y-4">
                        <h3 className="text-base md:text-lg font-semibold text-gray-900">Project Information</h3>
                        
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <FormField
                            control={form.control}
                            name="projectType"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel className="text-sm md:text-base">Project Type</FormLabel>
                                <Select onValueChange={field.onChange} value={field.value}>
                                  <FormControl>
                                    <SelectTrigger className="btn-touch text-base">
                                      <SelectValue placeholder="Select project type" />
                                    </SelectTrigger>
                                  </FormControl>
                                  <SelectContent className="z-50 bg-white border shadow-lg">
                                    <SelectItem value="plumbing">Plumbing</SelectItem>
                                    <SelectItem value="electrical">Electrical</SelectItem>
                                    <SelectItem value="hvac">HVAC</SelectItem>
                                    <SelectItem value="roofing">Roofing</SelectItem>
                                    <SelectItem value="landscaping">Landscaping</SelectItem>
                                    <SelectItem value="cleaning">House Cleaning</SelectItem>
                                    <SelectItem value="handyman">Handyman Services</SelectItem>
                                    <SelectItem value="renovation">Home Renovation</SelectItem>
                                    <SelectItem value="other">Other</SelectItem>
                                  </SelectContent>
                                </Select>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                          
                          <FormField
                            control={form.control}
                            name="estimatedCost"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel className="text-sm md:text-base">Estimated Project Cost</FormLabel>
                                <Select onValueChange={field.onChange} value={field.value}>
                                  <FormControl>
                                    <SelectTrigger className="btn-touch text-base">
                                      <SelectValue placeholder="Select cost range" />
                                    </SelectTrigger>
                                  </FormControl>
                                  <SelectContent className="z-50 bg-white border shadow-lg">
                                    <SelectItem value="500-2000">
                                      <div className="flex items-center justify-between w-full">
                                        <span>$500 - $2,000</span>
                                        <span className="ml-2 text-xs bg-green-100 text-green-800 px-2 py-1 rounded">
                                          BNPL Available
                                        </span>
                                      </div>
                                    </SelectItem>
                                    <SelectItem value="2000-5000">$2,000 - $5,000</SelectItem>
                                    <SelectItem value="5000-10000">$5,000 - $10,000</SelectItem>
                                    <SelectItem value="10000-25000">$10,000 - $25,000</SelectItem>
                                    <SelectItem value="25000+">$25,000+</SelectItem>
                                  </SelectContent>
                                </Select>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        </div>
                        
                        <FormField
                          control={form.control}
                          name="timeline"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel className="text-sm md:text-base">Preferred Timeline</FormLabel>
                              <Select onValueChange={field.onChange} value={field.value}>
                                <FormControl>
                                  <SelectTrigger className="btn-touch text-base">
                                    <SelectValue placeholder="When do you want to start?" />
                                  </SelectTrigger>
                                </FormControl>
                                <SelectContent className="z-50 bg-white border shadow-lg">
                                  <SelectItem value="immediately">Immediately</SelectItem>
                                  <SelectItem value="1-month">Within 1 month</SelectItem>
                                  <SelectItem value="3-months">Within 3 months</SelectItem>
                                  <SelectItem value="6-months">Within 6 months</SelectItem>
                                  <SelectItem value="planning">Just planning</SelectItem>
                                </SelectContent>
                              </Select>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>

                      {/* Financing Preferences */}
                      <div className="space-y-4">
                        <h3 className="text-lg font-semibold text-gray-900">Financing Preferences</h3>
                        
                        <FormField
                          control={form.control}
                          name="financingType"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Preferred Financing Type</FormLabel>
                              <Select onValueChange={field.onChange} value={field.value}>
                                <FormControl>
                                  <SelectTrigger>
                                    <SelectValue placeholder="Select financing preference" />
                                  </SelectTrigger>
                                </FormControl>
                                <SelectContent>
                                  <SelectItem value="bnpl">
                                    Buy Now, Pay Later (BNPL) - Instant approval for qualifying projects
                                  </SelectItem>
                                  <SelectItem value="traditional">
                                    Traditional Financing - Lower rates for larger projects
                                  </SelectItem>
                                  <SelectItem value="both">
                                    Show me both options
                                  </SelectItem>
                                </SelectContent>
                              </Select>
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        {watchFinancingType === 'bnpl' && (
                          <FormField
                            control={form.control}
                            name="bnplProvider"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Preferred BNPL Provider (Optional)</FormLabel>
                                <Select onValueChange={field.onChange} value={field.value}>
                                  <FormControl>
                                    <SelectTrigger>
                                      <SelectValue placeholder="Select preferred provider or leave blank" />
                                    </SelectTrigger>
                                  </FormControl>
                                  <SelectContent>
                                    <SelectItem value="klarna">Klarna</SelectItem>
                                    <SelectItem value="afterpay">Afterpay</SelectItem>
                                    <SelectItem value="sezzle">Sezzle</SelectItem>
                                    <SelectItem value="affirm">Affirm</SelectItem>
                                    <SelectItem value="any">No preference</SelectItem>
                                  </SelectContent>
                                </Select>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        )}

                        <FormField
                          control={form.control}
                          name="paymentTerms"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Preferred Payment Terms</FormLabel>
                              <Select onValueChange={field.onChange} value={field.value}>
                                <FormControl>
                                  <SelectTrigger>
                                    <SelectValue placeholder="Select payment preference" />
                                  </SelectTrigger>
                                </FormControl>
                                <SelectContent>
                                  <SelectItem value="pay-in-4">Pay in 4 (every 2 weeks)</SelectItem>
                                  <SelectItem value="weekly">Weekly payments</SelectItem>
                                  <SelectItem value="bi-weekly">Bi-weekly payments</SelectItem>
                                  <SelectItem value="monthly">Monthly payments</SelectItem>
                                  <SelectItem value="12-months">12 months</SelectItem>
                                  <SelectItem value="24-months">24 months</SelectItem>
                                  <SelectItem value="36-months">36 months</SelectItem>
                                  <SelectItem value="48-months">48 months</SelectItem>
                                  <SelectItem value="60-months">60 months</SelectItem>
                                </SelectContent>
                              </Select>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>

                      {/* Financial Information */}
                      <div className="space-y-4">
                        <h3 className="text-lg font-semibold text-gray-900">Financial Information</h3>
                        <div className="grid md:grid-cols-2 gap-4">
                          <FormField
                            control={form.control}
                            name="annualIncome"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Annual Income</FormLabel>
                                <Select onValueChange={field.onChange} value={field.value}>
                                  <FormControl>
                                    <SelectTrigger>
                                      <SelectValue placeholder="Select income range" />
                                    </SelectTrigger>
                                  </FormControl>
                                  <SelectContent>
                                    <SelectItem value="under-30k">Under $30,000</SelectItem>
                                    <SelectItem value="30k-50k">$30,000 - $50,000</SelectItem>
                                    <SelectItem value="50k-75k">$50,000 - $75,000</SelectItem>
                                    <SelectItem value="75k-100k">$75,000 - $100,000</SelectItem>
                                    <SelectItem value="100k+">$100,000+</SelectItem>
                                  </SelectContent>
                                </Select>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                          <FormField
                            control={form.control}
                            name="employmentStatus"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Employment Status</FormLabel>
                                <Select onValueChange={field.onChange} value={field.value}>
                                  <FormControl>
                                    <SelectTrigger>
                                      <SelectValue placeholder="Select employment status" />
                                    </SelectTrigger>
                                  </FormControl>
                                  <SelectContent>
                                    <SelectItem value="employed">Employed Full-Time</SelectItem>
                                    <SelectItem value="part-time">Employed Part-Time</SelectItem>
                                    <SelectItem value="self-employed">Self-Employed</SelectItem>
                                    <SelectItem value="retired">Retired</SelectItem>
                                    <SelectItem value="student">Student</SelectItem>
                                    <SelectItem value="unemployed">Unemployed</SelectItem>
                                  </SelectContent>
                                </Select>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        </div>
                      </div>

                      {/* Instant Approval Notice */}
                      {isEligibleForInstantBNPL() && watchFinancingType === 'bnpl' && (
                        <Card className="bg-blue-50 border-blue-200">
                          <CardContent className="p-4">
                            <div className="flex items-center space-x-3">
                              <CheckCircle className="h-6 w-6 text-blue-600" />
                              <div>
                                <h4 className="font-semibold text-blue-900">Eligible for Instant Approval!</h4>
                                <p className="text-blue-800 text-sm">
                                  Your project qualifies for instant BNPL approval with flexible payment options.
                                </p>
                              </div>
                            </div>
                          </CardContent>
                        </Card>
                      )}

                      {/* Agreements */}
                      <div className="space-y-4">
                        <h3 className="text-lg font-semibold text-gray-900">Agreements</h3>
                        <FormField
                          control={form.control}
                          name="agreeTerms"
                          render={({ field }) => (
                            <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                              <FormControl>
                                <Checkbox checked={field.value} onCheckedChange={field.onChange} />
                              </FormControl>
                              <div className="space-y-1 leading-none">
                                <FormLabel>
                                  I agree to the Terms of Service and Privacy Policy
                                </FormLabel>
                                <FormMessage />
                              </div>
                            </FormItem>
                          )}
                        />
                        <FormField
                          control={form.control}
                          name="agreeCredit"
                          render={({ field }) => (
                            <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                              <FormControl>
                                <Checkbox checked={field.value} onCheckedChange={field.onChange} />
                              </FormControl>
                              <div className="space-y-1 leading-none">
                                <FormLabel>
                                  I authorize a soft credit check to determine my financing options (this will not affect my credit score)
                                </FormLabel>
                                <FormMessage />
                              </div>
                            </FormItem>
                          )}
                        />
                      </div>

                      <Button 
                        type="submit" 
                        size="lg" 
                        className="w-full btn-touch bg-blue-600 hover:bg-blue-700 text-white text-base md:text-lg font-semibold" 
                        disabled={form.formState.isSubmitting}
                      >
                        {form.formState.isSubmitting 
                          ? "Checking Your Rate..." 
                          : isEligibleForInstantBNPL() && watchFinancingType === 'bnpl'
                            ? "Get Instant BNPL Approval"
                            : "Check My Rate"
                        }
                      </Button>
                    </form>
                  </Form>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </Layout>
    </>
  );
};

export default FinancingRateCheck;
