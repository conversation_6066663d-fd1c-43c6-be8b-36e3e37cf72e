
import React from 'react';
import { Layout } from '@/components/Layout';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { ArrowLeft, Download, ArrowRight, Check, ClipboardList, Clock, Users, Calendar, BarChart, FileCheck } from 'lucide-react';
import { Link } from 'react-router-dom';
import { Separator } from '@/components/ui/separator';
import { 
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";

const OperationsGuide: React.FC = () => {
  return (
    <Layout>
      <div className="pt-24 pb-20 px-6 md:px-12">
        <div className="container mx-auto max-w-4xl">
          {/* Header with back button */}
          <div className="mb-6">
            <Link to="/free-tools" className="flex items-center text-primary hover:underline">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Free Tools
            </Link>
          </div>
          
          {/* Page Title */}
          <h1 className="text-3xl md:text-4xl font-bold mb-4">Business Operations Guide</h1>
          <p className="text-foreground/70 mb-8 max-w-2xl">
            Best practices for running an efficient and profitable service business.
          </p>

          {/* Introduction */}
          <Card className="mb-8">
            <CardHeader>
              <CardTitle>Introduction to Efficient Business Operations</CardTitle>
              <CardDescription>Creating systems that save time and increase profit</CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-foreground/80 mb-4">
                The difference between struggling service businesses and thriving ones often comes down to 
                their operations — the systems and processes that keep everything running smoothly. With 
                good operations, you'll minimize wasted time, reduce stress, prevent mistakes, and ultimately 
                increase your profit.
              </p>
              <p className="text-foreground/80">
                This guide covers the core operational areas that every service business should optimize, 
                with practical advice you can implement right away — whether you're a solo operator or 
                managing a growing team.
              </p>
            </CardContent>
            <CardFooter>
              <Button className="w-full md:w-auto">
                <Download className="mr-2 h-4 w-4" />
                Download Full Guide PDF
              </Button>
            </CardFooter>
          </Card>

          {/* Key Operational Areas */}
          <h2 className="text-2xl font-semibold mb-4">Key Operational Areas</h2>
          
          <div className="space-y-6 mb-10">
            <Card>
              <CardHeader>
                <div className="flex items-center">
                  <div className="bg-primary/10 rounded-full p-2 mr-3">
                    <ClipboardList className="h-5 w-5 text-primary" />
                  </div>
                  <CardTitle>Estimating and Bidding</CardTitle>
                </div>
              </CardHeader>
              <CardContent className="pl-14">
                <p className="mb-4">
                  Profitable businesses start with accurate estimates. Improper estimating leads to cutting corners, 
                  losing money, or missing out on jobs.
                </p>
                <h4 className="font-medium mb-2">Best Practices:</h4>
                <ul className="space-y-2 list-disc pl-6">
                  <li>
                    <strong>Use standardized templates:</strong> Create consistent, professional-looking estimates.
                  </li>
                  <li>
                    <strong>Track actual vs. estimated time:</strong> Review after each job to improve future estimates.
                  </li>
                  <li>
                    <strong>Include contingencies:</strong> Add 10-15% buffer for unexpected issues.
                  </li>
                  <li>
                    <strong>Define scope clearly:</strong> Detail what is and isn't included to prevent scope creep.
                  </li>
                </ul>
                <div className="bg-muted p-4 rounded-md mt-4">
                  <p className="text-sm">
                    <strong>Pro Tip:</strong> Use our <Link to="/free-tools/pricing-calculator" className="text-primary hover:underline">Pricing Calculator</Link> to 
                    ensure you're accounting for all costs and desired profit margin in your estimates.
                  </p>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <div className="flex items-center">
                  <div className="bg-primary/10 rounded-full p-2 mr-3">
                    <Calendar className="h-5 w-5 text-primary" />
                  </div>
                  <CardTitle>Scheduling and Dispatching</CardTitle>
                </div>
              </CardHeader>
              <CardContent className="pl-14">
                <p className="mb-4">
                  Efficient scheduling maximizes billable hours while minimizing travel time and customer wait times.
                </p>
                <h4 className="font-medium mb-2">Best Practices:</h4>
                <ul className="space-y-2 list-disc pl-6">
                  <li>
                    <strong>Use digital scheduling tools:</strong> Calendar apps with clear color-coding and reminders.
                  </li>
                  <li>
                    <strong>Group jobs geographically:</strong> Schedule by area to reduce travel time.
                  </li>
                  <li>
                    <strong>Set realistic time blocks:</strong> Include travel time and setup/cleanup in your scheduling.
                  </li>
                  <li>
                    <strong>Confirm appointments:</strong> Send reminders 24-48 hours before scheduled work.
                  </li>
                  <li>
                    <strong>Build in buffer time:</strong> Allow for jobs that run over or emergency calls.
                  </li>
                </ul>
                <div className="bg-muted p-4 rounded-md mt-4">
                  <p className="text-sm">
                    <strong>Pro Tip:</strong> Even small businesses benefit from scheduling software. Good options include Jobber, Housecall Pro, and ServiceTitan.
                  </p>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <div className="flex items-center">
                  <div className="bg-primary/10 rounded-full p-2 mr-3">
                    <FileCheck className="h-5 w-5 text-primary" />
                  </div>
                  <CardTitle>Work Order Management</CardTitle>
                </div>
              </CardHeader>
              <CardContent className="pl-14">
                <p className="mb-4">
                  Clear, detailed work orders ensure teams know exactly what needs to be done and what materials are required.
                </p>
                <h4 className="font-medium mb-2">Work Order Elements:</h4>
                <ul className="space-y-2 list-disc pl-6">
                  <li>
                    <strong>Customer information:</strong> Name, address, contact details, access instructions
                  </li>
                  <li>
                    <strong>Job details:</strong> Specific tasks, scope of work, and any customer preferences
                  </li>
                  <li>
                    <strong>Materials list:</strong> All supplies needed to complete the job
                  </li>
                  <li>
                    <strong>Time allocation:</strong> Expected duration and any timing constraints
                  </li>
                  <li>
                    <strong>Special instructions:</strong> Pets, security systems, parking restrictions, etc.
                  </li>
                </ul>
                <div className="bg-muted p-4 rounded-md mt-4">
                  <p className="text-sm">
                    <strong>Pro Tip:</strong> Use our <Link to="/free-tools/work-order-template" className="text-primary hover:underline">Work Order Template</Link> to 
                    create professional work orders that keep your team on track and your customers informed.
                  </p>
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader>
                <div className="flex items-center">
                  <div className="bg-primary/10 rounded-full p-2 mr-3">
                    <Users className="h-5 w-5 text-primary" />
                  </div>
                  <CardTitle>Team Management</CardTitle>
                </div>
              </CardHeader>
              <CardContent className="pl-14">
                <p className="mb-4">
                  Your team's performance directly impacts customer satisfaction and profitability. Even if you're 
                  a solo operator now, these principles will help when you start growing.
                </p>
                <h4 className="font-medium mb-2">Best Practices:</h4>
                <ul className="space-y-2 list-disc pl-6">
                  <li>
                    <strong>Create clear role descriptions:</strong> Define responsibilities and expectations.
                  </li>
                  <li>
                    <strong>Standardize training:</strong> Document procedures for consistency across team members.
                  </li>
                  <li>
                    <strong>Regular check-ins:</strong> Brief meetings to ensure everyone has what they need.
                  </li>
                  <li>
                    <strong>Performance metrics:</strong> Measure quality, efficiency, and customer feedback.
                  </li>
                  <li>
                    <strong>Recognition system:</strong> Acknowledge and reward excellent work.
                  </li>
                </ul>
                <div className="bg-muted p-4 rounded-md mt-4">
                  <p className="text-sm">
                    <strong>Pro Tip:</strong> Create simple checklists for common procedures to ensure consistency and quality 
                    even when you're not personally overseeing every job.
                  </p>
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader>
                <div className="flex items-center">
                  <div className="bg-primary/10 rounded-full p-2 mr-3">
                    <BarChart className="h-5 w-5 text-primary" />
                  </div>
                  <CardTitle>Financial Management</CardTitle>
                </div>
              </CardHeader>
              <CardContent className="pl-14">
                <p className="mb-4">
                  Strong financial operations ensure you know where you stand, can pay your bills, and make profitable business decisions.
                </p>
                <h4 className="font-medium mb-2">Best Practices:</h4>
                <ul className="space-y-2 list-disc pl-6">
                  <li>
                    <strong>Separate business finances:</strong> Keep personal and business accounts separate.
                  </li>
                  <li>
                    <strong>Use accounting software:</strong> QuickBooks, Xero, or Wave to track income/expenses.
                  </li>
                  <li>
                    <strong>Regular financial reviews:</strong> Weekly revenue check, monthly profit/loss review.
                  </li>
                  <li>
                    <strong>Track key metrics:</strong> Average job value, customer acquisition cost, profit margins.
                  </li>
                  <li>
                    <strong>Timely invoicing:</strong> Invoice immediately upon job completion.
                  </li>
                  <li>
                    <strong>Follow up on late payments:</strong> Have a system for overdue invoices.
                  </li>
                </ul>
                <div className="bg-muted p-4 rounded-md mt-4">
                  <p className="text-sm">
                    <strong>Pro Tip:</strong> Use our <Link to="/free-tools/profit-forecast" className="text-primary hover:underline">Profit Forecast Tool</Link> to 
                    project your finances and plan for growth or seasonal fluctuations.
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Systemizing Your Business */}
          <h2 className="text-2xl font-semibold mb-4">Systemizing Your Business</h2>
          
          <Card className="mb-10">
            <CardHeader>
              <CardTitle>How to Create Business Systems</CardTitle>
              <CardDescription>Build processes that run without constant supervision</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                <p>
                  A system is a documented, repeatable process that produces consistent results. Creating systems 
                  is the key to scaling your business and freeing yourself from daily operations.
                </p>
                
                <div className="space-y-3">
                  <h3 className="font-medium">1. Identify Key Processes</h3>
                  <p className="text-sm">
                    Start by listing the core processes in your business: answering calls, giving estimates, 
                    performing services, invoicing, following up with customers, etc.
                  </p>
                </div>
                
                <div className="space-y-3">
                  <h3 className="font-medium">2. Document Current Methods</h3>
                  <p className="text-sm">
                    Write down exactly how these processes are currently handled. Include every step, decision point, 
                    and resource needed. Use screenshots, videos, or checklists.
                  </p>
                </div>
                
                <div className="space-y-3">
                  <h3 className="font-medium">3. Identify Improvement Opportunities</h3>
                  <p className="text-sm">
                    Look for inefficiencies, bottlenecks, or error-prone steps. Ask team members for input on what 
                    causes problems or takes too much time.
                  </p>
                </div>
                
                <div className="space-y-3">
                  <h3 className="font-medium">4. Redesign for Efficiency</h3>
                  <p className="text-sm">
                    Simplify processes by eliminating unnecessary steps, automating repetitive tasks, and creating 
                    templates for common activities.
                  </p>
                </div>
                
                <div className="space-y-3">
                  <h3 className="font-medium">5. Create Standard Operating Procedures (SOPs)</h3>
                  <p className="text-sm">
                    Document the improved process with step-by-step instructions. Make these accessible to everyone 
                    who needs them, whether in a digital tool or physical binder.
                  </p>
                </div>
                
                <div className="space-y-3">
                  <h3 className="font-medium">6. Train and Implement</h3>
                  <p className="text-sm">
                    Train team members on the new systems. Emphasize why the system matters and how it benefits 
                    everyone (not just "because I said so").
                  </p>
                </div>
                
                <div className="space-y-3">
                  <h3 className="font-medium">7. Monitor and Refine</h3>
                  <p className="text-sm">
                    Regularly review how systems are working. Are they being followed? Are they producing the 
                    desired results? Adjust as needed.
                  </p>
                </div>
                
                <div className="bg-muted p-4 rounded-md">
                  <p className="text-sm">
                    <strong>Pro Tip:</strong> Start with the processes that cause the most problems or that you perform most 
                    frequently. Even a 10% improvement in a daily task adds up to significant time savings over a year.
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Systems Every Service Business Needs */}
          <h2 className="text-2xl font-semibold mb-4">Essential Business Systems</h2>
          
          <Accordion type="single" collapsible className="mb-10">
            <AccordionItem value="lead-management">
              <AccordionTrigger>
                <div className="flex items-center">
                  <Clock className="h-5 w-5 text-primary mr-2" />
                  <span>Lead Management System</span>
                </div>
              </AccordionTrigger>
              <AccordionContent>
                <div className="pl-7 space-y-4">
                  <p>
                    How you handle incoming leads can make or break your business. Quick, professional responses 
                    dramatically increase conversion rates.
                  </p>
                  <div className="space-y-2">
                    <h4 className="font-medium">System Elements:</h4>
                    <ul className="space-y-1 list-disc pl-6">
                      <li>Lead capture process (phone, email, website forms)</li>
                      <li>Response time standards (aim for under 30 minutes during business hours)</li>
                      <li>Initial qualification questions to ask</li>
                      <li>Follow-up sequence for leads that don't convert immediately</li>
                      <li>CRM or tracking system to prevent leads from falling through cracks</li>
                    </ul>
                  </div>
                  <div className="bg-muted p-3 rounded-md">
                    <p className="text-sm">
                      <strong>Action Item:</strong> Create email or text templates for common lead scenarios to save 
                      time and ensure consistency.
                    </p>
                  </div>
                </div>
              </AccordionContent>
            </AccordionItem>
            
            <AccordionItem value="customer-onboarding">
              <AccordionTrigger>
                <div className="flex items-center">
                  <Users className="h-5 w-5 text-primary mr-2" />
                  <span>Customer Onboarding System</span>
                </div>
              </AccordionTrigger>
              <AccordionContent>
                <div className="pl-7 space-y-4">
                  <p>
                    The transition from prospect to customer should be smooth and professional, setting expectations
                    and gathering all necessary information.
                  </p>
                  <div className="space-y-2">
                    <h4 className="font-medium">System Elements:</h4>
                    <ul className="space-y-1 list-disc pl-6">
                      <li>Welcome message or packet with your policies and procedures</li>
                      <li>Customer information form (contact details, property access info, etc.)</li>
                      <li>Payment terms and methods accepted</li>
                      <li>Scheduling process and confirmation method</li>
                      <li>Pre-job communication timeline</li>
                    </ul>
                  </div>
                  <div className="bg-muted p-3 rounded-md">
                    <p className="text-sm">
                      <strong>Action Item:</strong> Create a customer welcome email that outlines what they can expect
                      when working with your company.
                    </p>
                  </div>
                </div>
              </AccordionContent>
            </AccordionItem>
            
            <AccordionItem value="service-delivery">
              <AccordionTrigger>
                <div className="flex items-center">
                  <ClipboardList className="h-5 w-5 text-primary mr-2" />
                  <span>Service Delivery System</span>
                </div>
              </AccordionTrigger>
              <AccordionContent>
                <div className="pl-7 space-y-4">
                  <p>
                    This is your core service process — how you actually deliver the work you've been hired to do. 
                    Systematizing this ensures consistent quality no matter who performs the service.
                  </p>
                  <div className="space-y-2">
                    <h4 className="font-medium">System Elements:</h4>
                    <ul className="space-y-1 list-disc pl-6">
                      <li>Service checklists for each type of job you offer</li>
                      <li>Quality standards and how to verify them</li>
                      <li>Customer communication during service (updates, changes, etc.)</li>
                      <li>Standard tools and materials for each service</li>
                      <li>Problem resolution protocol for common issues</li>
                      <li>Job completion verification process</li>
                    </ul>
                  </div>
                  <div className="bg-muted p-3 rounded-md">
                    <p className="text-sm">
                      <strong>Action Item:</strong> Create a service checklist for your most common job type that
                      ensures nothing is forgotten or missed.
                    </p>
                  </div>
                </div>
              </AccordionContent>
            </AccordionItem>
            
            <AccordionItem value="invoicing">
              <AccordionTrigger>
                <div className="flex items-center">
                  <FileCheck className="h-5 w-5 text-primary mr-2" />
                  <span>Invoicing and Payment System</span>
                </div>
              </AccordionTrigger>
              <AccordionContent>
                <div className="pl-7 space-y-4">
                  <p>
                    Getting paid promptly and processing payments efficiently is essential for healthy cash flow.
                  </p>
                  <div className="space-y-2">
                    <h4 className="font-medium">System Elements:</h4>
                    <ul className="space-y-1 list-disc pl-6">
                      <li>Invoice creation and delivery process</li>
                      <li>Payment options and how to process each type</li>
                      <li>Receipt generation and delivery</li>
                      <li>Payment follow-up process for overdue invoices</li>
                      <li>Bookkeeping procedure for recorded payments</li>
                    </ul>
                  </div>
                  <div className="bg-muted p-3 rounded-md">
                    <p className="text-sm">
                      <strong>Action Item:</strong> Set up automated payment reminders for invoices that are 3, 7, and 14 days past due.
                    </p>
                  </div>
                </div>
              </AccordionContent>
            </AccordionItem>
            
            <AccordionItem value="follow-up">
              <AccordionTrigger>
                <div className="flex items-center">
                  <Calendar className="h-5 w-5 text-primary mr-2" />
                  <span>Customer Follow-Up System</span>
                </div>
              </AccordionTrigger>
              <AccordionContent>
                <div className="pl-7 space-y-4">
                  <p>
                    Following up after service builds relationships, generates reviews, and creates opportunities for 
                    additional business.
                  </p>
                  <div className="space-y-2">
                    <h4 className="font-medium">System Elements:</h4>
                    <ul className="space-y-1 list-disc pl-6">
                      <li>Post-service satisfaction check (email, call, or text)</li>
                      <li>Review request process and timing</li>
                      <li>Referral request procedure</li>
                      <li>Future service recommendations based on current work</li>
                      <li>Maintenance reminders or scheduling</li>
                    </ul>
                  </div>
                  <div className="bg-muted p-3 rounded-md">
                    <p className="text-sm">
                      <strong>Action Item:</strong> Create an automated email that sends 2 days after service completion
                      to check satisfaction and request a review.
                    </p>
                  </div>
                </div>
              </AccordionContent>
            </AccordionItem>
          </Accordion>

          {/* Operational Red Flags */}
          <h2 className="text-2xl font-semibold mb-4">Operational Red Flags</h2>
          
          <Card className="mb-10">
            <CardHeader>
              <CardTitle>Warning Signs Your Operations Need Attention</CardTitle>
              <CardDescription>Identify and address these common operational issues</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="border p-4 rounded-md">
                  <div className="flex items-start mb-2">
                    <div className="bg-red-100 dark:bg-red-900/30 p-1 rounded-full mr-2">
                      <div className="h-2 w-2 bg-red-600 rounded-full"></div>
                    </div>
                    <h3 className="font-medium">Frequently Running Behind Schedule</h3>
                  </div>
                  <p className="text-sm text-muted-foreground">
                    Indicates unrealistic time estimates, poor route planning, or inadequate buffer time. 
                    Review your scheduling practices and time allocations.
                  </p>
                </div>

                <div className="border p-4 rounded-md">
                  <div className="flex items-start mb-2">
                    <div className="bg-red-100 dark:bg-red-900/30 p-1 rounded-full mr-2">
                      <div className="h-2 w-2 bg-red-600 rounded-full"></div>
                    </div>
                    <h3 className="font-medium">Quoting Errors and Cost Overruns</h3>
                  </div>
                  <p className="text-sm text-muted-foreground">
                    Suggests incomplete estimating processes or failure to track all costs. 
                    Implement more detailed estimate templates and job costing reviews.
                  </p>
                </div>

                <div className="border p-4 rounded-md">
                  <div className="flex items-start mb-2">
                    <div className="bg-red-100 dark:bg-red-900/30 p-1 rounded-full mr-2">
                      <div className="h-2 w-2 bg-red-600 rounded-full"></div>
                    </div>
                    <h3 className="font-medium">Cash Flow Problems</h3>
                  </div>
                  <p className="text-sm text-muted-foreground">
                    Points to slow invoicing, payment collection issues, or poor financial planning. 
                    Review your invoicing system and implement stricter payment terms.
                  </p>
                </div>

                <div className="border p-4 rounded-md">
                  <div className="flex items-start mb-2">
                    <div className="bg-red-100 dark:bg-red-900/30 p-1 rounded-full mr-2">
                      <div className="h-2 w-2 bg-red-600 rounded-full"></div>
                    </div>
                    <h3 className="font-medium">Recurring Quality Issues</h3>
                  </div>
                  <p className="text-sm text-muted-foreground">
                    Indicates inadequate training, unclear standards, or lack of quality checks. 
                    Develop detailed service checklists and implement quality verification steps.
                  </p>
                </div>

                <div className="border p-4 rounded-md">
                  <div className="flex items-start mb-2">
                    <div className="bg-red-100 dark:bg-red-900/30 p-1 rounded-full mr-2">
                      <div className="h-2 w-2 bg-red-600 rounded-full"></div>
                    </div>
                    <h3 className="font-medium">Missing or Running Out of Materials</h3>
                  </div>
                  <p className="text-sm text-muted-foreground">
                    Shows inventory management or job preparation problems. Create material checklists 
                    for each job type and implement inventory tracking.
                  </p>
                </div>

                <div className="border p-4 rounded-md">
                  <div className="flex items-start mb-2">
                    <div className="bg-red-100 dark:bg-red-900/30 p-1 rounded-full mr-2">
                      <div className="h-2 w-2 bg-red-600 rounded-full"></div>
                    </div>
                    <h3 className="font-medium">Customer Communication Gaps</h3>
                  </div>
                  <p className="text-sm text-muted-foreground">
                    Reveals lack of standardized customer communication practices. Create templates 
                    for common communications and establish communication touchpoints.
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          {/* Next Steps */}
          <Card className="bg-primary/5 border-primary/20">
            <CardHeader>
              <CardTitle>Implementation Plan</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex flex-col md:flex-row gap-6">
                <div className="flex-1">
                  <h3 className="font-semibold text-lg mb-2">Start Small, Build Momentum</h3>
                  <p className="text-sm mb-4">
                    Choose one operational area to improve first. Create a simple system, test it, 
                    refine it, and then move to the next area. Small improvements compound over time.
                  </p>
                  <div className="space-x-2">
                    <Button asChild size="sm">
                      <Link to="/free-tools/work-order-template">
                        Work Order Template
                      </Link>
                    </Button>
                    <Button asChild variant="outline" size="sm">
                      <Link to="/free-tools/receipt-generator">
                        Receipt Generator
                      </Link>
                    </Button>
                  </div>
                </div>
                <div className="flex-1">
                  <h3 className="font-semibold text-lg mb-2">Explore More Resources</h3>
                  <p className="text-sm mb-4">
                    Check out our calculators and templates to help implement better operational systems.
                  </p>
                  <Button asChild variant="secondary" className="flex items-center" size="sm">
                    <Link to="/free-tools">
                      Browse All Tools
                      <ArrowRight className="ml-2 h-4 w-4" />
                    </Link>
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </Layout>
  );
};

export default OperationsGuide;
