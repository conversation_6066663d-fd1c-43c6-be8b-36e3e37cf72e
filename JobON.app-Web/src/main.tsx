
import React from 'react'
import { createRoot } from 'react-dom/client'
import App from './App.tsx'
import './index.css'
import { GoogleOAuthProvider } from "@react-oauth/google";
import AuthProvider from 'react-auth-kit/AuthProvider';
import { authStore } from './lib/auth';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';

// Set default theme to light theme on initial page load
if (!localStorage.getItem('theme')) {
  localStorage.setItem('theme', 'light');
  document.documentElement.classList.remove('dark');
} else if (localStorage.getItem('theme') === 'dark') {
  document.documentElement.classList.add('dark');
} else {
  document.documentElement.classList.remove('dark');
}

// Create a QueryClient instance with optimized settings for chat functionality
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 30000, // 30 seconds - data is considered fresh for 30s
      gcTime: 300000, // 5 minutes - data stays in cache for 5 minutes
      retry: 3, // Retry failed requests 3 times
      retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000), // Exponential backoff
      refetchOnWindowFocus: false, // Don't refetch on window focus for better UX
      refetchOnReconnect: true, // Refetch when network reconnects
    },
    mutations: {
      retry: 2, // Retry failed mutations 2 times
      retryDelay: 1000, // 1 second delay between retries
    },
  },
});

const rootElement = document.getElementById("root");
if (!rootElement) throw new Error('Failed to find the root element');

const root = createRoot(rootElement);
const clientId = import.meta.env.VITE_GOOGLE_CLIENT_ID;
root.render(
  <React.StrictMode>
    <QueryClientProvider client={queryClient}>
      <AuthProvider store={authStore}>
        <GoogleOAuthProvider clientId={clientId}>
          <App />
        </GoogleOAuthProvider>
      </AuthProvider>
    </QueryClientProvider>
  </React.StrictMode>
);
