import { apiService } from "./api";

// Stripe Checkout Session Request Interface
export interface CreateCheckoutSessionRequest {
  userId: string;
  planId: string;
  priceId: string;
  productId: string;
  successUrl?: string;
  cancelUrl?: string;
}

// Stripe Checkout Session Response Interface
export interface CreateCheckoutSessionResponse {
  success: boolean;
  message?: string;
  data?: {
    sessionId: string;
    url: string;
  };
}

// Stripe Service
export const stripeService = {
  /**
   * Create a Stripe Checkout Session
   */
  createCheckoutSession: async (
    request: CreateCheckoutSessionRequest,
    token?: string
  ): Promise<CreateCheckoutSessionResponse> => {
    try {
      const headers: Record<string, string> = {};
      if (token) {
        // Ensure token has Bearer prefix
        headers["Authorization"] = token.startsWith("Bearer ")
          ? token
          : `Bearer ${token}`;
      }

      // Set frontend URLs for success and cancel
      const baseUrl = window.location.origin;
      const requestData = {
        ...request,
        successUrl: request.successUrl || `${baseUrl}/stripe/success`,
        cancelUrl: request.cancelUrl || `${baseUrl}/stripe/cancel`,
      };

      const response = await apiService<{
        sessionId?: string;
        url?: string;
        session_id?: string; // Backend might return session_id instead of sessionId
      }>("/api/stripe/create-checkout-session", {
        method: "POST",
        body: requestData,
        headers,
        requiresAuth: true,
      });

      console.log("Full Stripe API response:", response);

      if (response.isSuccess && response.data) {
        console.log("Stripe API response.data:", response.data);

        // Handle nested response structure from backend
        let sessionData;

        // Check if response.data has nested data property
        if (response.data.data && typeof response.data.data === "object") {
          sessionData = response.data.data;
          console.log("Using nested data structure:", sessionData);
        } else if (response.data.sessionId || response.data.url) {
          sessionData = response.data;
          console.log("Using flat data structure:", sessionData);
        }

        if (sessionData) {
          const sessionId = sessionData.sessionId || sessionData.session_id;
          const url = sessionData.url;

          if (url) {
            console.log("Stripe checkout URL received:", url);
            return {
              success: true,
              message: "Checkout session created successfully",
              data: {
                sessionId: sessionId || "",
                url: url,
              },
            };
          } else {
            console.error("No URL in session data:", sessionData);
          }
        } else {
          console.error(
            "No valid session data found in response:",
            response.data
          );
        }
      } else {
        console.error("Stripe API call failed:", response);
      }

      return {
        success: false,
        message: "Failed to create checkout session",
      };
    } catch (error) {
      console.error("Error creating checkout session:", error);
      return {
        success: false,
        message: "Error creating checkout session",
      };
    }
  },

  /**
   * Verify a successful payment session
   */
  verifyPaymentSession: async (
    sessionId: string,
    token?: string
  ): Promise<{ success: boolean; message?: string }> => {
    try {
      const headers: Record<string, string> = {};
      if (token) {
        // Ensure token has Bearer prefix
        headers["Authorization"] = token.startsWith("Bearer ")
          ? token
          : `Bearer ${token}`;
      }

      const response = await apiService<any>(
        `/api/stripe/verify-session/${sessionId}`,
        {
          method: "GET",
          headers,
          requiresAuth: true,
        }
      );

      if (response.isSuccess) {
        return {
          success: true,
          message: "Payment verified successfully",
        };
      }

      return {
        success: false,
        message: "Failed to verify payment",
      };
    } catch (error) {
      console.error("Error verifying payment session:", error);
      return {
        success: false,
        message: "Error verifying payment session",
      };
    }
  },
};
