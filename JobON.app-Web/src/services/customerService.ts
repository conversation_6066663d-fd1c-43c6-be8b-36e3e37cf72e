import { ApiResponse, apiService } from './api';

// Customer interfaces
export interface Customer {
  id: string;
  name: string;
  email: string;
  phone: string;
  role_id: number;
  created_at: string;
  updated_at: string;
  status?: string;
  avatar?: string;
  location?: string;
  total_bookings?: number;
  last_active?: string;
  flagged?: boolean;
  reward_tier?: string;
  last_login?: string; // Added last_login
  conversation_history?: CustomerConversationEntry[]; // Added conversation_history
  bookings?: BookingEntry[]; // Added bookings
  messages?: MessageEntry[]; // Added messages
  adminMessages?: AdminMessageEntry[]; // Added adminMessages
}

// Interface for individual booking entries (simplified based on MobileCustomerDetailView usage)
export interface BookingEntry {
  id: string;
  title: string;
  date: string;
  status: string;
}

// Interface for individual message entries (simplified based on MobileCustomerDetailView usage)
export interface MessageEntry {
  id: string;
  providerName: string;
  date: string;
  jobTitle: string;
}

// Interface for individual admin message entries (simplified based on MobileCustomerDetailView usage)
export interface AdminMessageEntry {
  id: string;
  adminName: string;
  timestamp: string;
  content: string;
}

// Interface for individual conversation messages from the backend
export interface CustomerConversationEntry {
  id: string;
  message: string; // Assuming this is a simple text message for now
  sender_type: 'admin' | 'customer' | 'provider' | 'user'; // Or relevant types
  created_at: string; // Assuming ISO date string
  // Add any other relevant fields for a conversation entry
}

export interface CustomerStats {
  totalOrders: number;
  totalSpend: number;
  // Add any other relevant stats fields
}

// Placeholder for Booking response
export interface BookingResponse {
  // Define structure based on actual API response
  [key: string]: unknown;
}

// Placeholder for Message response
export interface MessageResponse {
  // Define structure based on actual API response
  [key: string]: unknown;
}

// Placeholder for Public Roles response
export interface PublicRolesResponse {
  // Define structure based on actual API response
  [key: string]: unknown;
}

export interface CustomerResponse {
  data: Customer[];
  meta: {
    current_page: number;
    from: number;
    last_page: number;
    per_page: number;
    to: number;
    total: number;
  };
}

// Request interfaces
export interface CreateCustomerRequest {
  name: string;
  email: string;
  phone?: string;
  password: string;
  password_confirmation: string;
  role_id: number;
}

export interface UpdateCustomerRequest {
  name?: string;
  email?: string;
  phone?: string;
  password?: string;
  password_confirmation?: string;
  role_id?: number;
  status?: string;
  flagged?: boolean;
}

// Customer service with API functions
export const customerService = {
  // Create a new customer
  createCustomer: (data: CreateCustomerRequest, token?: string): Promise<ApiResponse<Customer>> => {
    const headers: Record<string, string> = {};
    if (token) {
      headers['Authorization'] = token;
    }

    return apiService<Customer>('/api/user', {
      method: 'POST',
      body: data,
      requiresAuth: true,
      headers,
    });
  },

  // Get all customers with optional filtering and pagination
  getCustomers: async (
    page: number = 1,
    perPage: number = 10,
    search?: string,
    token?: string
  ): Promise<ApiResponse<CustomerResponse>> => {
    // Assuming role_id=2 is for customers
    let endpoint = `/api/user?role_id=3&page=${page}&per_page=${perPage}`;

    if (search) {
      endpoint += `&search=${encodeURIComponent(search)}`;
    }

    const headers: Record<string, string> = {};
    if (token) {
      headers['Authorization'] = token;
    }

    return apiService<CustomerResponse>(endpoint, {
      method: 'GET',
      requiresAuth: true,
      headers,
    });
  },

  // Get a single customer by ID
  getCustomer: (id: string, token?: string): Promise<ApiResponse<Customer>> => {
    const headers: Record<string, string> = {};
    if (token) {
      headers['Authorization'] = token;
    }

    return apiService<Customer>(`/api/user/${id}`, {
      method: 'GET',
      requiresAuth: true,
      headers,
    });
  },

  // Update a customer
  updateCustomer: (id: string, data: UpdateCustomerRequest, token?: string): Promise<ApiResponse<Customer>> => {
    const headers: Record<string, string> = {};
    if (token) {
      headers['Authorization'] = token;
    }

    return apiService<Customer>(`/api/user/${id}`, {
      method: 'PUT',
      body: data,
      requiresAuth: true,
      headers,
    });
  },

  // Delete a customer
  deleteCustomer: (id: string, token?: string): Promise<ApiResponse<unknown>> => {
    const headers: Record<string, string> = {};
    if (token) {
      headers['Authorization'] = token;
    }

    return apiService(`/api/user/${id}`, {
      method: 'DELETE',
      requiresAuth: true,
      headers,
    });
  },

  // Flag/unflag a customer
  toggleCustomerFlag: (id: string, flagged: boolean, token?: string): Promise<ApiResponse<Customer>> => {
    const headers: Record<string, string> = {};
    if (token) {
      headers['Authorization'] = token;
    }

    return apiService<Customer>(`/api/user/${id}/flag`, {
      method: 'PUT',
      body: { flagged },
      requiresAuth: true,
      headers,
    });
  },

  // Get customer bookings
  getCustomerBookings: (id: string, page: number = 1, perPage: number = 10, token?: string): Promise<ApiResponse<BookingResponse>> => {
    const headers: Record<string, string> = {};
    if (token) {
      headers['Authorization'] = token;
    }

    return apiService(`/api/user/${id}/bookings?page=${page}&per_page=${perPage}`, {
      method: 'GET',
      requiresAuth: true,
      headers,
    });
  },

  // Get customer messages
  getCustomerMessages: (id: string, page: number = 1, perPage: number = 10, token?: string): Promise<ApiResponse<MessageResponse>> => {
    const headers: Record<string, string> = {};
    if (token) {
      headers['Authorization'] = token;
    }

    return apiService(`/api/user/${id}/messages?page=${page}&per_page=${perPage}`, {
      method: 'GET',
      requiresAuth: true,
      headers,
    });
  },

  // Get public roles
  getPublicRoles: (token?: string): Promise<ApiResponse<{ id: number; name: string }[]>> => {
    const headers: Record<string, string> = {};
    if (token) {
      headers['Authorization'] = token;
    }

    return apiService<{ id: number; name: string }[]>('/api/roles-public', {
      method: 'GET',
      headers,
    });
  },

  // Get customer statistics
  getCustomerStats: (customerId: string, token?: string): Promise<ApiResponse<CustomerStats>> => {
    const headers: Record<string, string> = {};
    if (token) {
      headers['Authorization'] = token;
    }
    // This is a placeholder URL, replace with the actual endpoint
    return apiService<CustomerStats>(`/api/user/${customerId}/stats`, {
      method: 'GET',
      requiresAuth: true,
      headers,
    });
  },
};
