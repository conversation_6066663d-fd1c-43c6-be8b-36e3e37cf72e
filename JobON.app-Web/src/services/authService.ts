import axios from 'axios';
import { BASE_API_URL } from '@/lib/constants';

// Define interfaces for request/response types
interface LoginRequest {
  email: string;
  password: string;
}

interface SignupRequest {
  email: string;
  password: string;
  name: string;
  roleId?: string;
  password_confirmation?: string;
}

interface GoogleLoginRequest {
  id_token: string;
  roleId?: number;
}

// Implement API functions
export const authService = {
  login: (data: LoginRequest) => axios.post(`${BASE_API_URL}/api/login`, data),
  signup: (data: SignupRequest) => axios.post(`${BASE_API_URL}/api/register`, data),
  googleLogin: (data: GoogleLoginRequest) => axios.post(`${BASE_API_URL}/api/auth/google`, data),
  // Add other auth methods as needed
};
