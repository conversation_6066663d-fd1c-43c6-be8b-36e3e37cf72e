import { apiService } from "./api";
import { ProviderPlan } from "@/components/admin/provider-tier/schemas";

// Mock plans data with new pricing structure
const MOCK_PLANS: ProviderPlan[] = [
  {
    id: "starter",
    name: "Starter",
    price: 0,
    yearlyPrice: 0,
    description: "Perfect for getting started",
    jobMatches: 5,
    transactionFee: "15%",
    duration: "monthly",
    features: [
      { included: true, text: "In-app messaging" },
      { included: true, text: "Basic business profile listing" },
      { included: true, text: "Manual quoting" },
      { included: false, text: "SmartMatch™ job recommendations" },
      { included: false, text: "Review automation" },
      { included: false, text: "Calendar sync" },
      { included: false, text: "Auto-bidding" },
      { included: false, text: "BNPL access" },
      { included: false, text: "Analytics dashboard" },
      { included: false, text: "Team access" },
    ],
  },
  {
    id: "starter-yearly",
    name: "Starter",
    price: 0,
    yearlyPrice: 0,
    description: "Perfect for getting started",
    jobMatches: 5,
    transactionFee: "15%",
    duration: "yearly",
    features: [
      { included: true, text: "In-app messaging" },
      { included: true, text: "Basic business profile listing" },
      { included: true, text: "Manual quoting" },
      { included: false, text: "SmartMatch™ job recommendations" },
      { included: false, text: "Review automation" },
      { included: false, text: "Calendar sync" },
      { included: false, text: "Auto-bidding" },
      { included: false, text: "BNPL access" },
      { included: false, text: "Analytics dashboard" },
      { included: false, text: "Team access" },
    ],
  },
  {
    id: "pro",
    name: "Pro",
    price: 129,
    yearlyPrice: 109,
    description: "For growing businesses",
    jobMatches: 25,
    transactionFee: "10%",
    duration: "monthly",
    features: [
      { included: true, text: "Everything in Starter" },
      { included: true, text: "SmartMatch™ job recommendations" },
      { included: true, text: "Review automation" },
      { included: true, text: "Calendar sync" },
      { included: true, text: "Priority listing placement" },
      { included: true, text: "Job tracking tools" },
      { included: true, text: "Basic analytics dashboard" },
      { included: false, text: "Auto-bid with custom rules" },
      { included: false, text: "BNPL access" },
      { included: false, text: "Team access" },
    ],
  },
  {
    id: "pro-yearly",
    name: "Pro",
    price: 129,
    yearlyPrice: 109,
    description: "For growing businesses",
    jobMatches: 25,
    transactionFee: "10%",
    duration: "yearly",
    features: [
      { included: true, text: "Everything in Starter" },
      { included: true, text: "SmartMatch™ job recommendations" },
      { included: true, text: "Review automation" },
      { included: true, text: "Calendar sync" },
      { included: true, text: "Priority listing placement" },
      { included: true, text: "Job tracking tools" },
      { included: true, text: "Basic analytics dashboard" },
      { included: false, text: "Auto-bid with custom rules" },
      { included: false, text: "BNPL access" },
      { included: false, text: "Team access" },
    ],
  },
  {
    id: "elite",
    name: "Elite",
    price: 299,
    yearlyPrice: 254,
    description: "Maximum growth potential",
    jobMatches: "Unlimited",
    transactionFee: "5%",
    duration: "monthly",
    features: [
      { included: true, text: "Everything in Pro" },
      { included: true, text: "Auto-bid with custom rules" },
      { included: true, text: "Lead insights and advanced analytics" },
      { included: true, text: "Buy Now Pay Later (BNPL) integration" },
      { included: true, text: "Team access and job assignment" },
      { included: true, text: "Dedicated support" },
      { included: true, text: "Early access to new features" },
    ],
  },
  {
    id: "elite-yearly",
    name: "Elite",
    price: 299,
    yearlyPrice: 254,
    description: "Maximum growth potential",
    jobMatches: "Unlimited",
    transactionFee: "5%",
    duration: "yearly",
    features: [
      { included: true, text: "Everything in Pro" },
      { included: true, text: "Auto-bid with custom rules" },
      { included: true, text: "Lead insights and advanced analytics" },
      { included: true, text: "Buy Now Pay Later (BNPL) integration" },
      { included: true, text: "Team access and job assignment" },
      { included: true, text: "Dedicated support" },
      { included: true, text: "Early access to new features" },
    ],
  },
];

// Response interfaces
export interface PlansResponse {
  success: boolean;
  message?: string;
  data?: {
    data: ProviderPlan[];
    total: number;
    page: number;
    limit: number;
  };
}

export interface PlanResponse {
  success: boolean;
  message?: string;
  data?: ProviderPlan;
}

// Request interfaces
export interface CreatePlanRequest {
  name: string;
  price: number;
  yearlyPrice?: number;
  description: string;
  jobMatches: number | "Unlimited";
  transactionFee: string;
  features: Array<{
    included: boolean;
    text: string;
  }>;
}

export interface UpdatePlanRequest extends CreatePlanRequest {
  id: string;
}

// Provider Plan Service
export const planService = {
  /**
   * Get all subscription plans with pagination
   */
  getPlans: async (
    page: number = 1,
    limit: number = 10,
    token?: string
  ): Promise<PlansResponse> => {
    try {
      // For now, return mock data to demonstrate the new pricing structure
      // In production, this would call the actual API
      return {
        success: true,
        data: {
          data: MOCK_PLANS,
          total: MOCK_PLANS.length,
          page: 1,
          limit: MOCK_PLANS.length,
        },
      };

      // ... keep existing code (commented out API call for future use)
    } catch (error) {
      console.error("Error fetching plans:", error);
      return {
        success: false,
        message: "Failed to fetch plans",
      };
    }
  },

  /**
   * Get a specific plan by ID
   */
  getPlan: async (id: string, token?: string): Promise<PlanResponse> => {
    try {
      const plan = MOCK_PLANS.find(p => p.id === id);
      if (plan) {
        return {
          success: true,
          data: plan,
        };
      }
      return {
        success: false,
        message: "Plan not found",
      };
    } catch (error) {
      console.error(`Error fetching plan ${id}:`, error);
      return {
        success: false,
        message: "Failed to fetch plan",
      };
    }
  },

  /**
   * Create a new subscription plan
   */
  createPlan: async (
    planData: CreatePlanRequest,
    token?: string
  ): Promise<PlanResponse> => {
    try {
      // Transform our internal plan data format to match the API's expected format
      const apiPlanData = {
        name: planData.name,
        price: planData.price,
        yearlyPrice: planData.yearlyPrice,
        description: planData.description,
        jobMatches: planData.jobMatches,
        transactionFee: planData.transactionFee,
        // Extract feature information to set max values
        max_services: 0,
        max_addresses: 0,
        max_servicemen: 0,
        max_service_packages: 0,
        status: "active",
        duration: "Monthly" // Default duration
      };
      
      // Call the actual API endpoint to create a new plan
      const headers: Record<string, string> = {};
      if (token) {
        headers['Authorization'] = token;
      }

      const response = await apiService<any>('/api/subscription/plans', {
        method: 'POST',
        body: apiPlanData,
        headers,
        requiresAuth: true,
      });

      if (response.isSuccess && response.data) {
        // Transform the response data to match our ProviderPlan interface
        const transformedPlan: ProviderPlan = {
          id: response.data.id || String(response.data._id),
          name: response.data.name,
          price: response.data.price,
          yearlyPrice: response.data.yearlyPrice,
          description: response.data.description,
          jobMatches: planData.jobMatches,
          transactionFee: planData.transactionFee,
          features: planData.features, // Use the features from the request
        };
        
        return {
          success: true,
          message: "Plan created successfully",
          data: transformedPlan,
        };
      }
      
      return response as PlanResponse;
    } catch (error) {
      console.error("Error creating plan:", error);
      return {
        success: false,
        message: "Failed to create plan",
      };
    }
  },

  /**
   * Update an existing subscription plan
   */
  updatePlan: async (
    planData: UpdatePlanRequest,
    token?: string
  ): Promise<PlanResponse> => {
    try {
      // Transform our internal plan data format to match the API's expected format
      const apiPlanData = {
        name: planData.name,
        price: planData.price,
        yearlyPrice: planData.yearlyPrice,
        description: planData.description,
        jobMatches: planData.jobMatches,
        transactionFee: planData.transactionFee,
        // Extract feature information to set max values if needed
        // We're keeping the existing values for these fields
        status: "active",
      };
      
      // Call the actual API endpoint to update the plan
      const headers: Record<string, string> = {};
      if (token) {
        headers['Authorization'] = token;
      }

      const response = await apiService<any>(
        `/api/subscription/plans/${planData.id}`,
        {
          method: 'PUT',
          body: apiPlanData,
          headers,
          requiresAuth: true,
        }
      );

      if (response.isSuccess && response.data) {
        // Transform the response data to match our ProviderPlan interface
        const transformedPlan: ProviderPlan = {
          id: response.data.id || String(response.data._id),
          name: response.data.name,
          price: response.data.price,
          yearlyPrice: response.data.yearlyPrice,
          description: response.data.description,
          jobMatches: planData.jobMatches,
          transactionFee: planData.transactionFee,
          features: planData.features, // Use the features from the request
        };
        
        return {
          success: true,
          message: "Plan updated successfully",
          data: transformedPlan,
        };
      }
      
      return response as PlanResponse;
    } catch (error) {
      console.error(`Error updating plan ${planData.id}:`, error);
      return {
        success: false,
        message: "Failed to update plan",
      };
    }
  },

  /**
   * Delete a subscription plan
   */
  deletePlan: async (id: string, token?: string): Promise<{ success: boolean; message?: string }> => {
    try {
      // Call the actual API endpoint to delete the plan
      const headers: Record<string, string> = {};
      if (token) {
        headers['Authorization'] = token;
      }

      const response = await apiService<any>(`/api/subscription/plans/${id}`, {
        method: 'DELETE',
        headers,
        requiresAuth: true,
      });

      return {
        success: response.isSuccess,
        message: response.error || "Plan deleted successfully",
      };
    } catch (error) {
      console.error(`Error deleting plan ${id}:`, error);
      return {
        success: false,
        message: "Failed to delete plan",
      };
    }
  },

  /**
   * Assign a plan to a provider
   */
  assignPlanToProvider: async (
    providerId: string,
    planId: string,
    notes?: string,
    token?: string,
    duration?: 'monthly' | 'yearly'
  ): Promise<{ success: boolean; message?: string; subscription?: any }> => {
    try {
      // Call the actual API endpoint to assign a plan to a provider
      const headers: Record<string, string> = {};
      if (token) {
        headers['Authorization'] = token;
      }

      const response = await apiService<{
        success?: boolean;
        message?: string;
        subscription?: any;
      }>('/api/admin/provider/update-tier', {
        method: 'POST',
        body: {
          provider_id: providerId,
          plan_id: planId,
          duration: duration || undefined,
          notes
        },
        headers,
        requiresAuth: true,
      });

      if (response.isSuccess && response.data) {
        return {
          success: response.data.success ?? true,
          message: response.data.message || "Provider tier plan updated successfully",
          subscription: response.data.subscription
        };
      }

      return {
        success: false,
        message: response.error || "Failed to update provider tier",
      };
    } catch (error) {
      console.error(`Error updating provider tier ${providerId}:`, error);
      return {
        success: false,
        message: "Failed to update provider tier",
      };
    }
  },

  /**
   * Get the current plan for a provider
   */
  getProviderPlan: async (
    providerId: string,
    token?: string
  ): Promise<PlanResponse & { startDate?: string; endDate?: string; isActive?: boolean; notes?: string }> => {
    try {
      // Call the actual API endpoint to get the provider's current plan
      const headers: Record<string, string> = {};
      if (token) {
        headers['Authorization'] = token;
      }

      const response = await apiService<any>(`/api/subscription/provider-plans/${providerId}`, {
        method: 'GET',
        headers,
        requiresAuth: true,
      });

      if (response.isSuccess && response.data) {
        const providerPlan = response.data;
        const plan = providerPlan.plan;
        
        // Transform the data to match our ProviderPlan interface
        const transformedPlan: ProviderPlan = {
          id: plan.id || String(plan._id),
          name: plan.name,
          price: plan.price,
          yearlyPrice: plan.yearlyPrice,
          description: plan.description,
          jobMatches: plan.jobMatches || 5, // Default value
          transactionFee: plan.transactionFee || "15%", // Default value
          // Create features based on the plan attributes
          features: [
            { included: plan.max_services > 0, text: `Up to ${plan.max_services} services` },
            { included: plan.max_addresses > 0, text: `Up to ${plan.max_addresses} addresses` },
            { included: plan.max_servicemen > 0, text: `Up to ${plan.max_servicemen} servicemen` },
            { included: plan.max_service_packages > 0, text: `Up to ${plan.max_service_packages} service packages` },
            { included: plan.status === "active", text: "Active plan" },
            { included: true, text: `${plan.duration || "Monthly"} billing cycle` },
          ],
        };
        
        return {
          success: true,
          data: transformedPlan,
          startDate: providerPlan.start_date,
          endDate: providerPlan.end_date,
          isActive: providerPlan.is_active,
          notes: providerPlan.notes,
        };
      }
      
      return response as PlanResponse & { startDate?: string; endDate?: string; isActive?: boolean; notes?: string };
    } catch (error) {
      console.error(`Error fetching provider plan for ${providerId}:`, error);
      return {
        success: false,
        message: "Failed to fetch provider plan",
      };
    }
  },
};
