import React, { createContext, useContext, useReducer, useEffect, useCallback, useRef, useMemo } from 'react';
import { useAuth } from '@/features/auth/hooks/useAuth';
import { chatService } from '@/services/chatService';
import { websocketService } from '@/services/websocketService';
import debounce from 'lodash/debounce';
import { 
  Chat, 
  Message, 
  SendMessageRequest, 
  ChatContextType, 
  WebSocketMessage,
  MessageSentMessage 
} from '@/types/chat';

// Action types for reducer
type ChatAction =
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_ERROR'; payload: string | null }
  | { type: 'SET_CHATS'; payload: Chat[] }
  | { type: 'SET_CURRENT_CHAT'; payload: Chat | null }
  | { type: 'SET_MESSAGES'; payload: Message[] }
  | { type: 'ADD_MESSAGE'; payload: Message }
  | { type: 'UPDATE_MESSAGE'; payload: Message }
  | { type: 'SET_CONNECTED'; payload: boolean }
  | { type: 'UPDATE_CHAT_LAST_MESSAGE'; payload: { chatId: string; message: Message } };

// Initial state
interface ChatState {
  chats: Chat[];
  currentChat: Chat | null;
  messages: Message[];
  isLoading: boolean;
  isConnected: boolean;
  error: string | null;
}

const initialState: ChatState = {
  chats: [],
  currentChat: null,
  messages: [],
  isLoading: true, // Start with loading true to prevent initial error state
  isConnected: false,
  error: null,
};

// Reducer
function chatReducer(state: ChatState, action: ChatAction): ChatState {
  switch (action.type) {
    case 'SET_LOADING':
      return { ...state, isLoading: action.payload };
    case 'SET_ERROR':
      return { ...state, error: action.payload };
    case 'SET_CHATS':
      return { ...state, chats: action.payload };
    case 'SET_CURRENT_CHAT':
      return { ...state, currentChat: action.payload };
    case 'SET_MESSAGES':
      return { ...state, messages: action.payload };
    case 'ADD_MESSAGE':
      return { 
        ...state, 
        messages: [...state.messages, action.payload].sort(
          (a, b) => new Date(a.created_at).getTime() - new Date(b.created_at).getTime()
        )
      };
    case 'UPDATE_MESSAGE':
      return {
        ...state,
        messages: state.messages.map(msg => 
          msg.id === action.payload.id ? action.payload : msg
        )
      };
    case 'SET_CONNECTED':
      return { ...state, isConnected: action.payload };
    case 'UPDATE_CHAT_LAST_MESSAGE':
      return {
        ...state,
        chats: (state.chats || []).map(chat =>
          chat.id === action.payload.chatId
            ? { ...chat, last_message: action.payload.message }
            : chat
        )
      };
    default:
      return state;
  }
}

// Create context
const ChatContext = createContext<ChatContextType | undefined>(undefined);

// Provider component
export const ChatProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [state, dispatch] = useReducer(chatReducer, initialState);
  const { token, isAuthenticated } = useAuth();

  // Get token from auth
  const getToken = useCallback(() => {
    return token ? token.replace('Bearer ', '') : undefined;
  }, [token]);

  // Early return if not authenticated
  if (!isAuthenticated) {
    console.warn('ChatProvider: User not authenticated');
  }

  // Track loading state to prevent duplicate calls
  const isLoadingRef = useRef(false);
  const hasConnectedRef = useRef(false);
  const lastTokenRef = useRef<string | null>(null);
  const lastLoadTimeRef = useRef<number>(0);
  const connectionStatusRef = useRef<'connecting' | 'connected' | 'disconnected'>('disconnected');

  // Internal implementation of loadChats
  const loadChatsImpl = async () => {
    // Prevent duplicate calls if already loading
    if (isLoadingRef.current) {
      console.log('🔄 ChatContext.loadChats already in progress, skipping');
      return;
    }

    // Check if we've loaded chats recently (within 5 seconds)
    const now = Date.now();
    const timeSinceLastLoad = now - lastLoadTimeRef.current;
    if (lastLoadTimeRef.current > 0 && timeSinceLastLoad < 5000) {
      return;
    }
    isLoadingRef.current = true;
    lastLoadTimeRef.current = now;
    dispatch({ type: 'SET_LOADING', payload: true });
    dispatch({ type: 'SET_ERROR', payload: null });

    try {
      const token = getToken();

      const response = await chatService.getChats(token);

      if (response.isSuccess && response.data) {
        dispatch({ type: 'SET_ERROR', payload: null }); // Clear any previous errors
        dispatch({ type: 'SET_CHATS', payload: response.data.data });
      } else {
        throw new Error(response.error || 'Failed to load chats');
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to load chats';
      dispatch({ type: 'SET_ERROR', payload: errorMessage });
    } finally {
      isLoadingRef.current = false;
      dispatch({ type: 'SET_LOADING', payload: false });
    }
  };

  // Debounced version of loadChats that prevents multiple calls within 5 seconds
  const loadChats = useCallback(
    debounce(() => {
      loadChatsImpl();
    }, 5000, { leading: true, trailing: false }),
    [getToken]
  );

  // Load messages for a specific chat
  const loadMessages = useCallback(async (chatId: string, page: number = 1) => {
    dispatch({ type: 'SET_LOADING', payload: true });
    dispatch({ type: 'SET_ERROR', payload: null });

    try {
      const response = await chatService.getMessages(chatId, page, getToken());

      if (response.isSuccess && response.data) {
        if (page === 1) {
          // First page - replace all messages
          dispatch({ type: 'SET_MESSAGES', payload: response.data.data });
        } else {
          // Additional pages - prepend to existing messages
          // Get current messages from state at the time of execution
          const currentMessages = state.messages;
          const sortedMessages = [...response.data.data, ...currentMessages].sort(
            (a, b) => new Date(a.created_at).getTime() - new Date(b.created_at).getTime()
          );
          dispatch({ type: 'SET_MESSAGES', payload: sortedMessages });
        }
      } else {
        throw new Error(response.error || 'Failed to load messages');
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to load messages';
      dispatch({ type: 'SET_ERROR', payload: errorMessage });
      console.error('Error loading messages:', error);
    } finally {
      dispatch({ type: 'SET_LOADING', payload: false });
    }
  }, [getToken]);

  // Send message with loading state protection and retry mechanism
  const sendMessage = useCallback(async (chatId: string, messageData: SendMessageRequest) => {
    // Prevent multiple calls
    if (state.isLoading) {
      return;
    }

    // Set loading state
    dispatch({ type: 'SET_LOADING', payload: true });
    dispatch({ type: 'SET_ERROR', payload: null });

    // Validate token
    const token = getToken();
    if (!token) {
      const errorMessage = 'Authentication token is missing';
      dispatch({ type: 'SET_ERROR', payload: errorMessage });
      dispatch({ type: 'SET_LOADING', payload: false });
      throw new Error(errorMessage);
    }

    // Retry mechanism
    const maxRetries = 3;
    let retries = 0;
    let lastError: Error | null = null;

    while (retries < maxRetries) {
      try {
        const response = await chatService.sendMessage(chatId, messageData, token);

        if (!response.isSuccess || !response.data) {
          throw new Error(response.error || 'Failed to send message');
        }
        // Reset loading state
        dispatch({ type: 'SET_LOADING', payload: false });
        return response;

      } catch (error) {
        lastError = error instanceof Error ? error : new Error('Failed to send message');

        retries++;
        if (retries < maxRetries) {
          const backoffTime = Math.min(1000 * Math.pow(2, retries), 10000);
          await new Promise(resolve => setTimeout(resolve, backoffTime));
        }
      }
    }

    // If we get here, all retries failed
    const errorMessage = lastError?.message || 'Failed to send message after multiple attempts';
    dispatch({ type: 'SET_ERROR', payload: errorMessage });
    dispatch({ type: 'SET_LOADING', payload: false });
    throw new Error(errorMessage); // Re-throw so UI can handle it
  }, [getToken, state.isLoading]);

  // Join chat
  const joinChat = useCallback((chatId: string) => {
    const chat = (state.chats || []).find(c => c.id === chatId);
    if (chat) {
      dispatch({ type: 'SET_CURRENT_CHAT', payload: chat });
      websocketService.joinChat(chatId);
      loadMessages(chatId);
    }
  }, [loadMessages]);

  // Leave chat
  const leaveChat = useCallback(() => {
    dispatch({ type: 'SET_CURRENT_CHAT', payload: null });
    dispatch({ type: 'SET_MESSAGES', payload: [] });
    websocketService.leaveChat();
  }, []);

  // Mark as read
  const markAsRead = useCallback(async (chatId: string) => {
    try {
      await chatService.markAsRead(chatId, getToken());
    } catch (error) {
      console.error('Error marking chat as read:', error);
    }
  }, [getToken]);

  // Delete message
  const deleteMessage = useCallback(async (chatId: string, messageId: string) => {
    try {
      const response = await chatService.deleteMessage(chatId, messageId, getToken());

      if (response.isSuccess) {
        // Remove message from local state
        const currentMessages = state.messages;
        dispatch({
          type: 'SET_MESSAGES',
          payload: currentMessages.filter(msg => msg.id !== messageId)
        });
      } else {
        throw new Error(response.error || 'Failed to delete message');
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to delete message';
      dispatch({ type: 'SET_ERROR', payload: errorMessage });
      console.error('Error deleting message:', error);
      throw error;
    }
  }, [getToken]);

  // WebSocket connection management
  const connect = useCallback(() => {
    // Prevent multiple connection attempts
    if (connectionStatusRef.current === 'connecting' || connectionStatusRef.current === 'connected') {
      return;
    }
    connectionStatusRef.current = 'connecting';

    websocketService.connect()
      .then(() => {
        connectionStatusRef.current = 'connected';
      })
      .catch(error => {
        connectionStatusRef.current = 'disconnected';
        // dispatch({ type: 'SET_ERROR', payload: 'Failed to connect to real-time messaging' });
      });
  }, []);

  const disconnect = useCallback(() => {
    websocketService.disconnect();
    connectionStatusRef.current = 'disconnected';
  }, []);

  const reconnect = useCallback(() => {
    connectionStatusRef.current = 'connecting';

    websocketService.reconnect();
  }, []);

  // Combined WebSocket setup and connection management
  useEffect(() => {
    // Only proceed if authenticated
    if (!isAuthenticated || !token) {
      return;
    }

    // Track if this effect instance has connected
    const effectInstanceId = Date.now();

    // Connect to WebSocket if not already connected or connecting
    if (connectionStatusRef.current === 'disconnected') {
      connect();
    }

    // Set up event listeners
    const unsubscribeMessage = websocketService.onMessage((message: WebSocketMessage) => {
      if (message.action === 'message_sent') {
        const messageData = (message as MessageSentMessage).data;
        dispatch({ type: 'ADD_MESSAGE', payload: messageData });

        // Update chat's last message
        dispatch({ 
          type: 'UPDATE_CHAT_LAST_MESSAGE', 
          payload: { chatId: messageData.chat_id, message: messageData }
        });
      }
    });

    const unsubscribeConnect = websocketService.onConnect(() => {
      connectionStatusRef.current = 'connected';
      dispatch({ type: 'SET_CONNECTED', payload: true });
    });

    const unsubscribeDisconnect = websocketService.onDisconnect(() => {
      connectionStatusRef.current = 'disconnected';
      dispatch({ type: 'SET_CONNECTED', payload: false });
    });

    const unsubscribeError = websocketService.onError((error) => {
      // dispatch({ type: 'SET_ERROR', payload: 'WebSocket connection error' });
    });

    // Load chats immediately when authenticated and token changes
    if (token && token !== lastTokenRef.current) {
      lastTokenRef.current = token;
      loadChats();
    }

    // Cleanup function
    return () => {
      // Unsubscribe from all event listeners
      unsubscribeMessage();
      unsubscribeConnect();
      unsubscribeDisconnect();
      unsubscribeError();

      // Clear all listeners from the service
      websocketService.clearAllListeners();

      // Only disconnect if we're connected or connecting
      if (connectionStatusRef.current !== 'disconnected') {
        console.log(`WebSocket effect instance ${effectInstanceId} disconnecting from ${connectionStatusRef.current} state`);
        disconnect();
      }
    };
  }, [isAuthenticated, token, connect, disconnect, loadChats]); // Include all dependencies

  const contextValue: ChatContextType = useMemo(() => ({
    ...state,
    loadChats,
    loadMessages,
    sendMessage,
    joinChat,
    leaveChat,
    markAsRead,
    deleteMessage,
    connect,
    disconnect,
    reconnect,
  }), [
    state,
    loadChats,
    loadMessages,
    sendMessage,
    joinChat,
    leaveChat,
    markAsRead,
    deleteMessage,
    connect,
    disconnect,
    reconnect,
  ]);

  return (
    <ChatContext.Provider value={contextValue}>
      {children}
    </ChatContext.Provider>
  );
};

// Hook to use chat context
export const useChat = (): ChatContextType => {
  const context = useContext(ChatContext);
  if (context === undefined) {
    throw new Error('useChat must be used within a ChatProvider');
  }
  return context;
};
