import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';

interface GeoLocationState {
  latitude: number | null;
  longitude: number | null;
  zipCode: string | null;
  error: string | null;
  loading: boolean;
}

interface GeolocationContextType extends GeoLocationState {
  refetch: () => void;
}

const GeolocationContext = createContext<GeolocationContextType | undefined>(undefined);

interface GeolocationProviderProps {
  children: ReactNode;
}

export const GeolocationProvider: React.FC<GeolocationProviderProps> = ({ children }) => {
  const [state, setState] = useState<GeoLocationState>({
    latitude: null,
    longitude: null,
    zipCode: null,
    error: null,
    loading: true
  });

  // Function to convert coordinates to zipcode using reverse geocoding
  const getZipCodeFromCoordinates = async (lat: number, lng: number) => {
    try {
      // Using the Nominatim OpenStreetMap service for reverse geocoding
      const response = await fetch(`https://nominatim.openstreetmap.org/reverse?format=json&lat=${lat}&lon=${lng}&zoom=18&addressdetails=1`);
      const data = await response.json();
      
      if (data && data.address && data.address.postcode) {
        return data.address.postcode;
      }
      return null;
    } catch (error) {
      console.error('Error fetching zipcode:', error);
      return null;
    }
  };

  const fetchGeolocation = async () => {
    // Only try to get location if the API is available
    if (!navigator.geolocation) {
      setState(prevState => ({
        ...prevState,
        error: "Geolocation is not supported by your browser",
        loading: false
      }));
      return;
    }

    // Get current position without asking permission (silent)
    navigator.geolocation.getCurrentPosition(
      async (position) => {
        const { latitude, longitude } = position.coords;
        
        // Try to get zipcode from coordinates
        const zipCode = await getZipCodeFromCoordinates(latitude, longitude);
        
        setState({
          latitude,
          longitude,
          zipCode,
          error: null,
          loading: false
        });
      },
      (error) => {
        setState(prevState => ({
          ...prevState,
          error: error.message,
          loading: false
        }));
      },
      // High timeout, no permission prompt, cache for 5 minutes
      { timeout: 10000, enableHighAccuracy: false, maximumAge: 300000 }
    );
  };

  useEffect(() => {
    fetchGeolocation();
  }, []);

  const refetch = () => {
    setState(prevState => ({ ...prevState, loading: true }));
    fetchGeolocation();
  };

  const value: GeolocationContextType = {
    ...state,
    refetch
  };

  return (
    <GeolocationContext.Provider value={value}>
      {children}
    </GeolocationContext.Provider>
  );
};

export const useGeolocation = (): GeolocationContextType => {
  const context = useContext(GeolocationContext);
  if (context === undefined) {
    throw new Error('useGeolocation must be used within a GeolocationProvider');
  }
  return context;
};