import { ProviderBid, BidsSummary } from './bid';

export interface JobMessage {
  id: string;
  sender: "customer" | "provider" | "admin";
  senderName: string;
  senderAvatar?: string;
  text: string;
  timestamp: string;
  attachments?: JobAttachment[];
}

export interface JobAttachment {
  id: string;
  name: string;
  type: "image" | "document" | "other";
  url: string;
  uploadedBy: string;
  uploadDate: string;
}

export interface Job {
  id: string;
  title: string;
  description: string;
  customerName: string;
  customerAvatar?: string;
  customerEmail?: string;
  provider?: string;
  providerAvatar?: string;
  providerEmail?: string;
  serviceType: string;
  status: "Open" | "In Progress" | "Completed" | "Cancelled";
  createdDate: string;
  scheduledDate?: string;
  value: string;
  hasNotes: boolean;
  location?: string;
  messages?: JobMessage[];
  attachments?: JobAttachment[];
}

export type PaymentStatus = "pending" | "in_progress" | "completed" | "cancelled";

export interface JobDispute {
  id: string;
  jobId: string;
  title: string;
  description: string;
  status: DisputeStatus;
  severity: DisputeSeverity;
  initiator: "customer" | "provider";
  initiatorName: string;
  initiatorAvatar?: string;
  respondentName: string;
  respondentAvatar?: string;
  createdAt: string;
  updatedAt: string;
  evidence: DisputeEvidence[];
  adminNotes?: string[];
  resolution?: DisputeResolution;
}

export type DisputeStatus = 
  | "submitted" 
  | "under_review" 
  | "mediation" 
  | "resolved" 
  | "appealed" 
  | "closed";

export type DisputeSeverity = "low" | "medium" | "high" | "urgent";

export interface DisputeEvidence {
  id: string;
  type: "message" | "image" | "document" | "contract" | "payment";
  submittedBy: "customer" | "provider" | "admin";
  submitterName: string;
  description: string;
  url?: string;
  attachmentId?: string;
  timestamp: string;
}

export interface DisputeResolution {
  type: "admin_decision" | "mutual_agreement";
  description: string;
  actionTaken: DisputeAction[];
  adminId?: string;
  adminName?: string;
  resolvedAt: string;
}

export interface DisputeAction {
  type: "payment_adjustment" | "refund" | "service_modification" | "contract_enforcement" | "warning" | "account_penalty";
  description: string;
  amount?: string;
  appliedTo: "customer" | "provider" | "both";
}


// New interface for the provider-specific job booking details response
export interface ProviderJobBookingDetails {
  id: number | string; // API shows number, existing Job uses string
  jobId: string; // Assuming this is the UUID
  title: string;
  description: string;
  location: unknown; // Define more specific type if available (e.g., { address: string, city: string, ... })
  schedule: unknown; // Define more specific type if available (e.g., { date: string, time: string, ... })
  contact: unknown;  // Define more specific type if available (e.g., { name: string, phone: string, ... })
  // bids: Bid[]; // This seems to be only the current provider's bid, so provider_bid is more specific
  provider_bid: ProviderBid | null;
  bids_summary: BidsSummary;
  // Include other job booking fields from your API response here
  // e.g., serviceType: string;
  // e.g., status: string;
  // e.g., createdDate: string;
}

export interface ProviderJobBookingDetailsResponse {
  success: boolean;
  data: ProviderJobBookingDetails;
  message: string;
}
