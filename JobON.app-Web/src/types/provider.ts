
export interface Provider {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  phone?: string;
  address?: string;
  city?: string;
  state?: string;
  zipCode?: string;
  avatar?: string;
  businessName?: string;
  businessLogo?: string;
  status: 'active' | 'inactive' | 'pending';
  specialty: string[];
  location: {
    city: string;
    state: string;
  };
  plan: string;
  rating: number;
  createdAt: Date | string;
  updatedAt: Date | string;
}
