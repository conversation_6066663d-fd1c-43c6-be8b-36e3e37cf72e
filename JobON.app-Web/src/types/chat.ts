// Chat-related TypeScript interfaces and types

export interface User {
  id: string;
  name: string;
  email: string;
  avatar?: string;
  role: {
    id: number;
    name: string;
    guard_name: string;
  };
}

export interface ChatParticipant {
  id: string;
  name: string;
  avatar?: string;
  type: 'customer' | 'provider' | 'admin';
  email?: string;
  last_seen?: string;
  is_online?: boolean;
}

export interface Message {
  id: string;
  chat_id: string;
  user_id: string;
  type: 'text' | 'image' | 'file';
  message: string;
  created_at: string;
  updated_at: string;
  read_at?: string;
  user: ChatParticipant;
}

export interface Chat {
  id: string;
  type: 'direct' | 'group';
  name?: string;
  description?: string;
  created_at: string;
  updated_at: string;
  participants: ChatParticipant[];
  last_message?: Message;
  unread_count?: number;
}

export interface ChatListResponse {
  data: Chat[];
  meta?: {
    current_page: number;
    last_page: number;
    per_page: number;
    total: number;
  };
}

export interface MessagesResponse {
  data: Message[];
  meta?: {
    current_page: number;
    last_page: number;
    per_page: number;
    total: number;
  };
}

export interface SendMessageRequest {
  type: 'text' | 'image' | 'file';
  message: string;
}

export interface SendMessageResponse {
  data: Message;
  success: boolean;
  message?: string;
}

// WebSocket message types
export interface WebSocketMessage {
  action: 'join' | 'message_sent' | 'user_typing' | 'user_online' | 'user_offline';
  chat_id?: string;
  data?: any;
}

export interface JoinChatMessage extends WebSocketMessage {
  action: 'join';
  chat_id: string;
}

export interface MessageSentMessage extends WebSocketMessage {
  action: 'message_sent';
  data: Message;
}

export interface UserTypingMessage extends WebSocketMessage {
  action: 'user_typing';
  chat_id: string;
  data: {
    user_id: string;
    is_typing: boolean;
  };
}

// Chat context types
export interface ChatContextType {
  // State
  chats: Chat[];
  currentChat: Chat | null;
  messages: Message[];
  isLoading: boolean;
  isConnected: boolean;
  error: string | null;
  
  // Actions
  loadChats: () => Promise<void>;
  loadMessages: (chatId: string, page?: number) => Promise<void>;
  sendMessage: (chatId: string, message: SendMessageRequest) => Promise<void>;
  joinChat: (chatId: string) => void;
  leaveChat: () => void;
  markAsRead: (chatId: string) => Promise<void>;
  deleteMessage: (chatId: string, messageId: string) => Promise<void>;
  
  // WebSocket
  connect: () => void;
  disconnect: () => void;
  reconnect: () => void;
}

// Hook return types
export interface UseChatReturn extends ChatContextType {}

// Error types
export interface ChatError {
  code: string;
  message: string;
  details?: any;
}


export interface DataChat {
    id: string;
    type: string;
    participants: ({
        id: number;
        name: string;
        email: string;
        slug: string;
        system_reserve: number;
        served: number;
        phone: null;
        code: null;
        provider_id: null;
        status: number;
        is_featured: number;
        is_verified: number;
        type: null;
        email_verified_at: null;
        fcm_token: null;
        experience_interval: null;
        experience_duration: null;
        description: null;
        created_by: null;
        created_at: string;
        updated_at: string;
        deleted_at: null;
        company_id: null;
        business_uuid: null;
        location_cordinates: null;
        certificates: never[];
        certificates_status: null;
        bookings_count: number;
        reviews_count: number;
        role: {
            id: number;
            name: string;
            guard_name: string;
            system_reserve: number;
            created_at: string;
            updated_at: string;
            pivot: { model_type: string; model_id: number; role_id: number }
        };
        review_ratings: number;
        provider_rating_list: number[];
        service_man_rating_list: number[];
        primary_address: null;
        total_days_experience: number;
        ServicemanReviewRatings: number;
        media: never[];
        wallet: null;
        provider_wallet: null;
        serviceman_wallet: null;
        known_languages: never[];
        expertise: never[];
        zones: never[];
        provider: null;
        subscriptions: never[];
        roles: {
            id: number;
            name: string;
            guard_name: string;
            system_reserve: number;
            created_at: string;
            updated_at: string;
            pivot: { model_type: string; model_id: number; role_id: number }
        }[];
        reviews: never[];
        servicemanreviews: never[];
        current_subscription: null
    } | {
        id: number;
        name: string;
        email: string;
        slug: string;
        system_reserve: number;
        served: number;
        phone: null;
        code: null;
        provider_id: null;
        status: number;
        is_featured: number;
        is_verified: number;
        type: null;
        email_verified_at: null;
        fcm_token: null;
        experience_interval: null;
        experience_duration: null;
        description: null;
        created_by: number;
        created_at: string;
        updated_at: string;
        deleted_at: null;
        company_id: null;
        business_uuid: null;
        location_cordinates: null;
        certificates: {
            uuid: string;
            file_name: string;
            url: string;
            mime_type: string;
            file_size: number;
            collection_name: string;
            custom_properties: { uploaded_by: null; original_name: string }
        }[];
        certificates_status: string;
        bookings_count: number;
        reviews_count: number;
        role: {
            id: number;
            name: string;
            guard_name: string;
            system_reserve: number;
            created_at: string;
            updated_at: string;
            pivot: { model_type: string; model_id: number; role_id: number }
        };
        review_ratings: number;
        provider_rating_list: number[];
        service_man_rating_list: number[];
        primary_address: null;
        total_days_experience: number;
        ServicemanReviewRatings: number;
        media: never[];
        wallet: null;
        provider_wallet: null;
        serviceman_wallet: null;
        known_languages: never[];
        expertise: never[];
        zones: never[];
        provider: null;
        subscriptions: never[];
        roles: {
            id: number;
            name: string;
            guard_name: string;
            system_reserve: number;
            created_at: string;
            updated_at: string;
            pivot: { model_type: string; model_id: number; role_id: number }
        }[];
        reviews: never[];
        servicemanreviews: never[];
        current_subscription: null
    })[];
    last_message: null;
    created_at: string;
    updated_at: string;
}