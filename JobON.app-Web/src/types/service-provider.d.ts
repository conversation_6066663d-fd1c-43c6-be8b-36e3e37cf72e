
// This file defines types for service providers to fix type errors in admin components

export interface Provider {
  id: string;
  name: string;
  email: string;
  phone?: string;
  specialty?: string;
  location?: string;
  plan?: string;
  rating?: number;
  status: string;
  verified: boolean;
  // Add other properties as needed
}

export interface Customer {
  id: string;
  name: string;
  email: string;
  phone?: string;
  reward_tier?: string;
  total_bookings?: number;
  last_active?: string;
  joinDate?: string;
  status: string;
  // Add other properties as needed
}
