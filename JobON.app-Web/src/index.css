@import './styles/mobile-utils.css';

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer utilities {
  .no-scrollbar::-webkit-scrollbar {
    display: none;
  }
  
  .no-scrollbar {
    -ms-overflow-x: hidden;
    scrollbar-width: none;
  }
  
  .hide-scrollbar {
    scrollbar-width: none;
    -ms-overflow-style: none;
  }
  
  .hide-scrollbar::-webkit-scrollbar {
    display: none;
  }
  
  .animate-fade-in {
    animation: fadeIn 0.3s ease-in-out;
  }
  
  @keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
  }

  /* Add slide in animation */
  .animate-slide-in {
    animation: slideIn 0.4s ease-in-out;
  }
  
  @keyframes slideIn {
    from { opacity: 0; transform: translateX(-20px); }
    to { opacity: 1; transform: translateX(0); }
  }

  /* Add scale-in animation */
  .animate-scale-in {
    animation: scaleIn 0.3s ease-in-out;
  }
  
  @keyframes scaleIn {
    from { opacity: 0; transform: scale(0.95); }
    to { opacity: 1; transform: scale(1); }
  }

  /* Add pulse animation */
  .animate-pulse-gentle {
    animation: pulseGentle 2s infinite;
  }
  
  @keyframes pulseGentle {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
  }
  
  /* Add float animation */
  .animate-float {
    animation: float 3s ease-in-out infinite;
  }
  
  @keyframes float {
    0% { transform: translateY(0); }
    50% { transform: translateY(-8px); }
    100% { transform: translateY(0); }
  }
  
  /* Add attention pulse animation for unread notifications */
  .animate-attention-pulse {
    animation: attentionPulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }
  
  @keyframes attentionPulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
  }
  
  /* Add border gradient animation */
  .animate-border-glow {
    position: relative;
    overflow: hidden;
  }
  
  .animate-border-glow::after {
    content: '';
    position: absolute;
    inset: 0;
    border: 2px solid transparent;
    border-radius: inherit;
    background: linear-gradient(90deg, #4f46e5, #6366f1, #8b5cf6, #4f46e5) border-box;
    -webkit-mask: linear-gradient(#fff 0 0) padding-box, linear-gradient(#fff 0 0);
    -webkit-mask-composite: xor;
    mask-composite: exclude;
    animation: borderRotate 4s linear infinite;
  }
  
  @keyframes borderRotate {
    from { background-position: 0% center; }
    to { background-position: 400% center; }
  }
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 221.2 83.2% 53.3%; /* This is the HSL value for #2563EB */
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;

    --radius: 0.5rem;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 221.2 83.2% 53.3%; /* This is the HSL value for #2563EB */
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Custom card with shadow and subtle border */
.card-enhanced {
  @apply bg-white rounded-xl border border-gray-100 shadow-sm transition-all duration-300 hover:shadow-md;
}

/* Gradient texts */
.text-gradient-blue {
  @apply bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent;
}

.text-gradient-green {
  @apply bg-gradient-to-r from-green-500 to-emerald-500 bg-clip-text text-transparent;
}

/* Gradient borders */
.border-gradient-blue {
  @apply border-l-4 border-blue-500;
}

.border-gradient-green {
  @apply border-l-4 border-green-500;
}

.border-gradient-yellow {
  @apply border-l-4 border-yellow-500;
}

/* Gradient buttons */
.btn-gradient-blue {
  @apply bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700 text-white;
}

.btn-gradient-green {
  @apply bg-gradient-to-r from-green-500 to-emerald-500 hover:from-green-600 hover:to-emerald-600 text-white;
}

.btn-gradient-red {
  @apply bg-gradient-to-r from-red-500 to-rose-500 hover:from-red-600 hover:to-rose-600 text-white;
}

/* Message interface specific styles */
.message-bubble-admin {
  @apply bg-gradient-to-r from-blue-500 to-indigo-600 text-white rounded-2xl rounded-tr-none py-3 px-4;
}

.message-bubble-recipient {
  @apply bg-gradient-to-r from-gray-100 to-gray-200 dark:from-gray-800 dark:to-gray-700 rounded-2xl rounded-tl-none py-3 px-4;
}

.conversation-card {
  @apply bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-100 dark:border-gray-700 hover:shadow-md transition-all duration-200;
}

.conversation-card-active {
  @apply border-l-4 border-blue-500 bg-blue-50/50 dark:bg-blue-900/20;
}

/* Glass morphism card for decorative elements */
.glass-card {
  @apply bg-white/80 dark:bg-gray-800/80 backdrop-blur-md rounded-xl border border-gray-100/50 dark:border-gray-700/50 shadow-md;
}
