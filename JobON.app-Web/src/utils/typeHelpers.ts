
export function nullToUndefined<T>(value: T | null): T | undefined {
  return value === null ? undefined : value;
}

export function ensureDefined<T>(value: T | undefined | null, fallback: T): T {
  return value != null ? value : fallback;
}

export function safeStringAccess(value: string | null | undefined): string {
  return value || '';
}

export function convertNullToUndefined<T>(value: T | null): T | undefined {
  return value === null ? undefined : value;
}

export function isValidTabKey(key: string, validKeys: Record<string, any>): key is keyof typeof validKeys {
  return key in validKeys;
}

export interface BookingItem {
  id: string;
  title: string | string[];
  client: {
    fullName: string;
    avatar?: string;
    initials?: string;
  };
  provider: {
    name: string;
    avatar: string;
    initials: string;
  };
  status: string;
  date: string;
  amount: string;
  service: {
    category: string;
    tasks?: string[];
  };
  location: {
    zipCode: string;
  };
  description: string;
}
