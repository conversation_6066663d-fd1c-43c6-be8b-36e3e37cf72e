
/**
 * Touch target validation utilities for testing
 */

export interface TouchTargetRequirements {
  minWidth: number;
  minHeight: number;
  minSpacing: number;
}

export const DEFAULT_TOUCH_REQUIREMENTS: TouchTargetRequirements = {
  minWidth: 44,
  minHeight: 44,
  minSpacing: 8,
};

/**
 * Validates touch targets in a container element
 */
export function validateTouchTargets(
  container: HTMLElement,
  requirements: TouchTargetRequirements = DEFAULT_TOUCH_REQUIREMENTS
): { isValid: boolean; violations: string[] } {
  const violations: string[] = [];
  
  // Find all interactive elements
  const interactiveSelectors = [
    'button',
    'a',
    '[role="button"]',
    '[tabindex]',
    'input[type="button"]',
    'input[type="submit"]',
    'input[type="reset"]',
  ];
  
  const interactiveElements = container.querySelectorAll(
    interactiveSelectors.join(', ')
  ) as NodeListOf<HTMLElement>;
  
  interactiveElements.forEach((element, index) => {
    const rect = element.getBoundingClientRect();
    const computedStyle = window.getComputedStyle(element);
    
    // Check minimum size requirements
    if (rect.width < requirements.minWidth) {
      violations.push(
        `Element ${index} (${element.tagName}) width ${rect.width}px is below minimum ${requirements.minWidth}px`
      );
    }
    
    if (rect.height < requirements.minHeight) {
      violations.push(
        `Element ${index} (${element.tagName}) height ${rect.height}px is below minimum ${requirements.minHeight}px`
      );
    }
    
    // Check if element is visible
    if (computedStyle.display === 'none' || computedStyle.visibility === 'hidden') {
      violations.push(`Element ${index} (${element.tagName}) is not visible`);
    }
  });
  
  return {
    isValid: violations.length === 0,
    violations,
  };
}

/**
 * Creates a visual overlay showing touch target areas for debugging
 */
export function createTouchTargetOverlay(container: HTMLElement): HTMLElement {
  const overlay = document.createElement('div');
  overlay.style.cssText = `
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 9999;
  `;
  
  const interactiveElements = container.querySelectorAll(
    'button, a, [role="button"], [tabindex]'
  ) as NodeListOf<HTMLElement>;
  
  interactiveElements.forEach((element) => {
    const rect = element.getBoundingClientRect();
    const containerRect = container.getBoundingClientRect();
    
    const highlight = document.createElement('div');
    highlight.style.cssText = `
      position: absolute;
      left: ${rect.left - containerRect.left}px;
      top: ${rect.top - containerRect.top}px;
      width: ${rect.width}px;
      height: ${rect.height}px;
      border: 2px solid ${rect.width >= 44 && rect.height >= 44 ? 'green' : 'red'};
      background: ${rect.width >= 44 && rect.height >= 44 ? 'rgba(0, 255, 0, 0.1)' : 'rgba(255, 0, 0, 0.1)'};
      border-radius: 4px;
    `;
    
    overlay.appendChild(highlight);
  });
  
  return overlay;
}
