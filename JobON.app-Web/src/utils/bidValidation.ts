/**
 * Bid validation utilities for Task 17 - Subtask 17.1
 * Implements comprehensive input validation for bid creation
 */

export interface BidValidationError {
  field: string;
  message: string;
  code: string;
}

export interface BidValidationResult {
  isValid: boolean;
  errors: BidValidationError[];
}

/**
 * Validates service request ID
 */
export const validateServiceRequestId = (serviceRequestId: string): BidValidationError[] => {
  const errors: BidValidationError[] = [];

  if (!serviceRequestId) {
    errors.push({
      field: 'serviceRequestId',
      message: 'Service request ID is required',
      code: 'REQUIRED_FIELD'
    });
    return errors;
  }

  if (typeof serviceRequestId !== 'string') {
    errors.push({
      field: 'serviceRequestId',
      message: 'Service request ID must be a string',
      code: 'INVALID_TYPE'
    });
  }

  // Check if it's a valid UUID format (basic check)
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
  if (!uuidRegex.test(serviceRequestId.trim())) {
    errors.push({
      field: 'serviceRequestId',
      message: 'Service request ID must be a valid UUID',
      code: 'INVALID_FORMAT'
    });
  }

  return errors;
};

/**
 * Validates bid amount
 */
export const validateBidAmount = (amount: number | string): BidValidationError[] => {
  const errors: BidValidationError[] = [];

  if (amount === null || amount === undefined || amount === '') {
    errors.push({
      field: 'amount',
      message: 'Bid amount is required',
      code: 'REQUIRED_FIELD'
    });
    return errors;
  }

  const numericAmount = typeof amount === 'string' ? parseFloat(amount) : amount;

  if (isNaN(numericAmount)) {
    errors.push({
      field: 'amount',
      message: 'Bid amount must be a valid number',
      code: 'INVALID_TYPE'
    });
    return errors;
  }

  if (numericAmount <= 0) {
    errors.push({
      field: 'amount',
      message: 'Bid amount must be greater than 0',
      code: 'INVALID_VALUE'
    });
  }

  if (numericAmount > 1000000) {
    errors.push({
      field: 'amount',
      message: 'Bid amount cannot exceed $1,000,000',
      code: 'EXCEEDS_MAXIMUM'
    });
  }

  // Check for reasonable decimal places (max 2)
  const decimalPlaces = (numericAmount.toString().split('.')[1] || '').length;
  if (decimalPlaces > 2) {
    errors.push({
      field: 'amount',
      message: 'Bid amount can have at most 2 decimal places',
      code: 'INVALID_PRECISION'
    });
  }

  return errors;
};

/**
 * Validates bid description
 */
export const validateBidDescription = (description: string): BidValidationError[] => {
  const errors: BidValidationError[] = [];

  if (!description || !description.trim()) {
    errors.push({
      field: 'description',
      message: 'Bid description is required',
      code: 'REQUIRED_FIELD'
    });
    return errors;
  }

  if (typeof description !== 'string') {
    errors.push({
      field: 'description',
      message: 'Bid description must be a string',
      code: 'INVALID_TYPE'
    });
    return errors;
  }

  const trimmedDescription = description.trim();

  if (trimmedDescription.length < 10) {
    errors.push({
      field: 'description',
      message: 'Bid description must be at least 10 characters long',
      code: 'TOO_SHORT'
    });
  }

  if (trimmedDescription.length > 2000) {
    errors.push({
      field: 'description',
      message: 'Bid description cannot exceed 2000 characters',
      code: 'TOO_LONG'
    });
  }

  // Check for potentially harmful content (basic check)
  const suspiciousPatterns = [
    /<script/i,
    /javascript:/i,
    /on\w+\s*=/i,
    /<iframe/i
  ];

  for (const pattern of suspiciousPatterns) {
    if (pattern.test(trimmedDescription)) {
      errors.push({
        field: 'description',
        message: 'Bid description contains invalid content',
        code: 'INVALID_CONTENT'
      });
      break;
    }
  }

  return errors;
};

/**
 * Validates estimated completion time
 */
export const validateEstimatedCompletionTime = (estimatedCompletionTime: string): BidValidationError[] => {
  const errors: BidValidationError[] = [];

  if (!estimatedCompletionTime || !estimatedCompletionTime.trim()) {
    errors.push({
      field: 'estimated_completion_time',
      message: 'Estimated completion time is required',
      code: 'REQUIRED_FIELD'
    });
    return errors;
  }

  if (typeof estimatedCompletionTime !== 'string') {
    errors.push({
      field: 'estimated_completion_time',
      message: 'Estimated completion time must be a string',
      code: 'INVALID_TYPE'
    });
    return errors;
  }

  // Validate that it's a valid ISO date string
  const completionDate = new Date(estimatedCompletionTime.trim());
  if (isNaN(completionDate.getTime())) {
    errors.push({
      field: 'estimated_completion_time',
      message: 'Estimated completion time must be a valid date',
      code: 'INVALID_FORMAT'
    });
    return errors;
  }

  // Check that the date is in the future
  const now = new Date();
  if (completionDate <= now) {
    errors.push({
      field: 'estimated_completion_time',
      message: 'Estimated completion time must be in the future',
      code: 'INVALID_VALUE'
    });
  }

  return errors;
};

/**
 * Comprehensive bid validation
 */
export const validateBidData = (bidData: {
  jobId: string;
  amount: number | string;
  description: string;
  estimated_completion_time: string;
}): BidValidationResult => {
  const allErrors: BidValidationError[] = [];

  // Validate service request ID (using jobId as service_request_id)
  allErrors.push(...validateServiceRequestId(bidData.jobId));

  // Validate amount
  allErrors.push(...validateBidAmount(bidData.amount));

  // Validate description
  allErrors.push(...validateBidDescription(bidData.description));

  // Validate estimated completion time
  allErrors.push(...validateEstimatedCompletionTime(bidData.estimated_completion_time));

  return {
    isValid: allErrors.length === 0,
    errors: allErrors
  };
};

/**
 * Format validation errors for display
 */
export const formatValidationErrors = (errors: BidValidationError[]): string => {
  if (errors.length === 0) return '';
  
  if (errors.length === 1) {
    return errors[0].message;
  }

  return errors.map(error => `• ${error.message}`).join('\n');
};

/**
 * Get first error for a specific field
 */
export const getFieldError = (errors: BidValidationError[], field: string): string | null => {
  const fieldError = errors.find(error => error.field === field);
  return fieldError ? fieldError.message : null;
};

/**
 * Validates bid amount for updates (optional field)
 */
export const validateUpdateBidAmount = (amount?: number | string): BidValidationError[] => {
  const errors: BidValidationError[] = [];

  // Amount is optional for updates
  if (amount === null || amount === undefined || amount === '') {
    return errors; // No validation errors for optional field
  }

  const numericAmount = typeof amount === 'string' ? parseFloat(amount) : amount;

  if (isNaN(numericAmount)) {
    errors.push({
      field: 'amount',
      message: 'Bid amount must be a valid number',
      code: 'INVALID_TYPE'
    });
    return errors;
  }

  if (numericAmount <= 0) {
    errors.push({
      field: 'amount',
      message: 'Bid amount must be greater than 0',
      code: 'INVALID_VALUE'
    });
  }

  if (numericAmount > 1000000) {
    errors.push({
      field: 'amount',
      message: 'Bid amount cannot exceed $1,000,000',
      code: 'EXCEEDS_MAXIMUM'
    });
  }

  // Check for reasonable decimal places (max 2)
  const decimalPlaces = (numericAmount.toString().split('.')[1] || '').length;
  if (decimalPlaces > 2) {
    errors.push({
      field: 'amount',
      message: 'Bid amount can have at most 2 decimal places',
      code: 'INVALID_PRECISION'
    });
  }

  return errors;
};

/**
 * Validates bid description for updates (optional field)
 */
export const validateUpdateBidDescription = (description?: string): BidValidationError[] => {
  const errors: BidValidationError[] = [];

  // Description is optional for updates
  if (!description || description === '') {
    return errors; // No validation errors for optional field
  }

  if (typeof description !== 'string') {
    errors.push({
      field: 'description',
      message: 'Bid description must be a string',
      code: 'INVALID_TYPE'
    });
    return errors;
  }

  const trimmedDescription = description.trim();

  if (trimmedDescription.length < 10) {
    errors.push({
      field: 'description',
      message: 'Bid description must be at least 10 characters long',
      code: 'TOO_SHORT'
    });
  }

  if (trimmedDescription.length > 2000) {
    errors.push({
      field: 'description',
      message: 'Bid description cannot exceed 2000 characters',
      code: 'TOO_LONG'
    });
  }

  // Check for potentially harmful content (basic check)
  const suspiciousPatterns = [
    /<script/i,
    /javascript:/i,
    /on\w+\s*=/i,
    /<iframe/i
  ];

  for (const pattern of suspiciousPatterns) {
    if (pattern.test(trimmedDescription)) {
      errors.push({
        field: 'description',
        message: 'Bid description contains invalid content',
        code: 'INVALID_CONTENT'
      });
      break;
    }
  }

  return errors;
};

/**
 * Comprehensive bid update validation
 */
export const validateBidUpdateData = (updateData: {
  amount?: number | string;
  description?: string;
}): BidValidationResult => {
  const allErrors: BidValidationError[] = [];

  // Check that at least one field is being updated
  if (updateData.amount === undefined && !updateData.description) {
    allErrors.push({
      field: 'general',
      message: 'At least one field (amount or description) must be provided for update',
      code: 'NO_FIELDS_TO_UPDATE'
    });
    return {
      isValid: false,
      errors: allErrors
    };
  }

  // Validate amount if provided
  if (updateData.amount !== undefined) {
    allErrors.push(...validateUpdateBidAmount(updateData.amount));
  }

  // Validate description if provided
  if (updateData.description !== undefined) {
    allErrors.push(...validateUpdateBidDescription(updateData.description));
  }

  return {
    isValid: allErrors.length === 0,
    errors: allErrors
  };
};

/**
 * Validates bid ID format
 */
export const validateBidId = (bidId: string): BidValidationError[] => {
  const errors: BidValidationError[] = [];

  if (!bidId) {
    errors.push({
      field: 'bidId',
      message: 'Bid ID is required',
      code: 'REQUIRED_FIELD'
    });
    return errors;
  }

  if (typeof bidId !== 'string') {
    errors.push({
      field: 'bidId',
      message: 'Bid ID must be a string',
      code: 'INVALID_TYPE'
    });
  }

  // Check if it's a valid UUID format (basic check)
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
  if (!uuidRegex.test(bidId.trim())) {
    errors.push({
      field: 'bidId',
      message: 'Bid ID must be a valid UUID format',
      code: 'INVALID_FORMAT'
    });
  }

  return errors;
};
