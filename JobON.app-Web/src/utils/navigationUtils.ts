
export const setPreviousPage = () => {
  const currentPage = window.location.pathname;
  // Store the current page as the previous page for future navigation
  document.cookie = `previousPage=${currentPage}; path=/`;
};

export const getPreviousPage = (): string => {
  const cookies = document.cookie.split(';');
  const previousPageCookie = cookies.find(cookie => cookie.trim().startsWith('previousPage='));
  return previousPageCookie ? previousPageCookie.split('=')[1] : '/';
};

export const navigateToPreviousPage = (navigate: any, locationState?: any) => {
  // First check if there's a referring page in location state
  if (locationState && locationState.from) {
    navigate(locationState.from);
    return;
  }
  
  // Fall back to cookie-based previous page if no state is available
  const previousPage = getPreviousPage();
  navigate(previousPage);
};
