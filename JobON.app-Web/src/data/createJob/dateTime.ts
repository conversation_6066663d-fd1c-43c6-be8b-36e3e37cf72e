interface timeOptionsType {
    id: string;
    value: string;
    label: string;
    description: string;
    iconColor: string;
}

export const timeOptions:timeOptionsType[] = [
    {
        id: "morning",
        value: "morning",
        label: "Morning",
        description: "8am - 12pm",
        iconColor: "text-yellow-500"
    },
    {
        id: "afternoon",
        value: "afternoon",
        label: "Afternoon",
        description: "12pm - 5pm",
        iconColor: "text-orange-500"
    },
    {
        id: "evening",
        value: "evening",
        label: "Evening",
        description: "5pm - 9pm",
        iconColor: "text-blue-500"
    },
    {
        id: "flexible",
        value: "flexible",
        label: "Flexible",
        description: "Any time",
        iconColor: "text-purple-500"
    }
];

