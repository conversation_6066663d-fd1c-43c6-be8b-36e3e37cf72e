import {Briefcase, Building, HomeIcon} from "lucide-react";
import {SVGProps} from "react";

interface propertyType {
    type: string;
    icon: React.FC<SVGProps<SVGSVGElement>>;
    label: string;
}

export const property:propertyType[] = [
    { type: 'residential', icon: HomeIcon, label: 'Home' },
    { type: 'commercial', icon: Building, label: 'Business' },
    { type: 'industrial', icon: Briefcase, label: 'Other' }
]
