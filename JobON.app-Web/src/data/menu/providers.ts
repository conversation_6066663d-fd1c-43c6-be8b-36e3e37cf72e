import {Calculator, Calendar, FileSpreadsheet, FileText, Search, Wrench} from "lucide-react";
import {FC, SVGProps} from "react";

interface subItemsType {
    id: string;
    path: string;
    name: string;
    icon: React.FC<SVGProps<SVGSVGElement>>;
    className: string;
}

export interface ToolItem {
    id: string;
    path: string;
    name: string;
    icon: FC<SVGProps<SVGSVGElement>>;
    subItems? : subItemsType[];
}


export const providersData: ToolItem[] = [
    {
        id: 'find-jobs',
        path: '/jobs',
        name: 'Find Jobs',
        icon: Search,
    },
    {
        id: 'free-tools',
        path: '/free-tools',
        name: 'All Free Tools',
        icon: Wrench,
        subItems: [
            {
                id: 'pricing-calculator',
                path: '/free-tools/pricing-calculator',
                name: 'Service Price Calculator',
                icon: Calculator,
                className: 'text-sm text-foreground/60',
            },
            {
                id: 'profit-calculator',
                path: '/free-tools/profit-calculator',
                name: 'Profit Margin Calculator',
                icon: Calculator,
                className: 'text-sm text-foreground/60',
            },
            {
                id: 'invoice-templates',
                path: '/free-tools/invoice-templates',
                name: 'Invoice Generator',
                icon: FileSpreadsheet,
                className: 'text-sm text-foreground/60',
            },
            {
                id: 'estimate-template',
                path: '/free-tools/estimate-template',
                name: 'Estimate Template',
                icon: FileText,
                className: 'text-sm text-foreground/60',
            },
            {
                id: 'scheduling-assistant',
                path: '/free-tools/scheduling-assistant',
                name: 'Scheduling Assistant',
                icon: Calendar,
                className: 'text-sm text-foreground/60',
            },
        ],
    },
];
