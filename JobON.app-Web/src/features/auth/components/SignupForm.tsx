import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { 
  Form, 
  FormControl, 
  FormField, 
  FormItem, 
  FormLabel, 
  FormMessage,
  FormDescription
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { Alert, AlertTitle, AlertDescription } from '@/components/ui/alert';
import { ArrowRight, Info } from 'lucide-react';
import { cn } from '@/lib/utils';
import { useAuthForms } from '../hooks/useAuthForms';
import AuthProviderButtons from './AuthProviderButtons';

interface SignupFormProps {
  accountType: 'customer' | 'professional';
  onModeChange: () => void;
  onBack: () => void;
  redirectUrl?: string;
}

const SignupForm: React.FC<SignupFormProps> = ({ 
  accountType, 
  onModeChange,
  onBack,
  redirectUrl
}) => {
  const { signupForm, onSignupSubmit, isLoading } = useAuthForms(redirectUrl);

  // Set account type in form when it changes
  React.useEffect(() => {
    signupForm.setValue('accountType', accountType);
  }, [accountType, signupForm]);

  return (
    <div className="space-y-4">
      <div className="flex items-center gap-3 mb-2">
        <Button 
          variant="ghost" 
          size="icon" 
          className="h-8 w-8" 
          onClick={onBack}
        >
          <ArrowRight className="h-4 w-4 rotate-180" />
        </Button>
        <h3 className="text-lg font-semibold flex items-center gap-2">
          {accountType === 'professional' ? (
            <>Create Provider Account</>
          ) : (
            <>Create Customer Account</>
          )}
        </h3>
      </div>

      <AuthProviderButtons activeTab={accountType} redirectUrl={redirectUrl} />

      <Form {...signupForm}>
        <form onSubmit={signupForm.handleSubmit(onSignupSubmit)} className="space-y-4">
          <FormField
            control={signupForm.control}
            name="name"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Full Name</FormLabel>
                <FormControl>
                  <Input 
                    placeholder="John Doe" 
                    {...field} 
                    className="bg-background focus:ring-2 focus:ring-primary/20" 
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={signupForm.control}
            name="email"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Email</FormLabel>
                <FormControl>
                  <Input 
                    placeholder="<EMAIL>" 
                    {...field} 
                    className="bg-background focus:ring-2 focus:ring-primary/20" 
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={signupForm.control}
            name="password"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Password</FormLabel>
                <FormControl>
                  <Input 
                    type="password" 
                    placeholder="••••••••" 
                    {...field} 
                    className="bg-background focus:ring-2 focus:ring-primary/20" 
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={signupForm.control}
            name="confirmPassword"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Confirm Password</FormLabel>
                <FormControl>
                  <Input 
                    type="password" 
                    placeholder="••••••••" 
                    {...field} 
                    className="bg-background focus:ring-2 focus:ring-primary/20" 
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Hidden field for account type */}
          <input type="hidden" {...signupForm.register("accountType")} value={accountType} />

          {accountType === "professional" && (
            <FormField
              control={signupForm.control}
              name="invitationCode"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Invitation Code (Optional)</FormLabel>
                  <FormControl>
                    <Input 
                      placeholder="Enter your invitation code" 
                      {...field} 
                      className="bg-background focus:ring-2 focus:ring-primary/20" 
                    />
                  </FormControl>
                  <FormDescription>
                    If you received an invitation code, enter it here for special offers.
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
          )}

          {accountType === "professional" && (
            <Alert className="bg-blue-50 text-blue-800 border-blue-200">
              <Info className="h-4 w-4" />
              <AlertTitle>Professional Account Notice</AlertTitle>
              <AlertDescription>
                Professional accounts require verification. After signup, your account will be reviewed within 24-48 hours before you can bid on jobs.
              </AlertDescription>
            </Alert>
          )}

          <FormField
            control={signupForm.control}
            name="agreeTerms"
            render={({ field }) => (
              <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md p-4">
                <FormControl>
                  <Checkbox
                    checked={field.value}
                    onCheckedChange={field.onChange}
                  />
                </FormControl>
                <div className="space-y-1 leading-none">
                  <FormLabel>
                    I agree to the{' '}
                    <Link to="/terms" className="text-primary hover:underline">
                      terms of service
                    </Link>{' '}
                    and{' '}
                    <Link to="/privacy" className="text-primary hover:underline">
                      privacy policy
                    </Link>
                  </FormLabel>
                </div>
              </FormItem>
            )}
          />

          <Button 
            type="submit" 
            className={cn(
              "w-full",
              accountType === 'professional' ? "bg-green-600 hover:bg-green-700" : ""
            )}
            disabled={isLoading}
          >
            {isLoading ? "Creating account..." : "Create account"}
          </Button>
        </form>
      </Form>

      <div className="text-center mt-4">
        <p className="text-sm text-muted-foreground">
          Already have an account?{' '}
          <button 
            type="button" 
            className="text-primary font-medium hover:underline" 
            onClick={onModeChange}
          >
            Log in
          </button>
        </p>
      </div>
    </div>
  );
};

export default SignupForm;
