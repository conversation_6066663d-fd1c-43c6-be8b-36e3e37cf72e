import React from 'react';
import { Link } from 'react-router-dom';
import { 
  Form, 
  FormControl, 
  FormField, 
  FormItem, 
  FormLabel, 
  FormMessage 
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { useAuthForms } from '../hooks/useAuthForms';
import AuthProviderButtons from './AuthProviderButtons';

interface LoginFormProps {
  onModeChange: () => void;
  redirectUrl?: string;
  activeTab?: 'customer' | 'professional';
}

const LoginForm: React.FC<LoginFormProps> = ({ onModeChange, redirectUrl, activeTab = 'customer' }) => {
  const { loginForm, onLoginSubmit, isLoading } = useAuthForms(redirectUrl);

  return (
    <div className="space-y-4">
      <AuthProviderButtons activeTab={activeTab} redirectUrl={redirectUrl} />

      <Form {...loginForm}>
        <form onSubmit={loginForm.handleSubmit(onLoginSubmit)} className="space-y-4">
          <FormField
            control={loginForm.control}
            name="email"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Email</FormLabel>
                <FormControl>
                  <Input 
                    placeholder="<EMAIL>" 
                    {...field} 
                    className="bg-background focus:ring-2 focus:ring-primary/20" 
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={loginForm.control}
            name="password"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Password</FormLabel>
                <FormControl>
                  <Input 
                    type="password" 
                    placeholder="••••••••" 
                    {...field} 
                    className="bg-background focus:ring-2 focus:ring-primary/20" 
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={loginForm.control}
            name="rememberMe"
            render={({ field }) => (
              <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md py-2">
                <FormControl>
                  <Checkbox
                    checked={field.value}
                    onCheckedChange={field.onChange}
                  />
                </FormControl>
                <div className="space-y-1 leading-none">
                  <FormLabel>
                    Remember me
                  </FormLabel>
                </div>
              </FormItem>
            )}
          />

          <Button type="submit" className="w-full" disabled={isLoading}>
            {isLoading ? "Logging in..." : "Login"}
          </Button>
        </form>
      </Form>

      <div className="text-center mt-4">
        <Link to="/forgot-password" className="text-sm text-primary hover:underline">
          Forgot your password?
        </Link>
      </div>

      <div className="text-center mt-4">
        <p className="text-sm text-muted-foreground">
          Don't have an account?{' '}
          <button 
            type="button" 
            className="text-primary font-medium hover:underline" 
            onClick={onModeChange}
          >
            Sign up
          </button>
        </p>
      </div>
    </div>
  );
};

export default LoginForm;
