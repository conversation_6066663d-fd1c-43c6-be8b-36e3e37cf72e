
import React from 'react';
import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON>er, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { useAuth } from '../hooks/useAuth';
import { LogOut, User } from 'lucide-react';

const AuthenticatedUser: React.FC = () => {
  const { user, logout } = useAuth();

  return (
    <div className="max-w-md mx-auto pt-20">
      <Card className="border-none shadow-lg">
        <CardHeader className="bg-gradient-to-r from-blue-50 to-indigo-50">
          <div className="flex justify-center mb-4">
            <div className="w-20 h-20 bg-primary/10 rounded-full flex items-center justify-center">
              <User className="h-10 w-10 text-primary" />
            </div>
          </div>
          <CardTitle className="text-center">Welcome back!</CardTitle>
          <CardDescription className="text-center">
            You are already signed in
          </CardDescription>
        </CardHeader>
        <CardContent className="pt-6">
          <div className="space-y-4">
            <div className="border rounded-md p-3">
              <div className="text-sm text-muted-foreground mb-1">Name</div>
              <div className="font-medium">{user?.name || 'User'}</div>
            </div>
            <div className="border rounded-md p-3">
              <div className="text-sm text-muted-foreground mb-1">Email</div>
              <div className="font-medium">{user?.email || '<EMAIL>'}</div>
            </div>
            <div className="border rounded-md p-3">
              <div className="text-sm text-muted-foreground mb-1">Account Type</div>
              <div className="font-medium capitalize">{user?.role?.name || 'Customer'}</div>
            </div>
          </div>
        </CardContent>
        <CardFooter>
          <Button 
            variant="outline" 
            className="w-full flex items-center justify-center gap-2"
            onClick={logout}
          >
            <LogOut className="h-4 w-4" />
            Sign Out
          </Button>
        </CardFooter>
      </Card>
    </div>
  );
};

export default AuthenticatedUser;
