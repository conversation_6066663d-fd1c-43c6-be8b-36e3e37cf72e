
import React from 'react';
import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON>er, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { ArrowRight, Briefcase, Check, Home } from 'lucide-react';
import { cn } from '@/lib/utils';

interface AccountTypeSelectionProps {
  onSelect: (type: 'customer' | 'professional') => void;
  isMobile?: boolean;
}

const AccountTypeSelection: React.FC<AccountTypeSelectionProps> = ({ onSelect, isMobile }) => {
  return (
    <div className={cn(
      "grid gap-6 mx-auto",
      isMobile ? "grid-cols-1" : "md:grid-cols-2", 
      isMobile ? "max-w-sm" : "max-w-4xl"
    )}>
      {/* Customer Card */}
      <Card 
        className={cn(
          "border-2 overflow-hidden transition-all hover:shadow-lg",
          isMobile && "mb-0"
        )}
        onClick={() => onSelect('customer')}
      >
        <div className={cn(
          "bg-gradient-to-r from-blue-50 to-indigo-50 border-b-2 border-blue-100",
          isMobile ? "p-2" : "p-4"
        )}>
          <div className={cn(
            "mx-auto flex items-center justify-center bg-blue-100 rounded-full text-blue-600",
            isMobile ? "w-10 h-10" : "w-12 h-12"
          )}>
            <Home className={cn(isMobile ? "h-5 w-5" : "h-6 w-6")} />
          </div>
        </div>
        <CardHeader className={cn(isMobile && "p-3 pb-0")}>
          <CardTitle className={cn(
            "flex items-center justify-center gap-2",
            isMobile ? "text-lg" : "text-xl"
          )}>
            I Need a Service
            <span className="text-blue-500">
              <ArrowRight className="h-4 w-4" />
            </span>
          </CardTitle>
          <CardDescription className={cn("text-center", isMobile && "text-xs")}>
            Sign up as a Customer
          </CardDescription>
        </CardHeader>
        <CardContent className={cn(isMobile && "p-3 pt-0")}>
          <ul className={cn("space-y-3", isMobile && "space-y-2")}>
            <li className="flex items-start gap-2">
              <div className="mt-0.5 text-blue-500">
                <Check className={cn(isMobile ? "h-3 w-3" : "h-4 w-4")} />
              </div>
              <span className={cn(isMobile ? "text-xs" : "text-sm")}>
                Find trusted professionals in your area
              </span>
            </li>
            <li className="flex items-start gap-2">
              <div className="mt-0.5 text-blue-500">
                <Check className={cn(isMobile ? "h-3 w-3" : "h-4 w-4")} />
              </div>
              <span className={cn(isMobile ? "text-xs" : "text-sm")}>
                Post jobs and receive competitive bids
              </span>
            </li>
            <li className="flex items-start gap-2">
              <div className="mt-0.5 text-blue-500">
                <Check className={cn(isMobile ? "h-3 w-3" : "h-4 w-4")} />
              </div>
              <span className={cn(isMobile ? "text-xs" : "text-sm")}>
                Manage projects and payments securely
              </span>
            </li>
          </ul>
        </CardContent>
        <CardFooter className={cn(isMobile && "p-3")}>
          <Button 
            className={cn(
              "w-full flex items-center justify-center gap-2 group",
              isMobile && "text-sm py-1.5"
            )}
            onClick={() => onSelect('customer')}
          >
            Sign Up as Customer
            <ArrowRight className="h-4 w-4 transition-transform group-hover:translate-x-1" />
          </Button>
        </CardFooter>
      </Card>

      {/* Provider Card */}
      <Card 
        className={cn(
          "border-2 overflow-hidden transition-all hover:shadow-lg",
          isMobile && "mb-0"
        )}
        onClick={() => onSelect('professional')}
      >
        <div className={cn(
          "bg-gradient-to-r from-green-50 to-teal-50 border-b-2 border-green-100",
          isMobile ? "p-2" : "p-4"
        )}>
          <div className={cn(
            "mx-auto flex items-center justify-center bg-green-100 rounded-full text-green-600",
            isMobile ? "w-10 h-10" : "w-12 h-12"
          )}>
            <Briefcase className={cn(isMobile ? "h-5 w-5" : "h-6 w-6")} />
          </div>
        </div>
        <CardHeader className={cn(isMobile && "p-3 pb-0")}>
          <CardTitle className={cn(
            "flex items-center justify-center gap-2",
            isMobile ? "text-lg" : "text-xl"
          )}>
            I Offer Services
            <span className="text-green-500">
              <ArrowRight className="h-4 w-4" />
            </span>
          </CardTitle>
          <CardDescription className={cn("text-center", isMobile && "text-xs")}>
            Sign up as a Provider
          </CardDescription>
        </CardHeader>
        <CardContent className={cn(isMobile && "p-3 pt-0")}>
          <ul className={cn("space-y-3", isMobile && "space-y-2")}>
            <li className="flex items-start gap-2">
              <div className="mt-0.5 text-green-500">
                <Check className={cn(isMobile ? "h-3 w-3" : "h-4 w-4")} />
              </div>
              <span className={cn(isMobile ? "text-xs" : "text-sm")}>
                Connect with new customers in your area
              </span>
            </li>
            <li className="flex items-start gap-2">
              <div className="mt-0.5 text-green-500">
                <Check className={cn(isMobile ? "h-3 w-3" : "h-4 w-4")} />
              </div>
              <span className={cn(isMobile ? "text-xs" : "text-sm")}>
                Receive job alerts matching your skills
              </span>
            </li>
            <li className="flex items-start gap-2">
              <div className="mt-0.5 text-green-500">
                <Check className={cn(isMobile ? "h-3 w-3" : "h-4 w-4")} />
              </div>
              <span className={cn(isMobile ? "text-xs" : "text-sm")}>
                Build your reputation with verified reviews
              </span>
            </li>
          </ul>
        </CardContent>
        <CardFooter className={cn(isMobile && "p-3")}>
          <Button 
            className={cn(
              "w-full bg-green-600 hover:bg-green-700 flex items-center justify-center gap-2 group",
              isMobile && "text-sm py-1.5"
            )}
            onClick={() => onSelect('professional')}
          >
            Sign Up as Provider
            <ArrowRight className="h-4 w-4 transition-transform group-hover:translate-x-1" />
          </Button>
        </CardFooter>
      </Card>
    </div>
  );
};

export default AccountTypeSelection;
