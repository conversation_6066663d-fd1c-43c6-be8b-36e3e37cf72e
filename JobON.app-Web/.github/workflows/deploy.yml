name: Build and Deploy

on:
  push:
    branches: [main]
  workflow_dispatch:

jobs:
  build-and-deploy:
    runs-on: ubuntu-latest
    environment: prod

    steps:
      - name: Checkout repository
        uses: actions/checkout@v3

      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: "18"
          cache: 'npm'

      - name: Clean install
        run: |
          rm -rf node_modules package-lock.json
          npm i -f

      - name: Create .env file
        run: |
          echo "VITE_API_URL=${{ secrets.VITE_API_URL }}" >> .env
          echo "VITE_GOOGLE_CLIENT_ID=${{secrets.VITE_GOOGLE_CLIENT_ID}}" >> .env
          echo "VITE_GOOGLE_CLIENT_SECRET=${{secrets.VITE_GOOGLE_CLIENT_SECRET}}" >> .env
          echo "VITE_MAPBOX_TOKEN=${{secrets.VITE_MAPBOX_TOKEN}}" >> .env
          echo "VITE_STRIPE_PUBLISHABLE_KEY=${{secrets.VITE_STRIPE_PUBLISHABLE_KEY}}" >> .env
          # Add other environment variables as needed
          echo ".env file created successfully"

      - name: Build project
        run: npm run build

      - name: Deploy to Server
        uses: appleboy/scp-action@master
        with:
          host: ${{ secrets.HOST }}
          username: ${{ secrets.USERNAME }}
          password: ${{ secrets.PASSWORD }}
          source: "dist/*"
          target: ${{ secrets.DEPLOY_PATH }}
          strip_components: 1